# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# typescript
*.tsbuildinfo
next-env.d.ts
# **/public/Sitemap-Beaches1.xml
# **/public/Sitemap-Beaches2.xml
# **/public/Sitemap-Beaches3.xml
# **/public/Sitemap-Beaches4.xml
# **/public/Sitemap-Beaches5.xml
# **/public/Sitemap-List.xml
# **/public/Sitemap-Blog.xml
# **/public/Sitemap-Island.xml
# **/public/Sitemap-City.xml
# **/public/Sitemap-State.xml
# **/public/Sitemap-Main.xml
# **/public/Sitemap-Country.xml
# **/public/sitemap.xml
# **/public/Sitemap-*.xml
# **/public/Sitemap-MapBeaches1.xml
# **/public/Sitemap-MapBeaches2.xml
# **/public/Sitemap-MapBeaches3.xml
# **/public/Sitemap-MapBeaches4.xml
# **/public/Sitemap-MapBeaches5.xml
# service worker
**/public/sw.js
**/public/workbox-*.js
**/public/worker-*.js
**/public/sw.js.map
**/public/workbox-*.js.map
**/public/worker-*.js.map
