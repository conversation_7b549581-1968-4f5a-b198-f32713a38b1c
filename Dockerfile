# ----------- STAGE 1: Builder ----------- #
FROM node:20-alpine AS builder

# Set working directory
WORKDIR /app

# Install system deps (for native packages like sharp)
RUN apk add --no-cache libc6-compat

# Copy package.json and lock file
COPY package*.json ./

# Install all dependencies (including dev)
RUN npm install --frozen-lockfile

# Copy app source
COPY . .

# Build Next.js app
RUN npm run build

# Clean up any build-time env files
RUN rm -f .env


# ----------- STAGE 2: Runner (Minimal Image) ----------- #
FROM node:20-alpine AS runner

WORKDIR /app

# Copy built code and node_modules from builder
COPY --from=builder /app/.next .next
COPY --from=builder /app/public ./public
COPY --from=builder /app/next.config.js ./next.config.js
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json

COPY .env.runtime .env

EXPOSE 3000

CMD ["npx", "next", "start"]
