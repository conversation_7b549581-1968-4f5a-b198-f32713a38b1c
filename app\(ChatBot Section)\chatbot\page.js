// import MapWithBeaches from "@/components/map/MapWithBeaches";
// import ChatbotFinalsPage from "@/components/chatbotPage/ChatBotUIFinals";
// import { SiteDataPageWise } from "@/data/siteMetadata";
import ChatbotFinals2Page from "@/components/chatbotPage/ChatbotUIFinals2";
import React from "react";
// import ChatbotPage from "@/components/chatbotPage/ChatBotUI";
// import ChatbotFinalPage from "@/components/chatbotPage/ChatBotUIFinal";
// import ChatbotFinals1Page from "@/components/chatbotPage/ChatBotUIFinals1";
// import ChatbotFinalsPage from "@/components/chatbotPage/ChatBotUIFinals";

const page = async (props) => {
  // const decodedCoordinates = decodeURIComponent(
  //   props?.searchParams?.coordinates
  // );
  // const isMobile = isMobileView();
  // const [latStr, longStr] = decodedCoordinates?.substring(1)?.split(",");
  // const getSingleBeachMapCoordinateData =
  //   latStr && longStr
  //     ? await getSingleBeachMapCoordinate(`${latStr},${longStr}`)
  //     : {};
  // if (
  //   latStr &&
  //   longStr &&
  //   getSingleBeachMapCoordinateData?.data?.length === 0
  // ) {
  //   redirect("/");
  // }
  return (
    <div className="h-screen w-full">
      {/* <ChatbotFinals1Page /> */}
      <ChatbotFinals2Page />
    </div>
  );
};

export default page;
