import { CustomContainer, CustomGrid } from "@/components/Custom-Display";
import React from "react";

const loading = () => {
  return (
    <div className="animate-pulse">
      <CustomContainer>
        {/**
         *
         * Heading
         *
         */}
        <div className="heading my-8">
          <div className="animate-pulse h-5 bg-slate-200 my-4"></div>
          <div className="animate-pulse h-5 bg-slate-200 my-4"></div>
        </div>

        <div className="heading my-2">
          <div className="animate-pulse h-5 bg-slate-200 my-4"></div>
          <div className="animate-pulse h-4 bg-slate-200 my-4"></div>
          <div className="animate-pulse h-3 bg-slate-200 my-4"></div>
        </div>
        <div className=" flex w-full gap-3">
          <div className="col bg-slate-200 block sticky top-0 w-full lg:w-4/12 xl:w-3/12 px-5 h-[40vh]"></div>
          <div className="col bg-slate-200 block w-full lg:w-8/12 xl:w-9/12 h-[70vh]"></div>
        </div>
        {/* <div
          className="  flex h-[60vh] bg-slate-200"
          style={{ marginTop: "30px" }}
        >
          <div className="col bg-slate-200 h-[430px] w-full lg:w-9/12"></div>
        </div> */}

        {/**
         *
         * Blog Section
         *
         */}
        <div className="heading my-2">
          <div className="animate-pulse h-5 bg-slate-200 my-4"></div>
          <div className="animate-pulse h-6 bg-slate-200 my-4"></div>
        </div>
        <CustomGrid
          className="gap-4 sm:gap-8 my-6"
          data={Array(6).fill(1)}
          Component={() => {
            return (
              <div className="relative h-[105px] group rounded-sandee bg-slate-200 animate-pulse"></div>
            );
          }}
          xs={1}
          sm={2}
          // md={2}
          lg={3}
          // xl={3}
        />
        {/* <div className=" h-14 bg-slate-200 animate-pulse my-8"></div> */}
      </CustomContainer>
    </div>
  );
};

export default loading;
