import Blog_Related_Blog_Section from "@/components/BlogPage/BlogRelatedBlog";
import BlogPage_Overview_Section from "@/components/BlogPage/overview";
import { CustomContainer } from "@/components/Custom-Display";
import { API_BASE_URL, EnvTrueFalse, WEBSITE_URL, sanitizeHtml } from "@/helper/functions";
import axios from "axios";
import React from "react";
import * as Cheerio from "cheerio";
import "@/components/BlogPage/blog.css";
import BreadCumber from "@/components/Common/BreadCumber";
import { redirect } from "next/navigation";
import SingleBlogPageJSONLD from "@/components/BlogPage/SingleBlogPageJSONLD";
import moment from "moment";
const page = async (props) => {
  // const {data: ResponseOfRedirect} = await PostRedirectBlogUrl(
  //   props?.params?.blogSlug
  // );
  // RedirectionFunction(ResponseOfRedirect);
  const blogs = await getTestBlogSingle(props?.params?.blogSlug);
  if (!!!blogs?.data?.overview || !!!blogs?.data?.id) {
    redirect("/");
  }
  /**
   * Related Blog fetch and carete Array of it Satrts Here
   */
  const relatedBlogsMainData = await getTestBlogRelated(blogs?.data?.id);
  const relatedBlogs = relatedBlogsMainData?.data?.map((el) => ({
    ...el,
    data: el,
    Tag: el?.blogCategories?.map((tag) => tag?.category?.name),
  }));

  const $ = Cheerio.load(sanitizeHtml(blogs?.data?.overview), null, false);
  const h2TextArray = [];
  const h2Ids = [];
  $("h2").each((index, element) => {
    const uniqueId = `${index + 1}`;
    $(element).attr("id", uniqueId);
    const text = $(element).text();
    h2TextArray.push(text);
    const idAttribute = $(element).attr("id");
    if (idAttribute && text !== "") {
      h2Ids.push(idAttribute);
    }
  });

  $("img").attr({
    title: blogs?.data?.title,
    alt: blogs?.data?.title,
  });

  $("a").each((index, element) => {
    const aElement = $(element);
    const textContent = aElement.text();
    aElement.attr("title", textContent);
  });

  $("span").css({
    height: null,
    width: null,
  });
  const modifiedHtmlString = $.html();
  /**
   * Related Blog fetch and carete Array of it Ends Here
   */

  // logController(DataForBlog);
  // const { content, toc: TableConetnt } = processContent(blogs);
  return (
    <>
      {/* <link
        rel="stylesheet"
        href="http://cdn.quilljs.com/1.3.6/quill.snow.css"
      /> */}
      <SingleBlogPageJSONLD
        blogData={{
          name: blogs?.data?.title,
          slug: blogs?.data?.slug,
          headline: blogs?.data?.metaTitle || blogs?.data?.title,
          description: blogs?.data?.metaDescription || blogs?.data?.description,
          datePublished: moment(blogs?.createdAt)?.format("YYYY-MM-DD"),
          dateModified: moment(blogs?.updatedAt)?.format("YYYY-MM-DD"),
        }}
      />
      <div className=" bg-gray-100 w-full p-4 flex items-center">
        <CustomContainer>
          <BreadCumber
            data={[
              {
                title: "All Blogs",
                to: `/blog`,
              },
              {
                title: `${blogs?.data?.title ?? "Country"}`,
              },
            ]}
          />
        </CustomContainer>
      </div>
      {/* <CustomContainer>
        <Blog_SearchBar_Section />
      </CustomContainer> */}
      <CustomContainer>
        <BlogPage_Overview_Section
          BlogData={blogs?.data}
          h2Ids={h2Ids}
          headings={h2TextArray}
          modifiedHtmlString={modifiedHtmlString}
        />
      </CustomContainer>
      {relatedBlogs?.length ? (
        <CustomContainer className="mt-4">
          <Blog_Related_Blog_Section data={relatedBlogs} />
        </CustomContainer>
      ) : (
        ""
      )}
    </>
  );
};

export default page;

export const getTestBlogSingle = async (id) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/blog/${id}`);

    return response?.data;
  } catch (error) {
    // logController(error);
  }
};

export const getTestBlogRelated = async (id) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/blog/related/${id}`);

    return response?.data;
  } catch (error) {
    // logController(error);
  }
};

export const PostRedirectBlogUrl = async (data) => {
  const response = await axios.post(`${API_BASE_URL}/beachesUrls/check`, {
    url: `/blog/${data}`,
  });

  return response?.data;
};

export async function generateMetadata({ params }) {
  const BlogDataUp = await getTestBlogSingle(params?.blogSlug);
  if (!BlogDataUp?.data) {
    return {};
  }
  const { data: BlogData } = BlogDataUp;
  return {
    title: BlogData?.metaTitle ?? BlogData?.title,
    description: BlogData?.metaDescription ?? BlogData?.description,
    alternates: {
      canonical: `${WEBSITE_URL}/blog/${params?.blogSlug}`,
    },
  };
}

// export const generateStaticParams = async () => {
//   return []
// }

// export const revalidate = 43200;

// export const dynamicParams =
//   EnvTrueFalse?.[process.env.NEXT_PUBLIC_DYNAMIC_PARAMS] ?? true;