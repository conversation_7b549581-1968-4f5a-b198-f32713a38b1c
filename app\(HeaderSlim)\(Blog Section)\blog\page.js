import BlogPageJSONLD from "@/components/BlogPage/BlogPageJSONLD";
import BlogSection from "@/components/BlogPage/BlogSection";
import BreadCumber from "@/components/Common/BreadCumber";
import { CustomContainer } from "@/components/Custom-Display";
import { SiteDataPageWise } from "@/data/siteMetadata";
import { API_BASE_URL, buildQueryString } from "@/helper/functions";
import axios from "axios";
import React from "react";

const page = async () => {
  const { data } = await getTestBlog({
    sortOrder: "DESC",
    sortBy: "createdAt",
    page: 1,
    limit: 20,
  });

  return (
    <>
      <BlogPageJSONLD />
      <div className=" bg-gray-100 w-full p-4 flex items-center">
        <CustomContainer>
          <BreadCumber
            data={[
              {
                title: "All Blogs",
                to: `/blog`,
              },
            ]}
          />
        </CustomContainer>
      </div>
      <BlogSection initialData={data?.rows ?? []} />
    </>
  );
};

export default page;
// export const revalidate = 3600;
export const dynamic = "force-dynamic";

export const getTestBlog = async (query) => {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/blog${buildQueryString(query)}`
    );

    return response?.data;
  } catch (error) {
    return {
      data: [],
    };
    // logController(error);
  }
};

export function generateMetadata() {
  return SiteDataPageWise.blogs;
}
