"use server";

import { API_BASE_URL, buildQueryString } from "@/helper/functions";
import axios from "axios";
import { withErrorHandling } from "@/app/(HeaderSlim)/(Single Country Page)/[countrySlug]/action";

export const TaggetAllRelatedBeaches = withErrorHandling(async (data) => {
    const response = await axios.get(
      `${API_BASE_URL}/beachMain/getBeachByTag?${data?.param}=true&limit=${
        data?.limit ?? 10
      }&page=${data?.page}`
    );
    return response?.data;
  });