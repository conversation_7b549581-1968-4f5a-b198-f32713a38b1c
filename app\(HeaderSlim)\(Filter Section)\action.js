"use server";

import { API_BASE_URL, buildQueryString } from "@/helper/functions";
import axios from "axios";

// Helper function for error handling (not exported as it's not a server action)
const handleError = (error) => {
  return { data: null, error: true };
};

export async function TaggetAllRelatedBeaches(data) {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/beachMain/getBeachByTag?${data?.param}=true&limit=${data?.limit ?? 10
      }&page=${data?.page}`
    );
    return response?.data;
  } catch (error) {
    return handleError(error);
  }
}