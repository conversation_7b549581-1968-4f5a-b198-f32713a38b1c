import { withErrorHandling } from "@/app/(HeaderSlim)/(Single Country Page)/[countrySlug]/action";
import BreadCumberJSONLD from "@/components/BreadCumberJSONLD";
import { CustomContainer } from "@/components/Custom-Display";
import Home_Listcle_Section from "@/components/HomePage/HomeListcle";
import FilterPage_Hero_Section from "@/components/filterPage/FilterHeroSection";
import Filter_List_Section from "@/components/filterPage/FilterListSection";
import Filter_In_World_Section from "@/components/filterPage/FilterinWorldSection";
import { API_BASE_URL, CDNImageUrl, WEBSITE_URL } from "@/helper/functions";
import axios from "axios";
import { redirect } from "next/navigation";
import React from "react";
import { TaggetAllRelatedBeaches } from "../../action";

const HeaderPropData = {
  "Family-Friendly": {
    image: CDNImageUrl("images/header/Family-Head.avif"),
    title: "Family Beaches",
    filterSlug: "Family-Friendly",
  },
  LGBTQ: {
    image: CDNImageUrl("images/header/LGBTQ-Beach-hero.avif"),
    title: "LGBTQ+ Beaches",
    filterSlug: "LGBTQ",
  },
  Disability: {
    image: CDNImageUrl("images/header/Disability-Beach-hero.avif"),
    title: "Disability Access Beaches",
    filterSlug: "Disability",
  },
  Nude: {
    image: CDNImageUrl("images/header/NudeBeachHeader.avif"),
    title: "Nude Beaches",
    filterSlug: "Nude",
  },
  Camping: {
    image: CDNImageUrl("images/header/Camping-Head.avif"),
    title: "Camping Beaches",
    filterSlug: "Camping",
  },
  Surfing: {
    image: CDNImageUrl("images/header/Surfing-Head.avif"),
    title: "Surfing Beaches",
    filterSlug: "Surfing",
  },
  Dog: {
    image: CDNImageUrl("images/header/Dog-Header.avif"),
    title: "Dog Beaches",
    filterSlug: "Dog",
  },
};

const page = async (props) => {
  const param = props?.params?.filterSlug;
  if (!!!HeaderPropData[param]) {
    return redirect("/");
  }
  const Popularresponse = await TaggetAllPopularBeach(
    param === "Family-Friendly" ? "Family Friendly" : param
  );
  const PopularWorld = Popularresponse?.data;

  const Listicleresponse = await TaggetAllListicles(
    param === "Family-Friendly" ? "Family Friendly" : param
  );
  const Listicle = Listicleresponse?.data;

  const Worldresponse = await TaggetAllRelatedBeaches({
    param,
    page: 1,
    limit: 30,
  });
  const FullCount = {
    limit: 30,
    page: 1,
    count: Worldresponse?.count,
    totalpages: Math.ceil(Worldresponse?.data?.count / 30),
  };
  const World = Worldresponse?.data?.rows;

  const ListResponse = await TaggetAllRelatedBlogs(param === "Family-Friendly" ? "Family Friendly" : param);
  const Blog = ListResponse?.data;
  // logController(
  //   "All data get ",
  //   PopularWorld,
  //   "+++++\n++\n",
  //   Listicle,
  //   "+++++\n++\n",
  //   World,
  //   "+++++\n++\n",
  //   Blog,
  //   "+++++\n++\n"
  // );
  let title = "Sandee - Choose Your Beach, Attraction, Hotels and Restaurants";
  let description =
    "Sandee is the premier and most comprehensive database and information source. We feature 100 different categories of beach information, including beach location, beach parking, beach access, amenities, beach activities, beach attractions, beach weather, and many more beach activities and beach attractions.";
  if (param == "Surfing") {
    title = "Best Surfing Beaches | Sandee";
    description =
      "Experience the thrill of surfing with Sandee - the ultimate beach guide. Discover the perfect waves and embrace the joy of riding the surf on pristine beaches. Dive into the world of sun, sea, and adventure with Sandee, your go-to companion for an unforgettable surfing experience";
  } else if (param == "Family-Friendly") {
    title = "Best Family Friendly Beaches | Sandee";
    description =
      "Discover the perfect seaside retreat for your family with Sandee, the ultimate beach guide. Explore our curated list of family-friendly beaches, where sun, sand, and smiles await. Find the ideal destination for creating unforgettable memories with your loved ones";
  } else if (param == "Disability") {
    title = "All Disability Access Beaches | Sandee";
    description =
      "Discover the ultimate beach experience with Sandee - the ultimate beach guide. Explore our curated list of 'All Disability Access Beaches' ensuring everyone can enjoy the sun and surf.";
  } else if (param == "LGBTQ") {
    title = "Best LGBTQ+ Beaches | Sandee";
    description =
      "Discover the most welcoming LGBTQ+ beaches worldwide with Sandee - the ultimate beach guide. Explore inclusive destinations where sun, sea, and acceptance meet. Plan your perfect getaway with our curated LGBTQ+ beach recommendations for a memorable and inclusive beach experience";
  } else if (param == "Dog") {
    title = "Dog Friendly Beaches | Sandee";
    description =
      "Discover the best dog-friendly beaches with Sandee - the ultimate beach guide. Explore pristine shores where your furry friend can play and unwind. Plan your next seaside adventure with our comprehensive beach listings, ensuring a pawsitively delightful experience for both you and your four-legged companion";
  } else if (param == "Nude") {
    title = "Best Nude Beaches | Sandee";
    description =
      "Discover the allure of nude beaches with Sandee – the ultimate beach guide. Immerse yourself in natural beauty and liberating experiences as we unveil the world's best clothing-optional shores.";
  } else if (param == "Camping") {
    title = "Best Camping Beaches | Sandee";
    description =
      "Discover the joy of the great outdoors with Sandee - the ultimate beach guide. Unleash your adventurous spirit with the best camping gear and tips for an unforgettable outdoor experience. From cozy tents to essential camping accessories, Sandee has you covered";
  }

  return (
    <>
      <BreadCumberJSONLD
        name={`Best ${HeaderPropData[param]?.title} `}
        slug={`filter/${param}`}
        title={title}
        description={description}
      />
      <FilterPage_Hero_Section
        data={{
          ...HeaderPropData[param],
          name: `Best ${HeaderPropData[param]?.title} `,
        }}
        param={param}
      />
      <Home_Listcle_Section data={Listicle} />
      <CustomContainer>
        <Filter_In_World_Section data={World} details={HeaderPropData[param]} FullCount={FullCount} />
        <Filter_List_Section data={Blog} details={HeaderPropData[param]} />
      </CustomContainer>
      <Home_Listcle_Section data={PopularWorld} />
    </>
  );
};

export default page;

export const dynamicParams = true;
// export async function generateStaticParams() {
//   const posts = [
//     "Family-Friendly",
//     "Dog",
//     "Nude",
//     "Camping",
//     "Disability",
//     "Surfing",
//   ];

//   return posts.map((post) => ({
//     filterSlug: post,
//   }));
// }

export function generateMetadata({ params, searchParams }, parent) {
  let title = "Sandee - Choose Your Beach, Attraction, Hotels and Restaurants";
  let description =
    "Sandee is the premier and most comprehensive database and information source. We feature 100 different categories of beach information, including beach location, beach parking, beach access, amenities, beach activities, beach attractions, beach weather, and many more beach activities and beach attractions.";
  if (params.filterSlug == "Surfing") {
    title = "Best Surfing Beaches | Sandee";
    description =
      "Experience the thrill of surfing with Sandee - the ultimate beach guide. Discover the perfect waves and embrace the joy of riding the surf on pristine beaches. Dive into the world of sun, sea, and adventure with Sandee, your go-to companion for an unforgettable surfing experience";
  } else if (params.filterSlug == "Family-Friendly") {
    title = "Best Family Friendly Beaches | Sandee";
    description =
      "Discover the perfect seaside retreat for your family with Sandee, the ultimate beach guide. Explore our curated list of family-friendly beaches, where sun, sand, and smiles await. Find the ideal destination for creating unforgettable memories with your loved ones";
  } else if (params.filterSlug == "Disability") {
    title = "All Disability Access Beaches | Sandee";
    description =
      "Discover the ultimate beach experience with Sandee - the ultimate beach guide. Explore our curated list of 'All Disability Access Beaches' ensuring everyone can enjoy the sun and surf.";
  } else if (params.filterSlug == "LGBTQ") {
    title = "Best LGBTQ+ Beaches | Sandee";
    description =
      "Discover the most welcoming LGBTQ+ beaches worldwide with Sandee - the ultimate beach guide. Explore inclusive destinations where sun, sea, and acceptance meet. Plan your perfect getaway with our curated LGBTQ+ beach recommendations for a memorable and inclusive beach experience";
  } else if (params.filterSlug == "Dog") {
    title = "Dog Friendly Beaches | Sandee";
    description =
      "Discover the best dog-friendly beaches with Sandee - the ultimate beach guide. Explore pristine shores where your furry friend can play and unwind. Plan your next seaside adventure with our comprehensive beach listings, ensuring a pawsitively delightful experience for both you and your four-legged companion";
  } else if (params.filterSlug == "Nude") {
    title = "Best Nude Beaches | Sandee";
    description =
      "Discover the allure of nude beaches with Sandee – the ultimate beach guide. Immerse yourself in natural beauty and liberating experiences as we unveil the world's best clothing-optional shores.";
  } else if (params.filterSlug == "Camping") {
    title = "Best Camping Beaches | Sandee";
    description =
      "Discover the joy of the great outdoors with Sandee - the ultimate beach guide. Unleash your adventurous spirit with the best camping gear and tips for an unforgettable outdoor experience. From cozy tents to essential camping accessories, Sandee has you covered";
  }

  return {
    title,
    description,
    alternates: {
      canonical: `${WEBSITE_URL}/filter/${params?.filterSlug}`,
    },
  };
}

export const TaggetAllPopularBeach = withErrorHandling(async (data) => {
  const response = await axios.get(
    `${API_BASE_URL}/listiclesMain/getByTag?limit=1&tag=${data}&worldwide=true`
  );
  return response?.data;
});

export const TaggetAllRelatedBlogs = withErrorHandling(async (data) => {
  const response = await axios.get(
    `${API_BASE_URL}/listiclesMain/getRelatedBlog?limit=12&tag=${data}`
  );
  return response?.data;
});
// export const TaggetAllRelatedBeaches = withErrorHandling(async (data) => {
//   const response = await axios.get(
//     `${API_BASE_URL}/beachMain/getBeachByTag?${data?.param}=true&limit=${
//       data?.limit ?? 20
//     }&page=${data?.page}`
//   );
//   return response?.data;
// });

export const TaggetAllListicles = withErrorHandling(async (data) => {
  const response = await axios.get(
    `${API_BASE_URL}/listiclesMain/getByTag?limit=1&countrySlug=united-states&tag=${data}`
    // `${API_BASE_URL}/listiclesMain/getByTag?limit=1&stateSlug=california&countrySlug=united-states&tag=Party Beach`
  );
  return response?.data;
});
