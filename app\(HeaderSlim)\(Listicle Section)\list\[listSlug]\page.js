import { withErrorHandling } from "@/app/(HeaderSlim)/(Single Country Page)/[countrySlug]/action";
import ListCard from "@/components/Cards/ListCard";
import BreadCumber from "@/components/Common/BreadCumber";
import CustomeImage from "@/components/Common/CustomeImage";
import NameTitle from "@/components/Common/NameTitle";
import { CustomContainer, CustomGrid } from "@/components/Custom-Display";
import Home_Listcle_Section from "@/components/HomePage/HomeListcle";
import ListListicleBeach from "@/components/ListiclePage/ListListicleBeach";
import ListicleMap from "@/components/ListiclePage/ListicleMap";
import SingleListiclePageJSONLD from "@/components/ListiclePage/SingleListiclePageJSONLD";
import {
  API_BASE_URL,
  FinalImageGenerator,
  WEBSITE_URL,
  buildQueryString,
  slugConverter,
} from "@/helper/functions";
import axios from "axios";
import Image from "next/image";
import { redirect } from "next/navigation";
import React from "react";

const page = async (props) => {
  const { params } = props;
  const { data: ListBeach } = await ListggetAllListicle(params?.listSlug, {
    // page: 1,
    // limit: 30,
  });

  if (!!!ListBeach?.name) {
    return redirect("/");
  }

  const [{ data: ListiclesNew }, { data: RelatedListicle }] = await Promise.all(
    [
      ListgetAllListicleTagWise(ListBeach?.tag),
      getAllRelatedLists({
        tag: ListBeach?.tag,
        ...(ListBeach?.countryId ? { countryId: ListBeach?.countryId } : {}),
        limit: 10,
      }),
    ]
  );
  // const { data: ListiclesNew } = await ListgetAllListicleTagWise(
  //   ListBeach?.tag
  // );
  // const { data: RelatedListicle } = await getAllRelatedLists(ListBeach?.tag);

  const listicleData =
    ListBeach?.listiclesBeaches?.map((el) => ({
      ...el,
      ...el?.AllBeach,
      copyRightsData: el?.AllBeach?.images,
    })) || [];
  return (
    <>
      <SingleListiclePageJSONLD params={params} listData={ListBeach} />
      <div className=" bg-gray-100 w-full p-4 flex items-center">
        <CustomContainer>
          <BreadCumber
            data={[
              {
                title: "All Lists",
                to: `/list`,
              },
              {
                title: `${ListBeach?.name}`,
              },
            ]}
          />
        </CustomContainer>
      </div>
      <CustomContainer>
        <NameTitle
          htag={1}
          className="mt-6"
          name={ListBeach?.name}
          description={ListBeach?.description}
        />

        <div className="flex lg:gap-10 mt-6 flex-col lg:flex-row">
          <div className=" w-full lg:w-8/12 ">
            {listicleData?.length ? (
              <ListListicleBeach listicleData={listicleData} />
            ) : (
              <div className="w-full h-full">
                <div className="relative  overflow-hidden  aspect-video  rounded-sandee ">
                  <CustomeImage
                    fill
                    src={FinalImageGenerator(ListBeach?.listicleImage, 1600, 3)}
                    alt="Beach Image"
                  />
                </div>
              </div>
            )}
          </div>
          <div className="w-full my-5 lg:my-0 lg:w-4/12 relative">
            <div className="sticky top-5">
              {/* <div className="mb-8">
                <ListicleMap countryName={ListBeach?.countryName} />
              </div> */}
              <div className="">
                {RelatedListicle?.filter(
                  (i, index) => i?.nameSlug !== params?.listSlug
                )?.length ? (
                  <NameTitle className="mb-2" name={"Related Listicles"} />
                ) : (
                  ""
                )}

                <CustomGrid
                  data={RelatedListicle?.filter(
                    (i, index) => i?.nameSlug !== params?.listSlug
                  )}
                  className="gap-4 sm:gap-8"
                  Component={({ data: dataProps }) => {
                    dataProps.link = `/list/${dataProps?.nameSlug}`; //
                    dataProps.imageSrc = FinalImageGenerator(
                      dataProps?.listicleImage
                    );
                    return ListCard({ data: { ...dataProps } });
                  }}
                  xs={1}
                  sm={1}
                  // md={2}
                  lg={1}
                // xl={3}
                />
              </div>
            </div>
          </div>
        </div>
      </CustomContainer>

      <Home_Listcle_Section data={typeof ListiclesNew == "array" ? ListiclesNew : ListiclesNew?.rows} className="!my-0" />
    </>
  );
};

export default page;

export const ListggetAllListicle = withErrorHandling(async (data, query) => {
  const response = await axios.get(
    `${API_BASE_URL}/listiclesMain/${data}${buildQueryString(query)}`
  );
  return response?.data;
});

export const ListgetAllListicleTagWise = withErrorHandling(async (data) => {
  const response = await axios.get(
    `${API_BASE_URL}/listiclesMain/getByTag?tag=${data}&limit=1&worldwide=true`
  );
  return response?.data;
});

export const getAllRelatedLists = withErrorHandling(async (query) => {
  const response = await axios.get(
    `${API_BASE_URL}/listiclesMain/getRelatedBlog${buildQueryString(query)}`
  );
  return response?.data;
});

export const ListgetAllLists = withErrorHandling(async (data) => {
  const response = await axios.get(
    `${API_BASE_URL}/listiclesMain/getRelatedBlog?limit=${data?.limit}&page=${data?.page}`
  );
  return response?.data;
});

export function generateMetadata({ params, searchParams }, parent) {
  return {
    title: `${slugConverter(params?.listSlug, true)} - Sandee `,
    description:
      `${slugConverter(params?.listSlug, true)} — Sandee is the most comprehensive list of beaches around the world! We include over 100,000 beaches in every country, with details on activities, photos, maps, and reviews.`,
    // "Sandee is the most comprehensive list of beaches around the world! Sandee includes over 100,000 beaches in every country of the world. Includes activities, photos, maps, and reviews.",
    alternates: {
      canonical: `${WEBSITE_URL}/list/${params?.listSlug}`,
    },
  };
}
