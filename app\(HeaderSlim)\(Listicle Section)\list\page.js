import axios from "axios";
import React from "react";
import { withErrorHandling } from "@/app/(HeaderSlim)/(Single Country Page)/[countrySlug]/action";
import { CustomContainer } from "@/components/Custom-Display";
import { API_BASE_URL, WEBSITE_URL } from "@/helper/functions";
import BreadCumber from "@/components/Common/BreadCumber";
import AllListWithScroll from "@/components/ListiclePage/AllListWithScroll";
import ListiclePageJSONLD from "@/components/ListiclePage/ListiclePageJSONLD";

const page = async () => {
  const { data: listDataResponse } = await ListgetAllListicle({
    limit: 200,
    page: 1,
  });
  const listicleData = listDataResponse;

  // let filteredListData=listicleData?.filter((i)=> i?.countryName !== null);

  // const listicleData = listDataResponse?.map((el) => ({
  //   ...el,
  //   title: el?.name,
  //   // link: `/list/${el?.nameSlug}`,
  //   // image: LinkGenerator(
  //   //   el?.listicleImage,
  //   //   400,
  //   //   ProceedImageURL(el?.listicleImage?.id)
  //   // ),
  //   // title: `${el?.name} - UUID : (${el?.listicleImage?.id})`,

  //   copyRightsData: [el?.listicleImage],
  // }));
  return (
    <>
      <ListiclePageJSONLD />
      <div className=" bg-gray-100 w-full p-4 flex items-center ">
        <CustomContainer>
          <BreadCumber
            data={[
              {
                title: "All Lists",
              },
            ]}
          />
        </CustomContainer>
      </div>
      <AllListWithScroll initialData={listicleData} />
    </>
  );
};

export default page;
// export const revalidate = 3600;
export const dynamic = "force-dynamic";

export const ListgetAllListicle = withErrorHandling(async (data) => {
  const response = await axios.get(
    `${API_BASE_URL}/listiclesMain/getRelatedBlog?limit=${data?.limit}&page=${data?.page}`
  );
  return response?.data;
});
export function generateMetadata({ params, searchParams }, parent) {
  return {
    title: `Lists | Sandee the Ultimate beach guide`,
    description:
      "Discover the best beaches with Sandee - the ultimate beach guide! Dive into our lists for travel tips, hidden gems, and beachfront bliss. Uncover paradise with Sandee's expert insights. Your journey to sun, sea, and sand starts here",
    alternates: {
      canonical: `${WEBSITE_URL}/list`,
    },
  };
}
