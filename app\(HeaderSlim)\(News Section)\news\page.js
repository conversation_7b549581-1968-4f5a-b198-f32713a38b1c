import BreadCumber from "@/components/Common/BreadCumber";
import { CustomContainer } from "@/components/Custom-Display";
import NewsPageJSONLD from "@/components/NewsPage/NewsPageJSONLD";
import NewsSection from "@/components/NewsPage/NewsSection";
import { SiteDataPageWise } from "@/data/siteMetadata";
import { API_BASE_URL, buildQueryString } from "@/helper/functions";
import axios from "axios";
import dayjs from "dayjs";
import React from "react";

const page = async () => {
    const { data } = await getTestNews({
        sortOrder: "DESC",
        sortBy: "date",
        page: 1,
        limit: 10,
        date: dayjs().utc().startOf('month').toISOString()
    });
    return (
        <>
            <NewsPageJSONLD />
            <div className=" bg-gray-100 w-full p-4 flex items-center">
                <CustomContainer>
                    <BreadCumber
                        data={[
                            {
                                title: "Beach News",
                                to: `/news`,
                            },
                        ]}
                    />
                </CustomContainer>
            </div>
            <NewsSection initialData={data?.rows ?? []} />
        </>
    );
};

export default page;
// export const revalidate = 3600;
export const dynamic = "force-dynamic";

export const getTestNews = async (query) => {
    try {
        const response = await axios?.get(
            `${API_BASE_URL}/news${buildQueryString(query)}`
        );

        return response?.data;
    } catch (error) {
        return {
            data: [],
        };
        // logController(error);
    }
};

export function generateMetadata() {
    return SiteDataPageWise.news;
}
