import BreadCumber from "@/components/Common/BreadCumber";
import { CustomContainer } from "@/components/Custom-Display";
import React from "react";
import { withErrorHandling } from "../../(Single Country Page)/[countrySlug]/action";
import SharkDescription from "@/components/Shark/SharkDescription";
import axios from "axios";
import { API_BASE_URL, buildQueryString, CDNImageUrl } from "@/helper/functions";
import HeaderLine from "@/components/Shark/HeaderLine";
import { nudeIntroduction } from "@/data/sharkPageData";
import AllNudeOnMap from "@/components/NudeBeaches/AllNudeOnMap";
import Home_Listcle_Section from "@/components/HomePage/HomeListcle";
import Filter_In_World_Section from "@/components/filterPage/FilterinWorldSection";
import Filter_List_Section from "@/components/filterPage/FilterListSection";
import { TaggetAllRelatedBeaches } from "../../(Filter Section)/action";
import Home_Bottom_Button from "@/components/HomePage/HomeBottomButton";
import NameTitle from "@/components/Common/NameTitle";
import WithoutSharkAttackStateList from "@/components/Shark/WithoutSharkAttackStateList";
import CountryNudeBeaches from "@/components/NudeBeaches/CountryNudeBeaches";
import NudeBeachListicle from "@/components/NudeBeaches/NudeBeachListicle";
import WithoutNudeBeachesStateList from "@/components/NudeBeaches/WithoutNudeBeachesStateList";
import NearByNudeBeaches from "@/components/NudeBeaches/NearByNudeBeaches";
import NudeBeachPageJSONLD from "@/components/NudeBeaches/NudeBeachPageJSONLD";
import { SiteDataPageWise } from "@/data/siteMetadata";
// import NearByNudeBeaches from "@/components/NudeBeaches/NearByNudeBeaches";

const HeaderPropData = {
  image: CDNImageUrl("images/header/NudeBeachHeader.avif"),
  title: "Nude Beaches",
  filterSlug: "Nude",
}

const page = async (props) => {
  const { params } = props;

  const [
    // { data: Blog },
    { data: Worldresponse },
    // { data: PopularWorld },
    { data: Listicle },
    { data: withoutNudeBeachStateList },
    { data: bestNudeBeaches },
    { data: bestLGBTQFriendlyBeaches },
    { data: countriesAttack },
    { data: introductionData },
    { data: nudeCountriesData },
    // { data: nearByNudeBeaches },
  ] = await Promise.all([
    // TaggetAllRelatedBlogs("Nude"),
    TaggetAllRelatedBeaches({
      param: "Nude",
      page: 1,
      limit: 20,
    }),
    // TaggetAllPopularBeach("Nude"),
    TaggetAllListicles("Nude"),
    getWithoutNudeBeachStateList({
      // withOutAttacks: 1,
      limit: 40,
      page: 1,
      // countryId: HeaderCountryData?.id,
    }),
    ListggetAllListicle("best-17-nude-beaches-in-the-world", {
      limit: 8,
    }),
    ListggetAllListicle("best-lgbtq-friendly-beaches-in-the-world", {
      limit: 8,
    }),
    getShakiestCountries({
      limit: 12,
      page: 1,
    }),
    GetNudeCountry({
      page: 1, limit: 6
    }),
    GetNudeCountry({
      page: 1, limit: 12, includeImage: 1
    }),
    // BeachgetNearByNudeBeach({
    //   limit: 8,
    //   page: 1,
    //   // coordinates: `${res?.lon},${res?.lat}`
    // }),
  ]);

  const FullCount = {
    limit: 30,
    page: 1,
    count: Worldresponse?.count,
    totalpages: Math.ceil(Worldresponse?.count / 30),
  };

  return (
    <>
      <NudeBeachPageJSONLD />
      <CustomContainer>
        <BreadCumber
          data={[
            {
              title: "Nude Beaches",
            },
          ]}
        />
        <HeaderLine
          title={`International Nude Beaches`}
          link={`/nude-beaches`}
          fullViewLink={`/maps/nude-beaches`}
        />
        <AllNudeOnMap
          zoom={0} />
        <h2 className="text-sandee-32 font-bold pb-0">Introduction</h2>
        <SharkDescription className="nude-description" data={{ description: nudeIntroduction(introductionData?.rows) }} />
        {/* <NameTitle
          className="mt-5"
          type={2}
          name="Best Nude Beaches in the World"
          description={"Best Places to lose the suit"
            // <>
            //   <span className=" font-semibold">
            //     {HomePageData.CountriesSection.sub_title}
            //   </span>
            //   {HomePageData.CountriesSection.description}
            // </>
          }
          extraButton={
            <div className=" hidden md:flex justify-end items-start ">
              <Link
                href={"/list/best-17-nude-beaches-in-the-world"}
                className=" custom-hover-slide-button group !text-nowrap"
              >
                <span className="custom-hover-slide group-hover:h-full"></span>
                <span className="custom-hover-slide-text group-hover:text-white !text-nowrap font-bold">
                  <CategoryIcon className="me-2 fill-sandee-orange group-hover:fill-white h-4 w-4" />
                  {"View All"}
                </span>
              </Link>
            </div>
          }
        /> */}
      </CustomContainer>
      <NudeBeachListicle
        data={bestNudeBeaches}
        title="Best Nude Beaches in the World"
        description="Best Places to lose the suit"
      />
      {/* <NudeBeachListicle
        data={Listicle}
        title="Best Nude Beaches in the World"
        description="Best Places to lose the suit"
      /> */}
      <Home_Listcle_Section data={Listicle} />
      <CustomContainer>
        <CountryNudeBeaches
          countryData={nudeCountriesData?.rows}
          totalRecords={nudeCountriesData?.count}
          name="Countries with the Most Nude Beaches"
          description="Top Nude Beaches Countries"
        />
        {/* <Filter_In_World_Section data={Worldresponse?.rows} details={HeaderPropData} FullCount={FullCount} />
        <Filter_List_Section data={Blog} details={HeaderPropData} /> */}
      </CustomContainer>
      {/* <Home_Listcle_Section data={PopularWorld} /> */}
      <NearByNudeBeaches />
      <NudeBeachListicle
        data={bestLGBTQFriendlyBeaches}
      // title="Best Nude Beaches in the World"
      // description="Best Places to lose the suit"
      />
      <CustomContainer>
        <NameTitle
          className="mt-4 pb-2"
          name={"All States Without Nude Beaches"}
          description={"States without nude beaches"}
          type={2}
        />
        <WithoutNudeBeachesStateList
          data={withoutNudeBeachStateList?.rows}
          isPagination
          totalRecords={withoutNudeBeachStateList?.count}
        />
        <Home_Bottom_Button />
      </CustomContainer>
    </>
  );
};

export default page;

// export const revalidate = 60;
export const dynamic = "force-dynamic";

export const TaggetAllPopularBeach = withErrorHandling(async (data) => {
  const response = await axios.get(
    `${API_BASE_URL}/listiclesMain/getByTag?limit=1&tag=${data}&worldwide=true`
  );
  return response?.data;
});
export const GetNudeCountry = withErrorHandling(async (query) => {
  const response = await axios.get(
    `${API_BASE_URL}/countries/getNudeBeachCountries${buildQueryString(
      query
    )}`
  );
  return response?.data;
});

export const getWithoutNudeBeachStateList = withErrorHandling(async (query) => {
  const APISharkSpecies = `${API_BASE_URL}/states/getNoNudeBeachStates${buildQueryString(
    query
  )}`;
  const response = await axios.get(APISharkSpecies);
  return response?.data;
});

export const TaggetAllRelatedBlogs = withErrorHandling(async (data) => {
  const response = await axios.get(
    `${API_BASE_URL}/listiclesMain/getRelatedBlog?limit=12&tag=${data}`
  );
  return response?.data;
});

export const TaggetAllListicles = withErrorHandling(async (data) => {
  const response = await axios.get(
    `${API_BASE_URL}/listiclesMain/getByTag?limit=1&countrySlug=united-states&tag=${data}`
    // `${API_BASE_URL}/listiclesMain/getByTag?limit=1&stateSlug=california&countrySlug=united-states&tag=Party Beach`
  );
  return response?.data;
});

export const getShakiestCountries = withErrorHandling(async (query) => {
  const APIShakiestCountries = `${API_BASE_URL}/countries/shakiestCountries${buildQueryString(
    query
  )}`;
  const response = await axios.get(APIShakiestCountries);
  return response?.data;
});

export const ListggetAllListicle = withErrorHandling(async (data, query) => {
  const response = await axios.get(
    `${API_BASE_URL}/listiclesMain/${data}${buildQueryString(query)}`
  );
  return response?.data;
});

export const BeachgetNearByNudeBeach = withErrorHandling(
  async (query) => {
    const response = await axios.get(
      `${API_BASE_URL}/beachMain/getNearByBeach${buildQueryString(query)}`
    );
    return response?.data;
  }
);

export function generateMetadata() {
  return SiteDataPageWise.nudeBeach;
}