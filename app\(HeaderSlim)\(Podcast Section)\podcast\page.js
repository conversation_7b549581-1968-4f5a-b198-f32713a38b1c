import React from "react";
import { CustomContainer } from "@/components/Custom-Display";
import BreadCumber from "@/components/Common/BreadCumber";
import PodcastContent from "@/components/Podcast/podcast-content";
import { SiteDataPageWise } from "@/data/siteMetadata";
import PodcastPageJSONLD from "@/components/Podcast/PodcastJSON";

const page = async () => {
  return (
    <>
      <PodcastPageJSONLD />
      <div className=" bg-gray-100 w-full p-4 flex items-center ">
        <CustomContainer>
          <BreadCumber
            data={[
              {
                title: "The Beaches Podcast",
              },
            ]}
          />
        </CustomContainer>
      </div>
      <PodcastContent />
    </>
  );
};

export default page;
export const revalidate = 3600;

export function generateMetadata() {
  return SiteDataPageWise.podcast;
}
