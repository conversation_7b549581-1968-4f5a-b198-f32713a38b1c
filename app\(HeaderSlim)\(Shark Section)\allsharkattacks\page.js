import BreadCumber from '@/components/Common/BreadCumber';
import { CustomContainer } from '@/components/Custom-Display';
import RecentSharkAttacks from '@/components/Shark/RecentSharkAttacks';
import SharkDetailSection from '@/components/Shark/SharkDetailSection';
import React from 'react'
import { API_BASE_URL, buildQueryString, convertSlugToSentence, EnvTrueFalse } from '@/helper/functions';
import axios from 'axios';
import HeaderLine from '@/components/Shark/HeaderLine';
import NameTitle from '@/components/Common/NameTitle';
import SharkListDetail from '@/components/Shark/SharkListDetail';
import { withErrorHandling } from '../shark/[countrySlug]/action';
import AllSharkAttacksPageJSONLD from '@/components/Shark/AllSharkAttacksPageJSONLD';
import { SiteDataPageWise } from '@/data/siteMetadata';


const page = async (props) => {
    const { params, searchParams } = props;
    const { data: recentAttack } = await getRecentSharkAttacks(
        searchParams?.countryId ? {
            limit: 25,
            page: 1,
            countryId: searchParams?.countryId
        } : {
            limit: 25,
            page: 1,
        }
    );
    // if (!sharkDetails?.sharkSpecies?.id) {
    //     console.log(params, "Shark Params");
    //     return redirect("/");
    // }

    return (
        <>
            <AllSharkAttacksPageJSONLD />
            {/* <CustomContainer> */}
            <div className=" bg-gray-100 w-full p-4 flex items-center ">
                <CustomContainer>
                    <BreadCumber
                        data={[
                            {
                                title: "All Shark Attacks",
                            },
                        ]}
                    />
                </CustomContainer>
            </div>
            <CustomContainer>
                <NameTitle
                    htag={1}
                    className="mt-6"
                    // name={convertSlugToSentence(params?.sharkList)}
                    name={"All Shark Attacks"}

                // description={ListBeach?.description}
                />
                <SharkListDetail countryId={searchParams?.countryId} initialData={recentAttack?.rows} />
            </CustomContainer>
            {/* <div className="flex justify-between items-center">
                    <h1 className="text-sandee-32 font-bold">
                        Shark Attacks in the {sharkDetails?.sharkSpecies?.name}
                    </h1>
                    <div className="flex gap-x-2 items-center">
                        <Link href={`/map`} className=" !text-nowrap">
                            <CustomButton type={4}>
                                <FullViewIcon className=" me-2 fill-sandee-orange group-hover:fill-white  h-4 w-4" />
                                Full View
                            </CustomButton>
                        </Link>
                        <Link href={`/map`} className=" !text-nowrap">
                            <CustomButton type={4}>
                                <ShareIcon className=" me-2 stroke-sandee-orange group-hover:stroke-white  h-[18px] w-[18px] " />
                                Share
                            </CustomButton>
                        </Link>
                    </div>
                </div> */}

            {/* <SharkDetailSection data={sharkDetails} /> */}
            {/* </CustomContainer> */}
            {/* <RecentSharkAttacks
                title={`Most Recent ${sharkDetails?.sharkSpecies?.name} Attacks`}
                description={`recent ${sharkDetails?.sharkSpecies?.name} Attacks`}
                recentAttack={sharkDetails?.mostRecentAttacks?.rows} /> */}
        </>
    );
}

export default page;
// export const revalidate = 3600;
// export const dynamic = "force-dynamic";



export const getRecentSharkAttacks = withErrorHandling(async (query) => {
    try {
        const APIRecentAttacks = `${API_BASE_URL}/shark-attacks/recentSharkAttacks${buildQueryString(
            query
        )}`;
        const response = await axios.get(APIRecentAttacks);
        return response?.data;
    } catch (error) {
        console.log(error)
    }

});

export function generateMetadata() {
    return SiteDataPageWise.allShark;
}

export const generateStaticParams = async () => {
    return []
}

export const revalidate = 43200;

export const dynamicParams =
    EnvTrueFalse?.[process.env.NEXT_PUBLIC_DYNAMIC_PARAMS] ?? true;
