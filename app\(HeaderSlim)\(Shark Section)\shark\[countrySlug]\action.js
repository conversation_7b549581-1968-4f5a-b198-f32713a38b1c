"use server";

import {
  API_BASE_URL,
  EnvTrueFalse,
  buildQueryString,
} from "@/helper/functions";
import axios from "axios";

// Helper function for error handling (not exported as it's not a server action)
const handleError = (error) => {
  // if (
  //   process?.env?.NEXT_PUBLIC_ERROR_LOG &&
  //   EnvTrueFalse?.[process.env.NEXT_PUBLIC_ERROR_LOG]
  // )
  // logController(
  //   "++++++++++++++++++++++++++==Full Error==+++++++++++++++++++++++++++++++++++\n",
  //   error?.config
  // );
  // logController(
  //   "++++++++++++++++++++++++++==Error URL==+++++++++++++++++++++++++++++++++++\n",
  //   error?.config?.url,
  //   "\n==========================================================================="
  // );

  // throw error;
  return { data: null, error: true };
};


export async function getStatesWithSharkAttacks(query) {
  try {
    const APIRecentAttacks = `${API_BASE_URL}/states/getStatesWithSharkAttacks${buildQueryString(
      query
    )}`;
    const response = await axios.get(APIRecentAttacks);
    return response?.data;
  } catch (error) {
    return handleError(error);
  }
}

export async function getRecentSharkAttacks(query) {
  try {
    const APIRecentAttacks = `${API_BASE_URL}/shark-attacks/recentSharkAttacks${buildQueryString(
      query
    )}`;
    const response = await axios.get(APIRecentAttacks);
    return { data: response?.data?.data?.rows };
  } catch (error) {
    console.log(error);
    return handleError(error);
  }
}