"use server";

import {
  API_BASE_URL,
  EnvTrueFalse,
  buildQueryString,
} from "@/helper/functions";
import axios from "axios";

export const withErrorHandling =
  (fn) =>
    async (...args) => {
      try {
        return await fn(...args);
      } catch (error) {
        // if (
        //   process?.env?.NEXT_PUBLIC_ERROR_LOG &&
        //   EnvTrueFalse?.[process.env.NEXT_PUBLIC_ERROR_LOG]
        // )
        // logController(
        //   "++++++++++++++++++++++++++==Full Error==+++++++++++++++++++++++++++++++++++\n",
        //   error?.config
        // );
        // logController(
        //   "++++++++++++++++++++++++++==Error URL==+++++++++++++++++++++++++++++++++++\n",
        //   error?.config?.url,
        //   "\n==========================================================================="
        // );

        // throw error;
        return { data: null, error: true };
      }
    };


export const getStatesWithSharkAttacks = withErrorHandling(async (query) => {
  const APIRecentAttacks = `${API_BASE_URL}/states/getStatesWithSharkAttacks${buildQueryString(
    query
  )}`;
  const response = await axios.get(APIRecentAttacks);
  return response?.data;
});

export const getRecentSharkAttacks = withErrorHandling(async (query) => {
  try {
    const APIRecentAttacks = `${API_BASE_URL}/shark-attacks/recentSharkAttacks${buildQueryString(
      query
    )}`;
    const response = await axios.get(APIRecentAttacks);
    return { data: response?.data?.data?.rows };
  } catch (error) {
    console.log(error)
  }

});