import BreadCumber from "@/components/Common/BreadCumber";
import { CustomContainer } from "@/components/Custom-Display";
import React from "react";
import AllSharkOnMap from "@/components/Shark/AllSharkOnMap";
import SharkDescription from "@/components/Shark/SharkDescription";
import axios from "axios";
import { API_BASE_URL, buildQueryString, EnvTrueFalse, slugConverter, WEBSITE_URL } from "@/helper/functions";
import RecentSharkAttacks from "@/components/Shark/RecentSharkAttacks";
import DangerSharkAttacks from "@/components/Shark/DangerSharkAttacks";
import CountrySharkAttacks from "@/components/Shark/CountrySharkAttacks";
import CustomButton from "@/components/Custom-Button";
import Link from "next/link";
import { CategoryIcon, FullViewIcon, ShareIcon } from "@/components/social-icons/icons";
import { withErrorHandling } from "@/app/(HeaderSlim)/(Single Country Page)/[countrySlug]/action";
import { CountrygetcountryHeaderImage } from "@/app/(HeaderSlim)/(Single Country Page)/[countrySlug]/page";
import StateSharkAttacks from "@/components/Shark/StateSharkAttacks";
import NameTitle from "@/components/Common/NameTitle";
import AllAttackedBeaches from "@/components/Shark/AllAttackedBeaches";
import { getStatesWithSharkAttacks } from "./action";
import WithoutSharkAttackStateList from "@/components/Shark/WithoutSharkAttackStateList";
import Image from "next/image";
import SharkDetailSection from "@/components/Shark/SharkDetailSection";
import HeaderLine from "@/components/Shark/HeaderLine";
import Home_Bottom_Button from "@/components/HomePage/HomeBottomButton";
import SharkCountryPageJSONLD from "@/components/Shark/SharkCountryPageJSONLD";
import { redirect } from "next/navigation";
const sharkDesc =
  "<p>Have you ever wondered why sharks get such a bad rap from humans? It surely can’t be because of their razor-sharp teeth, craving for blood, or uncanny hunting skills, right?  In actuality, it is!  Sharks, in general, epitomize human fear.  There have been over 6,000 shark attacks ever recorded, however, the United States, South Africa, and Australia take the cake for most shark attacks in the world. Specifically, the United States clocks in at over 1,600 shark attacks with over 60.</p>";
const page = async (props) => {
  const { params } = props;
  const { data: HeaderCountryData } = await CountrygetcountryHeaderImage(
    params?.countrySlug
  );


  const [
    { data: recentAttack },
    { data: dangerAttack },
    { data: stateAttack },
    { data: statesWithSharkAttacks },
    { data: withoutSharkAttackStateList },
    // { data: sharkDetails },
  ] = await Promise.all([
    getRecentSharkAttacks({
      limit: 8,
      page: 1,
      countryId: HeaderCountryData?.id,
    }),
    getShakiestBeaches({
      limit: 8,
      page: 1,
      countryId: HeaderCountryData?.id,
    }),
    getShakiestStates({
      limit: 12,
      page: 1,
      countryId: HeaderCountryData?.id,
    }),

    getStatesWithSharkAttacks({
      // sortOrder: "DESC",
      // sortBy: "createdAt",
      page: 1,
      limit: 3,
      countryId: HeaderCountryData?.id,

    }),
    getWithoutSharkAttackStateList({
      withOutAttacks: 1,
      limit: 40,
      page: 1,
      countryId: HeaderCountryData?.id,
    }),
    // getSharkSpecies({
    //   // limit: 200,
    //   page: 1,
    // }),
  ]);
  if (!HeaderCountryData?.id) {
    console.log(params, "Country Params");
    return redirect("/");
  }
  // console.log(HeaderCountryData, "shark details")
  const latStr = HeaderCountryData?.GeoLoc?.coordinates?.length
    ? HeaderCountryData?.GeoLoc?.coordinates?.[1]
    : 0;
  const longStr = HeaderCountryData?.GeoLoc?.coordinates?.length
    ? HeaderCountryData?.GeoLoc?.coordinates?.[0]
    : 0;
  // console.log(statesWithSharkAttacks?.data?.rows)
  return (
    <>
      <SharkCountryPageJSONLD params={params} countryData={HeaderCountryData} />
      <CustomContainer>
        <BreadCumber
          data={[
            {
              title: "Sharks Attacks",
              to: "/shark",
            },
            {
              title: HeaderCountryData?.name,
            },
          ]}
        />
        <HeaderLine
          title={`Shark Attacks in ${HeaderCountryData?.name}`}
          link={`/shark/${HeaderCountryData?.slug}`}
        />
        {/* <div className="flex justify-between items-center">
          <h1 className="text-sandee-32 font-bold">
            Shark Attacks in the {HeaderCountryData?.name}
          </h1>
          <div className="flex gap-x-2 items-center">
            <Link href={`/map`} className=" !text-nowrap">
              <CustomButton type={4}>
                <FullViewIcon className=" me-2 fill-sandee-orange group-hover:fill-white  h-4 w-4" />
                Full View
              </CustomButton>
            </Link>
            <Link href={`/map`} className=" !text-nowrap">
              <CustomButton type={4}>
                <ShareIcon className=" me-2 stroke-sandee-orange group-hover:stroke-white  h-[18px] w-[18px] " />
                Share
              </CustomButton>
            </Link>
          </div>
        </div> */}
        <AllSharkOnMap zoom={3} latStr={latStr} longStr={longStr} />
        {/* <SharkDetailSection /> */}

        <h2 className="text-sandee-32 font-bold">Introduction</h2>
        <SharkDescription data={{ description: sharkDesc }} />
      </CustomContainer>
      {recentAttack?.rows?.length > 0 && <RecentSharkAttacks
        title={"Most Recent Shark Attacks in " + HeaderCountryData?.name ?? ""}
        recentAttack={recentAttack?.rows}
        ExtraButton={
          // <div className=" hidden md:flex justify-end items-start w-3/12">
          <Link
            href={`/shark/${params?.countrySlug}/allsharkattacks`}
            className=" !text-nowrap"
          >
            <CustomButton className="custom-hover-slide-button group font-bold w-full md:w-auto" type={4}>
              <CategoryIcon
                className={`me-2 fill-sandee-orange group-hover:fill-white  h-4 w-4`}
              />
              View All
            </CustomButton>
          </Link>
          // </div>
        }
      />}
      {dangerAttack?.data?.rows?.length > 0 && <DangerSharkAttacks description={`Sharkiest Beaches In ${HeaderCountryData?.name ?? ""}`}
        dangerAttack={dangerAttack?.data?.rows} />}
      {stateAttack?.data?.count > 0 && <CustomContainer>

        <StateSharkAttacks
          stateData={stateAttack?.data?.rows}
          totalRecords={stateAttack?.data?.count}
          countryId={HeaderCountryData?.id}
          countryslug={HeaderCountryData?.slug}
        />
      </CustomContainer>}
      {statesWithSharkAttacks && statesWithSharkAttacks?.length > 0 && <CustomContainer>
        <NameTitle
          className="mt-7 pb-1"
          name={"All States With Shark Attacks "}
          description={`${HeaderCountryData?.name ?? ""} Shark attacks`}
          type={2}
        />
        <SharkDescription data={{ description: "Since 1819, the United States has experienced 1,091 shark attacks – 231 of which have been fatal.  The most common for shark attacks are, in order, Florida, Hawaii, California, and Oregon. This is the list of all states with shark attacks:" }} />
        < AllAttackedBeaches initialData={statesWithSharkAttacks} countryId={HeaderCountryData?.id} />
      </CustomContainer>}
      <CustomContainer>
        <NameTitle
          className="mt-7 pb-2"
          name={"All States Without Shark Attacks"}
          description={"All “Shark-Free” States!"}
          type={2}
        />
        <WithoutSharkAttackStateList
          totalRecords={withoutSharkAttackStateList?.data?.count}
          data={withoutSharkAttackStateList?.data?.rows}
          countryId={HeaderCountryData?.id}
          countrySlug={params?.countrySlug} />
        <Home_Bottom_Button />
      </CustomContainer>
    </>
  );
};

export default page;

// export const revalidate = 3600;
// export const dynamic = "force-dynamic";


export const getRecentSharkAttacks = withErrorHandling(async (query) => {
  const APIRecentAttacks = `${API_BASE_URL}/shark-attacks/recentSharkAttacks${buildQueryString(
    query
  )}`;
  const response = await axios.get(APIRecentAttacks);
  return response?.data;
});
export const getShakiestBeaches = withErrorHandling(async (query) => {
  const APIShakiestBeaches = `${API_BASE_URL}/beachMain/getShakiestBeaches${buildQueryString(
    query
  )}`;
  const response = await axios.get(APIShakiestBeaches);
  return response?.data;
});
export const getShakiestStates = withErrorHandling(async (query) => {
  const APIShakiestCountries = `${API_BASE_URL}/states/shakiestStates${buildQueryString(
    query
  )}`;
  const response = await axios.get(APIShakiestCountries);
  return response?.data;
});
export const getSharkSpecies = withErrorHandling(async (query) => {
  const APISharkSpecies = `${API_BASE_URL}/shark-species${buildQueryString(
    query
  )}`;
  const response = await axios.get(APISharkSpecies);
  return response?.data;
});

export const getWithoutSharkAttackStateList = withErrorHandling(async (query) => {
  const APISharkSpecies = `${API_BASE_URL}/states/shakiestStates${buildQueryString(
    query
  )}`;
  const response = await axios.get(APISharkSpecies);
  return response?.data;
});
// export const getStatesWithSharkAttacks = withErrorHandling(async (query) => {
//   const APIRecentAttacks = `${API_BASE_URL}/states/getStatesWithSharkAttacks${buildQueryString(
//     query
//   )}`;
//   const response = await axios.get(APIRecentAttacks);
//   return response?.data;
// });

export async function generateMetadata({ params }) {
  // const { data: MetaData } = await CountrygetcountryHeaderImage(
  //   params?.countrySlug
  // );
  return {
    title:
      // MetaData?.title ??
      `Shark Protection Laws in ${slugConverter(params?.countrySlug, true)} 2025 | Sandee`,
    description:
      // MetaData?.description ??
      `Learn about shark protection laws and regulations in ${slugConverter(
        params?.countrySlug,
        true
      )} for 2025. Stay informed with Sandee on safe practices and conservation efforts to protect marine life in your region.`,
    alternates: {
      canonical: `${WEBSITE_URL}/shark/${params?.countrySlug}`,
    },
  };
}

export const generateStaticParams = async () => {
  return []
}

export const revalidate = 43200;

export const dynamicParams =
  EnvTrueFalse?.[process.env.NEXT_PUBLIC_DYNAMIC_PARAMS] ?? true;
