import BreadCumber from "@/components/Common/BreadCumber";
import { CustomContainer } from "@/components/Custom-Display";
import React from "react";
import { withErrorHandling } from "../../(Single Country Page)/[countrySlug]/action";
import AllSharkOnMap from "@/components/Shark/AllSharkOnMap";
import SharkDescription from "@/components/Shark/SharkDescription";
import axios from "axios";
import { API_BASE_URL, buildQueryString, EnvTrueFalse } from "@/helper/functions";
import RecentSharkAttacks from "@/components/Shark/RecentSharkAttacks";
import DangerSharkAttacks from "@/components/Shark/DangerSharkAttacks";
import CountrySharkAttacks from "@/components/Shark/CountrySharkAttacks";
import SharkSpicesAttacks from "@/components/Shark/SharkSpicesAttacks";
import CustomButton from "@/components/Custom-Button";
import Link from "next/link";
import { CategoryIcon, FullViewIcon, ShareIcon } from "@/components/social-icons/icons";
import HeaderLine from "@/components/Shark/HeaderLine";
import { sharkIntroduction } from "@/data/sharkPageData";
import Home_Bottom_Button from "@/components/HomePage/HomeBottomButton";
import SharkPageJSONLD from "@/components/Shark/SharkPageJSONLD";
import { SiteDataPageWise } from "@/data/siteMetadata";
import SharkAttackForm from "@/components/SharkForm/SharkAttackForm";
import SharkDescriptionWithForm from "@/components/Shark/SharkDescriptionWithForm";
import SharkSightingForm from "@/components/SharkForm/SharkSightingForm";
const sharkDesc =
  "<p>Have you ever wondered why sharks get such a bad rap from humans? It surely can’t be because of their razor-sharp teeth, craving for blood, or uncanny hunting skills, right?  In actuality, it is!  Sharks, in general, epitomize human fear.  There have been over 6,000 shark attacks ever recorded, however, the United States, South Africa, and Australia take the cake for most shark attacks in the world. Specifically, the United States clocks in at over 1,600 shark attacks with over 60.</p>";
const page = async () => {
  const [
    { data: recentAttack },
    { data: dangerAttack },
    { data: countriesAttack },
    { data: sharkSpecies },
  ] = await Promise.all([
    getRecentSharkAttacks({
      limit: 8,
      page: 1,
    }),
    getShakiestBeaches({
      limit: 8,
      page: 1,
    }),
    getShakiestCountries({
      limit: 12,
      page: 1,
    }),
    getSharkSpecies({
      // limit: 200,
      page: 1,
    }),
  ]);

  return (
    <>
      <SharkPageJSONLD />
      <CustomContainer>
        <BreadCumber
          data={[
            {
              title: "Sharks Attacks",
            },
          ]}
        />
        {/* <div className="md:flex justify-between items-center">
          <h1 className="text-sandee-32 font-bold text-center md:text-start">
            International Shark Attacks
          </h1>
          <div className="flex gap-x-2 justify-between md:justify-normal items-center">
            <Link href={`/map`} className=" !text-nowrap">
              <CustomButton type={4}>
                <FullViewIcon className=" me-2 fill-sandee-orange group-hover:fill-white  h-4 w-4" />
                Full View
              </CustomButton>
            </Link>
            <Link href={`/map`} className=" !text-nowrap">
              <CustomButton type={4}>
                <ShareIcon className=" me-2 stroke-sandee-orange group-hover:stroke-white  h-[18px] w-[18px] " />
                Share
              </CustomButton>
            </Link>
          </div>
        </div> */}
        <HeaderLine
          title={`International Shark Attacks`}
          link={`/shark`}
        // isSharkForm={true}
        />
        <AllSharkOnMap
          // latStr={-118.4117325} 
          // longStr={34.020479} 
          zoom={0} />
        <h2 className="text-sandee-32 font-bold pb-0">Introduction</h2>
        <SharkDescription data={{ description: sharkIntroduction }} />
        {/* <SharkDescriptionWithForm data={{ description: sharkIntroduction }} /> */}
      </CustomContainer>
      {recentAttack?.rows?.length > 0 && <RecentSharkAttacks
        recentAttack={recentAttack?.rows}
        ExtraButton={
          <Link
            href={`/allsharkattacks`}
            className=" !text-nowrap"
          >
            <CustomButton className="custom-hover-slide-button group font-bold w-full md:w-auto" type={4}>
              <CategoryIcon
                className={`me-2 fill-sandee-orange group-hover:fill-white  h-4 w-4`}
              />
              View All
            </CustomButton>
          </Link>
        }
      />}
      {dangerAttack?.data?.rows?.length > 0 && <DangerSharkAttacks dangerAttack={dangerAttack?.data?.rows} />}
      <CustomContainer>
        <CountrySharkAttacks
          countryData={countriesAttack?.data?.rows}
          totalRecords={countriesAttack?.data?.count}
          description={"Top Shark Attack Countries"}
          title={"Countries with The Most Shark Attacks"}

        />
        <SharkSpicesAttacks speciesData={sharkSpecies?.rows} />
        <Home_Bottom_Button />

        <SharkAttackForm />
        <SharkSightingForm />
      </CustomContainer>

    </>
  );
};

export default page;

// export const revalidate = 60;
// export const dynamic = "force-dynamic";

// export const dynamicParams = "force-dynamic";
export const getRecentSharkAttacks = withErrorHandling(async (query) => {
  const APIRecentAttacks = `${API_BASE_URL}/shark-attacks/recentSharkAttacks${buildQueryString(
    query
  )}`;
  const response = await axios.get(APIRecentAttacks);
  return response?.data;
});
export const getShakiestBeaches = withErrorHandling(async (query) => {
  const APIShakiestBeaches = `${API_BASE_URL}/beachMain/getShakiestBeaches${buildQueryString(
    query
  )}`;
  const response = await axios.get(APIShakiestBeaches);
  return response?.data;
});
export const getShakiestCountries = withErrorHandling(async (query) => {
  const APIShakiestCountries = `${API_BASE_URL}/countries/shakiestCountries${buildQueryString(
    query
  )}`;
  const response = await axios.get(APIShakiestCountries);
  return response?.data;
});
export const getSharkSpecies = withErrorHandling(async (query) => {
  const APISharkSpecies = `${API_BASE_URL}/shark-species${buildQueryString(
    query
  )}`;
  const response = await axios.get(APISharkSpecies);
  return response?.data;
});

export function generateMetadata() {
  return SiteDataPageWise.shark;
}

export const generateStaticParams = async () => {
  return []
}

export const revalidate = 43200;

export const dynamicParams =
  EnvTrueFalse?.[process.env.NEXT_PUBLIC_DYNAMIC_PARAMS] ?? true;