import BreadCumber from '@/components/Common/BreadCumber';
import CustomButton from '@/components/Custom-Button';
import { CustomContainer } from '@/components/Custom-Display';
import RecentSharkAttacks from '@/components/Shark/RecentSharkAttacks';
import SharkDetailSection from '@/components/Shark/SharkDetailSection';
import { FullViewIcon, ShareIcon } from '@/components/social-icons/icons';
import Link from 'next/link';
import React from 'react'
import { withErrorHandling } from '../../[countrySlug]/action';
import { API_BASE_URL, EnvTrueFalse, slugConverter, WEBSITE_URL } from '@/helper/functions';
import axios from 'axios';
import HeaderLine from '@/components/Shark/HeaderLine';
import Home_Bottom_Button from '@/components/HomePage/HomeBottomButton';
import SharkSpeciesPageJSONLD from '@/components/Shark/SharkSpeciesPageJSONLD';

const page = async (props) => {
    const { params } = props;

    const { data: sharkDetails } = await getSharkSpeciesDetail(
        params?.sharkspecies
    );
    // console.log(HeaderCountryData, "country data...")

    if (!sharkDetails?.sharkSpecies?.id) {
        console.log(params, "Shark Params");
        return redirect("/");
    }

    return (
        <>
            <SharkSpeciesPageJSONLD data={sharkDetails} />
            <CustomContainer>
                <div className='hidden md:flex'>
                    <BreadCumber
                        data={[
                            {
                                title: "Sharks Attacks",
                                to: "/shark",
                            },
                            // {
                            //     title: "Shark Species",
                            // },
                            {
                                title: sharkDetails?.sharkSpecies?.name,
                            },
                        ]}
                    />
                </div>
                <HeaderLine
                    title={`${sharkDetails?.sharkSpecies?.name}`}
                    link={`/shark/shark-species/${sharkDetails?.sharkSpecies?.slug}`}
                    isMobile
                />
                {/* <div className="flex justify-between items-center">
                    <h1 className="text-sandee-32 font-bold">
                        Shark Attacks in the {sharkDetails?.sharkSpecies?.name}
                    </h1>
                    <div className="flex gap-x-2 items-center">
                        <Link href={`/map`} className=" !text-nowrap">
                            <CustomButton type={4}>
                                <FullViewIcon className=" me-2 fill-sandee-orange group-hover:fill-white  h-4 w-4" />
                                Full View
                            </CustomButton>
                        </Link>
                        <Link href={`/map`} className=" !text-nowrap">
                            <CustomButton type={4}>
                                <ShareIcon className=" me-2 stroke-sandee-orange group-hover:stroke-white  h-[18px] w-[18px] " />
                                Share
                            </CustomButton>
                        </Link>
                    </div>
                </div> */}

                <SharkDetailSection data={sharkDetails} />

            </CustomContainer>
            {sharkDetails?.mostRecentAttacks?.rows?.length > 0 && <RecentSharkAttacks
                title={`Most Recent ${sharkDetails?.sharkSpecies?.name} Attacks`}
                description={`recent ${sharkDetails?.sharkSpecies?.name} Attacks`}
                recentAttack={sharkDetails?.mostRecentAttacks?.rows} />}
            <CustomContainer>
                <Home_Bottom_Button />
            </CustomContainer>
        </>
    );
}

export default page;
// export const revalidate = 3600;
// export const dynamic = "force-dynamic";



export const getSharkSpeciesDetail = withErrorHandling(async (id) => {
    const APISharkSpecies = `${API_BASE_URL}/shark-species/getSharkSpeciesDetails/${id}`;

    const response = await axios.get(APISharkSpecies);

    return response?.data;
});

export async function generateMetadata({ params }) {

    const { data: sharkDetails } = await getSharkSpeciesDetail(
        params?.sharkspecies
    );
    return {
        title:
            // MetaData?.title ??
            `Explore "${slugConverter(sharkDetails?.sharkSpecies?.name, true)}" with Sandee – The Ultimate Guide to Understanding and Conserving Sharks`,
        description:
            // MetaData?.description ??
            `Dive into the world of "${slugConverter(sharkDetails?.sharkSpecies?.name, true)}" with Sandee. Learn about their habitat, behavior, and conservation efforts, making you a shark expert in no time!`,
        alternates: {
            canonical: `${WEBSITE_URL}/shark/shark-species/${params?.sharkspecies}`,
        },
    };
}

export const generateStaticParams = async () => {
    return []
}

export const revalidate = 43200;

export const dynamicParams =
    EnvTrueFalse?.[process.env.NEXT_PUBLIC_DYNAMIC_PARAMS] ?? true;