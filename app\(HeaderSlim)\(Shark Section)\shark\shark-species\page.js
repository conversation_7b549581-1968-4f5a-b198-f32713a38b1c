import { redirect } from "next/navigation";

const Page = () => {
    redirect('/');
    return null; // The page is blank because we are redirecting
};

export async function getServerSidePros() {
    return {
        redirect: {
            destination: '/', // Redirect to the homepage
            permanent: false, // Set to `true` if this is a permanent redirect (301), `false` for a temporary redirect (302)
        },
    };
}

export default Page;