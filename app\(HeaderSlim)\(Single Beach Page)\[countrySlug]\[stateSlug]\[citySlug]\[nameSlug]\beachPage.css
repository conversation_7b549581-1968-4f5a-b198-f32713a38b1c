.container--tabs {
  @apply mt-5;
}
.container--tabs .nav-tabs {
  @apply float-left w-full list-none m-0 border-b-[#ddd] border-b border-solid gap-x-8 gap-y-5 flex-wrap;
}
.container--tabs .nav-tabs > li {
  @apply -mb-px;
}
.container--tabs .nav-tabs > li > p {
  @apply mr-0.5 p-2.5  border-transparent hover:border-b-2  border-b-black cursor-pointer text-sandee-grey hover:text-black;
}
.container--tabs .nav-tabs > li.active > p,
.container--tabs .nav-tabs > li.active > p:hover,
.container--tabs .nav-tabs > li.active > p:focus {
  @apply font-bold cursor-default bg-white border-transparent border-2 border-b-black border-solid text-black;
}
.container--tabs .tab-content {
  @apply float-left w-full -mb-4;
}
.container--tabs .tab-content > .tab-pane {
  @apply hidden mt-5;
}
.container--tabs .tab-content > .tab-pane.active {
  @apply block  pt-3 text-start;
}
.container--tabs .tab-content > .active {
  @apply block;
}

.beachDescription p a {
  color: #00aae3 !important;
  text-decoration: none;
  font-size: 16px !important;
}

.beachDescription h3 {
  margin-bottom: 10px;
  margin-top: 30px;
  font-size: 16px !important;
  font-weight: 700;
}

.beachDescription p {
  letter-spacing: 0.7px;
  line-height: 33px !important;
  margin-bottom: 10px;
  font-size: 16px !important;
}
.beachDescription p span {
  font-size: 16px !important;
}
.faqBoxShadow {
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.12),
    0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 2px 1px -1px rgba(0, 0, 0, 0.2);
}

.accordion {
  @apply w-full  overflow-hidden  mt-2 bg-[#FAFAFA] border-none rounded-lg border-gray-400 cursor-pointer;
}
.accordion__intro {
  @apply relative cursor-pointer font-normal p-2 mx-2 text-sandee-sm text-black;
}
.accordion__content {
  @apply max-h-0 overflow-hidden will-change-[max-height] transition-all duration-[0.25s] ease-[ease-out]  opacity-0 px-5 py-0;
}
.accordion__active .accordion__content {
  @apply opacity-100 pt-0 pb-2 px-5 text-sandee-grey text-sandee-sm max-h-full;
}
.accordion__active .icon__content {
  @apply rotate-180 transition-all duration-[0.25s] ease-[ease-out];
}
.icon__content {
  @apply rotate-0 transition-all duration-[0.25s] ease-[ease-out];
}

.box-shadow-bottom {
  box-shadow: 0px 20px 10px -25px rgba(0, 0, 0, 0.45);
}

.customab .swiper {
  width: 100%;
  height: 100%;
}

.customab .swiper-slide {
  text-align: center;
  font-size: 18px;
  background: #fff;

  /* Center slide text vertically */
  display: flex;
  justify-content: center;
  align-items: center;
}

.customab .swiper-slide img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.customab .swiper {
  width: 100%;
  height: 300px;
  margin-left: auto;
  margin-right: auto;
}

.customab .swiper-slide {
  background-size: cover;
  background-position: center;
}

.customab .mySwiper2 {
  height: 80%;
  width: 100%;
}

.customab .mySwiper {
  height: 20%;
  box-sizing: border-box;
  /* padding: 26px 0 0 0; */
}

.customab .mySwiper .swiper-slide {
  /* width: 20%; */
  height: 100%;
  opacity: 1;
  margin-right: 15px !important;
  /* opacity: 0.4; */
}

.customab .mySwiper .swiper-slide-thumb-active {
  opacity: 0.4;
}

.customab .swiper-slide img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.rating:not(:checked) > input {
  position: absolute;
  appearance: none;
}

.rating:not(:checked) > label {
  float: right;
  cursor: pointer;
  font-size: 30px;
  fill: #666;
}

.rating:not(:checked) > label > svg {
  fill: #666; /* Set default color for SVG */
  transition: fill 0.3s ease; /* Add a transition effect */
}

.rating > input:checked + label:hover,
.rating > input:checked + label:hover ~ label,
.rating > input:checked ~ label:hover,
.rating > input:checked ~ label:hover ~ label,
.rating > label:hover ~ input:checked ~ label {
  fill: #e58e09;
}

.rating:not(:checked) > label:hover,
.rating:not(:checked) > label:hover ~ label {
  fill: #ff9e0b;
}

.rating > input:checked ~ label > svg {
  fill: #ffa723; /* Set color for selected stars */
}

.customab .swiper-button-next,
.customab .swiper-button-prev {
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);

  width: 40px;
  max-height: 40px;
  background-repeat: no-repeat;

  border-radius: 100%;
}
/* background-color: rgba(255, 255, 255, 0.4); */
/* background-color: white; */
/* background-position: center; */
/* background-size: 40px; */
/* padding: 8px 16px; */
/* border: 2px solid black; */
/* color: red; */
.customab .swiper-button-prev {
  background-image: url("/static/icons/prev.svg");
}

.customab .swiper-button-next {
  background-image: url("/static/icons/next.svg");
}

.customab .swiper-button-next::after,
.customab .swiper-button-prev::after {
  content: "";
}

/* .customab .swiper-pagination-bullet {
  width: 40px;
  height: 40px;
  background-color: red;
} */
