import BeachCamVideoSkeleton from "@/components/BeachPage/Skeleton/BeachCamVideoSkeleton";
import BeachFAQSkeleton from "@/components/BeachPage/Skeleton/BeachFAQSkeleton";
import BeachHeaderSkeleton from "@/components/BeachPage/Skeleton/BeachHeaderSkeleton";
import BeachNearBySkeleton from "@/components/BeachPage/Skeleton/BeachNearBySkeleton";
import BeachSummaryBasicInfoSkeleton from "@/components/BeachPage/Skeleton/BeachSummaryBasicInfoSkeleton";
import BeachSwiperSkeleton from "@/components/BeachPage/Skeleton/BeachSwiperSkeleton";
import CountryBlogSectionSkeleton from "@/components/CountryPage/Skeleton/CountryBlogSectionSkeleton";
import { CustomContainer } from "@/components/Custom-Display";
import React from "react";

const loading = () => {
  return (
    <div>
      <div className="w-full py-4 flex items-center bg-slate-100 h-10"></div>
      <div className="my-5">
        <div className="w-full py-4 flex items-center bg-slate-100 h-14"></div>
        <div className="w-full py-4 flex items-center bg-slate-100 h-10"></div>
        <div className=" md:hidden w-full py-4 flex items-center bg-slate-100 h-16"></div>
        <div className=" md:hidden w-full py-4 flex items-center bg-slate-100 h-16"></div>
      </div>
      <CustomContainer>
        <BeachHeaderSkeleton />
        {/* <BeachSwiperSkeleton /> */}
        <BeachSummaryBasicInfoSkeleton />
        <BeachCamVideoSkeleton />
        <BeachCamVideoSkeleton />
        <BeachFAQSkeleton />
        <BeachNearBySkeleton />
      </CustomContainer>
      <div className="listicle-skeleton my-8 ">
        <div className="heading">
          <div className="animate-pulse h-5 bg-slate-200 my-4"></div>
          <div className="animate-pulse h-5 bg-slate-200 my-4"></div>
        </div>
        <div className=" my-8 h-[220px] bg-slate-200 animate-pulse "></div>
      </div>
      <CustomContainer>
        <CountryBlogSectionSkeleton />
        <div className=" h-14 bg-slate-200 animate-pulse my-8"></div>
      </CustomContainer>
    </div>
  );
};

export default loading;
