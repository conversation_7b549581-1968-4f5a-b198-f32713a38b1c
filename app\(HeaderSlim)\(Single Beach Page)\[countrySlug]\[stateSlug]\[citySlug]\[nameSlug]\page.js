import Beach_BreadCumber_Section from "@/components/BeachPage/BeachBreadCumber";
import BeachPage_BeachHero_Section from "@/components/BeachPage/BeachHero";
import BeachSummary_Section from "@/components/BeachPage/BeachSummary";
import BeachTitle_And_Detail_Section from "@/components/BeachPage/BeachTitleAndDetail";
import { CustomContainer } from "@/components/Custom-Display";
import {
  API_BASE_URL,
  EnvTrueFalse,
  WEBSITE_URL,
  buildQueryString,
  getEmbedUrl,
  slugConverter,
} from "@/helper/functions";
import axios from "axios";
import { redirect } from "next/navigation";
import React from "react";
import "./beachPage.css";
import BeachInformation_Section from "@/components/BeachPage/BeachInformation";
import BeachBlog_Related_Bottom_Section from "@/components/BeachPage/BeachBlogRelatedBottom";
import BeachFAQ from "@/components/BeachPage/BeachFAQ";
// import Beach_NearBy_Section from "@/components/BeachPage/BeachNearBy";
import Home_Listcle_Section from "@/components/HomePage/HomeListcle";
import BeachPageJSONLD from "@/components/BeachPage/BeachPageJSONLD";
import BeachCamsSection from "@/components/BeachPage/BeachCamsSection";
import BeachBasicDetails_Section from "@/components/BeachPage/BeachBasicDetails";
import BeachLocationBig from "@/components/BeachPage/BeachLocationBig";
import Beach_Bottom_Button from "@/components/BeachPage/BeachBottomButton";
import { withErrorHandling } from "@/app/(HeaderSlim)/(Single Country Page)/[countrySlug]/action";
import { CountrygetBlogCountryWise } from "@/app/(HeaderSlim)/(Single Country Page)/[countrySlug]/page";
import BeachWeatherCard from "@/components/BeachPage/BeachWeatherCard";
import LocationMap from "@/components/Common/LocationMap";
import BeachReviewBottomDisplay from "@/components/BeachPage/BeachReviewBottomDisplay";
import Beach_NearBy_Section from "@/components/BeachPage/BeachNearBy";
import Link from "next/link";
import CustomButton from "@/components/Custom-Button";
import { ZoomInIcon } from "@/components/social-icons/icons";
import ReviewSection from "@/components/Common/ReviewSection";

const page = async (props) => {
  // try {
  const { params } = props;
  const ParamData = Object.values(params);
  // const {data: ResponseOfRedirect} = await PostRedirectBeachUrl(params);
  // RedirectionFunction(ResponseOfRedirect);

  const { data: beachData } = await BeachgetSingleBeach(ParamData.join("/"));
  if (beachData === null || !beachData) {
    redirect("/");
  }
  beachData.lat = beachData?.GeoLoc?.coordinates?.length
    ? beachData?.GeoLoc?.coordinates?.[1]
    : 0;
  beachData.lon = beachData?.GeoLoc?.coordinates?.length
    ? beachData?.GeoLoc?.coordinates?.[0]
    : 0;

  const [
    MultipleImages,
    WeatherDeatils,
    { data: MetaData = [] },
    { data: ListicleResponse = [] },
    { data: NearBeach = [] },
    { data: blogsData = [] },
    { data: faqsData = [] },
    { data: reviewAnalysis = [] },
  ] = await Promise.all([
    BeachgetMultipleImage(beachData.id),
    BeachgetWeather(beachData.lat, beachData.lon),
    BeachgetSingleBeachSEO(ParamData.join("/")),
    BeachgetListiclesBeachWise({
      ...params,
    }),
    BeachgetNearByBeach(beachData),
    CountrygetBlogCountryWise({
      tags: beachData?.country?.name ?? beachData?.name,
      limit: 12,
      page: 1,
    }),
    getExtraFaqs({
      AllBeachId: beachData?.id,
    })
    , getReviewAnalysis({}, params)
  ]);
  const SEOData = MetaData;

  // const faqsData = [];
  return (
    <>
      <BeachPageJSONLD
        data-testid="beach-jsonld"
        beachData={beachData}
        SEOData={SEOData}
        params={params}
      />
      <div data-testid="beach-breadcrumb-section" className="  w-full py-4 flex items-center z-50">
        <CustomContainer>
          <Beach_BreadCumber_Section beachData={beachData} />
        </CustomContainer>
      </div>
      <div data-testid="beach-title-detail-section" className=" md:sticky top-0 bg-white pb-2 z-50 box-shadow-bottom lg:block hidden">
        <CustomContainer>
          <BeachTitle_And_Detail_Section beachData={beachData} />
          {/* data-testid="beach-title-detail" */}
        </CustomContainer>
      </div>

      <CustomContainer dataTestid="beach-hero-section-container" className=" max-w-full sm:!container ">
        <BeachPage_BeachHero_Section data-testid="beach-hero-section"
          beachData={beachData}
          MultipleImages={
            MultipleImages?.data?.length > 0
              ? [
                beachData?.images?.[0],
                ...MultipleImages.data.filter((item) => !item.isCoverPhoto),
              ]?.slice(0, 11)
              : [...beachData?.images?.slice(0, 11)]
          }
          AllImages={MultipleImages?.data?.length > 0
            ? [
              beachData?.images?.[0],
              ...MultipleImages.data,
            ]?.filter(
              (image, index, self) =>
                image?.id && index === self.findIndex((img) => img?.id === image?.id)
            )
            : [beachData?.images?.[0]]}
          WeatherDeatils={WeatherDeatils}
        />
      </CustomContainer>
      <CustomContainer
        dataTestid="beach-summary-container"
        className="xs:pt-[30px]"
      >
        <div
          data-testid="beach-summary-sections"
          className="realtive w-full flex flex-col"
        >
          <BeachSummary_Section
            data-testid="beach-summary-section"
            beachData={beachData}
          />
          <BeachBasicDetails_Section
            data-testid="beach-basic-details-section"
            beachData={beachData}
          />
          <BeachInformation_Section
            data-testid="beach-information-section"
            beachData={beachData}
          />
        </div>
        {/* <div className=" w-full gap-5 flex-col flex"> */}
        {/* <div className="w-full md:flex md:space-x-4  "> */}
        {/* <div className="md:w-1/2 col-6 py-3 md:px-4 my-5  flex flex-col justify-center items-center lg:bg-[#FAFAFA] rounded-sandee lg:min-h-[400px]">
            <p className="font-semibold leading-[44px] text-center lg:text-sandee-24 text-lg">
              {beachData?.name} - Weather
            </p>
            <div className="  rounded-sandee shadow-md w-full lg:w-[90%] ">
              <BeachWeatherCard
                WeatherDeatils={WeatherDeatils}
                beachData={beachData}
              />
            </div>
          </div> */}
        {/* <div className="md:w-1/2 col-6 lg:bg-[#FAFAFA] pt-5 lg:pt-8  md:px-4 my-5 py-3 flex flex-col justify-center items-center rounded-sandee lg:min-h-[400px]"> */}
        {/* <div className=" flex flex-col justify-center items-center  "> */}
        {/* <p className="font-semibold leading-[44px] text-center lg:text-sandee-24 text-lg">
              {beachData?.name} - Beach Map
            </p> */}
        {/* <Link
              href={`/map/${beachData?.nameSlug}/@${beachData?.lat},${beachData?.lon}`}
            >
              <CustomButton type={4}>View Full Map</CustomButton>
            </Link> */}
        {/* <div className=" w-full  lg:w-[90%]">
              <LocationMap
                latStr={beachData.lat}
                longStr={beachData.lon}
                mapClass="md:map-container w-full lg:h-[256px] h-[300px] md:h-[256px] p-0 rounded-[22px]"
                zoom={12}
              />
              <Link
                href={`/map/${beachData?.nameSlug}/@${beachData?.lat},${beachData?.lon}`}
                className="absolute bottom-10 right-10"
              >
                <ZoomInIcon />
              </Link>
            </div> */}
        {/* </div> */}
        {/* </div> */}
        {/* </div> */}

        <div data-testid="beach-map-section" className=" lg:bg-[#FAFAFA] lg:pt-8 pb-3 md:px-4 my-5 rounded-sandee lg:min-h-[400px]">
          <div className=" flex flex-col justify-center items-center">
            <p data-testid="beach-map-title" className="font-semibold leading-[44px] text-center lg:text-sandee-24 text-lg">
              {beachData?.name} - Beach Map
            </p>
            {/* <Link
              href={`/map/${beachData?.nameSlug}/@${beachData?.lat},${beachData?.lon}`}
            >
              <CustomButton type={4}>View Full Map</CustomButton>
            </Link> */}
            <div
              data-testid="beach-map-container"
              className="relative w-full lg:w-3/5"
            >
              <LocationMap
                dataTestid="beach-location-map"
                latStr={beachData.lat}
                longStr={beachData.lon}
                zoom={12}
              />
              <Link
                data-testid="beach-map-link"
                href={`/map/${beachData?.nameSlug}/@${beachData?.lat},${beachData?.lon}`}
                className="absolute bottom-10 right-10"
              >
                <ZoomInIcon data-testid="beach-zoom-icon" />
              </Link>
            </div>
          </div>
        </div>
        {/* <div className={`${beachData?.droneVideo && beachData?.beachCamLink ? "md:flex w-full md:space-x-4" : ""}`}>
          <div className={`${beachData?.droneVideo && beachData?.beachCamLink ? "md:w-1/2" : ""}`}>
            {beachData?.beachCamLink ? (
              <div className=" lg:bg-[#FAFAFA] lg:pt-8 lg:pb-3 md:px-4 my-5 rounded-sandee lg:min-h-[400px]" id={"beachCams"}>
                <div className="relative flex flex-col justify-center items-center">
                  <div className=" w-full lg:w-3/4 flex lg:flex-row flex-col gap-5 items-center justify-between">
                    <p className="text-red-500 bg-white rounded-full px-4 py-2 flex items-center justify-center gap-2 lg:text-xl text-sm shadow-md">
                      <span className="aspect-square lg:w-4 lg:h-4 w-2 h-2 rounded-full bg-red-500 "></span>
                      LIVE
                    </p>
                    <p className="font-semibold text-center lg:text-sandee-24 text-lg">
                      {`${beachData?.name ?? "Beach"} - Live Beach Cam`}
                    </p>
                   
                  </div>
                  <div className="w-full lg:w-3/5 md:flex items-center justify-center my-5 lg:px-5">
                    {beachData?.beachCamLink && (
                      <div className=" w-full  lg:min-h-[20%]  flex flex-col  rounded-sandee bg-gray-200 shadow-lg aspect-auto md:aspect-video">
                        <iframe
                          src={`${getEmbedUrl(beachData?.beachCamLink)}`}
                          // src={`${beachData?.beachCamLink}${
                          //   beachData?.beachCamLink?.includes("?")
                          //     ? ""
                          //     : "?autoplay=1&mute=1"
                          // }`}
                          allowFullScreen
                          loading="lazy"
                          className=" bg-gray-200 w-full h-full rounded-sandee min-h-[230px]"
                        ></iframe>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ) : (
              ""
            )}
          </div>
          <div className={`${beachData?.beachCamLink && beachData?.droneVideo ? "md:w-1/2" : ""}`}>
            {beachData?.droneVideo && (
              <div className=" lg:bg-[#FAFAFA] lg:pt-8 pb-3 md:px-4 my-5 rounded-sandee lg:min-h-[400px]">
                <div className="relative pt-12 md:pt-0 flex flex-col justify-center items-center">
                  <p className="font-semibold leading-[44px]  text-center lg:text-sandee-24 text-lg">
                    {beachData?.name} - Beach Video
                  </p>

                  <div className=" lg:min-h-[20%]  aspect-video flex flex-col  rounded-sandee bg-gray-200 shadow-lg mt-5 w-full lg:w-3/5">
                    <iframe
                      src={`${getEmbedUrl(beachData?.droneVideo)}`}
                      allowFullScreen
                      loading="lazy"
                      className=" bg-gray-200 h-full rounded-sandee  lg:aspect-video "
                    ></iframe>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div> */}
        <BeachCamsSection dataTestid="beach-cams-section" beachData={beachData} />

        {/* {beachData?.droneVideo && (
          <div className=" lg:bg-[#FAFAFA] lg:pt-8 pb-8 md:px-4 my-5 rounded-sandee lg:min-h-[400px]">
            <div className="relative flex flex-col justify-center items-center">
              <p className="font-semibold leading-[44px] text-center lg:text-sandee-24 text-lg">
                {beachData?.name} - Beach Video
              </p>

              <div className="  max-h-[380px] aspect-video flex flex-col  rounded-sandee bg-gray-200 shadow-lg mt-5 w-full lg:w-3/5">
                <iframe
                  src={`${getEmbedUrl(beachData?.droneVideo)}`}
                  allowFullScreen
                  loading="lazy"
                  className=" bg-gray-200 h-full rounded-sandee  lg:aspect-video "
                ></iframe>
              </div>
            </div>
          </div>
        )} */}
        {/* </div> */}
        <BeachFAQ dataTestid="beach-faq-section" beachData={beachData} extraFaq={faqsData ? faqsData?.rows?.map((el, index) => {
          return {
            id: index + 11,
            question: el?.question,
            answer: el?.answer
          }
        }) : []} />
      </CustomContainer>
      {/* <BeachCamsSection beachData={beachData} />
      <BeachLocationBig beachData={beachData} /> */}

      <CustomContainer dataTestid="beach-nearby-container">
        <Beach_NearBy_Section data-testid="beach-nearby-section" data={NearBeach} beachData={beachData} />
      </CustomContainer>
      <Home_Listcle_Section
        dataTestid="beach-listicle-section"
        data={typeof ListicleResponse === "object" ? ListicleResponse?.rows : ListicleResponse}
        className="mb-5 "
        buttonType={{ type: 5, color: "fill-sandee-orange" }}
      />
      <CustomContainer dataTestid="beach-blog-container">
        {blogsData?.rows?.length ? (
          <BeachBlog_Related_Bottom_Section
            dataTestid="beach-blog-section"
            data={blogsData?.rows}
          />
        ) : (
          ""
        )}
        <ReviewSection
          dataTestid="beach-review-section"
          reviewAnalysis={reviewAnalysis}
          params={params}
          beachData={beachData}
        />
        <Beach_Bottom_Button
          dataTestid="beach-bottom-button"
          beachData={beachData}
        />
        {/* <BeachBottomReviewSection beachData={beachData} /> */}
        {/* <BeachReviewBottomDisplay beachData={beachData} /> */}
      </CustomContainer>
    </>
  );
  // } catch (error) {
  //   logController(error);
  //   redirect("/");
  //   // return <div>Somthing Went Wrong for this PAge - MG</div>;
  // }
};

export default page;

export async function generateMetadata({ params }) {
  const ParamData = Object.values(params);
  const { data: MetaData } = await BeachgetSingleBeachSEO(ParamData.join("/"));
  const SEOData = MetaData;
  return {
    title:
      !!SEOData?.title ? SEOData?.title :
        `Explore Beaches of ${slugConverter(params?.nameSlug, true)} in ${slugConverter(
          params?.countrySlug,
          true
        )} - Sandee`,
    // title:
    //   !!SEOData?.title ? SEOData?.title :
    //     `Explore Beaches of ${slugConverter(params?.nameSlug, true)} in  ${slugConverter(
    //       params?.citySlug,
    //       true
    //     )}, ${slugConverter(params?.stateSlug, true)}, ${slugConverter(
    //       params?.countrySlug,
    //       true
    //     )} - Sandee `,
    description:
      !!SEOData?.description ? SEOData?.description :
        `Learn about and discover ${slugConverter(
          params?.nameSlug,
          true
        )}, ${slugConverter(params?.citySlug, true)}, ${slugConverter(
          params?.stateSlug,
          true
        )}, ${slugConverter(
          params?.countrySlug,
          true
        )}. - we have over 100 categories of data including activities, parking, photos, attractions, hotels, and restaurants.`,
    alternates: {
      canonical: `${WEBSITE_URL}/${params?.countrySlug}/${params?.stateSlug}/${params?.citySlug}/${params?.nameSlug}`,
    },
  };
}

export const getExtraFaqs = (async (data) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/faqs/?AllBeachId=${data?.AllBeachId}`);
    return response?.data;
  } catch (error) {
    console.log(error, 'error')
    return [];
  }
})

export const getReviewAnalysis = async (query, params) => {
  // console.log(`${API_BASE_URL}beachReview/beaches/santa-monica-beach${buildQueryString(
  //     query
  // )}`)
  // //beachReview/getBeachReviewAnalytics/india/gujarat/dwarka/dwarka-beach
  const APIShakiestCountries = `${API_BASE_URL}/beachReview/getBeachReviewAnalytics/${params?.countrySlug}/${params?.stateSlug}/${params?.citySlug}/${params?.nameSlug}${buildQueryString(
    query
  )}`;
  const response = await axios.get(APIShakiestCountries);
  // console.log(response?.data)
  return response?.data;
};

export const getTestBlog = withErrorHandling(async () => {
  const response = await axios.get(`${API_BASE_URL}/blog?page=1&limit=9`);
  return response?.data;
});

export const BeachgetListiclesBeachWise = withErrorHandling(async (data) => {
  const response = await axios.get(
    `${API_BASE_URL}/listiclesMain/getByTag?stateSlug=${data?.stateSlug}&countrySlug=${data?.countrySlug}&tag=State&cityId=null&limit=1`
  );
  return response?.data;
});

export const BeachgetSingleBeach = withErrorHandling(async (data) => {
  const response = await axios.get(
    API_BASE_URL + `/beachMain/getOneBeachForUser/${data}`
  );
  return response?.data;
});

export const BeachgetSingleBeachSEO = withErrorHandling(async (data) => {
  const response = await axios.get(API_BASE_URL + `/beachSEO/${data}`);
  return response?.data;
});

export const PostRedirectBeachUrl = withErrorHandling(async (data) => {
  const response = await axios.post(`${API_BASE_URL}/beachesUrls/check`, {
    url: `/${data?.countrySlug}/${data?.stateSlug}/${data?.citySlug}/${data?.nameSlug}`,
  });

  return response?.data;
});

export const BeachgetWeather = withErrorHandling(async (lat, lon) => {
  const response = await axios.get(
    `https://api.openweathermap.org/data/2.5/forecast?lat=${lat}&lon=${lon}&appid=39af32fe7a7da8528d6c444ca1f7afb9`
    // `https://api.openweathermap.org/data/2.5/forecast?lat=${lat}&lon=${lon}&appid=d57f8c3baf6bb12c1c6f23e9e1315929` //working
    // `https://api.openweathermap.org/data/2.5/weather?lat=${lat}&lon=${lon}&appid=d57f8c3baf6bb12c1c6f23e9e1315929`
    // `https://api.openweathermap.org/data/2.5/weather?lat=${lat}&lon=${lon}&appid=d57f8c3baf6bb12c1c6f23e9e1315929&units=Metric`
  );
  // logController(
  //   `https://api.openweathermap.org/data/2.5/forecast?lat=${lat}&lon=${lon}&appid=d57f8c3baf6bb12c1c6f23e9e1315929`
  // );
  // return response?.data;
  const data = response?.data;
  // if (!data || !data?.list) {
  //   return null;
  // }

  // const today = new Date().toISOString().split('T')[0]; // Get today's date in 'YYYY-MM-DD' format
  // const todayWeather = data?.list.filter(item => item?.dt_txt?.startsWith(today));

  return data;
});

export const BeachgetNearByBeach = withErrorHandling(
  async (res, page = 1, limit = 10) => {
    const response = await axios.get(
      `${API_BASE_URL}/beachMain/getNearByBeach?limit=${limit}&page=${page}&coordinates=${res?.lon},${res?.lat}`
    );
    return response?.data;
  }
);

export const BeachgetMultipleImage = withErrorHandling(async (data) => {
  // logController(
  //   `${API_BASE_URL}/image/getByBeach/${data}\n`,
  //   `${API_BASE_URL}/beachMain/getMultipleImages/${data}`
  // );
  const response = await axios.get(
    `${API_BASE_URL}/image/getByBeach/${data}`
    // `${API_BASE_URL}/beachMain/getMultipleImages/${data}`
  );
  return response?.data;
});

// export const generateStaticParams = async () => {
//   // const response = await axios.get(`${API_BASE_URL}/beachMain/getAllBeaches`);
//   return []
//   // response?.data?.rows?.map((el) => {
//   //   return {
//   //     countrySlug: el?.countrySlug,
//   //     stateSlug: el?.stateSlug,
//   //     citySlug: el?.citySlug,
//   //     nameSlug: el?.nameSlug,
//   //   }
//   // })
// }

// export const revalidate = 43200;

// export const dynamicParams = false;
// EnvTrueFalse?.[process.env.NEXT_PUBLIC_DYNAMIC_PARAMS] ?? true;

