import City_Hero_Section from "@/components/CityPage/CityHeroSection";
import Country_Blog_Section from "@/components/CountryPage/Country_BlogSection";
import { CustomContainer } from "@/components/Custom-Display";
import Home_Listcle_Section from "@/components/HomePage/HomeListcle";
import {
  API_BASE_URL,
  WEBSITE_URL,
  buildQueryString,
  slugConverter,
} from "@/helper/functions";
import axios from "axios";
import React from "react";
import { withErrorHandling } from "@/app/(HeaderSlim)/(Single Country Page)/[countrySlug]/action";
import AllBeachSectionGlobalComponent from "@/components/Common/AllBeachSection";
import CustomButton from "@/components/Custom-Button";
import { redirect } from "next/navigation";
import City_Bottom_Button from "@/components/CityPage/CityBottomButton";
import CityPageJSONLD from "@/components/CityPage/CityPageJSONLD";

const page = async (props) => {
  const { params } = props;
  const [{ data: cityHeaderImageResponse }, listicleResponse] =
    await Promise.all([
      CitygetcityHeaderImage({
        ...params,
      }),
      CitygetListiclesCityWise(params),
    ]);

  if (!cityHeaderImageResponse?.id) {
    console.log(params, "City is not available");
    return redirect("/");
  }
  // const listicleResponse = await CitygetListiclesCityWise(params);

  const [
    AllBeachesResponse,
    //  {data: CityAnalyticsData},
    { data: Blogs },
  ] = await Promise.all([
    CitygetAllBeaches({
      limit: 20,
      page: 1,
      cityId: cityHeaderImageResponse?.id,
    }),
    // CityAnalytics({
    //   cityId: cityHeaderImageResponse?.id,
    // }),
    CitygetBlogCountryWise({
      // beachTags: "nude",
      tags: cityHeaderImageResponse?.name,
      limit: 12,
      page: 1,
    }),
  ]);
  // const { data: CityAnalyticsData } = await CityAnalytics({
  //   cityId: cityHeaderImageResponse?.id,
  // });
  // const AllBeachesResponse = await CitygetAllBeaches({
  //   limit: 20,
  //   page: 1,
  //   cityId: cityHeaderImageResponse?.id,
  // });
  // const { data: Blogs } = await CitygetBlogCountryWise({
  //   // beachTags: "nude",
  //   tags: cityHeaderImageResponse?.name,
  //   limit: 12,
  //   page: 1,
  // });
  const CityAllBeachesInitial = AllBeachesResponse?.data?.rows;
  const FullCount = {
    limit: 20,
    page: 1,
    count: AllBeachesResponse?.data?.count,
    totalpages: Math.ceil(AllBeachesResponse?.data?.count / 20),
    query: {
      cityId: cityHeaderImageResponse?.id,
      limit: 20,
    },
  };
  const breadCumberData = [
    {
      title: "All Countries",
      to: `/countries`,
    },
    {
      title: cityHeaderImageResponse?.country?.name,
      to: `/${cityHeaderImageResponse?.country?.slug}`,
    },
    {
      title: cityHeaderImageResponse?.state?.name,
      to: `/${cityHeaderImageResponse?.country?.slug}/${cityHeaderImageResponse?.state?.slug}`,
    },
    {
      title: cityHeaderImageResponse?.name,
    },
  ];
  return (
    <>
      <CityPageJSONLD params={params} cityData={cityHeaderImageResponse} />
      <City_Hero_Section
        // CityAnalyticsData={CityAnalyticsData}
        data={cityHeaderImageResponse}
        breadCumberData={breadCumberData}
      />
      <Home_Listcle_Section
        data={listicleResponse?.data}
        className="!my-0"
      // ExtraButton={<CustomButton type={5}>View All</CustomButton>}
      />
      <CustomContainer>
        {CityAllBeachesInitial?.length ? (
          <AllBeachSectionGlobalComponent
            data={cityHeaderImageResponse}
            FullCount={FullCount}
            AllBeachesInitial={CityAllBeachesInitial}
          />
        ) : (
          ""
        )}
        {Blogs?.rows?.length ? <Country_Blog_Section data={Blogs?.rows} /> : ""}
        <City_Bottom_Button cityHeaderImageResponse={cityHeaderImageResponse} />
      </CustomContainer>
    </>
  );
};
export default page;
export async function generateMetadata({ params, searchParams }, parent) {
  const [{ data: cityHeaderImageResponse }] =
    await Promise.all([
      CitygetcityHeaderImage({
        ...params,
      }),
    ]);

  return {
    title: !!cityHeaderImageResponse?.title ? cityHeaderImageResponse?.title : `Explore Beaches of ${slugConverter(
      params?.citySlug,
      true
    )}, ${slugConverter(
      params?.countrySlug,
      true
    )} `,
    // title: !!cityHeaderImageResponse?.title ? cityHeaderImageResponse?.title : `Explore Beaches of ${slugConverter(
    //   params?.citySlug,
    //   true
    // )}, ${slugConverter(params?.stateSlug, true)}, ${slugConverter(
    //   params?.countrySlug,
    //   true
    // )} `,
    description: !!cityHeaderImageResponse?.metaDescription ? cityHeaderImageResponse?.metaDescription : `Discover the complete list of beaches in the ${slugConverter(
      params?.citySlug,
      true
    )}, ${slugConverter(params?.stateSlug, true)}, ${slugConverter(
      params?.countrySlug,
      true
    )}. Plan your ${slugConverter(
      params?.citySlug,
      true
    )} beach vacation with 100+ activities, photos, attractions, restaurants, and hotels.`,
    alternates: {
      canonical: `${WEBSITE_URL}/${params?.countrySlug}/${params?.stateSlug}/${params?.citySlug}`,
    },
  };
}

export const CitygetListiclesCityWise = withErrorHandling(async (query) => {
  const APIListicles = `${API_BASE_URL}/listiclesMain/getListicle${buildQueryString(
    query
  )}`;
  const response = await axios.get(APIListicles);

  return response?.data;
});

export const CitygetcityHeaderImage = withErrorHandling(async (data) => {
  const response = await axios.get(
    `${API_BASE_URL}/cities/getBySlug${buildQueryString(data)}`
  );
  // logController(`${API_BASE_URL}/cities/getBySlug${buildQueryString(data)}`);
  return response?.data;
});

export const CityAnalytics = withErrorHandling(async (query) => {
  const APICityAnalytics = `${API_BASE_URL}/beachMain/overview/getOverviewAnalytics${buildQueryString(
    query
  )}`;
  // logController(APICityAnalytics);
  const response = await axios.get(APICityAnalytics);
  return response?.data;
});

export const CitygetAllBeaches = withErrorHandling(async (query) => {
  const APIStateAllBeaches = `${API_BASE_URL}/beachMain/getAllForUsers${buildQueryString(
    query
  )}`;
  const response = await axios.get(APIStateAllBeaches);
  return response?.data;
});

export const CitygetBlogCountryWise = withErrorHandling(async (query) => {
  const APIBlogCountry = `${API_BASE_URL}/blog${buildQueryString(query)}`;
  const response = await axios.get(APIBlogCountry);

  return response?.data;
});
