"use server";

import {
  API_BASE_URL,
  EnvTrueFalse,
  buildQueryString,
} from "@/helper/functions";
import axios from "axios";

export const withErrorHandling =
  (fn) =>
    async (...args) => {
      try {
        return await fn(...args);
      } catch (error) {
        // if (
        //   process?.env?.NEXT_PUBLIC_ERROR_LOG &&
        //   EnvTrueFalse?.[process.env.NEXT_PUBLIC_ERROR_LOG]
        // )
        // logController(
        //   "++++++++++++++++++++++++++==Full Error==+++++++++++++++++++++++++++++++++++\n",
        //   error?.config
        // );
        // logController(
        //   "++++++++++++++++++++++++++==Error URL==+++++++++++++++++++++++++++++++++++\n",
        //   error?.config?.url,
        //   "\n==========================================================================="
        // );

        // throw error;
        return { data: null, error: true };
      }
    };

export const getAllBeachesForUserViaQuery = withErrorHandling(async (query) => {
  const APICountryAllBeaches = `${API_BASE_URL}/beachMain/getAllForUsers${buildQueryString(
    query
  )}`;
  // logController(APICountryAllBeaches);
  const response = await axios.get(APICountryAllBeaches);
  return response?.data;
});
export const getAllReviewsForUserViaQuery = withErrorHandling(async (query) => {
  const APIGetAllReviews = `${API_BASE_URL}/reviews/getAllForUsers${buildQueryString(
    query
  )}`;

  const response = await axios.get(APIGetAllReviews);
  return response?.data;
});

// export const CountrygetAllBeaches = async (query) => {
//   try {
//     const APICountryAllBeaches = `${API_BASE_URL}/beachMain/getAllForUsers${buildQueryString(
//       query
//     )}`;
//     // logController(APICountryAllBeaches);
//     const response = await axios.get(APICountryAllBeaches);
//     return response?.data;
//   } catch (error) {
//     logController(error, "endpoint");
//   }
// };

// export const StategetAllBeaches = async (query) => {
//   try {
//     const APIStateAllBeaches = `${API_BASE_URL}/beachMain/getAllForUsers${buildQueryString(
//       query
//     )}`;
//     // logController(APIStateAllBeaches);
//     const response = await axios.get(APIStateAllBeaches);
//     return response?.data;
//   } catch (error) {
//     logController(error, "endpoint");
//   }
// };

export const getTestBlog = async (query) => {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/blog${buildQueryString(query)}`
    );

    return response?.data;
  } catch (error) {
    return {
      data: [],
    };
    // logController(error);
  }
};

export const getTestNews = async (query) => {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/news${buildQueryString(query)}`
    );

    return response?.data;
  } catch (error) {
    return {
      data: [],
    };
    // logController(error);
  }
};

export const BeachgetNearByBeach = withErrorHandling(
  async (res, page = 1, limit = 10) => {
    const response = await axios.get(
      `${API_BASE_URL}/beachMain/getNearByBeach?limit=${limit}&page=${page}&coordinates=${res?.lon},${res?.lat}`
    );
    return response?.data;
  }
);

export const BeachgetNearByBeachWithoutPage = withErrorHandling(
  async (res) => {
    const response = await axios.get(
      `${API_BASE_URL}/beachMain/getNearByBeach?coordinates=${res?.log},${res?.lat}`
    );
    return response?.data;
  }
);