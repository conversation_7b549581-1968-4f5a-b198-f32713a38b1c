"use server";

import {
  API_BASE_URL,
  EnvTrueFalse,
  buildQueryString,
} from "@/helper/functions";
import axios from "axios";

// Helper function for error handling (not exported as it's not a server action)
const handleError = (error) => {
  // if (
  //   process?.env?.NEXT_PUBLIC_ERROR_LOG &&
  //   EnvTrueFalse?.[process.env.NEXT_PUBLIC_ERROR_LOG]
  // )
  // logController(
  //   "++++++++++++++++++++++++++==Full Error==+++++++++++++++++++++++++++++++++++\n",
  //   error?.config
  // );
  // logController(
  //   "++++++++++++++++++++++++++==Error URL==+++++++++++++++++++++++++++++++++++\n",
  //   error?.config?.url,
  //   "\n==========================================================================="
  // );

  // throw error;
  return { data: null, error: true };
};

export async function getAllBeachesForUserViaQuery(query) {
  try {
    const APICountryAllBeaches = `${API_BASE_URL}/beachMain/getAllForUsers${buildQueryString(
      query
    )}`;
    // logController(APICountryAllBeaches);
    const response = await axios.get(APICountryAllBeaches);
    return response?.data;
  } catch (error) {
    return handleError(error);
  }
}

export async function getAllReviewsForUserViaQuery(query) {
  try {
    const APIGetAllReviews = `${API_BASE_URL}/reviews/getAllForUsers${buildQueryString(
      query
    )}`;

    const response = await axios.get(APIGetAllReviews);
    return response?.data;
  } catch (error) {
    return handleError(error);
  }
}

// export const CountrygetAllBeaches = async (query) => {
//   try {
//     const APICountryAllBeaches = `${API_BASE_URL}/beachMain/getAllForUsers${buildQueryString(
//       query
//     )}`;
//     // logController(APICountryAllBeaches);
//     const response = await axios.get(APICountryAllBeaches);
//     return response?.data;
//   } catch (error) {
//     logController(error, "endpoint");
//   }
// };

// export const StategetAllBeaches = async (query) => {
//   try {
//     const APIStateAllBeaches = `${API_BASE_URL}/beachMain/getAllForUsers${buildQueryString(
//       query
//     )}`;
//     // logController(APIStateAllBeaches);
//     const response = await axios.get(APIStateAllBeaches);
//     return response?.data;
//   } catch (error) {
//     logController(error, "endpoint");
//   }
// };

export const getTestBlog = async (query) => {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/blog${buildQueryString(query)}`
    );

    return response?.data;
  } catch (error) {
    return {
      data: [],
    };
    // logController(error);
  }
};

export const getTestNews = async (query) => {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/news${buildQueryString(query)}`
    );

    return response?.data;
  } catch (error) {
    return {
      data: [],
    };
    // logController(error);
  }
};

export async function BeachgetNearByBeach(res, page = 1, limit = 10) {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/beachMain/getNearByBeach?limit=${limit}&page=${page}&coordinates=${res?.lon},${res?.lat}`
    );
    return response?.data;
  } catch (error) {
    return handleError(error);
  }
}

export async function BeachgetNearByBeachWithoutPage(res) {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/beachMain/getNearByBeach?coordinates=${res?.log},${res?.lat}`
    );
    return response?.data;
  } catch (error) {
    return handleError(error);
  }
}