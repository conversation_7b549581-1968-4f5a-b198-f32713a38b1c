import CountryBlogSectionSkeleton from "@/components/CountryPage/Skeleton/CountryBlogSectionSkeleton";
import CountryHeroSectionSkeleton from "@/components/CountryPage/Skeleton/CountryHeroSectionSkeleton";
import CountryIslandSectionSkeleton from "@/components/CountryPage/Skeleton/CountryIslandSectionSkeleton";
import { CustomContainer } from "@/components/Custom-Display";
import React from "react";

const loading = () => {
  return (
    <>
      <CountryHeroSectionSkeleton />
      {Array(4)
        .fill(1)
        .map((el, i) => {
          return (
            <div className="listicle-skeleton my-8 " key={i}>
              <div className="heading">
                <div className="animate-pulse h-5 bg-slate-200 my-4"></div>
                <div className="animate-pulse h-5 bg-slate-200 my-4"></div>
              </div>
              <div className=" my-8 h-[220px] bg-slate-200 animate-pulse "></div>
            </div>
          );
        })}
      <CustomContainer>
        <CountryIslandSectionSkeleton />
        <CountryIslandSectionSkeleton />
        <CountryBlogSectionSkeleton />
      </CustomContainer>
      <div className=" h-14 bg-slate-200 animate-pulse my-8"></div>
    </>
  );
};

export default loading;
