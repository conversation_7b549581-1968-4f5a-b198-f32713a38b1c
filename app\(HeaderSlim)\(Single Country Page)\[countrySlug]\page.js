import Country_Hero_Section from "@/components/CountryPage/CountryHeroSection";
import Country_Island_Section from "@/components/CountryPage/CountryIslandSection";

import Country_Blog_Section from "@/components/CountryPage/Country_BlogSection";
import { CustomContainer } from "@/components/Custom-Display";
import Home_Listcle_Section from "@/components/HomePage/HomeListcle";
import {
  API_BASE_URL,
  FinalImageGenerator,
  WEBSITE_URL,
  apiGenerator,
  buildQueryString,
  slugConverter,
} from "@/helper/functions";
import axios from "axios";
import { redirect } from "next/navigation";
import React from "react";
import { withErrorHandling } from "@/app/(HeaderSlim)/(Single Country Page)/[countrySlug]/action";
import AllBeachSectionGlobalComponent from "@/components/Common/AllBeachSection";
import CustomButton from "@/components/Custom-Button";
import Country_Bottom_Button from "@/components/CountryPage/CountryBottomButton";
import CountryPageJSONLD from "@/components/CountryPage/CountryPageJSONLD";
import Country_Region_Section from "@/components/CountryPage/CountryRegionSection";

const page = async (props) => {
  const { params } = props;
  const [
    topStateData,
    { data: countryHeaderImageResponse },
    listicleResponse,
    islandResponse,
  ] = await Promise.all([
    CountrygetTopStates(params),
    CountrygetcountryHeaderImage(params?.countrySlug),
    CountrygetListiclesCountryWise(params),
    CountrygetAllIceland(params?.countrySlug, {
      page: 1,
      limit: 20,
    }),
  ]);

  if (!countryHeaderImageResponse?.id) {
    console.log(params, "Country Params");
    return redirect("/");
  }

  // const { data: topStateData } = await CountrygetTopStates(params);
  // if (!topStateData?.length) {
  //   redirect("/");
  // }

  // const { data: countryHeaderImageResponse } =
  //   await CountrygetcountryHeaderImage(params?.countrySlug);
  // const listicleResponse = await CountrygetListiclesCountryWise(params);
  // const { data: islandResponse } = await CountrygetAllIceland(
  //   params?.countrySlug,
  //   {
  //     page: 1,
  //     limit: 20,
  //   }
  // );

  const topStateResponse = topStateData?.data?.map((el) => ({
    ...el,
    images: [el?.image],
    copyRightsData: [el?.image],
    image: FinalImageGenerator(el?.image),
    name: el?.name,
    link: `/${params?.countrySlug}/${el?.slug}`,
  }));

  const FilteredIsland = islandResponse?.data?.filter((el) => el?.slug);

  const [
    AllBeachesResponse,
    // {data: CountryAnalyticsData},
    { data: Blogs },
  ] = await Promise.all([
    CountrygetAllBeaches({
      limit: 20,
      page: 1,
      countryId: countryHeaderImageResponse?.id,
    }),
    // CountryAnalytics({
    //   countryId: countryHeaderImageResponse?.id,
    // }),
    CountrygetBlogCountryWise({
      // beachTags: "nude",
      tags: countryHeaderImageResponse?.name,
      limit: 12,
      page: 1,
    }),
  ]);
  // const AllBeachesResponse = await CountrygetAllBeaches({
  //   limit: 20,
  //   page: 1,
  //   countryId: countryHeaderImageResponse?.id,
  // });
  // const { data: CountryAnalyticsData } = await CountryAnalytics({
  //   countryId: countryHeaderImageResponse?.id,
  // });
  // // const { data: blogData } = await HomegetAllBlog();
  // const { data: Blogs } = await CountrygetBlogCountryWise({
  //   // beachTags: "nude",
  //   tags: countryHeaderImageResponse?.name,
  //   limit: 12,
  //   page: 1,
  // });
  const CountryAllBeachesInitial = AllBeachesResponse?.data?.rows;
  const FullCount = {
    limit: 20,
    page: 1,
    count: AllBeachesResponse?.data?.count,
    totalpages: Math.ceil(AllBeachesResponse?.data?.count / 20),
    query: {
      countryId: countryHeaderImageResponse?.id,
      limit: 20,
    },
  };
  return (
    <>
      <CountryPageJSONLD countryData={countryHeaderImageResponse} />
      <Country_Hero_Section
        data={countryHeaderImageResponse}
        // CountryAnalyticsData={CountryAnalyticsData}
        Topstates={topStateResponse}
      />

      <CustomContainer>
        {topStateResponse?.length ? (
          <Country_Region_Section
            data={countryHeaderImageResponse}
            {...props}
            topStateData={topStateResponse || []}
            totalRecords={topStateData?.totalRecords}
          />
        ) : (
          ""
        )}
        {FilteredIsland?.length ? (
          <Country_Island_Section
            data={countryHeaderImageResponse}
            {...props}
            islandData={FilteredIsland || []}
            totalRecords={islandResponse?.totalRecords}
          />
        ) : (
          ""
        )}
      </CustomContainer>
      <Home_Listcle_Section
        data={listicleResponse?.data}
        className="!my-0"
      // ExtraButton={<CustomButton type={4}>View All</CustomButton>}
      />
      <CustomContainer>
        {CountryAllBeachesInitial?.length ? (
          <AllBeachSectionGlobalComponent
            data={countryHeaderImageResponse}
            FullCount={FullCount}
            AllBeachesInitial={CountryAllBeachesInitial}
          />
        ) : (
          ""
        )}

        {Blogs?.rows?.length ? <Country_Blog_Section data={Blogs?.rows} /> : ""}
        <Country_Bottom_Button />
      </CustomContainer>
    </>
  );
};

export default page;
export async function generateMetadata({ params }) {
  const { data: MetaData } = await CountrygetcountryHeaderImage(
    params?.countrySlug
  );
  return {
    title:
      !!MetaData?.title ? MetaData?.title :
        `Explore Beaches of ${slugConverter(params?.countrySlug, true)} - Sandee `,
    description:
      !!MetaData?.metaDescription ? MetaData?.metaDescription :
        `Discover the complete list of beaches in the ${slugConverter(
          params?.countrySlug,
          true
        )}. Plan your ${slugConverter(
          params?.countrySlug,
          true
        )} beach vacation with 100+ activities, photos, attractions, restaurants, and hotels.`,
    alternates: {
      canonical: `${WEBSITE_URL}/${params?.countrySlug}`,
    },
  };
}

export const CountrygetTopStates = withErrorHandling(
  async (data = {}, query) => {
    const APITopState = apiGenerator(
      `${API_BASE_URL}/beachMain/getTopStateForUser/:countrySlug${buildQueryString(
        query
      )}`,
      data
    );
    const response = await axios.get(APITopState);
    return response?.data;
  }
);

export const CountrygetcountryHeaderImage = withErrorHandling(async (data) => {
  const response = await axios.get(
    `${API_BASE_URL}/countries/getBySlug/${data}`
  );
  return response?.data;
});

export const CountrygetListiclesCountryWise = withErrorHandling(
  async (query) => {
    const APIListicles = `${API_BASE_URL}/listiclesMain/getListicle${buildQueryString(
      query
    )}`;
    const response = await axios.get(APIListicles);

    return response?.data;
  }
);

export const CountrygetAllIceland = withErrorHandling(async (data, query) => {
  const response = await axios.get(
    `${API_BASE_URL}/beachMain/getTopIslands/${data}${buildQueryString(query)}`
  );

  return response?.data;
});

// all are working with id below

export const CountrygetAllBeaches = withErrorHandling(async (query) => {
  const APICountryAllBeaches = `${API_BASE_URL}/beachMain/getAllForUsers${buildQueryString(
    query
  )}`;
  try {
    const response = await axios.get(APICountryAllBeaches);
    return response?.data;
  } catch (error) {
    console.log(error, "error");
  }

});

export const CountryAnalytics = withErrorHandling(async (query) => {
  const APICountryAnalytics = `${API_BASE_URL}/beachMain/overview/getOverviewAnalytics${buildQueryString(
    query
  )}`;
  const response = await axios.get(APICountryAnalytics);
  return response?.data;
});

export const CountrygetBlogCountryWise = withErrorHandling(async (query) => {
  const APIBlogCountry = `${API_BASE_URL}/blog${buildQueryString(query)}`;
  const response = await axios.get(APIBlogCountry);
  return response?.data;
});
