import Country_Blog_Section from "@/components/CountryPage/Country_BlogSection";
import { CustomContainer } from "@/components/Custom-Display";
import Home_Listcle_Section from "@/components/HomePage/HomeListcle";
import State_Hero_Section from "@/components/StatePage/StateHeroSection";
import State_City_Section from "@/components/StatePage/StateTopCitySection";
import {
  API_BASE_URL,
  FinalImageGenerator,
  WEBSITE_URL,
  apiGenerator,
  buildQueryString,
  slugConverter,
} from "@/helper/functions";
import axios from "axios";
import { redirect } from "next/navigation";
import React from "react";
import { withErrorHandling } from "@/app/(HeaderSlim)/(Single Country Page)/[countrySlug]/action";
import AllBeachSectionGlobalComponent from "@/components/Common/AllBeachSection";
import State_Bottom_Button from "@/components/StatePage/StateBottomButton";
import StatePageJSONLD from "@/components/StatePage/StatePageJSONLD";

const page = async (props) => {
  const { params } = props;
  const [
    topCityDataResponse,
    { data: stateHeaderImageResponse },
    listicleResponse,
  ] = await Promise.all([
    StategetTopCities(params),
    StategetstateHeaderImage({
      ...params,
    }),
    StategetListiclesCountryWise(params),
  ]);
  // const { data: topCityDataResponse } = await StategetTopCities(params);
  if (!stateHeaderImageResponse?.id) {
    console.log(params, "State Params");
    // return redirect("/");
  }
  console.log(stateHeaderImageResponse)
  // if (!topCityDataResponse?.length) {
  //   return redirect("/");
  // }

  // const { data: stateHeaderImageResponse } = await StategetstateHeaderImage({
  //   ...params,
  // });
  const topCityData = topCityDataResponse?.data?.map((el) => ({
    ...el,
    // image: el?.image?.imageUrl,
    images: [el?.image],
    copyRightsData: [el?.image],
    // image: FinalImageGenerator(el?.image),
    title: el?.name,
    link: `${params?.countrySlug}/${params?.stateSlug}/${el?.slug}`,
  }));
  // const listicleResponse = await StategetListiclesCountryWise(params);

  // logController(stateHeaderImageResponse);
  const [
    AllBeachesResponse,
    // {data: StateAnalyticsData},
    { data: Blogs },
  ] = await Promise.all([
    StategetAllBeaches({
      limit: 20,
      page: 1,
      stateId: stateHeaderImageResponse?.id,
    }),
    // StateAnalytics({
    //   stateId: stateHeaderImageResponse?.id,
    // }),
    StategetBlogCountryWise({
      tags: stateHeaderImageResponse?.name,
      limit: 12,
      page: 1,
    }),
  ]);
  // const { data: StateAnalyticsData } = await StateAnalytics({
  //   stateId: stateHeaderImageResponse?.id,
  // });
  // const AllBeachesResponse = await StategetAllBeaches({
  //   limit: 20,
  //   page: 1,
  //   stateId: stateHeaderImageResponse?.id,
  // });
  // const { data: Blogs } = await StategetBlogCountryWise({
  //   tags: stateHeaderImageResponse?.name,
  //   limit: 12,
  //   page: 1,
  // });

  const StateAllBeachesInitial = AllBeachesResponse?.data?.rows;
  const FullCount = {
    limit: 20,
    page: 1,
    count: AllBeachesResponse?.data?.count,
    totalpages: Math.ceil(AllBeachesResponse?.data?.count / 20),
    query: {
      stateId: stateHeaderImageResponse?.id,
      limit: 20,
    },
  };

  const breadCumberData = [
    {
      title: "All Countries",
      to: `/countries`,
    },
    {
      title: stateHeaderImageResponse?.country?.name,
      to: `/${stateHeaderImageResponse?.country?.slug}`,
    },
    {
      title: stateHeaderImageResponse?.name,
    },
  ];

  return (
    <>
      <StatePageJSONLD params={params} StateData={stateHeaderImageResponse} />
      <State_Hero_Section
        data={stateHeaderImageResponse}
        breadCumberData={breadCumberData}
      // StateAnalyticsData={StateAnalyticsData}
      // Topstates={topCityData}
      />
      <CustomContainer>
        {topCityData?.length ? (
          <State_City_Section
            data={stateHeaderImageResponse}
            {...props}
            topCityData={topCityData || []}
            totalRecords={topCityDataResponse?.totalRecords}
          />
        ) : null}
      </CustomContainer>

      <Home_Listcle_Section
        data={listicleResponse?.data}
        className="!my-0"
      // ExtraButton={<CustomButton type={5}>View All</CustomButton>}
      />
      <CustomContainer>
        {StateAllBeachesInitial?.length ? (
          <AllBeachSectionGlobalComponent
            data={stateHeaderImageResponse}
            FullCount={FullCount}
            AllBeachesInitial={StateAllBeachesInitial}
          />
        ) : (
          ""
        )}

        {Blogs?.rows?.length ? <Country_Blog_Section data={Blogs?.rows} /> : ""}
        <State_Bottom_Button
          stateHeaderImageResponse={stateHeaderImageResponse}
        />
      </CustomContainer>
    </>
  );
};

export default page;
export async function generateMetadata({ params }) {
  const [
    { data: stateHeaderImageResponse },
  ] = await Promise.all([
    StategetstateHeaderImage({
      ...params,
    }),
  ]);
  return {
    title: !!stateHeaderImageResponse?.title ? stateHeaderImageResponse?.title : `Explore Beaches of ${slugConverter(
      params?.stateSlug,
      true
    )} in ${slugConverter(params?.countrySlug, true)} - Sandee `,

    description: !!stateHeaderImageResponse?.metaDescription ? stateHeaderImageResponse?.metaDescription : `Discover the complete list of beaches in the ${slugConverter(
      params?.stateSlug,
      true
    )},  ${slugConverter(params?.countrySlug, true)}. Plan your ${slugConverter(
      params?.stateSlug,
      true
    )} beach vacation with 100+ activities, photos, attractions, restaurants, and hotels.`,
    alternates: {
      canonical: `${WEBSITE_URL}/${params?.countrySlug}/${params?.stateSlug}`,
    },
  };
}

export const StategetAllBeaches = withErrorHandling(async (query) => {
  const APIStateAllBeaches = `${API_BASE_URL}/beachMain/getAllForUsers${buildQueryString(
    query
  )}`;
  const response = await axios.get(APIStateAllBeaches);
  return response?.data;
});

export const StategetTopCities = withErrorHandling(async (data = {}, query) => {
  const APITopCity = apiGenerator(
    `${API_BASE_URL}/beachMain/getTopCityForUser/:countrySlug/:stateSlug${buildQueryString(query)}`,
    data
  );
  const response = await axios.get(APITopCity);
  return response?.data;
});

export const StategetListiclesCountryWise = withErrorHandling(async (query) => {
  const APIListicles = `${API_BASE_URL}/listiclesMain/getListicle${buildQueryString(
    query
  )}`;
  const response = await axios.get(APIListicles);

  return response?.data;
});

export const StategetstateHeaderImage = withErrorHandling(async (data) => {
  const response = await axios.get(
    `${API_BASE_URL}/states/getBySlug${buildQueryString(data)}`
  );
  // logController(`${API_BASE_URL}/states/getBySlug${buildQueryString(data)}`);
  return response?.data;
});

export const StateAnalytics = withErrorHandling(async (query) => {
  const APIStateAnalytics = `${API_BASE_URL}/beachMain/overview/getOverviewAnalytics${buildQueryString(
    query
  )}`;
  const response = await axios.get(APIStateAnalytics);
  return response?.data;
});

export const StategetBlogCountryWise = withErrorHandling(async (query) => {
  const APIBlogCountry = `${API_BASE_URL}/blog${buildQueryString(query)}`;

  const response = await axios.get(APIBlogCountry);

  return response?.data;
});
