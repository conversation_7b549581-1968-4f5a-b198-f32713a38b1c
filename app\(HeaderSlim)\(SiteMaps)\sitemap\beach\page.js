import BreadCumber from "@/components/Common/BreadCumber";
import Pagination from "@/components/Common/Pagination";
import { CustomContainer, CustomGrid } from "@/components/Custom-Display";
import SiteHeader from "@/components/SitemapCompo/SiteHeader";
import sitemapDataForHTML from "@/data/SitemapGenrationData";
import { API_BASE_URL } from "@/helper/functions";
import axios from "axios";
import Link from "next/link";
import React from "react";

const { limit, totalPages } = sitemapDataForHTML.beach;
const CityHTMLSitemap = async () => {
  const {
    data: { data: beachesData },
  } = await axios.get(
    `${API_BASE_URL}/sitemap/getAllBeach?limit=${limit}&page=1`
  );

  return (
    <>
      <div className="  w-full pt-10 pb-5 flex items-center bg-">
        <CustomContainer>
          <BreadCumber
            data={[
              {
                title: "All Sitemaps",
                to: "/sitemap",
              },
              {
                title: "All Beaches",
              },
            ]}
          />
        </CustomContainer>
      </div>
      <SiteHeader />

      <CustomContainer>
        <CustomGrid
          className="mt-5 mb-10 !gap-1"
          data={beachesData}
          Component={({ data, index }) => {
            return (
              <Link
                // href={`${data?.path}`}
                key={`${data?.countrySlug}-${data?.stateSlug}-${data?.citySlug}-${data?.nameSlug}-${index}`}
                href={`/${data?.countrySlug}/${data?.stateSlug}/${data?.citySlug}/${data?.nameSlug}`}
                className="sitemap-links"
              >
                {data?.name} in {data?.city}, {data?.state}, {data?.country}
              </Link>
            );
          }}
          xs={1}
          sm={2}
          md={3}
          lg={3}
          xl={3}
        />
        <Pagination
          className="pagination-bar"
          currentPage={1}
          // siblingCount={1}
          totalCount={totalPages * limit}
          pageSize={limit}
          isLink={true}
          baseLink={"/sitemap/beach/"}
        />
      </CustomContainer>
    </>
  );
};

export default CityHTMLSitemap;
