import BreadCumber from "@/components/Common/BreadCumber";
import Pagination from "@/components/Common/Pagination";
import { CustomContainer, CustomGrid } from "@/components/Custom-Display";
import SiteHeader from "@/components/SitemapCompo/SiteHeader";
import sitemapDataForHTML from "@/data/SitemapGenrationData";
import { API_BASE_URL } from "@/helper/functions";
import axios from "axios";
import Link from "next/link";
import React from "react";

const { limit, totalPages } = sitemapDataForHTML.city;
const CityHTMLSitemap = async ({ params }) => {
  const start =
    params?.cityPage && !isNaN(params?.cityPage) ? +params?.cityPage ?? 1 : 1;
  const {
    data: { data: citiesData },
  } = await axios.get(
    `${API_BASE_URL}/sitemap/getAllCity?limit=${limit}&page=${start}`
  );

  // const miniData = citiesData?.slice(+start * limit, (+start + 1) * limit);
  return (
    <>
      <div className="  w-full pt-10 pb-5 flex items-center bg-">
        <CustomContainer>
          <BreadCumber
            data={[
              {
                title: "All Sitemaps",
                to: "/sitemap",
              },
              {
                title: "All Cities",
              },
            ]}
          />
        </CustomContainer>
      </div>
      <SiteHeader />
      <CustomContainer>
        <CustomGrid
          className="mt-5 mb-10 !gap-1"
          data={citiesData}
          Component={({ data, index }) => {
            return (
              <Link
                // href={`${data?.path}`}
                key={`${data?.countrySlug}-${data?.stateSlug}-${data?.citySlug}-${index}`}
                href={`/${data?.countrySlug}/${data?.stateSlug}/${data?.citySlug}`}
                className="sitemap-links"
              >
                All Beaches in {data?.city}, {data?.state}, {data?.country}
              </Link>
            );
          }}
          xs={1}
          sm={2}
          md={3}
          lg={3}
          xl={3}
        />
        <Pagination
          className="pagination-bar"
          currentPage={+params?.cityPage}
          totalCount={totalPages * limit}
          pageSize={limit}
          isLink={true}
          // siblingCount={1}
          baseLink={"/sitemap/city/"}
        />
      </CustomContainer>
    </>
  );
};

export default CityHTMLSitemap;
export const revalidate = 216000;

export const dynamicParams = true;
export async function generateStaticParams() {
  // return [];
  const { totalPages } = sitemapDataForHTML.city;
  const posts = Array(totalPages)
    .fill(0)
    ?.map((el, index) => index + 1);

  return posts.map((post) => ({
    cityPage: `${post}`,
  }));
}
