import BreadCumber from "@/components/Common/BreadCumber";
import Pagination from "@/components/Common/Pagination";
import { CustomContainer, CustomGrid } from "@/components/Custom-Display";
import SiteHeader from "@/components/SitemapCompo/SiteHeader";
import sitemapDataForHTML from "@/data/SitemapGenrationData";
import { API_BASE_URL } from "@/helper/functions";
import axios from "axios";
import Link from "next/link";
import React from "react";

const { limit, totalPages } = sitemapDataForHTML.city;
const CityHTMLSitemap = async () => {
  const {
    data: { data: citiesData },
  } = await axios.get(
    `${API_BASE_URL}/sitemap/getAllCity?limit=${limit}&page=1`
  );
  // const miniData = citiesData?.slice(+start * limit, (+start + 1) * limit);

  // const groupedCities = miniData.reduce((acc, city) => {
  //   const firstLetter = city.city.charAt(0).toUpperCase();
  //   if (!acc[firstLetter]) {
  //     acc[firstLetter] = [];
  //   }
  //   acc[firstLetter].push(city);
  //   return acc;
  // }, {});

  // const alphabetKeys = Object.keys(groupedCities).sort();
  return (
    <>
      <div className="  w-full pt-10 pb-5 flex items-center bg-">
        <CustomContainer>
          <BreadCumber
            data={[
              {
                title: "All Sitemaps",
                to: "/sitemap",
              },
              {
                title: "All Cities",
              },
            ]}
          />
        </CustomContainer>
      </div>
      <SiteHeader />

      <CustomContainer>
        <CustomGrid
          className="mt-5 mb-10 !gap-1"
          data={citiesData}
          // data={citiesData?.slice(+start * limit, (+start + 1) * limit)}
          Component={({ data, index }) => {
            return (
              <Link
                // href={`${data?.path}`}
                key={`${data?.countrySlug}-${data?.stateSlug}-${data?.citySlug}-${index}`}
                href={`/${data?.countrySlug}/${data?.stateSlug}/${data?.citySlug}`}
                className="sitemap-links"
              >
                All Beaches in {data?.city}, {data?.state}, {data?.country}
              </Link>
            );
          }}
          xs={1}
          sm={2}
          md={3}
          lg={3}
          xl={3}
        />
        {/* <div className={` flex  items-center flex-wrap gap-3 justify-center`}>
          <Link href={`/sitemap/city/2`} className={`flex justify-center `}>
            <CustomButton type={1}>Next</CustomButton>
          </Link>
        </div> */}
        <Pagination
          className="pagination-bar"
          currentPage={1}
          totalCount={totalPages * limit}
          pageSize={limit}
          isLink={true}
          // siblingCount={1}
          baseLink={"/sitemap/city/"}
        />
      </CustomContainer>
    </>
  );
};

export default CityHTMLSitemap;
