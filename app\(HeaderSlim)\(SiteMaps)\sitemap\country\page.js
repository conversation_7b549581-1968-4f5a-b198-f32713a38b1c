import BreadCumber from "@/components/Common/BreadCumber";
import { CustomContainer, CustomGrid } from "@/components/Custom-Display";
import SiteHeader from "@/components/SitemapCompo/SiteHeader";
import { API_BASE_URL } from "@/helper/functions";
import axios from "axios";
import Link from "next/link";
import React from "react";

const CountryHTMLSitemap = async () => {
  const {
    data: { data: countryData },
  } = await axios.get(`${API_BASE_URL}/sitemap/getAllCountry`);

  // const groupedCountries = countryData.reduce((acc, country) => {
  //   const firstLetter = country?.country?.charAt(0)?.toUpperCase();
  //   if (!acc[firstLetter]) {
  //     acc[firstLetter] = [];
  //   }
  //   acc[firstLetter]?.push(country);
  //   return acc;
  // }, {});

  // const alphabetKeys = Object.keys(groupedCountries).sort();
  return (
    <>
      <div className="  w-full pt-10 pb-5 flex items-center bg-">
        <CustomContainer>
          <BreadCumber
            data={[
              {
                title: "All Sitemaps",
                to: "/sitemap",
              },
              {
                title: "All Countries",
              },
            ]}
          />
        </CustomContainer>
      </div>
      <SiteHeader />
      <CustomContainer>
        <CustomGrid
          className=" mt-5 mb-10 !gap-1"
          data={countryData}
          Component={({ data, index }) => {
            return (
              <Link
                // href={`${data?.path}`}
                key={`${data?.countrySlug}-${index}`}
                href={`/${data?.countrySlug}/`}
                className="sitemap-links"
              >
                All States in {data?.country}
              </Link>
            );
          }}
          xs={1}
          sm={2}
          md={3}
          lg={3}
          xl={3}
        />
      </CustomContainer>
    </>
  );
};

export default CountryHTMLSitemap;
