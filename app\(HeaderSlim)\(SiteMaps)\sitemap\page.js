import BreadCumber from "@/components/Common/BreadCumber";
import NameTitle from "@/components/Common/NameTitle";
import CustomButton from "@/components/Custom-Button";
import { CustomContainer, CustomGrid } from "@/components/Custom-Display";
import SiteHeader from "@/components/SitemapCompo/SiteHeader";
import { ExploreMoreArrow } from "@/components/social-icons/icons";
import { SiteDataPageWise } from "@/data/siteMetadata";
import { API_BASE_URL } from "@/helper/functions";
import axios from "axios";
import Link from "next/link";
import React from "react";

const page = async () => {
  // const AllHTMLSitemaps = [
  //   { label: "Country", path: "/sitemap/country" },
  //   { label: "State", path: "/sitemap/state" },
  //   { label: "City", path: "/sitemap/city" },
  //   { label: "Beach 1", path: "/sitemap/beach/1" },
  //   { label: "Beach 2", path: "/sitemap/beach/2" },
  //   { label: "Beach 3", path: "/sitemap/beach/3" },
  //   { label: "Beach 4", path: "/sitemap/beach/4" },
  //   { label: "Beach 5", path: "/sitemap/beach/5" },
  // ];
  const {
    data: { data: citiesData },
  } = await axios.get(`${API_BASE_URL}/sitemap/getAllCity?page=1&limit=30`);
  const {
    data: { data: countryData },
  } = await axios.get(`${API_BASE_URL}/sitemap/getAllCountry?page=1&limit=30`);
  const {
    data: { data: statesData },
  } = await axios.get(`${API_BASE_URL}/sitemap/getAllState?page=1&limit=30`);
  const {
    data: { data: beachesData },
  } = await axios.get(`${API_BASE_URL}/sitemap/getAllBeach?page=1&limit=30`);

  return (
    <>
        <h1 className="hidden">Sitemaps</h1>
      <div className="  w-full pt-10 pb-5 flex items-center bg-">
        <CustomContainer>
          <BreadCumber
            data={[
              {
                title: "All Sitemaps",
              },
            ]}
          />
        </CustomContainer>
      </div>
      <SiteHeader />
      <CustomContainer>
        <NameTitle
          className="my-5"
          name="Best Countries"
          extraButton={
            <div className=" hidden md:flex justify-end items-start w-3/12">
              <Link href={`/sitemap/country`} className=" !text-nowrap">
                <CustomButton type={4}>
                  All Countries Sitemap
                  <ExploreMoreArrow className=" ml-2 fill-sandee-orange group-hover:fill-white  h-4 w-4" />
                </CustomButton>
              </Link>
            </div>
          }
        />
        <CustomGrid
          className=" mb-10 !gap-1"
          data={countryData}
          Component={({ data, index }) => {
            return (
              <Link
                // href={`${data?.path}`}
                key={`${data?.countrySlug}-${index}`}
                href={`/${data?.countrySlug}/`}
                className="sitemap-links"
              >
                All States in {data?.country}
              </Link>
            );
          }}
          xs={1}
          sm={2}
          md={3}
          lg={3}
          xl={3}
        />

        <NameTitle
          className="my-5"
          name="Best States"
          extraButton={
            <div className=" hidden md:flex justify-end items-start w-3/12">
              <Link href={`/sitemap/state`} className=" !text-nowrap">
                <CustomButton type={4}>
                  All States Sitemap
                  <ExploreMoreArrow className=" ml-2 fill-sandee-orange group-hover:fill-white  h-4 w-4" />
                </CustomButton>
              </Link>
            </div>
          }
        />

        <CustomGrid
          className=" mb-10 !gap-1"
          data={statesData}
          Component={({ data, index }) => {
            return (
              <Link
                // href={`${data?.path}`}
                key={`${data?.countrySlug}-${data?.stateSlug}-${index}`}
                href={`/${data?.countrySlug}/${data?.stateSlug}/`}
                className="sitemap-links"
              >
                All Cities in {data.state}, {data.country}
              </Link>
            );
          }}
          xs={1}
          sm={2}
          md={3}
          lg={3}
          xl={3}
        />

        <NameTitle
          className="my-5"
          name="Best Cities"
          extraButton={
            <div className=" hidden md:flex justify-end items-start w-3/12">
              <Link href={`/sitemap/city`} className=" !text-nowrap">
                <CustomButton type={4}>
                  All Cities Sitemap
                  <ExploreMoreArrow className=" ml-2 fill-sandee-orange group-hover:fill-white  h-4 w-4" />
                </CustomButton>
              </Link>
            </div>
          }
        />
        <CustomGrid
          className=" mb-10 !gap-1"
          data={citiesData}
          Component={({ data, index }) => {
            return (
              <Link
                // href={`${data?.path}`}
                key={`${data?.countrySlug}-${data?.stateSlug}-${data?.citySlug}-${index}`}
                href={`/${data?.countrySlug}/${data?.stateSlug}/${data?.citySlug}`}
                className="sitemap-links"
              >
                All Beaches in {data?.city}, {data?.state}, {data?.country}
              </Link>
            );
          }}
          xs={1}
          sm={2}
          md={3}
          lg={3}
          xl={3}
        />

        <NameTitle
          className="my-5"
          name="Best Beaches"
          extraButton={
            <div className=" hidden md:flex justify-end items-start w-3/12">
              <Link href={`/sitemap/beach`} className=" !text-nowrap">
                <CustomButton type={4}>
                  All Beaches Sitemap
                  <ExploreMoreArrow className=" ml-2 fill-sandee-orange group-hover:fill-white  h-4 w-4" />
                </CustomButton>
              </Link>
            </div>
          }
        />
        <CustomGrid
          className="mb-10 !gap-1"
          data={beachesData}
          Component={({ data, index }) => {
            return (
              <Link
                // href={`${data?.path}`}
                key={`${data?.countrySlug}-${data?.stateSlug}-${data?.citySlug}-${data?.nameSlug}-${index}`}
                href={`/${data?.countrySlug}/${data?.stateSlug}/${data?.citySlug}/${data?.nameSlug}`}
                className="sitemap-links"
              >
                {data?.name} in {data?.city}, {data?.state}, {data?.country}
              </Link>
            );
          }}
          xs={1}
          sm={2}
          md={3}
          lg={3}
          xl={3}
        />
      </CustomContainer>
    </>
  );
};

export default page;
export function generateMetadata() {
  return SiteDataPageWise.sitemaps;
}