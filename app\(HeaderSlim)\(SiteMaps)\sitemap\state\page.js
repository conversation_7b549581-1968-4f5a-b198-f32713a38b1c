import BreadCumber from "@/components/Common/BreadCumber";
import { CustomContainer, CustomGrid } from "@/components/Custom-Display";
import SiteHeader from "@/components/SitemapCompo/SiteHeader";
import { API_BASE_URL } from "@/helper/functions";
import axios from "axios";
import Link from "next/link";
import React from "react";

const StateHTMLSitemap = async () => {
  const {
    data: { data: statesData },
  } = await axios.get(`${API_BASE_URL}/sitemap/getAllState`);

  // const groupedStates = statesData.reduce((acc, state) => {
  //   const firstLetter = state.state.charAt(0).toUpperCase();
  //   if (!acc[firstLetter]) {
  //     acc[firstLetter] = [];
  //   }
  //   acc[firstLetter].push(state);
  //   return acc;
  // }, {});

  // const alphabetKeys = Object.keys(groupedStates).sort();

  return (
    <>
      <div className="  w-full pt-10 pb-5 flex items-center bg-">
        <CustomContainer>
          <BreadCumber
            data={[
              {
                title: "All Sitemaps",
                to: "/sitemap",
              },
              {
                title: "All States",
              },
            ]}
          />
        </CustomContainer>
      </div>
      <SiteHeader />
      <CustomContainer>
        <CustomGrid
          className=" mt-5 mb-10 !gap-1"
          data={statesData}
          Component={({ data, index }) => {
            return (
              <Link
                // href={`${data?.path}`}
                key={`${data?.countrySlug}-${data?.stateSlug}-${index}`}
                href={`/${data?.countrySlug}/${data?.stateSlug}/`}
                className="sitemap-links"
              >
                All Cities in {data.state}, {data.country}
              </Link>
            );
          }}
          xs={1}
          sm={2}
          md={3}
          lg={3}
          xl={3}
        />
      </CustomContainer>
    </>
  );
};

export default StateHTMLSitemap;
