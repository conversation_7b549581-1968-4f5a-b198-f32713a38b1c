import NameTitle from "@/components/Common/NameTitle";
import CustomButton from "@/components/Custom-Button";
import { CustomContainer, CustomGrid } from "@/components/Custom-Display";
import AboutUsJSONLd from "@/components/about/AboutUsJSONLd";
import FilterPage_Hero_Section from "@/components/filterPage/FilterHeroSection";
import { SiteDataPageWise } from "@/data/siteMetadata";
import staticPageData from "@/data/staticPageData";
import { CDNImageUrl, blurDataURL } from "@/helper/functions";
import Image from "next/image";
import Link from "next/link";
import React from "react";

const page = () => {
  return (
    <>
      <AboutUsJSONLd />
      <FilterPage_Hero_Section
        data={{
          name: "About US",
          image: CDNImageUrl("images/header/AboutUsHeader.avif"),
        }}
      />
      <CustomContainer>
        <NameTitle name=" Mission Statement" className="my-5" />
        <p className=" text-sandee-base my-5">
          To give the billion beachgoers around the world the most detailed,
          comprehensive, and easy-to-use information that will help them plan
          beach vacations, visits to beaches once they arrive in a new location,
          and visits to local beaches - and make all of their visits to the
          beach happier and more enjoyable by allowing them to choose their
          perfect beach.
        </p>
        <NameTitle className="my-5" name="Why Sandee?" />
        <CustomGrid
          data={staticPageData?.ABOUTUS}
          className="gap-4 sm:gap-8 !my-5"
          Component={({ data: dataProps, index }) => {
            // dataProps.link = `/blog/${dataProps?.slug}`; //
            // dataProps.imageSrc = dataProps?.imgSrc;
            return PressCard({
              data: { ...dataProps, name: dataProps?.title },
              index,
            });
          }}
          xs={1}
          sm={2}
          // md={2}
          lg={4}
          // xl={3}
        />
        <NameTitle
          className="mt-10 mb-5"
          name="Sandee Team"
          description="Randall Kaplan - CEO and CBO (Chief Beach Officer)"
          extraButton={
            <div className=" hidden md:flex justify-end items-start w-3/12">
              <Link
                href={`https://www.randallkaplan.com/biography`}
                className=" !text-nowrap"
              >
                <CustomButton type={4}>Learn More</CustomButton>
              </Link>
            </div>
          }
        />
        <div className="flex flex-col md:flex-row gap-5 my-5">
          <div className=" w-full md:w-4/12 lg:3/12 flex items-center justify-center">
            <div className=" min-h-[300px] max-h-[450px] min-w-[40px]  h-full w-full rounded-sandee">
              <div className="min-h-[300px] max-h-[450px] h-full w-full relative  overflow-hidden  rounded-sandee ">
                <Image
                  // priority
                  className=" h-full w-full transition-transform duration-1000 ease-in-out rounded-sandee object-center transform group-hover:scale-125 object-contain"
                  src={`/static/images/Kaplan-Photo.avif`}
                  alt={`Sandee - Founder Randall Kaplan | CEO | CBO`}
                  fill
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  blurDataURL={blurDataURL(900, 600)}
                  placeholder="blur"
                />
              </div>
            </div>
          </div>
          <div className=" w-full md:8/12 lg:w-9/12 px-5 flex flex-col gap-3">
            <p className=" text-sandee-18 leading-snug ">
              Randall is a serial entrepreneur and venture capitalist. Randall
              is a co-founder of Akamai Technologies, a content delivery and
              security software company that serves nearly 30% of the
              world&apos;s web traffic, is a member of the S&P 500, employs
              nearly 8,000 people with 64 offices in 28 countries, and had $3.45
              billion in 2021 revenues.
            </p>
            <p className=" text-sandee-18 leading-snug ">
              Randall is also the founder and CEO of Sandee, a travel company
              focused on the promotion of beaches and beach tourism around the
              world whose mission is to create the world&apos;s first beach
              brand; the co-founder and Co-CEO of Thrive Properties, a real
              estate investment firm specializing in the ownership, management,
              leasing, and redevelopment of properties in primary markets
              throughout the United States; and the owner and CEO of CollarCard,
              LLC, a promotional products company that makes patented men&apos;s
              collar stays that are sold throughout the world.
            </p>
            <p className=" text-sandee-18 leading-snug ">
              Randall is also the founder and CEO of JUMP Investors which
              functions as his family office. Since its formation in 1999, JUMP
              has invested in more than 80 early-to-late- stage technology
              companies including Google, Seagate, Lyft, and Chime. JUMP is also
              an active investor in real estate, private equity, hedge funds,
              and public equities.
            </p>
            <p className=" text-sandee-18 leading-snug ">
              Over the past 20 years, Randall has been an advisor to more than
              50 companies, has served on the board of directors of many
              companies, has been an active public speaker, and has mentored
              more than 200 students through JUMP&apos;s annual summer
              internship program.
            </p>
          </div>
        </div>
      </CustomContainer>
    </>
  );
};
//  link = "https://www.randallkaplan.com/biography";
export default page;

export function generateMetadata() {
  return SiteDataPageWise.aboutUS;
}
export const PressCard = ({ data, index }) => {
  return (
    <div
      className={`relative flex flex-col group  rounded-sandee shadow-[0_3px_10px_rgb(0,0,0,0.2)] ${
        index % 2 == 0
          ? "bg-white text-sandee-blue"
          : "bg-sandee-blue text-white"
      }`}
    >
      <div className="flex items-center justify-center my-3 ">{data?.icon}</div>
      <div className=" p-4 px-2 rounded-sandee flex gap-2 justify-center items-center flex-col">
        <p className=" text-sandee-32 font-bold text-center leading-snug">
          {data?.count}
        </p>
        <p className=" text-sandee-24 font-bold text-center  leading-snug">
          {data?.title}
        </p>
      </div>
    </div>
  );
};
