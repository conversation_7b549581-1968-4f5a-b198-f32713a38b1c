import BreadCumberJSONLD from "@/components/BreadCumberJSONLD";
import ContactForm from "@/components/ContactUs/contactForm";
import { CustomContainer } from "@/components/Custom-Display";
import FilterPage_Hero_Section from "@/components/filterPage/FilterHeroSection";

import { SiteDataPageWise } from "@/data/siteMetadata";
import { CDNImageUrl } from "@/helper/functions";
import React from "react";

const page = () => {
  return (
    <>
      <BreadCumberJSONLD name={"Contact Us"} slug={`contact-us`}
        title={SiteDataPageWise?.contactUS?.title} description={SiteDataPageWise?.contactUS?.description}
      />
      <FilterPage_Hero_Section
        data={{
          name: "Contact Us",
          image: CDNImageUrl("images/header/ContactUsHeader.avif"),
        }}
      />
      <CustomContainer>
        <div className=" flex flex-col md:flex-row gap-5 my-5">
          <div className="w-full md:w-1/2 md:px-10">
            <div className="custom-auth ">
              <ContactForm />
            </div>
          </div>
          <div className=" md:w-1/2 flex items-center justify-center">
            {/* <iframe
              width="100%"
              height="70%"
              src="https://www.youtube.com/embed/terrOcNf-UM?autoplay=1&mute=1"
              frameborder="0"
              allowFullScreen
            ></iframe> */}

            <iframe
              className="w-full h-full md:h-4/6 min-h-[300px]"
              // src="https://www.youtube.com/embed/terrOcNf-UM"
              src="https://www.youtube.com/embed/terrOcNf-UM?autoplay=1&mute=1"
              allowFullScreen
            />

            {/* <video loop autoPlay width="100%" height="70%">
              <source
                controls
                muted
                autoPlay={"autoplay"}
                preLoad="auto"
                loop
                // src="https://www.youtube.com/embed/terrOcNf-UM"
                src="https://www.youtube.com/embed/terrOcNf-UM?autoplay=1&mute=1"
                allowFullScreen
              />
            </video> */}
          </div>
        </div>
      </CustomContainer>
    </>
  );
};

export default page;

export function generateMetadata() {
  return SiteDataPageWise.contactUS;
}
