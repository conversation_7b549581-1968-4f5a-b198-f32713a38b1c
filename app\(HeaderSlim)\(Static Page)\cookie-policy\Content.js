import React from "react";

const ContentCookiePolicy = () => {
  return (
    <div className="pmargin0 mt-8 mb-10">
      <ol style={{ marginBottom: 0, marginTop: 0, paddingInlineStart: 48 }}>
        <li
          style={{
            backgroundColor: "transparent",
            color: "#000000",

            fontSize: "12pt",
            fontStyle: "normal",
            fontVariant: "normal",
            listStyleType: "decimal",
            textDecoration: "none",
            verticalAlign: "baseline",
            whiteSpace: "pre",
          }}
          dir="ltr"
          aria-level={1}
        >
          <h3
            style={{
              backgroundColor: "#ffffff",
              lineHeight: "1.2",
              marginBottom: "6pt",
              marginTop: "0pt",
            }}
            id="e3702d42fcbb6638407614628172eb8a7"
            dir="ltr"
            role="presentation"
          >
            <span
              style={{
                backgroundColor: "transparent",
                color: "#000000",

                fontSize: "12pt",
              }}
            >
              <span
                style={{
                  fontStyle: "normal",
                  fontVariant: "normal",
                  textDecoration: "none",
                  verticalAlign: "baseline",
                  whiteSpace: "pre-wrap",
                }}
              >
                <strong>DOES SANDEE USE COOKIES?</strong>
              </span>
            </span>
          </h3>
          <ol style={{ marginBottom: 0, marginTop: 0, paddingInlineStart: 48 }}>
            <li
              style={{
                backgroundColor: "transparent",
                color: "#000000",

                fontSize: "12pt",
                fontStyle: "normal",
                fontVariant: "normal",
                fontWeight: 400,
                listStyleType: "lower-alpha",
                textDecoration: "none",
                verticalAlign: "baseline",
                whiteSpace: "pre",
              }}
              dir="ltr"
              aria-level={2}
            >
              <h3
                style={{
                  backgroundColor: "#ffffff",
                  lineHeight: "1.2",
                  marginBottom: "6pt",
                  marginTop: "0pt",
                }}
                id="efe3be4b2c2f690f76d74d4738b4ccbc1"
                dir="ltr"
                role="presentation"
              >
                <span
                  style={{
                    backgroundColor: "transparent",
                    color: "#000000",

                    fontSize: "12pt",
                  }}
                >
                  <span
                    style={{
                      fontStyle: "normal",
                      fontVariant: "normal",
                      fontWeight: 400,
                      textDecoration: "none",
                      verticalAlign: "baseline",
                      whiteSpace: "pre-wrap",
                    }}
                  >
                    Yes.&nbsp; We use cookies and other similar technologies to
                    ensure every person, beachgoer, and beachlover who uses our
                    website has the best possible experience and allows them to
                    choose their perfect beach.
                  </span>
                </span>
              </h3>
            </li>
          </ol>
        </li>
        <li
          style={{
            backgroundColor: "transparent",
            color: "#000000",

            fontSize: "12pt",
            fontStyle: "normal",
            fontVariant: "normal",
            listStyleType: "decimal",
            textDecoration: "none",
            verticalAlign: "baseline",
            whiteSpace: "pre",
          }}
          dir="ltr"
          aria-level={1}
        >
          <h3
            style={{
              backgroundColor: "#ffffff",
              lineHeight: "1.2",
              marginBottom: "6pt",
              marginTop: "0pt",
            }}
            id="e76be2883777cb62acdf15b4fd445b78f"
            dir="ltr"
            role="presentation"
          >
            <span
              style={{
                backgroundColor: "transparent",
                color: "#000000",

                fontSize: "12pt",
              }}
            >
              <span
                style={{
                  fontStyle: "normal",
                  fontVariant: "normal",
                  textDecoration: "none",
                  verticalAlign: "baseline",
                  whiteSpace: "pre-wrap",
                }}
              >
                <strong>WHAT IS A COOKIE?</strong>
              </span>
            </span>
          </h3>
          <ol style={{ marginBottom: 0, marginTop: 0, paddingInlineStart: 48 }}>
            <li
              style={{
                backgroundColor: "transparent",
                color: "#000000",

                fontSize: "12pt",
                fontStyle: "normal",
                fontVariant: "normal",
                fontWeight: 400,
                listStyleType: "lower-alpha",
                textDecoration: "none",
                verticalAlign: "baseline",
                whiteSpace: "pre",
              }}
              dir="ltr"
              aria-level={2}
            >
              <span className="ck-list-bogus-paragraph">
                <span
                  style={{
                    backgroundColor: "transparent",
                    color: "#000000",

                    fontSize: "12pt",
                  }}
                >
                  <span
                    style={{
                      fontStyle: "normal",
                      fontVariant: "normal",
                      fontWeight: 400,
                      textDecoration: "none",
                      verticalAlign: "baseline",
                      whiteSpace: "pre-wrap",
                    }}
                  >
                    A cookie is a small text file that is placed on your
                    computer’s hard drive or mobile device by a web page
                    server.&nbsp; Cookies contain information that can later be
                    read by a web server in the domain that issued the cookie to
                    you.&nbsp; We may use both session cookies (which expire
                    once you close your web browser) and persistent cookies
                    (which stay on your computer or mobile device until you
                    delete them) to provide you with a more personal,
                    interactive, and informative experience on our
                    website.&nbsp; You can find out more about each cookie by
                    viewing our current cookie list below.
                  </span>
                </span>
              </span>
            </li>
            <li
              style={{
                backgroundColor: "transparent",
                color: "#000000",

                fontSize: "12pt",
                fontStyle: "normal",
                fontVariant: "normal",
                fontWeight: 400,
                listStyleType: "lower-alpha",
                textDecoration: "none",
                verticalAlign: "baseline",
                whiteSpace: "pre",
              }}
              dir="ltr"
              aria-level={2}
            >
              <span className="ck-list-bogus-paragraph">
                <span
                  style={{
                    backgroundColor: "transparent",
                    color: "#000000",

                    fontSize: "12pt",
                  }}
                >
                  <span
                    style={{
                      fontStyle: "normal",
                      fontVariant: "normal",
                      fontWeight: 400,
                      textDecoration: "none",
                      verticalAlign: "baseline",
                      whiteSpace: "pre-wrap",
                    }}
                  >
                    Web beacons, tags, and scripts may also be used on our
                    website or in emails to us or from us to help us deliver
                    cookies, count website or mobile visits, understand usage
                    and the effectiveness of advertising campaigns, and
                    determine whether an email has been opened and
                    clicked.&nbsp; We may receive reports based on the use of
                    these technologies by our service or analytics providers on
                    both an individual and aggregated basis.
                  </span>
                </span>
              </span>
            </li>
          </ol>
        </li>
        <li
          style={{
            backgroundColor: "transparent",
            color: "#000000",

            fontSize: "12pt",
            fontStyle: "normal",
            fontVariant: "normal",
            listStyleType: "decimal",
            textDecoration: "none",
            verticalAlign: "baseline",
            whiteSpace: "pre",
          }}
          dir="ltr"
          aria-level={1}
        >
          <h3
            style={{
              backgroundColor: "#ffffff",
              lineHeight: "1.2",
              marginBottom: "6pt",
              marginTop: "0pt",
            }}
            id="e36d15856186b95d5c6dd95607ef6368a"
            dir="ltr"
            role="presentation"
          >
            <span
              style={{
                backgroundColor: "transparent",
                color: "#000000",

                fontSize: "12pt",
              }}
            >
              <span
                style={{
                  fontStyle: "normal",
                  fontVariant: "normal",
                  textDecoration: "none",
                  verticalAlign: "baseline",
                  whiteSpace: "pre-wrap",
                }}
              >
                <strong>WHY DO WE USE COOKIES?</strong>
              </span>
            </span>
          </h3>
          <ol style={{ marginBottom: 0, marginTop: 0, paddingInlineStart: 48 }}>
            <li
              style={{
                backgroundColor: "transparent",
                color: "#000000",

                fontSize: "12pt",
                fontStyle: "normal",
                fontVariant: "normal",
                fontWeight: 400,
                listStyleType: "lower-alpha",
                textDecoration: "none",
                verticalAlign: "baseline",
                whiteSpace: "pre",
              }}
              dir="ltr"
              aria-level={2}
            >
              <span className="ck-list-bogus-paragraph">
                <span
                  style={{
                    backgroundColor: "transparent",
                    color: "#000000",

                    fontSize: "12pt",
                  }}
                >
                  <span
                    style={{
                      fontStyle: "normal",
                      fontVariant: "normal",
                      fontWeight: 400,
                      textDecoration: "none",
                      verticalAlign: "baseline",
                      whiteSpace: "pre-wrap",
                    }}
                  >
                    When you visit our website, we may place cookies on your
                    computer or mobile device.&nbsp; These are first-party
                    cookies, and they allow us to hold session information as
                    you navigate within our website.&nbsp; For example, we use
                    cookies on our website to understand visitor and user
                    preferences, improve their experiences, and track and
                    analyze usage, navigational, and other statistical
                    information.&nbsp; You can control the use of cookies at the
                    individual browser level.&nbsp; If you choose not to
                    activate cookies or to disable them later, you can still
                    visit our website although your ability to use some of our
                    awesome features may be limited.&nbsp; We may also use
                    third-party cookies, which are served by our awesome
                    partners or service providers on our website.
                  </span>
                </span>
              </span>
            </li>
          </ol>
        </li>
        <li
          style={{
            backgroundColor: "transparent",
            color: "#000000",

            fontSize: "12pt",
            fontStyle: "normal",
            fontVariant: "normal",
            listStyleType: "decimal",
            textDecoration: "none",
            verticalAlign: "baseline",
            whiteSpace: "pre",
          }}
          dir="ltr"
          aria-level={1}
        >
          <h3
            style={{
              backgroundColor: "#ffffff",
              lineHeight: "1.2",
              marginBottom: "6pt",
              marginTop: "0pt",
            }}
            id="e70fd2493464db7afb7147567694b1f7c"
            dir="ltr"
            role="presentation"
          >
            <span
              style={{
                backgroundColor: "transparent",
                color: "#000000",

                fontSize: "12pt",
              }}
            >
              <span
                style={{
                  fontStyle: "normal",
                  fontVariant: "normal",
                  textDecoration: "none",
                  verticalAlign: "baseline",
                  whiteSpace: "pre-wrap",
                }}
              >
                <strong>HOW DO YOU DISABLE OUR COOKIES?</strong>
              </span>
            </span>
          </h3>
          <ol style={{ marginBottom: 0, marginTop: 0, paddingInlineStart: 48 }}>
            <li
              style={{
                backgroundColor: "transparent",
                color: "#000000",

                fontSize: "12pt",
                fontStyle: "normal",
                fontVariant: "normal",
                fontWeight: 400,
                listStyleType: "lower-alpha",
                textDecoration: "none",
                verticalAlign: "baseline",
                whiteSpace: "pre",
              }}
              dir="ltr"
              aria-level={2}
            >
              <span className="ck-list-bogus-paragraph">
                <span
                  style={{
                    backgroundColor: "transparent",
                    color: "#000000",

                    fontSize: "12pt",
                  }}
                >
                  <span
                    style={{
                      fontStyle: "normal",
                      fontVariant: "normal",
                      fontWeight: 400,
                      textDecoration: "none",
                      verticalAlign: "baseline",
                      whiteSpace: "pre-wrap",
                    }}
                  >
                    You can generally activate or later deactivate the use of
                    cookies through your web browser settings.&nbsp; Many web
                    browsers are set to accept cookies until you change your
                    settings.&nbsp; For your convenience, please find your
                    browser below to learn more about how to manage your cookie
                    settings.
                  </span>
                </span>
              </span>
              <ol
                style={{
                  marginBottom: 0,
                  marginTop: 0,
                  paddingInlineStart: 48,
                }}
              >
                <li
                  style={{
                    backgroundColor: "transparent",
                    color: "#000000",

                    fontSize: "12pt",
                    fontStyle: "normal",
                    fontVariant: "normal",
                    fontWeight: 400,
                    listStyleType: "lower-roman",
                    textDecoration: "none",
                    verticalAlign: "baseline",
                    whiteSpace: "pre",
                  }}
                  dir="ltr"
                  aria-level={3}
                >
                  <span className="ck-list-bogus-paragraph">
                    <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#000000",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        <strong>Android</strong>
                      </span>
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          fontWeight: 400,
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        : Click&nbsp;
                      </span>
                    </span>
                    <a
                      style={{ textDecoration: "none" }}
                      target="_blank"
                      rel="noopener noreferrer"
                      href="https://support.google.com/chrome/answer/95647?co=GENIE.Platform%20%3DAndroid&hl=en"
                    >
                      {/* <span
                          style={{
                            backgroundColor: "transparent",
                            color: "#0000ff",

                            fontSize: "12pt",
                          }}
                        >
                          <span
                            style={{
                              WebkitTextDecorationSkip: "none",
                              fontStyle: "normal",
                              fontVariant: "normal",
                              fontWeight: 400,
                              textDecorationSkipInk: "none",
                              verticalAlign: "baseline",
                              whiteSpace: "pre-wrap",
                            }}
                          > */}
                      <>here </>
                      {/* </span>
                        
                        </span> */}
                    </a>
                    <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#000000",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          fontWeight: 400,
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        to learn more about &quot;Private Browsing&quot; and
                        managing cookie settings.(Please refer to your device’s
                        documentation for manufacturers’ own browsers)
                      </span>
                    </span>
                  </span>
                </li>
                <li
                  style={{
                    backgroundColor: "transparent",
                    color: "#000000",

                    fontSize: "12pt",
                    fontStyle: "normal",
                    fontVariant: "normal",
                    fontWeight: 400,
                    listStyleType: "lower-roman",
                    textDecoration: "none",
                    verticalAlign: "baseline",
                    whiteSpace: "pre",
                  }}
                  dir="ltr"
                  aria-level={3}
                >
                  <span className="ck-list-bogus-paragraph">
                    <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#000000",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        <strong>Chrome</strong>
                      </span>
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          fontWeight: 400,
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        : Click&nbsp;
                      </span>
                    </span>
                    <a
                      style={{ textDecoration: "none" }}
                      target="_blank"
                      rel="noopener noreferrer"
                      href="https://support.google.com/chrome/answer/95647"
                    >
                      {/* <span
                          style={{
                            backgroundColor: "transparent",
                            color: "#0000ff",

                            fontSize: "12pt",
                          }}
                        >
                          <span
                            style={{
                              WebkitTextDecorationSkip: "none",
                              fontStyle: "normal",
                              fontVariant: "normal",
                              fontWeight: 400,
                              textDecorationSkipInk: "none",
                              verticalAlign: "baseline",
                              whiteSpace: "pre-wrap",
                            }}
                          > */}
                      <>here</>
                      {/* </span>
                        </span> */}
                    </a>
                    <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#000000",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          fontWeight: 400,
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        &nbsp;to learn more about &quot;Incognito&quot; and
                        managing cookie settings.
                      </span>
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        <strong>&nbsp;</strong>
                      </span>
                    </span>
                  </span>
                </li>
                <li
                  style={{
                    backgroundColor: "transparent",
                    color: "#000000",

                    fontSize: "12pt",
                    fontStyle: "normal",
                    fontVariant: "normal",
                    fontWeight: 400,
                    listStyleType: "lower-roman",
                    textDecoration: "none",
                    verticalAlign: "baseline",
                    whiteSpace: "pre",
                  }}
                  dir="ltr"
                  aria-level={3}
                >
                  <span className="ck-list-bogus-paragraph">
                    <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#000000",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        <strong>Edge</strong>
                      </span>
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          fontWeight: 400,
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        : Click&nbsp;
                      </span>
                    </span>
                    <a
                      style={{ textDecoration: "none" }}
                      target="_blank"
                      rel="noopener noreferrer"
                      href="https://support.microsoft.com/engb/products/microsoft-edge"
                    >
                      {/* <span
                          style={{
                            backgroundColor: "transparent",
                            color: "#0000ff",

                            fontSize: "12pt",
                          }}
                        >
                          <span
                            style={{
                              WebkitTextDecorationSkip: "none",
                              fontStyle: "normal",
                              fontVariant: "normal",
                              fontWeight: 400,
                              textDecorationSkipInk: "none",
                              verticalAlign: "baseline",
                              whiteSpace: "pre-wrap",
                            }}
                          > */}
                      <>here</>
                      {/* </span>
                          <span
                            style={{
                              fontStyle: "normal",
                              fontVariant: "normal",
                              fontWeight: 400,
                              textDecoration: "none",
                              verticalAlign: "baseline",
                              whiteSpace: "pre-wrap",
                            }}
                          >
                            &nbsp;
                          </span>
                        </span> */}
                    </a>
                    <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#000000",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          fontWeight: 400,
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        to learn more about &quot;Private Browsing&quot; and
                        managing cookie settings. (Please note that there are no
                        specific instructions at this time, but Microsoft
                        support will be able to assist)
                      </span>
                    </span>
                  </span>
                </li>
                <li
                  style={{
                    backgroundColor: "transparent",
                    color: "#000000",

                    fontSize: "12pt",
                    fontStyle: "normal",
                    fontVariant: "normal",
                    fontWeight: 400,
                    listStyleType: "lower-roman",
                    textDecoration: "none",
                    verticalAlign: "baseline",
                    whiteSpace: "pre",
                  }}
                  dir="ltr"
                  aria-level={3}
                >
                  <span className="ck-list-bogus-paragraph">
                    <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#000000",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        <strong>Explorer</strong>
                      </span>
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          fontWeight: 400,
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        : Click&nbsp;
                      </span>
                    </span>
                    <a
                      style={{ textDecoration: "none" }}
                      target="_blank"
                      rel="noopener noreferrer"
                      href="https://support.microsoft.com/enus/kb/278835"
                    >
                      {/* <span
                          style={{
                            backgroundColor: "transparent",
                            color: "#0000ff",

                            fontSize: "12pt",
                          }}
                        >
                          <span
                            style={{
                              WebkitTextDecorationSkip: "none",
                              fontStyle: "normal",
                              fontVariant: "normal",
                              fontWeight: 400,
                              textDecorationSkipInk: "none",
                              verticalAlign: "baseline",
                              whiteSpace: "pre-wrap",
                            }}
                          > */}
                      <>here</>
                      {/* </span>
                          <span
                            style={{
                              fontStyle: "normal",
                              fontVariant: "normal",
                              fontWeight: 400,
                              textDecoration: "none",
                              verticalAlign: "baseline",
                              whiteSpace: "pre-wrap",
                            }}
                          >
                            &nbsp;
                          </span>
                        </span> */}
                    </a>
                    <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#000000",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          fontWeight: 400,
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        to learn more about &quot;Private Browsing&quot; and
                        managing cookie settings.&nbsp;&nbsp;
                      </span>
                    </span>
                  </span>
                </li>
                <li
                  style={{
                    backgroundColor: "transparent",
                    color: "#000000",

                    fontSize: "12pt",
                    fontStyle: "normal",
                    fontVariant: "normal",
                    fontWeight: 400,
                    listStyleType: "lower-roman",
                    textDecoration: "none",
                    verticalAlign: "baseline",
                    whiteSpace: "pre",
                  }}
                  dir="ltr"
                  aria-level={3}
                >
                  <span className="ck-list-bogus-paragraph">
                    <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#000000",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        <strong>Firefox</strong>
                      </span>
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          fontWeight: 400,
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        :&nbsp; Click&nbsp;
                      </span>
                    </span>
                    <a
                      style={{ textDecoration: "none" }}
                      target="_blank"
                      rel="noopener noreferrer"
                      href="https://support.mozilla.org/en-US/kb/enhanced-tracking-protection-firefox-desktop?redirectlocale=en-US&redirectslug=enable-and-disable-cookies-website-preferences"
                    >
                      {/* <span
                          style={{
                            backgroundColor: "transparent",
                            color: "#0000ff",

                            fontSize: "12pt",
                          }}
                        >
                          <span
                            style={{
                              WebkitTextDecorationSkip: "none",
                              fontStyle: "normal",
                              fontVariant: "normal",
                              fontWeight: 400,
                              textDecorationSkipInk: "none",
                              verticalAlign: "baseline",
                              whiteSpace: "pre-wrap",
                            }}
                          > */}
                      <>here</>
                      {/* </span>
                        </span> */}
                    </a>
                    <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#000000",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          fontWeight: 400,
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        &nbsp;to learn more about &quot;Private Browsing&quot;
                        and managing cookie settings.
                      </span>
                    </span>
                  </span>
                </li>
                <li
                  style={{
                    backgroundColor: "transparent",
                    color: "#000000",

                    fontSize: "12pt",
                    fontStyle: "normal",
                    fontVariant: "normal",
                    fontWeight: 400,
                    listStyleType: "lower-roman",
                    textDecoration: "none",
                    verticalAlign: "baseline",
                    whiteSpace: "pre",
                  }}
                  dir="ltr"
                  aria-level={3}
                >
                  <span className="ck-list-bogus-paragraph">
                    <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#000000",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        <strong>Lynx</strong>
                      </span>
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          fontWeight: 400,
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        : Click&nbsp;
                      </span>
                    </span>
                    <a
                      style={{ textDecoration: "none" }}
                      target="_blank"
                      rel="noopener noreferrer"
                      href="https://www.lynx.com/privacy-policy"
                    >
                      {/* <span
                          style={{
                            backgroundColor: "transparent",
                            color: "#0000ff",

                            fontSize: "12pt",
                          }}
                        >
                          <span
                            style={{
                              WebkitTextDecorationSkip: "none",
                              fontStyle: "normal",
                              fontVariant: "normal",
                              fontWeight: 400,
                              textDecorationSkipInk: "none",
                              verticalAlign: "baseline",
                              whiteSpace: "pre-wrap",
                            }}
                          > */}
                      <>here</>
                      {/* </span>
                        </span> */}
                    </a>
                    <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#000000",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          fontWeight: 400,
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        to learn more about &quot;Private Browsing&quot; and
                        managing cookie settings.
                      </span>
                    </span>
                  </span>
                </li>
                <li
                  style={{
                    backgroundColor: "transparent",
                    color: "#000000",

                    fontSize: "12pt",
                    fontStyle: "normal",
                    fontVariant: "normal",
                    fontWeight: 400,
                    listStyleType: "lower-roman",
                    textDecoration: "none",
                    verticalAlign: "baseline",
                    whiteSpace: "pre",
                  }}
                  dir="ltr"
                  aria-level={3}
                >
                  <span className="ck-list-bogus-paragraph">
                    <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#000000",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        <strong>Safari</strong>
                      </span>
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          fontWeight: 400,
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        : Click&nbsp;
                      </span>
                    </span>
                    <a
                      style={{ textDecoration: "none" }}
                      target="_blank"
                      rel="noopener noreferrer"
                      href="https://support.apple.com/guide/safari/manage-cookies-and-website-data-sfri11471/11.0/mac/10.13"
                    >
                      {/* <span
                          style={{
                            backgroundColor: "transparent",
                            color: "#0000ff",

                            fontSize: "12pt",
                          }}
                        >
                          <span
                            style={{
                              WebkitTextDecorationSkip: "none",
                              fontStyle: "normal",
                              fontVariant: "normal",
                              fontWeight: 400,
                              textDecorationSkipInk: "none",
                              verticalAlign: "baseline",
                              whiteSpace: "pre-wrap",
                            }}
                          > */}
                      <>here</>
                      {/* </span>
                        </span> */}
                    </a>
                    <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#0000ff",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          fontWeight: 400,
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        &nbsp;
                      </span>
                    </span>
                    <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#000000",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          fontWeight: 400,
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        to learn more about &quot;Private Browsing&quot; and
                        managing cookie settings.
                      </span>
                    </span>
                  </span>
                </li>
                <li
                  style={{
                    backgroundColor: "transparent",
                    color: "#000000",

                    fontSize: "12pt",
                    fontStyle: "normal",
                    fontVariant: "normal",
                    fontWeight: 400,
                    listStyleType: "lower-roman",
                    textDecoration: "none",
                    verticalAlign: "baseline",
                    whiteSpace: "pre",
                  }}
                  dir="ltr"
                  aria-level={3}
                >
                  <span className="ck-list-bogus-paragraph">
                    <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#000000",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        <strong>Safari(macOS)</strong>
                      </span>
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          fontWeight: 400,
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        : Click&nbsp;
                      </span>
                    </span>
                    <a
                      style={{ textDecoration: "none" }}
                      target="_blank"
                      rel="noopener noreferrer"
                      href="https://support.apple.com/kb/PH21411?viewlocale=en_GB&locale=en_GB"
                    >
                      {/* <span
                          style={{
                            backgroundColor: "transparent",
                            color: "#0000ff",

                            fontSize: "12pt",
                          }}
                        >
                          <span
                            style={{
                              WebkitTextDecorationSkip: "none",
                              fontStyle: "normal",
                              fontVariant: "normal",
                              fontWeight: 400,
                              textDecorationSkipInk: "none",
                              verticalAlign: "baseline",
                              whiteSpace: "pre-wrap",
                            }}
                          > */}
                      <>here</>
                      {/* </span>
                          <span
                            style={{
                              fontStyle: "normal",
                              fontVariant: "normal",
                              fontWeight: 400,
                              textDecoration: "none",
                              verticalAlign: "baseline",
                              whiteSpace: "pre-wrap",
                            }}
                          >
                            &nbsp;
                          </span>
                        </span> */}
                    </a>
                    <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#000000",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          fontWeight: 400,
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        to learn more about &quot;Private Browsing&quot; and
                        managing cookie settings.
                      </span>
                    </span>
                  </span>
                </li>
                <li
                  style={{
                    backgroundColor: "transparent",
                    color: "#000000",

                    fontSize: "12pt",
                    fontStyle: "normal",
                    fontVariant: "normal",
                    fontWeight: 400,
                    listStyleType: "lower-roman",
                    textDecoration: "none",
                    verticalAlign: "baseline",
                    whiteSpace: "pre",
                  }}
                  dir="ltr"
                  aria-level={3}
                >
                  <span className="ck-list-bogus-paragraph">
                    <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#000000",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        <strong>Safari(iOS)</strong>
                      </span>
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          fontWeight: 400,
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        : Click&nbsp;
                      </span>
                    </span>
                    <a
                      style={{ textDecoration: "none" }}
                      target="_blank"
                      rel="noopener noreferrer"
                      href="https://support.apple.com/en-gb/HT201265"
                    >
                      {/* <span
                          style={{
                            backgroundColor: "transparent",
                            color: "#0000ff",

                            fontSize: "12pt",
                          }}
                        >
                          <span
                            style={{
                              WebkitTextDecorationSkip: "none",
                              fontStyle: "normal",
                              fontVariant: "normal",
                              fontWeight: 400,
                              textDecorationSkipInk: "none",
                              verticalAlign: "baseline",
                              whiteSpace: "pre-wrap",
                            }}
                          > */}
                      <>here</>
                      {/* </span>
                        </span> */}
                    </a>
                    <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#000000",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          fontWeight: 400,
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        to learn more about &quot;Private Browsing&quot; and
                        managing cookie settings.
                      </span>
                    </span>
                  </span>
                </li>
                <li
                  style={{
                    backgroundColor: "transparent",
                    color: "#000000",

                    fontSize: "12pt",
                    fontStyle: "normal",
                    fontVariant: "normal",
                    fontWeight: 400,
                    listStyleType: "lower-roman",
                    textDecoration: "none",
                    verticalAlign: "baseline",
                    whiteSpace: "pre",
                  }}
                  dir="ltr"
                  aria-level={3}
                >
                  <span className="ck-list-bogus-paragraph">
                    <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#000000",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        <strong>Opera</strong>
                      </span>
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          fontWeight: 400,
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        : Click&nbsp;
                      </span>
                    </span>
                    <a
                      style={{ textDecoration: "none" }}
                      target="_blank"
                      rel="noopener noreferrer"
                      href="https://legal.opera.com/privacy/privacy.html"
                    >
                      {/* <span
                          style={{
                            backgroundColor: "transparent",
                            color: "#0000ff",

                            fontSize: "12pt",
                          }}
                        >
                          <span
                            style={{
                              WebkitTextDecorationSkip: "none",
                              fontStyle: "normal",
                              fontVariant: "normal",
                              fontWeight: 400,
                              textDecorationSkipInk: "none",
                              verticalAlign: "baseline",
                              whiteSpace: "pre-wrap",
                            }}
                          > */}
                      <>here</>
                      {/* </span>
                          <span
                            style={{
                              fontStyle: "normal",
                              fontVariant: "normal",
                              fontWeight: 400,
                              textDecoration: "none",
                              verticalAlign: "baseline",
                              whiteSpace: "pre-wrap",
                            }}
                          >
                            &nbsp;
                          </span>
                        </span> */}
                    </a>
                    <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#000000",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          fontWeight: 400,
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        to learn more about &quot;Private Browsing&quot; and
                        managing cookie settings.
                      </span>
                    </span>
                  </span>
                </li>
              </ol>
            </li>
            <li
              style={{
                backgroundColor: "transparent",
                color: "#000000",

                fontSize: "12pt",
                fontStyle: "normal",
                fontVariant: "normal",
                fontWeight: 400,
                listStyleType: "lower-alpha",
                textDecoration: "none",
                verticalAlign: "baseline",
                whiteSpace: "pre",
              }}
              dir="ltr"
              aria-level={2}
            >
              <span className="ck-list-bogus-paragraph">
                <span
                  style={{
                    backgroundColor: "transparent",
                    color: "#000000",

                    fontSize: "12pt",
                  }}
                >
                  <span
                    style={{
                      fontStyle: "normal",
                      fontVariant: "normal",
                      fontWeight: 400,
                      textDecoration: "none",
                      verticalAlign: "baseline",
                      whiteSpace: "pre-wrap",
                    }}
                  >
                    If you want to learn more about cookies or how to control,
                    disable, or delete them, please click
                  </span>
                </span>
                <span
                  style={{
                    backgroundColor: "transparent",
                    color: "#0064f3",

                    fontSize: "12pt",
                  }}
                >
                  <span
                    style={{
                      fontStyle: "normal",
                      fontVariant: "normal",
                      fontWeight: 400,
                      textDecoration: "none",
                      verticalAlign: "baseline",
                      whiteSpace: "pre-wrap",
                    }}
                  >
                    &nbsp;
                  </span>
                </span>
                <a
                  style={{ textDecoration: "none" }}
                  target="_blank"
                  rel="noopener noreferrer"
                  href="https://www.aboutcookies.org/"
                >
                  {/* <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#0064f3",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          WebkitTextDecorationSkip: "none",
                          fontStyle: "normal",
                          fontVariant: "normal",
                          fontWeight: 400,
                          textDecorationSkipInk: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      > */}
                  <>here</>
                  {/* </span>
                    </span> */}
                </a>
                <span
                  style={{
                    backgroundColor: "transparent",
                    color: "#000000",

                    fontSize: "12pt",
                  }}
                >
                  <span
                    style={{
                      fontStyle: "normal",
                      fontVariant: "normal",
                      fontWeight: 400,
                      textDecoration: "none",
                      verticalAlign: "baseline",
                      whiteSpace: "pre-wrap",
                    }}
                  >
                    {" "}
                    for detailed guidance.
                  </span>
                </span>
              </span>
            </li>
            <li
              style={{
                backgroundColor: "transparent",
                color: "#000000",

                fontSize: "12pt",
                fontStyle: "normal",
                fontVariant: "normal",
                fontWeight: 400,
                listStyleType: "lower-alpha",
                textDecoration: "none",
                verticalAlign: "baseline",
                whiteSpace: "pre",
              }}
              dir="ltr"
              aria-level={2}
            >
              <span className="ck-list-bogus-paragraph">
                <span
                  style={{
                    backgroundColor: "transparent",
                    color: "#000000",

                    fontSize: "12pt",
                  }}
                >
                  <span
                    style={{
                      fontStyle: "normal",
                      fontVariant: "normal",
                      fontWeight: 400,
                      textDecoration: "none",
                      verticalAlign: "baseline",
                      whiteSpace: "pre-wrap",
                    }}
                  >
                    In addition, certain third-party advertising networks,
                    including Google, allow users to opt out of or customize
                    their preferences associated with your internet
                    browsing.&nbsp; To learn more about this feature from
                    Google, click&nbsp;
                  </span>
                </span>
                <a
                  style={{ textDecoration: "none" }}
                  target="_blank"
                  rel="noopener noreferrer"
                  href="https://adssettings.google.com/u/0/authenticated"
                >
                  {/* <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#000000",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          WebkitTextDecorationSkip: "none",
                          fontStyle: "normal",
                          fontVariant: "normal",
                          fontWeight: 400,
                          textDecorationSkipInk: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      > */}
                  <>here</>
                  {/* </span>
                    </span> */}
                </a>
                <span
                  style={{
                    backgroundColor: "transparent",
                    color: "#000000",

                    fontSize: "12pt",
                  }}
                >
                  <span
                    style={{
                      fontStyle: "normal",
                      fontVariant: "normal",
                      fontWeight: 400,
                      textDecoration: "none",
                      verticalAlign: "baseline",
                      whiteSpace: "pre-wrap",
                    }}
                  >
                    .
                  </span>
                </span>
              </span>
            </li>
            <li
              style={{
                backgroundColor: "transparent",
                color: "#000000",

                fontSize: "12pt",
                fontStyle: "normal",
                fontVariant: "normal",
                fontWeight: 400,
                listStyleType: "lower-alpha",
                textDecoration: "none",
                verticalAlign: "baseline",
                whiteSpace: "pre",
              }}
              dir="ltr"
              aria-level={2}
            >
              <span className="ck-list-bogus-paragraph">
                <span
                  style={{
                    backgroundColor: "transparent",
                    color: "#000000",

                    fontSize: "12pt",
                  }}
                >
                  <span
                    style={{
                      fontStyle: "normal",
                      fontVariant: "normal",
                      fontWeight: 400,
                      textDecoration: "none",
                      verticalAlign: "baseline",
                      whiteSpace: "pre-wrap",
                    }}
                  >
                    If you do not accept our cookies, you may experience some
                    inconvenience in your use of our website.&nbsp; For example,
                    we may not be able to recognize your computer or mobile
                    device and you may need to log in every time you visit our
                    awesome website.
                  </span>
                </span>
              </span>
            </li>
          </ol>
        </li>
        <li
          style={{
            backgroundColor: "transparent",
            color: "#000000",

            fontSize: "12pt",
            fontStyle: "normal",
            fontVariant: "normal",
            listStyleType: "decimal",
            textDecoration: "none",
            verticalAlign: "baseline",
            whiteSpace: "pre",
          }}
          dir="ltr"
          aria-level={1}
        >
          <span className="ck-list-bogus-paragraph">
            <span
              style={{
                backgroundColor: "transparent",
                color: "#000000",

                fontSize: "12pt",
              }}
            >
              <span
                style={{
                  fontStyle: "normal",
                  fontVariant: "normal",
                  textDecoration: "none",
                  verticalAlign: "baseline",
                  whiteSpace: "pre-wrap",
                }}
              >
                <strong>COOKIES THAT WE USE</strong>
              </span>
            </span>
          </span>
          <ol style={{ marginBottom: 0, marginTop: 0, paddingInlineStart: 48 }}>
            <li
              style={{
                backgroundColor: "transparent",
                color: "#000000",

                fontSize: "12pt",
                fontStyle: "normal",
                fontVariant: "normal",
                fontWeight: 400,
                listStyleType: "lower-alpha",
                textDecoration: "none",
                verticalAlign: "baseline",
                whiteSpace: "pre",
              }}
              dir="ltr"
              aria-level={2}
            >
              <span className="ck-list-bogus-paragraph">
                <span
                  style={{
                    backgroundColor: "transparent",
                    color: "#000000",

                    fontSize: "12pt",
                  }}
                >
                  <span
                    style={{
                      fontStyle: "normal",
                      fontVariant: "normal",
                      fontWeight: 400,
                      textDecoration: "none",
                      verticalAlign: "baseline",
                      whiteSpace: "pre-wrap",
                    }}
                  >
                    We may use any of the following categories of cookies on our
                    website as detailed below.&nbsp; Each cookie falls within
                    one of these four categories:
                  </span>
                </span>
              </span>
            </li>
          </ol>
        </li>
      </ol>
      <div style={{ marginLeft: "0pt" }} dir="ltr" align="left">
        <figure className="table ck-widget ck-widget_with-selection-handle">
          <table
            className="ck-table-resized"
            style={{ borderCollapse: "collapse" }}
          >
            <colgroup>
              <col style={{ width: "50%" }} width={127} />
              <col style={{ width: "50%" }} width={492} />
            </colgroup>
            <thead>
              <tr style={{ height: "0.95pt" }}>
                <th
                  className="ck-editor__editable ck-editor__nested-editable"
                  style={{
                    backgroundColor: "#e3f0f4",
                    border: "0.75pt solid #dbdad2",
                    overflowWrap: "break-word",
                    overflow: "hidden",
                    padding: "6pt",
                    verticalAlign: "bottom",
                  }}
                  role="textbox"
                  scope="col"
                >
                  <p
                    style={{
                      lineHeight: "1.2",
                      marginBottom: "0pt",
                      marginTop: "0pt",
                      textAlign: "center",
                    }}
                    dir="ltr"
                  >
                    <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#000000",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        <strong>Category</strong>
                      </span>
                    </span>
                  </p>
                  <div className="ck-table-column-resizer" />
                </th>
                <th
                  className="ck-editor__editable ck-editor__nested-editable"
                  style={{
                    backgroundColor: "#e3f0f4",
                    border: "0.75pt solid #dbdad2",
                    overflowWrap: "break-word",
                    overflow: "hidden",
                    padding: "6pt",
                    verticalAlign: "bottom",
                  }}
                  role="textbox"
                  scope="col"
                >
                  <p
                    style={{
                      lineHeight: "1.2",
                      marginBottom: "0pt",
                      marginTop: "0pt",
                      textAlign: "center",
                    }}
                    dir="ltr"
                  >
                    <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#000000",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        <strong>Description</strong>
                      </span>
                    </span>
                  </p>
                  <div className="ck-table-column-resizer" />
                </th>
              </tr>
            </thead>
            <tbody>
              <tr style={{ height: "130.3pt" }}>
                <td
                  className="ck-editor__editable ck-editor__nested-editable"
                  style={{
                    border: "0.75pt solid #dbdad2",
                    overflowWrap: "break-word",
                    overflow: "hidden",
                    padding: "6pt",
                    verticalAlign: "middle",
                  }}
                  role="textbox"
                >
                  <p
                    style={{
                      lineHeight: "1.2",
                      marginBottom: "12pt",
                      marginTop: "0pt",
                    }}
                    dir="ltr"
                  >
                    <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#000000",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          fontWeight: 400,
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        Essential Cookies
                      </span>
                    </span>
                  </p>
                  <div className="ck-table-column-resizer" />
                </td>
                <td
                  className="ck-editor__editable ck-editor__nested-editable"
                  style={{
                    border: "0.75pt solid #dbdad2",
                    overflowWrap: "break-word",
                    overflow: "hidden",
                    padding: "6pt",
                    verticalAlign: "middle",
                  }}
                  role="textbox"
                >
                  <p
                    style={{
                      lineHeight: "1.2",
                      marginBottom: "12pt",
                      marginTop: "0pt",
                    }}
                    dir="ltr"
                  >
                    <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#000000",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          fontWeight: 400,
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        Essential cookies, otherwise known as first-party
                        cookies, are sometimes called &quot;strictly
                        necessary&quot; because without them we cannot provide
                        many of the cool, fun, and informative services that you
                        need on our website.&nbsp; For example, essential
                        cookies help us remember your preferences as you
                        navigate our website and help the content of the pages
                        you request to load faster.&nbsp; Without these cookies,
                        the services that you have requested cannot be provided,
                        and we only use these cookies to provide you with these
                        services.
                      </span>
                    </span>
                  </p>
                  <div className="ck-table-column-resizer" />
                </td>
              </tr>
              <tr style={{ height: "214.1pt" }}>
                <td
                  className="ck-editor__editable ck-editor__nested-editable"
                  style={{
                    border: "0.75pt solid #dbdad2",
                    overflowWrap: "break-word",
                    overflow: "hidden",
                    padding: "6pt",
                    verticalAlign: "middle",
                  }}
                  role="textbox"
                >
                  <p
                    style={{
                      lineHeight: "1.2",
                      marginBottom: "12pt",
                      marginTop: "0pt",
                    }}
                    dir="ltr"
                  >
                    <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#000000",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          fontWeight: 400,
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        Analytics Cookies
                      </span>
                    </span>
                  </p>
                  <div className="ck-table-column-resizer" />
                </td>
                <td
                  className="ck-editor__editable ck-editor__nested-editable"
                  style={{
                    border: "0.75pt solid #dbdad2",
                    overflowWrap: "break-word",
                    overflow: "hidden",
                    padding: "6pt",
                    verticalAlign: "middle",
                  }}
                  role="textbox"
                >
                  <p
                    style={{
                      lineHeight: "1.2",
                      marginBottom: "12pt",
                      marginTop: "0pt",
                    }}
                    dir="ltr"
                  >
                    <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#000000",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          fontWeight: 400,
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        We’re constantly updating our content and features to
                        make your experience better, and these cookies track
                        information about your website visits so we can make
                        improvements and report our performance.&nbsp; For
                        example, we analyze visitor and user behavior to provide
                        more pertinent and better content or suggest certain
                        activities and places to visit on our website.&nbsp;
                        These cookies collect information about how visitors use
                        our website, which website the user came from, the
                        number of visits for each visitor, and how long a user
                        stays on our website.&nbsp; Please also note that we
                        could also use these analytics cookies to test new ads,
                        webpages, or features to determine how users react to
                        them.&nbsp; The information gathered via these cookies
                        does not specifically identify any individual visitor
                        although it could make visitors contingently
                        identifiable due to the fact that information collected
                        is usually linked to a pseudonymous identifier that
                        associated with the device you use to access our
                        website.&nbsp; The information collected is aggregated
                        and anonymous.
                      </span>
                    </span>
                  </p>
                  <div className="ck-table-column-resizer" />
                </td>
              </tr>
              <tr style={{ height: "130.3pt" }}>
                <td
                  className="ck-editor__editable ck-editor__nested-editable"
                  style={{
                    border: "0.75pt solid #dbdad2",
                    overflowWrap: "break-word",
                    overflow: "hidden",
                    padding: "6pt",
                    verticalAlign: "middle",
                  }}
                  role="textbox"
                >
                  <p
                    style={{
                      lineHeight: "1.2",
                      marginBottom: "12pt",
                      marginTop: "0pt",
                    }}
                    dir="ltr"
                  >
                    <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#000000",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          fontWeight: 400,
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        Functionality or Preference Cookies
                      </span>
                    </span>
                  </p>
                  <div className="ck-table-column-resizer" />
                </td>
                <td
                  className="ck-editor__editable ck-editor__nested-editable"
                  style={{
                    border: "0.75pt solid #dbdad2",
                    overflowWrap: "break-word",
                    overflow: "hidden",
                    padding: "6pt",
                    verticalAlign: "middle",
                  }}
                  role="textbox"
                >
                  <p
                    style={{
                      lineHeight: "1.2",
                      marginBottom: "12pt",
                      marginTop: "0pt",
                    }}
                    dir="ltr"
                  >
                    <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#000000",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          fontWeight: 400,
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        During your visit to our awesome website, these cookies
                        are used to remember the information you have provided
                        or selections you make such as your username, language,
                        and location.&nbsp; In addition, they also keep your
                        preferences when personalizing our website to optimize
                        your use of Sandee, including preferred language.&nbsp;
                        Your preferences are remembered using the persistent
                        cookies, and the next time you visit our website you
                        won’t have to set them again.
                      </span>
                    </span>
                  </p>
                  <div className="ck-table-column-resizer" />
                </td>
              </tr>
              <tr style={{ height: "176.9pt" }}>
                <td
                  className="ck-editor__editable ck-editor__nested-editable"
                  style={{
                    border: "0.75pt solid #dbdad2",
                    overflowWrap: "break-word",
                    overflow: "hidden",
                    padding: "6pt",
                    verticalAlign: "middle",
                  }}
                  role="textbox"
                >
                  <p
                    style={{
                      lineHeight: "1.2",
                      marginBottom: "12pt",
                      marginTop: "0pt",
                    }}
                    dir="ltr"
                  >
                    <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#000000",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          fontWeight: 400,
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        Targeting or Advertising Cookies
                      </span>
                    </span>
                  </p>
                  <div className="ck-table-column-resizer" />
                </td>
                <td
                  className="ck-editor__editable ck-editor__nested-editable"
                  style={{
                    border: "0.75pt solid #dbdad2",
                    overflowWrap: "break-word",
                    overflow: "hidden",
                    padding: "6pt",
                    verticalAlign: "middle",
                  }}
                  role="textbox"
                >
                  <p
                    style={{
                      lineHeight: "1.2",
                      marginBottom: "12pt",
                      marginTop: "0pt",
                    }}
                    dir="ltr"
                  >
                    <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#000000",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          fontWeight: 400,
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        These third-party cookies are placed by third-party
                        advertising platforms or networks to deliver ads and
                        track ad performance which allows advertising networks
                        to deliver ads that could be relevant to you based upon
                        your activities on our awesome website.&nbsp; They could
                        also subsequently use information about your visit to
                        provide ads that you may be interested on our awesome
                        website and other websites.
                      </span>
                    </span>
                  </p>
                  <div className="ck-table-column-resizer" />
                </td>
              </tr>
              <tr style={{ height: "102.35pt" }}>
                <td
                  className="ck-editor__editable ck-editor__nested-editable"
                  style={{
                    border: "0.75pt solid #dbdad2",
                    overflowWrap: "break-word",
                    overflow: "hidden",
                    padding: "6pt",
                    verticalAlign: "middle",
                  }}
                  role="textbox"
                >
                  <p
                    style={{
                      lineHeight: "1.2",
                      marginBottom: "12pt",
                      marginTop: "0pt",
                    }}
                    dir="ltr"
                  >
                    <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#000000",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          fontWeight: 400,
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        Social Media Cookies
                      </span>
                    </span>
                  </p>
                  <div className="ck-table-column-resizer" />
                </td>
                <td
                  className="ck-editor__editable ck-editor__nested-editable"
                  style={{
                    border: "0.75pt solid #dbdad2",
                    overflowWrap: "break-word",
                    overflow: "hidden",
                    padding: "6pt",
                    verticalAlign: "middle",
                  }}
                  role="textbox"
                >
                  <p
                    style={{
                      lineHeight: "1.2",
                      marginBottom: "12pt",
                      marginTop: "0pt",
                    }}
                    dir="ltr"
                  >
                    <span
                      style={{
                        backgroundColor: "transparent",
                        color: "#000000",

                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          fontStyle: "normal",
                          fontVariant: "normal",
                          fontWeight: 400,
                          textDecoration: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        These cookies are used when you share information using
                        a social media sharing button or &quot;like&quot; button
                        on our website, or when you link your account or engage
                        with our awesome content on or through a social
                        networking website such as Facebook or Twitter.
                      </span>
                    </span>
                  </p>
                  <div className="ck-table-column-resizer" />
                </td>
              </tr>
            </tbody>
          </table>
        </figure>
      </div>
      <p
        style={{
          backgroundColor: "#ffffff",
          lineHeight: "1.2",
          marginBottom: "12pt",
          marginTop: "0pt",
        }}
        dir="ltr"
      >
        <br data-cke-filler="true" />
      </p>
      <ol
        style={{ marginBottom: 0, marginTop: 0, paddingInlineStart: 48 }}
        start={6}
      >
        <li
          style={{
            backgroundColor: "transparent",
            color: "#000000",

            fontSize: "12pt",
            fontStyle: "normal",
            fontVariant: "normal",
            listStyleType: "decimal",
            textDecoration: "none",
            verticalAlign: "baseline",
            whiteSpace: "pre",
          }}
          dir="ltr"
          aria-level={1}
        >
          <span className="ck-list-bogus-paragraph">
            <span
              style={{
                backgroundColor: "transparent",
                color: "#000000",

                fontSize: "12pt",
              }}
            >
              <span
                style={{
                  fontStyle: "normal",
                  fontVariant: "normal",
                  textDecoration: "none",
                  verticalAlign: "baseline",
                  whiteSpace: "pre-wrap",
                }}
              >
                <strong>WHAT IF YOU HAVE QUESTIONS?</strong>
              </span>
            </span>
          </span>
          <ol style={{ marginBottom: 0, marginTop: 0, paddingInlineStart: 48 }}>
            <li
              style={{
                backgroundColor: "transparent",
                color: "#000000",

                fontSize: "12pt",
                fontStyle: "normal",
                fontVariant: "normal",
                fontWeight: 400,
                listStyleType: "lower-alpha",
                textDecoration: "none",
                verticalAlign: "baseline",
                whiteSpace: "pre",
              }}
              dir="ltr"
              aria-level={2}
            >
              <span className="ck-list-bogus-paragraph">
                <span
                  style={{
                    backgroundColor: "transparent",
                    color: "#000000",

                    fontSize: "12pt",
                  }}
                >
                  <span
                    style={{
                      fontStyle: "normal",
                      fontVariant: "normal",
                      fontWeight: 400,
                      textDecoration: "none",
                      verticalAlign: "baseline",
                      whiteSpace: "pre-wrap",
                    }}
                  >
                    If you have any questions or concerns, please contact us
                    at&nbsp;
                  </span>
                </span>
                <a href="mailto:<EMAIL>">
                  {/* <span
                      style={{
                        backgroundColor: "transparent",
                        fontSize: "12pt",
                      }}
                    >
                      <span
                        style={{
                          WebkitTextDecorationSkip: "none",
                          fontStyle: "normal",
                          fontVariant: "normal",
                          fontWeight: 400,
                          textDecorationSkipInk: "none",
                          verticalAlign: "baseline",
                          whiteSpace: "pre-wrap",
                        }}
                      > */}
                  <><EMAIL></>
                  {/* </span>
                    </span> */}
                </a>
                <span
                  style={{
                    backgroundColor: "transparent",
                    color: "#000000",

                    fontSize: "12pt",
                  }}
                >
                  <span
                    style={{
                      fontStyle: "normal",
                      fontVariant: "normal",
                      fontWeight: 400,
                      textDecoration: "none",
                      verticalAlign: "baseline",
                      whiteSpace: "pre-wrap",
                    }}
                  >
                    .&nbsp;
                  </span>
                </span>
              </span>
            </li>
          </ol>
        </li>
      </ol>
    </div>
  );
};

export default ContentCookiePolicy;
