import { CustomContainer } from "@/components/Custom-Display";
import FilterPage_Hero_Section from "@/components/filterPage/FilterHeroSection";
import { CDNImageUrl } from "@/helper/functions";
import React from "react";
import ContentCookiePolicy from "./Content";
import { SiteDataPageWise } from "@/data/siteMetadata";
import BreadC<PERSON>berJSONLD from "@/components/BreadCumberJSONLD";

const page = () => {
  return (
    <>
      <BreadCumberJSONLD name={"Cookie Policy"} slug={`cookie-policy`}
        title={SiteDataPageWise?.cookiePolicy?.title} description={SiteDataPageWise?.cookiePolicy?.description}
      />

      <FilterPage_Hero_Section
        data={{
          name: "Cookie Policy",
          image: CDNImageUrl("images/header/CookiePolicy.avif"),
        }}
      />
      <CustomContainer>
        <ContentCookiePolicy />
      </CustomContainer>
    </>
  );
};

export default page;

export function generateMetadata() {
  return SiteDataPageWise.cookiePolicy;
}
