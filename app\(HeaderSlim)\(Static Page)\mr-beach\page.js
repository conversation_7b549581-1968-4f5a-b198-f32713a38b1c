import NameTitle from "@/components/Common/NameTitle";
import { CustomContainer, CustomGrid } from "@/components/Custom-Display";
import FilterPage_Hero_Section from "@/components/filterPage/FilterHeroSection";
import { SiteDataPageWise } from "@/data/siteMetadata";
import staticPageData from "@/data/staticPageData";
import { blurDataURL } from "@/helper/functions";
import React from "react";
import { PressCard } from "../press/page";
import Image from "next/image";
import BreadCumberJSONLD from "@/components/BreadCumberJSONLD";

const page = () => {
  const jsonLdMrBeach = {
    "@context": "https://schema.org/",
    "@type": "Person",
    name: "<PERSON>",
    url: "https://sandee.com/mr-beach",
    image:
      "https://encrypted-tbn2.gstatic.com/images?q=tbn:ANd9GcQCTR5bYGUu0-mpbgsDAS6iEPHcsORdw5KUI2fWafCaEFfgRrCA",
    sameAs: [
      "https://www.facebook.com/sandeebeachescompany/",
      "https://twitter.com/sandee",
      "https://www.instagram.com/sandee/",
      "https://www.youtube.com/@sandee",
      "https://www.linkedin.com/company/sandee/about/",
      "https://in.pinterest.com/chooseyourbeach/",
      "https://en.wikipedia.org/wiki/Randall_Kaplan",
      "https://sandee.com/",
    ],
    jobTitle: "Founder and CEO",
    worksFor: {
      "@type": "Organization",
      name: "https://sandee.com/",
    },
  };
  return (
    <>
      <script
        type="application/ld+json"
        id="application/ld+jsonMrBeach"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLdMrBeach) }}
      ></script>
      <BreadCumberJSONLD name={"Mr. Beach"} slug={`mr-beach`}
        title={SiteDataPageWise?.mrBeach?.title}
        description={SiteDataPageWise?.mrBeach?.description}
      />

      <FilterPage_Hero_Section
        data={{
          name: "Mr. Beach",
          title: (
            <>
              Mr. Beach
              <sup style={{ fontSize: "24px", top: "-25px", right: "0px" }}>
                TM
              </sup>
            </>
          ),
          image: "https://images.sandee.com/default/kaplan.avif",
        }}
      />
      <CustomContainer>
        <NameTitle name="The World's Foremost Beach Expert" className="my-5" />

        <div className=" text-sandee-18 leading-snug my-5">
          <p>
            Randall Kaplan is the Founder and CEO of Sandee, a travel company
            focused on the promotion of beaches and beach tourism around the
            world. Randall&apos;s goal when he created Sandee was simple &ndash;
            to give the billion+ beachgoers around the world incredibly detailed
            information that would help them plan beach vacations, help them
            plan visits to beaches once they arrived in a new location, and help
            them plan visits to local beaches - and make all of their visits to
            the beach happier by allowing them to choose their perfect beach.
          </p>
          <p>
            Over the last eight years, the Sandee team has spent 350,000 hours
            creating the world&apos;s largest and most comprehensive beach
            resource by cataloging more than 100 categories of information for
            every beach in the world &ndash; more than 100,000 beaches in 212
            countries. Randall has been quoted and featured in many
            publications.
          </p>
          <p>
            In addition to being the world&apos;s foremost beach expert, Randall
            is a serial entrepreneur and venture capitalist, and is also the
            host of the podcast In Search of Excellence which is designed to
            inspire and motivate people to overcome obstacles on their path to
            success.
          </p>
          <p>For media inquires, please contact <NAME_EMAIL>.</p>
        </div>
      </CustomContainer>
      <CustomContainer>
        <div className=" bg-sandee-blue rounded-sandee py-5 px-10 bg-opacity-70">
          <NameTitle
            name={
              <>
                Mr. Beach
                <sup className="mr-2">TM</sup>
                in the News
              </>
            }
            className="my-5"
          />
          <CustomGrid
            data={staticPageData?.MRBEACH}
            className="gap-4 sm:gap-8 my-6"
            Component={({ data: dataProps }) => {
              // dataProps.link = `/blog/${dataProps?.slug}`; //
              dataProps.imageSrc = dataProps?.imgSrc;
              return PressCard({
                data: { ...dataProps, name: dataProps?.title },
              });
            }}
            xs={1}
            sm={2}
            // md={2}
            lg={3}
          // xl={3}
          />
        </div>
      </CustomContainer>
      <CustomContainer>
        <div className="my-5  rounded-sandee py-5 px-10 ">
          <div className=" bg-sandee-blue rounded-sandee flex flex-col lg:flex-row">
            <div className=" w-full lg:w-7/12 rounded-sandee">
              <div className=" min-h-[300px] min-w-[40px]  h-full w-full rounded-sandee">
                <div className="min-h-[300px] h-full w-full relative  overflow-hidden  rounded-sandee ">
                  <Image
                    // priority
                    className=" h-full w-full transition-transform duration-1000 ease-in-out rounded-sandee object-center transform group-hover:scale-125 object-cover"
                    src={`https://images.sandee.com/default/miami.avif`}
                    alt={`Sandee - Our Mission`}
                    fill
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    blurDataURL={blurDataURL(900, 600)}
                    placeholder="blur"
                  />
                </div>
              </div>
            </div>

            <div className=" w-full lg:w-5/12 py-5 px-10 flex flex-col justify-around">
              <h2 className=" text-white text-sandee-32 font-bold">
                Mr. Beach&apos;s Top 50 Beaches for 2024
              </h2>
              <p className=" text-white text-sandee-18">
                Randall Kaplan, the world&apos;s foremost beach expert who is
                also known as Mr. Beach™, just announced his 2024 list of the
                Top 50 Beaches in the United States.
              </p>
              {/* <div className=" !text-nowrap flex justify-center items-center">
                <Link
                href={`#`}
                className=" !text-nowrap flex justify-center items-center"
              >
                <CustomButton type={4}>Join Our Cause</CustomButton>
                </Link>
              </div> */}
            </div>
          </div>
        </div>
      </CustomContainer>
    </>
  );
};

export default page;

export function generateMetadata() {
  return SiteDataPageWise.mrBeach;
}
