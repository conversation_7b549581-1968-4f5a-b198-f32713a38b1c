import BreadCumberJSONLD from "@/components/BreadCumberJSONLD";
import NameTitle from "@/components/Common/NameTitle";
import CustomButton from "@/components/Custom-Button";
import { CustomContainer, CustomGrid } from "@/components/Custom-Display";
import FilterPage_Hero_Section from "@/components/filterPage/FilterHeroSection";
import PressContent from "@/components/press/Content";
import { SiteDataPageWise } from "@/data/siteMetadata";
import staticPageData from "@/data/staticPageData";
import { CDNImageUrl, altText, blurDataURL } from "@/helper/functions";
import Image from "next/image";
import Link from "next/link";
import React from "react";

const page = () => {
  return (
    <>
      <BreadCumberJSONLD name={"Press"} slug={`press`} title={SiteDataPageWise?.press?.title} description={SiteDataPageWise?.press?.description} />

      <FilterPage_Hero_Section
        data={{
          name: "Press",
          image: CDNImageUrl("images/header/Press.avif"),
        }}
      />
      <PressContent />
      <CustomContainer>
        <div className=" bg-sandee-blue rounded-sandee py-5 px-10 bg-opacity-70">
          <NameTitle name="Sandee in the News" className="my-5" />
          <CustomGrid
            data={staticPageData?.PRESS}
            className="gap-4 sm:gap-8 my-6"
            Component={({ data: dataProps }) => {
              // dataProps.link = `/blog/${dataProps?.slug}`; //
              dataProps.imageSrc = dataProps?.imgSrc;
              return PressCard({
                data: { ...dataProps, name: dataProps?.title },
              });
            }}
            xs={1}
            sm={2}
            // md={2}
            lg={3}
          // xl={3}
          />
        </div>
      </CustomContainer>
      <CustomContainer>
        <div className="my-5  rounded-sandee py-5 px-10 ">
          <div className=" bg-sandee-blue rounded-sandee flex flex-col lg:flex-row">
            <div className=" w-full lg:w-7/12 rounded-sandee">
              <div className=" min-h-[300px] min-w-[40px]  h-full w-full rounded-sandee">
                <div className="min-h-[300px] h-full w-full relative  overflow-hidden  rounded-sandee ">
                  <Image
                    // priority
                    className=" h-full w-full transition-transform duration-1000 ease-in-out rounded-sandee object-center transform group-hover:scale-125 object-cover"
                    src={`https://images.sandee.com/default/miami.avif`}
                    alt={`Sandee - Our Mission`}
                    fill
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    blurDataURL={blurDataURL(900, 600)}
                    placeholder="blur"
                  />
                </div>
              </div>
            </div>

            <div className=" w-full lg:w-5/12 py-5 px-10 flex flex-col justify-around">
              <h2 className=" text-white text-sandee-32 font-bold">
                Our Mission
              </h2>
              <p className=" text-white text-sandee-18">
                To give the 1,000,000,000 annual beachgoers reliable beach
                information, the best beach images on the planet, and reserve
                beach tourism for future generations
              </p>
              {/* <div className=" !text-nowrap flex justify-center items-center">
                <Link
                href={`#`}
                className=" !text-nowrap flex justify-center items-center"
              >
                <CustomButton type={4}>Join Our Cause</CustomButton>
                </Link>
              </div> */}
            </div>
          </div>
        </div>
        <Link
          href={`/contact-us/`}
          className=" !text-nowrap flex justify-center items-center"
        >
          <CustomButton type={4}> Request Press Kit</CustomButton>
        </Link>
      </CustomContainer>
    </>
  );
};

export default page;

export function generateMetadata() {
  return SiteDataPageWise.press;
}

export const PressCard = ({ data }) => {
  return (
    <Link
      href={`${data?.link}`}
      target="_blank"
      className="relative flex flex-col  h-[200px] group  rounded-sandee shadow-[0_3px_10px_rgb(0,0,0,0.2)] bg-white"
    >
      <div className="    h-[110px] w-full rounded-s-sandee">
        <div className=" h-[110px] w-full relative  overflow-hidden  rounded-s-sandee ">
          <Image
            // priority
            className=" h-[110px] w-full transition-transform duration-1000 ease-in-out  object-center transform group-hover:scale-125 object-contain p-3"
            src={data?.imageSrc}
            alt={altText(data)}
            fill
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            blurDataURL={blurDataURL(300, 200)}
            placeholder="blur"
          />
        </div>
      </div>
      <div className=" px-4 rounded-sandee flex gap-2 justify-center items-start flex-col">
        {/* <p className=" text-sandee-base  line-clamp-2">{data?.name}</p> */}
        <p className=" text-sandee-base line-clamp-3 text-sandee-grey">
          {data?.description}
        </p>
      </div>
    </Link>
  );
};
