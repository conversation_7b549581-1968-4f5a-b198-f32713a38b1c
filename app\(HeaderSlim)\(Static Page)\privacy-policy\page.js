import { CustomContainer } from "@/components/Custom-Display";
import FilterPage_Hero_Section from "@/components/filterPage/FilterHeroSection";
import { CDNImageUrl } from "@/helper/functions";
import React from "react";
import PrivacyPolicyContent from "./Content";
import { SiteDataPageWise } from "@/data/siteMetadata";
import BreadCumberJSONLD from "@/components/BreadCumberJSONLD";

const page = () => {
  return (
    <>
      <BreadCumberJSONLD name={"Privacy Policy"} slug={`privacy-policy`}
        title={SiteDataPageWise?.privacyPolicy?.title} description={SiteDataPageWise?.privacyPolicy?.description} />

      <FilterPage_Hero_Section
        data={{
          name: "Privacy Policy",
          image: CDNImageUrl("images/header/PrivacyPolicy.avif"),
        }}
      />
      <CustomContainer>
        <PrivacyPolicyContent />
      </CustomContainer>
      <p>
        <br data-cke-filler="true" />
      </p>
    </>
  );
};

export default page;

export function generateMetadata() {
  return SiteDataPageWise.privacyPolicy;
}
