import { CustomContainer } from "@/components/Custom-Display";
import Save_Our_Ocean_Hero_Section from "@/components/SaveOurOcean/SaveOceanHeroSection";
import React from "react";
import { SaveOurOcean } from "@/data/saveourocean";
import SaveOcean_Charities_Section from "@/components/SaveOurOcean/SaveOcean_Charities_Section";
import SaveOcean_Bottom_Button from "@/components/SaveOurOcean/SaveOceanBottomButton";
import { SiteDataPageWise } from "@/data/siteMetadata";
import BreadCumberJSONLD from "@/components/BreadCumberJSONLD";

const page = () => {
  return (
    <>
      <BreadCumberJSONLD name={"Save Our Ocean"} slug={`save-our-ocean`}
        title={SiteDataPageWise?.saveOurOcean?.title} description={SiteDataPageWise?.saveOurOcean?.description} />

      <Save_Our_Ocean_Hero_Section data={SaveOurOcean?.HeroData} />
      <CustomContainer>
        <SaveOcean_Charities_Section data={SaveOurOcean?.ChritiesSection} />
        <SaveOcean_Bottom_Button />
      </CustomContainer>
    </>
  );
};

export default page;
export function generateMetadata() {
  return SiteDataPageWise.saveOurOcean;
}
