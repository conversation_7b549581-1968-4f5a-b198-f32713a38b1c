import BreadCumberJSONLD from '@/components/BreadCumberJSONLD'
import BreadCumber from '@/components/Common/BreadCumber'
import { CustomContainer } from '@/components/Custom-Display'
import SharkAttackForm from '@/components/SharkForm/SharkAttackForm'
import React from 'react'

const page = () => {
    return (
        <>
            {/* <BreadCumberJSONLD name={"Shark Attack Report Questionnaire"} slug={`shark-attack-report`} /> */}
            <div className=" bg-gray-100 w-full p-4 flex items-center">
                <CustomContainer>
                    <BreadCumber
                        data={[
                            {
                                title: "Shark Attack Report Questionnaire",
                                to: `/shark-attack-report`,
                            },
                        ]}
                    />
                </CustomContainer>
            </div>
            {/* <FilterPage_Hero_Section
      data={{
        name: "Cookie Policy",
        image: CDNImageUrl("images/header/CookiePolicy.avif"),
      }}
    /> */}
            <CustomContainer>
                {/* <ContentCookiePolicy /> */}
                <SharkAttackForm />
            </CustomContainer>
        </>
    )
}

export default page