import { CustomContainer } from "@/components/Custom-Display";
import FilterPage_Hero_Section from "@/components/filterPage/FilterHeroSection";
import { CDNImageUrl } from "@/helper/functions";
import React from "react";
import TermsContent from "./Content";
import { SiteDataPageWise } from "@/data/siteMetadata";
import BreadC<PERSON>berJSONLD from "@/components/BreadCumberJSONLD";

const page = () => {
  return (
    <>
      <BreadCumberJSONLD name={"Terms"} slug={`terms`}
        title={SiteDataPageWise?.terms?.title} description={SiteDataPageWise?.terms?.description} />

      <FilterPage_Hero_Section
        data={{
          name: "Terms",
          image: CDNImageUrl("images/header/Terms.avif"),
        }}
      />
      <CustomContainer>
        <TermsContent />
      </CustomContainer>
      <p>
        <br data-cke-filler="true" />
      </p>
    </>
  );
};

export default page;

export function generateMetadata() {
  return SiteDataPageWise.terms;
}
