import { withErrorHandling } from "@/app/(HeaderSlim)/(Single Country Page)/[countrySlug]/action";
import BreadCumberJSONLD from "@/components/BreadCumberJSONLD";
import BeachCard from "@/components/Cards/BeachCard";
import NameTitle from "@/components/Common/NameTitle";
import Country_Blog_Section from "@/components/CountryPage/Country_BlogSection";
import { CustomContainer, CustomGrid } from "@/components/Custom-Display";
import Filter_List_Section from "@/components/filterPage/FilterListSection";
import Island_Hero_Section from "@/components/islandPage/IslandHeroSection";
import {
  API_BASE_URL,
  FinalImageGenerator,
  WEBSITE_URL,
  buildQueryString,
  slugConverter,
} from "@/helper/functions";
import axios from "axios";
import { redirect } from "next/navigation";
import React from "react";

const IslandPage = async ({ params }) => {
  const { data: IslandData } = await IslandgetislandHeaderImage(params);
  if (!!!IslandData?.name && !!!IslandData?.id) {
    console.log(params, "Island Paramas");
    return redirect("/");
  }
  const { data: IslandBeachData } = await IslandgetAllIsland({
    islandId: IslandData?.id,
  });

  const [{ data: Blogs }, { data: Listicales }] = await Promise.all([
    IslandgetBlogCountryWise({
      tags: IslandData?.country?.name,
      limit: 12,
      page: 1,
    }),
    getListiclesCountryWise({
      countryId: IslandData?.country?.id,
      limit: 12,
      page: 1,
    }),
  ]);
  // logController(IslandBeachData, IslandData);
  // if (!!!IslandData?.name || !!!IslandBeachData?.rows?.length) {
  //   logController(params, "Island Paramas");
  //   return redirect("/");
  // }

  return (
    <>
      <BreadCumberJSONLD
        name={IslandData?.name}
        slug={`island/${params?.islandSlug}`}
        title={
          !!IslandData?.title ? IslandData?.title : `Sandee - Choose Your Beach, Attraction, Hotels and Restaurants ${slugConverter(
            params?.islandSlug,
            true
          )}`
        }
        description={
          !!IslandData?.metaDescription
            ? IslandData?.metaDescription
            : "Sandee is the premier and most comprehensive database and information source. We feature 100 different categories of beach information, including beach location, beach parking, beach access, amenities, beach activities, beach attractions, beach weather, and many more beach activities and beach attractions."
        }

      />
      <Island_Hero_Section
        data={{
          name: slugConverter(params?.islandSlug, true),
          ...IslandData,
        }}
      />
      <CustomContainer>
        {IslandBeachData?.rows?.length ? (
          <NameTitle
            className=" mt-5 mb-[13px]"
            name={`Best Beaches in ${IslandData?.name ?? slugConverter(params?.islandSlug, true)
              }`}
          />
        ) : (
          <p className=" px-2 py-5 text-error-red-600  border-error-red-400 bg-error-red-50 border-2 rounded-sandee text-center">
            {`No Data Found for ${IslandData?.name ?? slugConverter(params?.islandSlug, true)
              }`}
          </p>
        )}
        <CustomGrid
          data={IslandBeachData?.rows}
          className="gap-4 sm:gap-8"
          Component={({ data: dataProps }) => {
            if (dataProps?.city?.state?.country?.slug) {
              dataProps.link = `/${dataProps?.city?.state?.country?.slug}/${dataProps?.city?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.nameSlug}`; //
              dataProps.location = `${dataProps?.city?.name}, ${dataProps?.city?.state?.country?.code}`; //
            } else if (dataProps?.country?.slug) {
              dataProps.link = `/${dataProps?.country?.slug}/${dataProps?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.nameSlug}`; //
              dataProps.location = `${dataProps?.city?.name}, ${dataProps?.country?.code}`; //
            } else {
              dataProps.link = `/${dataProps?.countrySlug}/${dataProps?.stateSlug}/${dataProps?.citySlug}/${dataProps?.nameSlug}`; //
              dataProps.location = `${dataProps?.cityName}, ${dataProps?.countryName},`; //
            }
            dataProps.imageSrc = FinalImageGenerator(dataProps?.images?.[0]);
            return BeachCard({ data: { ...dataProps, ...dataProps } });
          }}
          // xs={1}
          xs={2}
          sm={2}
          md={3}
          lg={4}
          xl={5}
        />
        {Blogs?.rows?.length ? <Country_Blog_Section data={Blogs?.rows} /> : ""}
        <Filter_List_Section
          data={Listicales}
          details={{ title: `Beach` }}
        // details={{ title: `${IslandData?.country?.name} Beaches` }}
        />
      </CustomContainer>
    </>
  );
};
export async function generateMetadata({ params, searchParams }, parent) {
  const { data: MetaData } = await IslandgetislandHeaderImage(
    params
  );
  return {
    title: !!MetaData?.title ? MetaData?.title : `Sandee - Choose Your Beach, Attraction, Hotels and Restaurants ${slugConverter(
      params?.islandSlug,
      true
    )}`,
    description: !!MetaData?.metaDescription ? MetaData?.metaDescription :
      `Discover beaches in ${MetaData?.name || "Island"} with Sandee – the premier and most comprehensive beach database. Explore over 100 categories of beach info, including parking, access, amenities, attractions, weather, and more in ${MetaData?.name || "Island"}.`,     // "Sandee is the premier and most comprehensive database and information source. We feature 100 different categories of beach information, including beach location, beach parking, beach access, amenities, beach activities, beach attractions, beach weather, and many more beach activities and beach attractions.",
    alternates: {
      canonical: `${WEBSITE_URL}/island/${params?.islandSlug}`,
    },
  };
}

export default IslandPage;

export const IslandgetAllIsland = withErrorHandling(async (query) => {
  const APIIslandsAllBeaches = `${API_BASE_URL}/beachMain/getAllForUsers${buildQueryString(
    query
  )}`;
  const response = await axios.get(APIIslandsAllBeaches);
  return response?.data;
});

export const IslandgetislandHeaderImage = withErrorHandling(async (data) => {
  const response = await axios.get(
    `${API_BASE_URL}/islands/getBySlug${buildQueryString(data)}`
  );

  return response?.data;
});

export const IslandgetBlogCountryWise = withErrorHandling(async (query) => {
  const APIBlogCountry = `${API_BASE_URL}/blog${buildQueryString(query)}`;
  const response = await axios.get(APIBlogCountry);

  return response?.data;
});

export const getListiclesCountryWise = withErrorHandling(async (query) => {
  const APIListicles = `${API_BASE_URL}/listiclesMain/getRelatedBlog${buildQueryString(
    query
  )}`;
  const response = await axios.get(APIListicles);

  return response?.data;
});
