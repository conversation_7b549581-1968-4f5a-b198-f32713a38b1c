"use server";

import { API_BASE_URL, buildQueryString } from "@/helper/functions";
import axios from "axios";
import { withErrorHandling } from "@/app/(HeaderSlim)/(Single Country Page)/[countrySlug]/action";

export const CountryGetAllCountry = withErrorHandling(
  async (data = { limit: 30 }) => {
    //  const response = await axios.get(
    //   `${API_BASE_URL}/countries/getAllCountryStateCity${buildQueryString(
    //     data
    //   )}`
    // );
    const response = await axios.get(
      `${API_BASE_URL}/countries${buildQueryString(
        data
      )}`
    );
    return response?.data;
  }
);
