"use server";

import { API_BASE_URL, buildQueryString } from "@/helper/functions";
import axios from "axios";

// Helper function for error handling (not exported as it's not a server action)
const handleError = (error) => {
  return { data: null, error: true };
};

export async function CountryGetAllCountry(data = { limit: 30 }) {
  try {
    //  const response = await axios.get(
    //   `${API_BASE_URL}/countries/getAllCountryStateCity${buildQueryString(
    //     data
    //   )}`
    // );
    const response = await axios.get(
      `${API_BASE_URL}/countries${buildQueryString(
        data
      )}`
    );
    return response?.data;
  } catch (error) {
    return handleError(error);
  }
}
