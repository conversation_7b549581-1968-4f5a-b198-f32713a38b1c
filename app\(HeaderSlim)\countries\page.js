import Country_Top_Country_Section from "@/components/CountriesPage/CountriesTopCountrySection";
import { CustomContainer } from "@/components/Custom-Display";
import React from "react";
import { CountryGetAllCountry } from "./action";
import Country_All_Country_Section from "@/components/CountriesPage/CountriesAllCountrySection";
import CountriesHeaderSection from "@/components/CountriesPage/CountriesHeaderSection";
import BreadCumber from "@/components/Common/BreadCumber";
import {
  HomegetAllBlog,
  HomegetAllListicle,
  HomegetCountries,
} from "@/app/(HomeHeader)/page";
import Home_Listcle_Section from "@/components/HomePage/HomeListcle";
import Home_Blog_Section from "@/components/HomePage/HomeBlog";
import { SiteDataPageWise } from "@/data/siteMetadata";
import CountriesPageJSONLD from "@/components/CountriesPage/CountriesPageJSONLD";
import { EnvTrueFalse } from "@/helper/functions";

const page = async () => {
  // const CountryData = {};
  const [
    { data: CountryData },
    { data: listicleData },
    { data: blogData },
    { data: countryData },
  ] = await Promise.all([
    CountryGetAllCountry({
      limit: 300,
      page: 1,
    }),
    HomegetAllListicle(),
    HomegetAllBlog(),
    HomegetCountries(),
  ]);

  const FullCount = {
    limit: 20,
    page: 1,
    count: CountryData?.count,
    totalpages: Math.ceil(CountryData?.count / 20),
    query: {
      limit: 20,
    },
  };

  return (
    <>
      {/* <Header
        type={3}
        name="Countries"
        imageSrc="https://images.sandee.com/images/header/Country-HeadPage.avif"
      /> */}
      <CountriesPageJSONLD />
      <CountriesHeaderSection
        name="Countries"
        // imageSrc="https://images.sandee.com/images/header/Country-HeadPage.avif"
        imageSrc={"/static/images/header/Countries.png"}
        // imageSrc={"/static/images/header/Countries.avif"}
        description="Our complete guide to the world's best beaches allows you to choose your perfect beach and plan your next beach vacation by reviewing photos, activities,  amenities, attractions, hotels, and restaurants for every beach in the world."
      />
      <div className="  w-full pt-5 pb-3 flex items-center">
        {/* <div className=" bg-gray-100 w-full p-4 flex items-center"> */}
        <CustomContainer>
          <BreadCumber
            data={[
              {
                title: "All Countries",
              },
            ]}
          />
        </CustomContainer>
      </div>
      <CustomContainer className="">
        <Country_Top_Country_Section data={countryData} />
      </CustomContainer>
      <Home_Listcle_Section data={listicleData} className={"!my-0"} index={0} />
      {/* <Home_Listcle_Section data={listicleData} /> */}
      <CustomContainer>
        <Country_All_Country_Section
          AllCountries={CountryData?.rows}
          FullCount={FullCount}
        />
        <section className="mt-8">
          {blogData?.rows?.length ? (
            <Home_Blog_Section data={blogData?.rows} />
          ) : (
            ""
          )}
        </section>
      </CustomContainer>
    </>
  );
};

export default page;
// export const revalidate = 3600;
// export const dynamic = "force-dynamic";

export function generateMetadata() {
  return SiteDataPageWise.countries;
}

export const generateStaticParams = async () => {
  return []
}

export const revalidate = 43200;

export const dynamicParams =
  EnvTrueFalse?.[process.env.NEXT_PUBLIC_DYNAMIC_PARAMS] ?? true;

// export function chunkArray(array, chunkSize) {
//   const chunks = [];
//   for (let i = 0; i < array.length; i += chunkSize) {
//     chunks.push(array.slice(i, i + chunkSize));
//   }
//   return chunks;
// }
