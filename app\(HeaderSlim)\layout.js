// import {Suspense} from "react";
// import NotFound from "../not-found";

import Footer from "@/components/Footer";
import Header from "@/components/Header";
import { extractMainRoute } from "@/helper/functions";
import { headers } from 'next/headers';

export default function Header2Layout(props) {
  const { children } = props;
  // const headersList = headers();
  // const header_url = headersList.get('x-url') || "";
  // const pathname=extractMainRoute(header_url);
  // console.log(pathname)
  return (
    <>
      <Header type={2} />
      {children}
      <Footer/>
      {/* {pathname !== 'map' ?  <Footer />  :null} */}
      {/* <Suspense fallback={<NotFound />}>{children}</Suspense> */}
    </>
  );
}
