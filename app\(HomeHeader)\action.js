"use server";

import { API_BASE_URL, buildQueryString } from "@/helper/functions";
import axios from "axios";

// Helper function for error handling (not exported as it's not a server action)
const handleError = (error) => {
  return { data: null, error: true };
};

export async function HomegetAllListicle() {
  try {
    const response = await axios.get(`${API_BASE_URL}/listiclesMain/getForHome`);
    return response?.data;
  } catch (error) {
    return handleError(error);
  }
}

export async function ListgetAllListicle(data) {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/listiclesMain/getRelatedBlog${buildQueryString(data)}`
    );
    return response?.data;
  } catch (error) {
    return handleError(error);
  }
}

export async function HomegetAllBlog() {
  try {
    const response = await axios.get(
      // `${API_BASE_URL}/listiclesMain/getListForHomeV2`
      `${API_BASE_URL}/blog/getForUserHome`
    );
    return response?.data;
  } catch (error) {
    return handleError(error);
  }
}

export async function postSocialAuth(data) {
  try {
    const response = await axios.post(`${API_BASE_URL}/user/social-auth`, data);
    return response?.data;
  } catch (error) {
    return handleError(error);
  }
}

export async function getMyProfile(data) {
  try {
    const response = await axios.get(`${API_BASE_URL}/user/me`, {
      headers: {
        "Content-Type": "application/json",
        Authorization: "Bearer " + data,
      },
    });
    return response?.data;
  } catch (error) {
    return handleError(error);
  }
}

export async function postlogIn(data) {
  try {
    const response = await axios.post(`${API_BASE_URL}/admin/user/login`, data);
    return response?.data;
  } catch (error) {
    return handleError(error);
  }
}

export async function postSignUp(data) {
  try {
    const response = await axios.post(`${API_BASE_URL}/user/signup`, data);
    return response?.data;
  } catch (error) {
    return handleError(error);
  }
}

export async function postSuggestion(data, token) {
  try {
    if (!!!token) {
      const response = await axios.post(
        `${API_BASE_URL}/beach/suggestions`,
        data
      );
      return response?.data;
    } else {
      const response = await axios.post(
        `${API_BASE_URL}/beach/suggestions/protected`,
        data,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + token,
          },
        }
      );

      return response?.data;
    }
  } catch (error) {
    return handleError(error);
  }
}

export async function postReview(data = {}, token) {
  try {
    const response = await axios.post(`${API_BASE_URL}/beachReview`, data, {
      headers: {
        "Content-Type": "application/json",
        Authorization: "Bearer " + token,
      },
    });
    return response?.data;
  } catch (error) {
    console.log(error, 'errororoor');
    return handleError(error);
  }
}

export async function licenseData(data = {}, token) {
  try {
    const response = await axios.post(`${API_BASE_URL}/licenseData`, data, {
      headers: {
        "Content-Type": "application/json",
        Authorization: "Bearer " + token,
      },
    });

    return response?.data;
  } catch (error) {
    return handleError(error);
  }
}

export async function postImages(data, token) {
  try {
    const response = await axios.post(`${API_BASE_URL}/image`, data, {
      headers: {
        "Content-Type": "multipart/form-data",
        Authorization: "Bearer " + token,
      },
    });
    return response?.data;
  } catch (error) {
    return handleError(error);
  }
}

export async function ContactContactUS(payload) {
  try {
    const response = await axios.post(`${API_BASE_URL}/contactUs`, payload);
    return response?.data;
  } catch (error) {
    return handleError(error);
  }
}

export async function postComment(data = {}, token) {
  try {
    const response = await axios.post(`${API_BASE_URL}/blogComment`, data, {
      headers: {
        "Content-Type": "application/json",
        Authorization: "Bearer " + token,
      },
    });
    // logController(response);
    return response?.data;
  } catch (error) {
    return handleError(error);
  }
}

export async function getComment(data) {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/blogComment?blogId=${data}`
    );

    return response?.data;
  } catch (error) {
    return handleError(error);
  }
}

export async function FooterSubscribe(payload) {
  try {
    const response = await axios.post(`${API_BASE_URL}/subscribeEmail`, payload);
    return response?.data;
  } catch (error) {
    return handleError(error);
  }
}

export async function getBlogLikeCount(data) {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/blog/counts/${data?.slug}`,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: data?.token ? `Bearer ${data?.token}` : null,
        },
      }
    );
    return response?.data;
  } catch (error) {
    return handleError(error);
  }
}

export async function postBlogLike(data = {}, token) {
  try {
    const response = await axios.post(`${API_BASE_URL}/blogLike`, data, {
      headers: {
        "Content-Type": "application/json",
        Authorization: "Bearer " + token,
      },
    });
    // logController(response);
    return response?.data;
  } catch (error) {
    return handleError(error);
  }
}

export async function postBlogWhishlist(data = {}, token) {
  try {
    const response = await axios.post(`${API_BASE_URL}/blogWishlist`, data, {
      headers: {
        "Content-Type": "application/json",
        Authorization: "Bearer " + token,
      },
    });
    return response?.data;
  } catch (error) {
    return handleError(error);
  }
}

export async function SearchElastic(data) {
  try {
    const { search, value } = data;
    const response = await axios.get(
      `${API_BASE_URL}/beachMain/${search}/${value}`
    );

    const result =
      response?.data?.data?.map((item) => ({
        ...item,
      })) || [];
    return result;
  } catch (error) {
    return handleError(error);
  }
}

export async function SearchElasticForMap(data) {
  try {
    const { search, value } = data;
    const response = await axios.get(
      `${API_BASE_URL}/beachMain/${search}/${value}`
    );

    return response;
  } catch (error) {
    return handleError(error);
  }
}
export async function PostSearchForMap(data, query) {
  try {
    const response = await axios.post(
      `${API_BASE_URL}/beachMain/findBeachesInSquare${buildQueryString(query)}`,
      data,
      {
        headers: {
          "Content-Type": "application/json",
          // Authorization: "Bearer " ,
        },
      }
    );
    return response?.data;
  } catch (error) {
    console.log(error, "error");
    return handleError(error);
  }
}

export async function PostSharkForMap(data, token, query) {
  try {
    const response = await axios.post(
      `${API_BASE_URL}/shark-attacks/getSharkAttacksForMap${buildQueryString(
        query
      )}`,
      data,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: "Bearer " + token,
        },
      }
    );
    return response?.data;
  } catch (error) {
    console.log(error, "error");
    return handleError(error);
  }
}

export async function getSharkSpeciesList() {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/shark-species/getSharkSpeciesName`
    );
    return response?.data;
  } catch (error) {
    console.log(error, "error");
    return handleError(error);
  }
}

export async function GetSharkAttackeByLatLong(query, latlong, token) {
  try {
    // console.log(`${API_BASE_URL}/shark-attacks${buildQueryString(
    //   query
    // )}` + `&coordinates=${latlong}`, "kmhjh")
    // /shark-attacks/?page=1&limit=5&coordinates=33.6897725,-78.8816
    const response = await axios.get(
      `${API_BASE_URL}/shark-attacks${buildQueryString(query)}` +
      `&coordinates=${latlong}`,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: "Bearer " + token,
        },
      }
    );
    return response?.data;
  } catch (error) {
    console.log(error, "error");
    return handleError(error);
  }
}

export async function GetSharkAttackeByID(id, token, query) {
  try {
    // console.log(`${API_BASE_URL}/shark-attacks${id ? `/${id}` : ""}${buildQueryString(
    //   query
    // )}`)
    const response = await axios.get(
      `${API_BASE_URL}/shark-attacks${id ? `/${id}` : ""}${buildQueryString(
        query
      )}`,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: "Bearer " + token,
        },
      }
    );
    return response?.data;
  } catch (error) {
    console.log(error, "error");
    return handleError(error);
  }
}
