"use server";

import { API_BASE_URL, buildQueryString } from "@/helper/functions";
import axios from "axios";
import { withErrorHandling } from "@/app/(HeaderSlim)/(Single Country Page)/[countrySlug]/action";

export const HomegetAllListicle = withErrorHandling(async () => {
  const response = await axios.get(`${API_BASE_URL}/listiclesMain/getForHome`);
  return response?.data;
});

export const ListgetAllListicle = withErrorHandling(async (data) => {
  const response = await axios.get(
    `${API_BASE_URL}/listiclesMain/getRelatedBlog${buildQueryString(data)}`
  );
  return response?.data;
});

export const HomegetAllBlog = withErrorHandling(async () => {
  const response = await axios.get(
    // `${API_BASE_URL}/listiclesMain/getListForHomeV2`
    `${API_BASE_URL}/blog/getForUserHome`
  );
  return response?.data;
});

export const postSocialAuth = withErrorHandling(async (data) => {
  const response = await axios.post(`${API_BASE_URL}/user/social-auth`, data);
  return response?.data;
});
export const getMyProfile = withErrorHandling(async (data) => {
  const response = await axios.get(`${API_BASE_URL}/user/me`, {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + data,
    },
  });
  return response?.data;
});

export const postlogIn = withErrorHandling(async (data) => {
  const response = await axios.post(`${API_BASE_URL}/admin/user/login`, data);
  return response?.data;
});
export const postSignUp = withErrorHandling(async (data) => {
  const response = await axios.post(`${API_BASE_URL}/user/signup`, data);

  return response?.data;
});

export const postSuggestion = withErrorHandling(async (data, token) => {
  if (!!!token) {
    const response = await axios.post(
      `${API_BASE_URL}/beach/suggestions`,
      data
    );
    return response?.data;
  } else {
    const response = await axios.post(
      `${API_BASE_URL}/beach/suggestions/protected`,
      data,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: "Bearer " + token,
        },
      }
    );

    return response?.data;
  }
});

export const postReview = withErrorHandling(async (data = {}, token) => {

  try {
    const response = await axios.post(`${API_BASE_URL}/beachReview`, data, {
      headers: {
        "Content-Type": "application/json",
        Authorization: "Bearer " + token,
      },
    });
    return response?.data;
  } catch (error) {
    console.log(error, 'errororoor')
  }
});

export const licenseData = withErrorHandling(async (data = {}, token) => {
  const response = await axios.post(`${API_BASE_URL}/licenseData`, data, {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + token,
    },
  });

  return response?.data;
});

export const postImages = withErrorHandling(async (data, token) => {
  const response = await axios.post(`${API_BASE_URL}/image`, data, {
    headers: {
      "Content-Type": "multipart/form-data",
      Authorization: "Bearer " + token,
    },
  });
  return response?.data;
});

export const ContactContactUS = withErrorHandling(async (payload) => {
  const response = await axios.post(`${API_BASE_URL}/contactUs`, payload);
  return response?.data;
});

export const postComment = withErrorHandling(async (data = {}, token) => {
  const response = await axios.post(`${API_BASE_URL}/blogComment`, data, {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + token,
    },
  });
  // logController(response);
  return response?.data;
});

export const getComment = withErrorHandling(async (data) => {
  const response = await axios.get(
    `${API_BASE_URL}/blogComment?blogId=${data}`
  );

  return response?.data;
});

export const FooterSubscribe = withErrorHandling(async (payload) => {
  const response = await axios.post(`${API_BASE_URL}/subscribeEmail`, payload);
  return response?.data;
});

export const getBlogLikeCount = withErrorHandling(async (data) => {
  const response = await axios.get(
    `${API_BASE_URL}/blog/counts/${data?.slug}`,
    {
      headers: {
        "Content-Type": "application/json",
        Authorization: data?.token ? `Bearer ${data?.token}` : null,
      },
    }
  );
  return response?.data;
});
export const postBlogLike = withErrorHandling(async (data = {}, token) => {
  const response = await axios.post(`${API_BASE_URL}/blogLike`, data, {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + token,
    },
  });
  // logController(response);
  return response?.data;
});

export const postBlogWhishlist = withErrorHandling(async (data = {}, token) => {
  const response = await axios.post(`${API_BASE_URL}/blogWishlist`, data, {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + token,
    },
  });
  return response?.data;
});

export const SearchElastic = withErrorHandling(async (data) => {
  const { search, value } = data;
  const response = await axios.get(
    `${API_BASE_URL}/beachMain/${search}/${value}`
  );

  const result =
    response?.data?.data?.map((item) => ({
      ...item,
    })) || [];
  return result;
});
export const SearchElasticForMap = withErrorHandling(async (data) => {
  const { search, value } = data;
  const response = await axios.get(
    `${API_BASE_URL}/beachMain/${search}/${value}`
  );

  return response;
});
export const PostSearchForMap = withErrorHandling(async (data, query) => {
  try {
    const response = await axios.post(
      `${API_BASE_URL}/beachMain/findBeachesInSquare${buildQueryString(query)}`,
      data,
      {
        headers: {
          "Content-Type": "application/json",
          // Authorization: "Bearer " ,
        },
      }
    );
    return response?.data;
  } catch (error) {
    console.log(error, "error");
  }
});
export const PostSharkForMap = withErrorHandling(async (data, token, query) => {
  try {
    const response = await axios.post(
      `${API_BASE_URL}/shark-attacks/getSharkAttacksForMap${buildQueryString(
        query
      )}`,
      data,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: "Bearer " + token,
        },
      }
    );
    return response?.data;
  } catch (error) {
    console.log(error, "error");
  }
});

export const getSharkSpeciesList = withErrorHandling(async () => {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/shark-species/getSharkSpeciesName`
    );
    return response?.data;
  } catch (error) {
    console.log(error, "error");
  }
});

export const GetSharkAttackeByLatLong = withErrorHandling(
  async (query, latlong, token) => {
    try {
      // console.log(`${API_BASE_URL}/shark-attacks${buildQueryString(
      //   query
      // )}` + `&coordinates=${latlong}`, "kmhjh")
      // /shark-attacks/?page=1&limit=5&coordinates=33.6897725,-78.8816
      const response = await axios.get(
        `${API_BASE_URL}/shark-attacks${buildQueryString(query)}` +
        `&coordinates=${latlong}`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + token,
          },
        }
      );
      return response?.data;
    } catch (error) {
      console.log(error, "error");
    }
  }
);

export const GetSharkAttackeByID = withErrorHandling(
  async (id, token, query) => {
    try {
      // console.log(`${API_BASE_URL}/shark-attacks${id ? `/${id}` : ""}${buildQueryString(
      //   query
      // )}`)
      const response = await axios.get(
        `${API_BASE_URL}/shark-attacks${id ? `/${id}` : ""}${buildQueryString(
          query
        )}`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + token,
          },
        }
      );
      return response?.data;
    } catch (error) {
      console.log(error, "error");
    }
  }
);
