import "../globals.css";
// import 'pliny/search/algolia.css'

// import { Space_Grotesk } from "next/font/google";
// import { Analytics, AnalyticsConfig } from 'pliny/analytics'
// import { SearchProvider, SearchConfig } from 'pliny/search'
import Header from "@/components/Header";
import { siteMetadata } from "@/data/siteMetadata";
import { Suspense } from "react";
import NotFound from "@/app/not-found";
import { WEBSITE_URL } from "@/helper/functions";
import Footer from "@/components/Footer";
// import { ThemeProviders } from "./theme-providers";

// const space_grotesk = Space_Grotesk({
//   subsets: ["latin"],
//   display: "swap",
//   variable: "--font-space-grotesk",
// });

export const metadata = {
  metadataBase: new URL(siteMetadata.siteUrl),
  title: {
    default: siteMetadata.title,
    template: `%s | ${siteMetadata.title}`,
  },
  description: siteMetadata.description,
  openGraph: {
    title: siteMetadata.ogTitle,
    description: siteMetadata.description,
    url: `${WEBSITE_URL}`,
    siteName: siteMetadata.ogTitle,
    images: [siteMetadata.socialBanner],
    locale: "en_US",
    type: "website",
  },
  alternates: {
    canonical: `${WEBSITE_URL}`,
    types: {
      "application/rss+xml": `${siteMetadata.siteUrl}/feed.xml`,
    },
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  twitter: {
    title: siteMetadata.title,
    card: "summary_large_image",
    images: [siteMetadata.socialBanner],
  },
};

export default function RootLayout(props) {
  // const headersList = headers();

  const { children } = props;
  return (
    <>
      <Header type={4} />
      <Suspense fallback={<NotFound />}>{children}</Suspense>
      <Footer />
      {/* <main className="mb-auto">{children}</main> */}
      {/* </SearchProvider> */}
    </>
  );
}
