import { CustomContainer } from "@/components/Custom-Display";
import Home_Blog_Section from "@/components/HomePage/HomeBlog";
import Home_Bottom_Button from "@/components/HomePage/HomeBottomButton";
import Home_Country_Section from "@/components/HomePage/HomeCountry";
import Home_Filter_Component from "@/components/HomePage/HomeFilterComponent";
import Home_Listcle_Section from "@/components/HomePage/HomeListcle";
import React from "react";
import Home_List_Section from "@/components/HomePage/HomeListSection";
import { API_BASE_URL, EnvTrueFalse } from "@/helper/functions";
import axios from "axios";
import { withErrorHandling } from "@/app/(HeaderSlim)/(Single Country Page)/[countrySlug]/action";
import HomeJSONLD from "@/components/HomePage/HomeJSONLD";
// import dynamic from "next/dynamic";
// import MobileBottomNav from "@/components/MobileBottomNav";
import HomeBeachNearMe from "@/components/HomePage/HomeBeachNearMe";
// import { BeachIcon } from "@/components/social-icons/icons";
// const HomeBeachNearMe = dynamic(() => import('@/components/HomePage/HomeBeachNearMe'), { ssr: false });
// import LoadingPlaceholder from '@/components/Common/LoadingPlaceholder';

// const HomeBeachNearMe = dynamic(
//   () => import('@/components/HomePage/HomeBeachNearMe'),
//   {
//     ssr: false,
//     loading: () => <div className="animate-pulse h-[265px] bg-slate-200 mt-5"></div> // ✅ Explicit loading placeholder
//   }
// );
const page = async () => {
  const [
    { data: listicleData },
    { data: blogData },
    { data: alllists },
    { data: filterAnaytics },
    { data: countriesData },
  ] = await Promise.all([
    HomegetAllListicle(),
    HomegetAllBlog(),
    HomegetAllLists(),
    HomegetAllFilterAnalytics(),
    HomegetCountries(),
  ]);
  // const { data: listicleData } = await HomegetAllListicle();
  // const { data: blogData } = await HomegetAllBlog();
  // const { data: alllists } = await HomegetAllLists();
  // const { data: filterAnaytics } = await HomegetAllFilterAnalytics();
  return (
    <>
      <HomeJSONLD />
      <HomeBeachNearMe />
      <CustomContainer>
        <Home_Country_Section data={countriesData} />
      </CustomContainer>

      <Home_Listcle_Section data={listicleData} className={"!my-0"} index={0} />
      <section className="bg-[#faebd75e] md:rounded-[50px] rounded-[25px] !my-[15px]">
        <CustomContainer className="py-[15px]">
          <Home_Filter_Component data={filterAnaytics?.counts} />
        </CustomContainer>
      </section>

      <Home_Listcle_Section data={listicleData} className={"!my-0"} index={1} />
      <section className="">
        <CustomContainer>
          {blogData?.rows?.length ? (
            <Home_Blog_Section data={blogData?.rows} />
          ) : (
            ""
          )}
          {alllists?.length && <Home_List_Section data={alllists} />}
          <Home_Bottom_Button />
        </CustomContainer>
      </section>
    </>
  );
};

export default page;
// export const revalidate = 3600; // 1hr after re-call Api's
export const dynamic = "force-dynamic";

export const HomegetAllListicle = withErrorHandling(async () => {
  const response = await axios.get(`${API_BASE_URL}/listiclesMain/getForHome`);
  return response?.data;
});
export const HomegetAllFilterAnalytics = withErrorHandling(async () => {
  const response = await axios.get(
    `${API_BASE_URL}/beachMain/getUserHomeOverview`
  );
  return response?.data;
});
export const HomegetAllBlog = withErrorHandling(async () => {
  console.log(`${API_BASE_URL}/blog/getForUserHome`)
  const response = await axios.get(
    // `${API_BASE_URL}/listiclesMain/getListForHomeV2`
    `${API_BASE_URL}/blog/getForUserHome`
  );
  return response?.data;
});

export const HomegetAllLists = withErrorHandling(async () => {
  const response = await axios.get(
    // `${API_BASE_URL}/listiclesMain/getListForHomeV2`
    `${API_BASE_URL}/listiclesMain/getBlogForHomeV2`
  );
  return response?.data;
});
export const HomegetCountries = withErrorHandling(async () => {
  // console.log(`${API_BASE_URL}/countries/getHomeCountries`)
  try {
    const response = await axios.get(
      `${API_BASE_URL}/countries/getHomeCountries`
    );
    return response?.data;
  } catch (error) {
    console.log(error);
    return [];
  }
});

// export const generateStaticParams = async () => {
//   return []
// }

// export const revalidate = 43200;

// export const dynamicParams =
//   EnvTrueFalse?.[process.env.NEXT_PUBLIC_DYNAMIC_PARAMS] ?? true;