// // import MapWithBeaches from "@/components/map/MapWithBeaches";
// import { SiteDataPageWise } from "@/data/siteMetadata";
// import React from "react";
// import { withErrorHandling } from "../../(HeaderSlim)/(Single Country Page)/[countrySlug]/action";
// import { API_BASE_URL, WEBSITE_URL } from "@/helper/functions";
// import axios from "axios";
// import MapPageJSONLD from "@/components/map/MapPageJSONLD";
// import SingleBeachMapJSONLD from "@/components/map/SingleBeachMapJSONLD";
// import { redirect } from "next/navigation";
// import MapWithBeachesNew from "@/components/map/MapWithBeachesNew";

const page = async (props) => {

  return null
};

export default page;
