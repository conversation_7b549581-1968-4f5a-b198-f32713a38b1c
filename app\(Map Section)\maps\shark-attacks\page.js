// import MapWithBeaches from "@/components/map/MapWithBeaches";
import { SiteDataPageWise } from "@/data/siteMetadata";
import React from "react";
import { withErrorHandling } from "../../../(HeaderSlim)/(Single Country Page)/[countrySlug]/action";
import { API_BASE_URL, WEBSITE_URL } from "@/helper/functions";
import axios from "axios";
import MapPageJSONLD from "@/components/map/MapPageJSONLD";
import { redirect } from "next/navigation";
import MapWithBeachesNew from "@/components/map/MapWithBeachesNew";
import SingleBeachSharkMapJSON from "@/components/map/SingleBeachSharkMapJSON";
import MapWithBeachesNewMob from "@/components/map/MapWithBeachesNewMob";
import MapResposive from "@/components/map/MapResposive";

const page = async (props) => {
    const decodedCoordinates = decodeURIComponent(
        props?.searchParams?.coordinates
    );
    const [latStr, longStr] = decodedCoordinates?.substring(1)?.split(",");
    const getSingleBeachMapCoordinateData =
        latStr && longStr
            ? await getSingleBeachMapCoordinate(`${latStr},${longStr}`)
            : {};
    // if (
    //     latStr &&
    //     longStr &&
    //     getSingleBeachMapCoordinateData?.data?.length === 0
    // ) {
    //     redirect("/");
    // }
    return (
        <div className="">
            {/* {latStr && longStr ? (
                <SingleBeachSharkMapJSON
                    mapData={{
                        beachName: getSingleBeachMapCoordinateData?.data?.length
                            ? getSingleBeachMapCoordinateData?.data[0]?.name
                            : "",
                        beachSlug: props?.searchParams?.beachName,
                        coordinates: props?.searchParams?.coordinates,
                    }}
                />
            ) : ( */}
            <SingleBeachSharkMapJSON />
            {/* )} */}
            {/* <link
        href="https://api.mapbox.com/mapbox-assembly/v1.5.1/assembly.min.css"
        rel="stylesheet"
      /> */}
            <link
                href="https://api.tiles.mapbox.com/mapbox-gl-js/v1.11.1/mapbox-gl.css"
                rel="stylesheet"
            />
            {/* <script
        async
        defer
        src="https://api.mapbox.com/mapbox-assembly/v1.5.1/assembly.js"
      ></script> */}
            {/* <MapWithBeaches
        latStr={Number(latStr) || null}
        longStr={Number(longStr) || null}
        popupBeachData={
          getSingleBeachMapCoordinateData?.data?.length
            ? getSingleBeachMapCoordinateData?.data[0]
            : {}
        }
      />  */}
            <MapResposive
                latStr={latStr}
                longStr={longStr}
                getSingleBeachMapCoordinateData={
                    getSingleBeachMapCoordinateData}
                mode={"shark"}
                isFilter={false}
            />
            {/* <div className="hidden sm:block">
                <MapWithBeachesNew
                    latStr={Number(latStr) || null}
                    longStr={Number(longStr) || null}
                    popupBeachData={
                        getSingleBeachMapCoordinateData?.data?.length
                            ? getSingleBeachMapCoordinateData?.data[0]
                            : {}
                    }
                    mode={"shark"}
                //   isFilter={true}
                // isSharkToggle={true}
                // isNudeToggle={true}
                />
            </div>
            <div className="block sm:hidden">
                <MapWithBeachesNewMob
                    latStr={Number(latStr) || null}
                    longStr={Number(longStr) || null}
                    popupBeachData={
                        getSingleBeachMapCoordinateData?.data?.length
                            ? getSingleBeachMapCoordinateData?.data[0]
                            : {}
                    }
                    mode={"shark"}
                //   isFilter={true}
                />
            </div> */}
            {/* <MapWithBeachesNew
                latStr={Number(latStr) || null}
                longStr={Number(longStr) || null}
                popupBeachData={
                    getSingleBeachMapCoordinateData?.data?.length
                        ? getSingleBeachMapCoordinateData?.data[0]
                        : {}
                }
                mode={"shark"}
            // isFilter={false}
            // isSharkToggle={false}
            // isNudeToggle={false}
            /> */}
        </div>
    );
};

export default page;
export const getSingleBeachMapCoordinate = withErrorHandling(async (data) => {
    const APISingleBeachesMap = `${API_BASE_URL}/beachMain/getBeachByGeoLoc?coordinates=${data}`;
    // logController(APICountryAllBeaches);
    const response = await axios.get(APISingleBeachesMap);
    return response?.data;
});

export async function generateMetadata(props) {
    // const decodedCoordinates = decodeURIComponent(
    //     props?.searchParams?.coordinates
    // );
    // const [latStr, longStr] = decodedCoordinates?.substring(1)?.split(",");
    // if (latStr && longStr) {
    //     const getSingleBeachMapCoordinateSEO = withErrorHandling(async (data) => {
    //         const APISingleBeachesMap = `${API_BASE_URL}/beachMain/getBeachByGeoLoc/forSeo?coordinates=${data}`;
    //         const response = await axios.get(APISingleBeachesMap);
    //         return response?.data;
    //     });
    //     const MetaData = await getSingleBeachMapCoordinateSEO(
    //         `${latStr},${longStr}`
    //     );
    //     const beachName = MetaData?.data?.name ?? "";
    //     const beachLocation = `${MetaData?.data?.city?.name ?? ""}, ${MetaData?.data?.state?.name ?? ""
    //         }, ${MetaData?.data?.country?.name ?? ""} `;

    //     return {
    //         title: `${beachName} Map, ${beachLocation} | Sandee`,
    //         description: `${beachName}: Discover this iconic ${beachLocation} location with our interactive map. Explore nearby attractions, amenities, and more with Sandee.`,
    //         alternates: {
    //             canonical: `${WEBSITE_URL}/map/${props?.searchParams?.beachName}/${props?.searchParams?.coordinates}`,
    //         },
    //     };
    // } else {
    return SiteDataPageWise.sharkMap;
    // }
}
