import { revalidatePath } from 'next/cache'

export async function POST(request) {
    const origin = request.headers.get('origin')

    // Set CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Max-Age': '86400', // 24 hours
    }

    // Handle OPTIONS request (preflight)
    if (request.method === 'OPTIONS') {
        return new Response(null, {
            headers: headers,
            status: 204
        })
    }

    const req = await request.json()
    let slugs = req?.slugs

    console.log('POST: req body', req)

    if (slugs?.length > 0) {
        slugs.forEach((slug) => {
            revalidatePath(slug)
        })
        return Response.json({ revalidated: true, now: Date.now(), req }, {
            headers: headers,
        })
    }

    return Response.json({
        revalidated: false,
        now: Date.now(),
        message: 'Missing slug to revalidate',
    }, {
        headers: headers,
    })
}