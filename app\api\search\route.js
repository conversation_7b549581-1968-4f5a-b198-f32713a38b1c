import { SearchElasticForMap } from "@/app/(HomeHeader)/action";

export async function GET(req) {
  // Extract the query parameter from the request URL
  const { searchParams } = new URL(req.url);
  const query = searchParams.get('query');


  // Check if the query parameter is present
  if (!query) {
    return new Response(
      JSON.stringify({ error: 'Query parameter is required' }),
      { status: 400 } // Bad Request
    );
  }

  try {
    // Call your search function with the extracted query
    const data = await SearchElasticForMap({ search: 'searchByName', value: query });

    // Ensure you access the data properly based on your axios response
    return Response.json(data.data); // Adjust based on your response structure
  } catch (error) {
    console.error('Error fetching data:', error);
    return new Response(
      JSON.stringify({ error: 'Failed to fetch data' }),
      { status: 500 } // Internal Server Error
    );
  }
}
