@tailwind base;
@tailwind components;
@tailwind utilities;

@import "swiper/css";
@import "swiper/css/pagination";
@import "swiper/css/navigation";
@import "swiper/css/grid";

/* @import url("./font.css");
* {
  font-family: Helvetica, sans-serif;
  font-display: swap;
} */

@import url("./font.css");

* {
  font-family: "Manrope", "normal";
  /* font-family: Arial, Helvetica, sans-serif; */
  font-display: swap;
}

body {
  background: white;
  color: black;
}
:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}
@layer utilities {
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  .cardgradiant {
    background: linear-gradient(
      180deg,
      rgba(0, 0, 0, 0) 60%,
      rgba(0, 0, 0, 0.5) 90%
    );
  }
  .cardgradianthovered {
    background: linear-gradient(
      180deg,
      rgba(0, 0, 0, 0) 30%,
      rgba(0, 0, 0, 0.7) 80%
    );
    transition: all 3s ease-in;
  }
  .cardgradiant-listicle {
    background: linear-gradient(
      180deg,
      rgba(0, 0, 0, 0) 30%,
      rgba(0, 0, 0, 0.5) 90%
    );
  }
  .cardgradianthovered-listicle {
    background: linear-gradient(
      180deg,
      rgba(0, 0, 0, 0) 10%,
      rgba(0, 0, 0, 0.7) 80%
    );
    transition: all 3s ease-in;
  }
}

/* https://stackoverflow.com/questions/61083813/how-to-avoid-internal-autofill-selected-style-to-be-applied */
input:-webkit-autofill,
input:-webkit-autofill:focus {
  transition: background-color 600000s 0s, color 600000s 0s;
}

.content-gradiant-white {
  background: rgba(255, 255, 255, 0)
    linear-gradient(
      to bottom,
      rgba(0, 0, 0, 0) 10%,
      rgba(255, 255, 255, 0.1) 40%,
      rgba(255, 255, 255, 0.5) 75%,
      rgba(255, 255, 255, 1) 100%
    )
    repeat scroll 0 0;
}
.black-layer {
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0) 30%,
    rgba(0, 0, 0, 0.4) 80%
  );
}
/* .content-gradiant-black {
  background: rgba(0, 0, 0, 0)
    linear-gradient(
      to bottom,
      rgba(255, 255, 255, 0) 10%,
      rgba(255, 255, 255, 0) 70%,
      rgba(255, 255, 255, 0.2) 75%,
      rgba(0, 0, 0, 0.4) 100%
    )
    repeat scroll 0 0;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0) 59.37%,
    rgba(0, 0, 0, 0.32) 90.61%
  );
} */
.content-gradiant-black:hover {
  background: transparent;
}

.hoverfullcard {
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0) 60%,
    rgba(0, 0, 0, 0.5) 90%
  );
  /* border-image: fill 0 linear-gradient(#0001, #000); */
}
.hoverfullcard:hover {
  /* background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.13) 0%,
    rgba(0, 0, 0, 0.32) 100%
  ); */
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0) 30%,
    rgba(0, 0, 0, 0.7) 80%
  );
  transition: all 3s ease-in;

  /* background: rgba(0, 0, 0, 0.5); */
}

.tooltip {
  @apply invisible absolute;
}

.has-tooltip:hover .tooltip {
  @apply visible z-50;
}

.swiper-pagination-bullet {
  background: white !important;
  opacity: 0.6 !important;
}

.swiper-pagination-bullet-active {
  background: white !important;
  opacity: 1 !important;
  /* width: 20px !important; */
  border-radius: 50pt !important;
}
.swiper-horizontal > .swiper-pagination-bullets,
.swiper-pagination-bullets.swiper-pagination-horizontal,
.swiper-pagination-custom,
.swiper-pagination-fraction {
  padding-inline: 20px;
}

.sitemap-links {
  @apply flex justify-start w-full hover:underline text-sandee-blue hover:font-bold !line-clamp-1 pr-2;
}
select.round {
  background-image: linear-gradient(45deg, transparent 50%, #00aae3 50%),
    linear-gradient(135deg, #00aae3 50%, transparent 50%);
  background-position: calc(100% - 20px) calc(1em + 2px),
    calc(100% - 15px) calc(1em + 2px), calc(100% - 0.5em) 0.5em;
  background-size: 5px 5px, 5px 5px, 1.5em 1.5em;
  background-repeat: no-repeat;
}

select {
  /* styling */
  background-color: white;
  border-radius: 4px;
  display: inline-block;
  font: inherit;
  line-height: 1.5em;
  padding: 0.5em 3.5em 0.5em 1em;
  cursor: pointer;
  /* reset */

  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-appearance: none;
  -moz-appearance: none;
}

select.round:focus {
  background-image: linear-gradient(45deg, #00aae3 50%, transparent 50%),
    linear-gradient(135deg, transparent 50%, #00aae3 50%);
  background-position: calc(100% - 15px) 1em, calc(100% - 20px) 1em,
    calc(100% - 0.5em) 0.5em;
  background-size: 5px 5px, 5px 5px, 1.5em 1.5em;
  background-repeat: no-repeat;
  border-color: #0194c5;
  outline: 0;
}

.pagination-container {
  @apply flex list-none gap-2 my-5 items-center justify-center flex-wrap;
}
.pagination-container .pagination-item {
  @apply h-10 min-w-10  flex justify-center items-center text-center gap-1 text-black border border-[#F0f0f0]  text-xs font-medium hover:bg-[#f0f0f0]   px-3 py-3 rounded  hover:cursor-pointer;
  /* hover:border-sandee-blue hover:text-sandee-blue */
}
.pagination-container .pagination-item.dots:hover {
  @apply bg-transparent cursor-default;
}
.pagination-container .pagination-item.selected {
  @apply border border-sandee-blue bg-sandee-blue text-white;
}
.pagination-container .pagination-item .arrow::before {
  @apply relative content-[''] inline-block w-2 h-2 border-r-[0.12em] border-r-black group-hover:border-r-sandee-blue text-black group-hover:text-sandee-blue border-t-[0.12em] border-t-black group-hover:border-t-sandee-blue  border-solid;
}
.pagination-container .pagination-item .arrow.left {
  @apply rotate-[-135deg];
}
.pagination-container .pagination-item .arrow.right {
  @apply rotate-45;
}
.pagination-container .pagination-item.disabled {
  @apply pointer-events-none hover:bg-transparent hover:cursor-default;
}
.pagination-container .pagination-item.disabled .arrow::before {
  @apply border-r-[0.12em] border-r-[rgba(0,0,0,0.43)] border-t-[0.12em] border-t-[rgba(0,0,0,0.43)] border-solid;
}

/* .container-dropdown {
  @apply shadow-[0_1px_2px_0_rgba(0,0,0,0.12),0_2px_4px_0_rgba(0,0,0,0.24)] mx-auto my-0 p-5;
} */

.dropdown {
  @apply relative;
}
.dropdown.open .dropdown-menu {
  @apply block;
}
.selected-options {
  @apply min-w-[120px]   py-2 px-5 transition ease-in-out duration-300 text-center text-xs font-medium  rounded-full active:scale-[0.7] hover:scale-110 text-sandee-blue active:text-white active:bg-sandee-blue border-2 border-sandee-blue;
}
.dropdown-menu {
  @apply hidden min-w-[180px] max-h-[350px] overflow-y-scroll translate-x-[-20%] absolute bg-white border w-full z-[1] border-t-[none] border-solid border-sandee-blue rounded-xl mt-1 cursor-pointer;
}
.option {
  @apply p-2 border-b-sandee-blue border-b border-solid gap-1 flex text-sandee-blue;
}

.custom-hover-slide-button {
  @apply justify-center items-center  py-2 px-5 flex relative   overflow-hidden font-bold bg-white text-sandee-orange border border-sandee-orange active:text-white text-sandee-sm h-auto rounded-lg;
}
.custom-hover-slide {
  @apply absolute bottom-0 left-0 flex w-full h-0 mb-0 transition-all duration-200 ease-out transform translate-y-0 bg-sandee-orange  opacity-90;
}
.custom-hover-slide-text {
  @apply relative flex items-center;
}

.custom-hover-blue-slide-button {
  @apply justify-center items-center  py-2 px-5 flex relative   overflow-hidden font-medium bg-white text-sandee-blue border border-sandee-blue active:text-white text-sandee-sm h-auto rounded-lg;
}
.custom-hover-blue-slide {
  @apply absolute bottom-0 left-0 flex w-full h-0 mb-0 transition-all duration-200 ease-out transform translate-y-0 bg-sandee-blue  opacity-90;
}
.custom-hover-blue-slide-text {
  @apply relative flex items-center;
}

.white-logo {
  filter: brightness(0) invert(1);
}
.black-logo {
  /* filter: invert(100%) sepia(16%) saturate(100%) hue-rotate(222deg)
    brightness(119%) contrast(115%); */
  /* filter: invert(10%) sepia(90%) saturate(5268%) hue-rotate(245deg)
    brightness(109%) contrast(155%); */
  /* filter: brightness(0); */
}
/* .swiper-wrapper {
  @apply gap-8;
}
.swiper-slide {
  @apply p-0;
} */
.BoxShadowSearch {
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.12),
    0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 2px 1px -1px rgba(0, 0, 0, 0.2);
}

.pmargin0 p {
  margin: 0;
  padding: 0;
}
.pmargin0 a {
  color: #00aae3 !important;

  text-decoration: none !important;
}
.pmargin0 a:hover {
  text-decoration: underline !important;
  color: #00aae3 !important;
  /* font-weight: bold; */
}

::-webkit-scrollbar {
  width: 7px;
}
::-webkit-scrollbar-track {
  background: #f1f1f1;
  cursor: pointer;
  border-radius: 100pt;
}

::-webkit-scrollbar-thumb {
  background: #888;
  cursor: pointer;
  border-radius: 100pt;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
  cursor: pointer;
  border-radius: 100pt;
}

.custom-auth .container {
  /* max-width: 350px; */
  background: #f8f9fd;
  background: linear-gradient(
    0deg,
    rgb(255, 255, 255) 0%,
    rgb(244, 247, 251) 100%
  );
  border-radius: 40px;
  padding: 25px 35px;
  border: 5px solid rgb(255, 255, 255);
  box-shadow: rgba(133, 189, 215, 0.**********) 0px 30px 30px -20px;
  /* margin: 20px; */
}
.custom-auth .containerB {
  background: #f8f9fd;
  background: linear-gradient(
    0deg,
    rgb(255, 255, 255) 0%,
    rgb(244, 247, 251) 100%
  );
  border-radius: 40px;
  padding: 25px 40px;
  /* border: 5px solid rgb(255, 255, 255); */
  /* box-shadow: rgba(133, 189, 215, 0.**********) 0px 30px 30px -20px; */
  /* margin: 20px; */
}
.custom-authWrapperB {
  background: #f8f9fd;
  background: linear-gradient(
    0deg,
    rgb(255, 255, 255) 0%,
    rgb(244, 247, 251) 100%
  );
  border-radius: 40px;
  padding: 25px 40px;
}
.ant-modal .ant-modal-content {
  @apply !bg-transparent !p-0 !m-0 shadow-none;
}
.custom-auth .heading {
  @apply text-sandee-blue text-sandee-32 font-black text-center;
}

.custom-auth .form {
  @apply mt-5;
}

.custom-auth .form .input {
  @apply w-full bg-white border-none rounded-2xl mt-4 shadow-lg;
  padding: 15px 20px;
  border-inline: 2px solid transparent;
}

.custom-auth .form .input::-moz-placeholder {
  color: rgb(170, 170, 170);
}

.custom-auth .form .input::placeholder {
  color: rgb(170, 170, 170);
}

.custom-auth .form .input:focus {
  outline: none;
  border-inline: 2px solid #00aae3;
}

.custom-auth .form .forgot-password {
  display: block;
  margin-top: 10px;
  margin-left: 10px;
}

.custom-auth .form .forgot-password {
  font-size: 14px;
  text-transform: capitalize;
  cursor: pointer;
  color: #00aae3;
  text-decoration: none;
}

.custom-auth .login-button {
  display: block;
  width: 100%;
  font-weight: bold;
  background: linear-gradient(
    45deg,
    rgb(0, 170, 227) 0%,
    rgb(0, 170, 227) 100%
  );
  color: white;
  padding-block: 15px;
  margin: 20px auto;
  border-radius: 20px;
  box-shadow: rgba(133, 189, 215, 0.**********) 0px 20px 10px -15px;
  border: none;
  transition: all 0.2s ease-in-out;
}

.custom-auth .login-button:hover {
  transform: scale(1.03);
  box-shadow: rgba(133, 189, 215, 0.**********) 0px 23px 10px -20px;
}

.custom-auth .login-button:active {
  transform: scale(0.95);
  box-shadow: rgba(133, 189, 215, 0.**********) 0px 15px 10px -10px;
}

.custom-auth .social-account-container {
  margin-top: 25px;
}

.custom-auth .social-account-container .title {
  display: block;
  text-align: center;
  font-size: 10px;
  color: rgb(170, 170, 170);
}

.custom-auth .social-account-container .social-accounts {
  width: 100%;
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 5px;
}

.custom-auth .social-account-container .social-accounts .social-button {
  background: linear-gradient(45deg, rgb(0, 0, 0) 0%, rgb(112, 112, 112) 100%);
  border: 5px solid white;
  padding: 5px;
  border-radius: 50%;
  width: 40px;
  aspect-ratio: 1;
  display: grid;
  place-content: center;
  box-shadow: rgba(133, 189, 215, 0.**********) 0px 12px 10px -8px;
  transition: all 0.2s ease-in-out;
}

.custom-auth .social-account-container .social-accounts .social-button .svg {
  fill: white;
  margin: auto;
}

.custom-auth .social-account-container .social-accounts .social-button:hover {
  transform: scale(1.2);
}

.custom-auth .social-account-container .social-accounts .social-button:active {
  transform: scale(0.9);
}

.custom-auth .agreement {
  display: block;
  text-align: center;
  margin-top: 15px;
}

.custom-auth .agreement a {
  text-decoration: none;
  color: #00aae3;
  font-size: 9px;
}

.modal-root {
  @apply fixed bottom-0 right-0 z-[1000] max-w-full;
}

.custom-profile-card .tooltip-container {
  position: relative;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 17px;
  border-radius: 50pt;
}

.custom-profile-card .tooltip {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  padding: 10px;
  opacity: 0;
  pointer-events: none;
  transition: all 0.3s;
  border-radius: 15px;
  box-shadow: inset 5px 5px 5px rgba(0, 0, 0, 0.2),
    inset -5px -5px 15px rgba(255, 255, 255, 0.1),
    5px 5px 15px rgba(0, 0, 0, 0.3), -5px -5px 15px rgba(255, 255, 255, 0.1);
}

.custom-profile-card .profile {
  /* background: #3b5998; */
  border-radius: 10px 15px;
  /* padding: 10px; */
  /* border: 1px solid #29487d; */
}

.custom-profile-card .tooltip-container:hover .tooltip {
  top: 45px;
  z-index: 99999;
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
}

.custom-profile-card .icon {
  text-decoration: none;
  color: #fff;
  display: block;
  position: relative;
}

.custom-profile-card .icon .layer {
  width: 45px;
  height: 45px;
  /* border: 3px solid #1877f2; */
  border-radius: 50%;
  transition: transform 0.3s, border 0.3s ease, box-shadow 0.3s ease;
  /* box-shadow: 0 0 15px rgba(24, 119, 242, 0.7), 0 0 20px rgba(24, 119, 242, 0.5); */
}

.custom-profile-card .layer span {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  border: 1px solid #fff;
  border-radius: 50%;
  transition: all 0.3s;
}

.custom-profile-card .user {
  display: flex;
  gap: 10px;
}
.image-profile {
  width: 40px;
  height: 40px;
}
.ytp-chrome-top {
  position: absolute;
  background: white;
  bottom: 0;
  height: fit-content;
  display: flex;
  top: auto;
  width: 100%;
  left: 0;
  border-end-start-radius: 20px;
  border-end-end-radius: 20px;
  border-bottom: black;
}
.ytp-show-cards-title {
  position: absolute;
  background: white;
  bottom: 0;
  height: fit-content;
  display: flex;
  top: auto;
  width: 100%;
  left: 0;
  border-end-start-radius: 20px;
  border-end-end-radius: 20px;
  border-bottom: black;
}
.shark-modal-spin .ant-spin-dot-item {
  background-color: #00aae3 !important;
}
.ant-spin-dot-item {
  background-color: #fff !important;
}
button:hover .ant-spin-dot-item {
  background-color: #00aae3 !important;
}

.custom-searchbar-t2 .button {
  @apply rounded-full border-sandee-blue border bg-white text-sandee-blue cursor-pointer text-center;
  display: inline-block;
  margin: 4px 2px;
  /* background-color: #444; */
  font-size: 14px;
  padding-left: 32px;
  padding-right: 32px;
  height: 50px;
  line-height: 50px;
  /* text-align: center; */
  /* color: white; */
  text-decoration: none;
  /* cursor: pointer;
  -moz-user-select: none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none; */
}

.input-container-t8 {
  position: relative;
  display: flex;
  align-items: center;
}

.input-t8 {
  z-index: 3;
  width: 45px;
  height: 40px;
  border-radius: 20px;
  border: none;
  outline: none;
  padding: 18px 16px;
  border: 1px solid #00aae3;
  background-color: transparent;
  cursor: pointer;
  transition: all 0.5s ease-in-out;
}

.input-t8::placeholder {
  color: transparent;
}

.input-t8:focus::placeholder {
  color: rgb(131, 128, 128);
}

.input-t8:focus,
.input-t8:not(:placeholder-shown) {
  @apply w-[320px] md:w-[280px] lg:w-[300px] xl:w-[330px] 3xl:w-[350px];
  background-color: #fff;
  border: 1px solid #00aae3;
  /* width: 330px; */
  cursor: none;
  padding: 18px 16px 18px 45px;
}

.icon-t8 {
  position: absolute;
  left: 0;
  height: 45px;
  width: 45px;
  background-color: #fff;
  border-radius: 99px;
  z-index: 2;
  /* fill: #00aae3; */
  /* border: 1px solid #00aae3; */
}

.input-t8:focus + .icon-t8,
.input-t8:not(:placeholder-shown) + .icon-t8 {
  z-index: 3;
  background-color: transparent;
  border: none;
}

html {
  scroll-behavior: smooth !important;
}

.swiper-custom-sandee .swiper-button-next,
.swiper-custom-sandee .swiper-button-prev {
  width: 40px;
  max-height: 40px;
  background-repeat: no-repeat;
  background-color: white;
  border-radius: 100%;
  top: 30% !important;
}

.swiper-custom-sandee .swiper-button-prev.swiper-button-disabled {
  display: none;
}

@media screen and (max-width: 425px) {
  .swiper-custom-sandee .swiper-button-next,
  .swiper-custom-sandee .swiper-button-prev {
    top: 40% !important;
  }
}

.swiper-custom-sandee .swiper-button-prev {
  background-image: url("/static/icons/nextlist.svg");
  transform: rotate(180deg);
}

.swiper-custom-sandee .swiper-button-next {
  background-image: url("/static/icons/nextlist.svg");
}

.swiper-custom-sandee .swiper-button-next::after,
.swiper-custom-sandee .swiper-button-prev::after {
  content: "";
}

.accordion {
  @apply w-full  overflow-hidden  mt-2 bg-[#FAFAFA] border-none rounded-lg border-gray-400 cursor-pointer;
}
.accordion__intro {
  @apply relative cursor-pointer font-normal p-2 mx-2 text-sandee-sm text-black;
}
.accordion__content {
  @apply max-h-0 overflow-hidden will-change-[max-height] transition-all duration-[0.25s] ease-[ease-out]  opacity-0 px-5 py-0;
}
.accordion__active .accordion__content {
  @apply opacity-100 pt-0 pb-2 px-5 text-sandee-grey text-sandee-sm max-h-full;
}
.accordion__active .icon__content {
  @apply rotate-180 transition-all duration-[0.25s] ease-[ease-out];
}
.icon__content {
  @apply rotate-0 transition-all duration-[0.25s] ease-[ease-out];
}

.map-container {
  height: calc(100vh - 4.5rem);
}
/* .shark-map-container .map-container {
  height: 100vh;
} */

.map-sidebar {
  height: calc(100vh - 10.5rem);
}
.side-panel-search.loading:after {
  opacity: 1;
  pointer-events: auto;
  height: calc(100vh - 4.5rem);
}

.loading-spiner {
  border: 4px solid #ffffffe7;
  border-left-color: #00aae3;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  background: transparent;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
.side-panel-search:after {
  position: absolute;
  height: calc(100vh - 5rem);
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  background: rgba(0, 0, 0, 0.4);
  content: "";
  opacity: 0;
  pointer-events: none;
}
.filter-select-category button,
[type="button"] {
  /* background-color: revert-layer; */
  background-image: none;
}
/*map css*/
.mapboxgl-popup-content {
  padding: 0px !important;
}
.mapboxgl-ctrl-bottom-right .mapboxgl-ctrl {
  display: flex;
}
.scrollable-container::-webkit-scrollbar {
  width: 4px;
}
.listicle-map .mapboxgl-compact {
  display: none !important;
}
.header-image {
  min-height: 295px;
}
.spacing_overview {
  height: 0px !important;
}

/*Add photo modal*/
.add-photo-modal {
  overflow: hidden !important;
}
.add-photo-modal-custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.add-photo-modal-custom-scrollbar::-webkit-scrollbar-track {
  background-color: #f2f2f2; /* Tailwind's gray-300 */
}

.add-photo-modal-custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #dedede; /* Tailwind's gray-700 */
  border-radius: 4px;
}

/* Map popup design*/

.new-map-design .mapboxgl-popup-content {
  width: 300px !important;
  /* height: 450px!important; */
  border-radius: 16px !important;
}

.new-map-design-description p a {
  color: #00aae3 !important;
  text-decoration: none;
}

/* .new-map-custom-drawer .ant-drawer-content-wrapper {
  border-top-right-radius: 20px!important;
} */

.clamp-text {
  display: -webkit-box;
  -webkit-line-clamp: 5; /* Limit to 4 lines */
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* footer css */
.footer-accordion .ant-collapse-header {
  font-size: 16px !important;
  padding: 12px 0px !important;
  color: white !important;
  text-transform: uppercase !important;
}

.footer-accordion .ant-collapse-content-box,
.footer-accordion .ant-collapse-content > .ant-collapse-content-box {
  color: white !important;
  padding: 5px !important;
  font-size: 14px !important;
}

.hide-scrollbar-h-2 {
  scrollbar-height: thin; /* For Firefox (makes scrollbar thinner) */
}

.hide-scrollbar-h-2::-webkit-scrollbar {
  height: 4px; /* Adjust scrollbar height */
}

.hide-scrollbar-h-2::-webkit-scrollbar-thumb {
  background: #888; /* Customize the color of the scrollbar thumb */
  border-radius: 10px; /* Optional: make it rounded */
}

.hide-scrollbar-h-2::-webkit-scrollbar-track {
  background: #f1f1f1; /* Customize the scrollbar track */
}

.shark-map-container {
}
.shark-map-container .mapboxgl-ctrl-compass {
  display: none !important;
}
.shark-map-container .mapboxgl-ctrl-bottom-left {
  display: none !important;
}

.shark-map-container .mapboxgl-ctrl-attrib {
  display: none !important;
}

.mapboxgl-ctrl-attrib {
  display: none !important;
}

.shark-map-container .mapboxgl-ctrl-bottom-left {
  bottom: 0;
  left: 0;
}
.shark-map-container .mapboxgl-ctrl-bottom-right {
  right: 0;
  bottom: 0;
}

.shark-map-container .mapboxgl-ctrl-bottom-left,
.mapboxgl-ctrl-bottom-right,
.mapboxgl-ctrl-top-left,
.mapboxgl-ctrl-top-right {
  position: absolute;
  pointer-events: none;
  z-index: 2;
}

.shark-map-container .mapboxgl-ctrl-group:not(:empty) {
  -moz-box-shadow: 0 0 2px rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: 0 0 2px rgba(0, 0, 0, 0.1);
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
}

.shark-map-container .mapboxgl-ctrl-bottom-right .mapboxgl-ctrl {
  margin: 0 10px 10px 0;
  float: right;
}

.shark-map-container .mapboxgl-ctrl-bottom-right .mapboxgl-ctrl {
  display: flex;
}

.shark-map-container .mapboxgl-ctrl-group {
  border-radius: 4px;
  background: #fff;
}

.shark-map-container .mapboxgl-ctrl {
  clear: both;
  pointer-events: auto;
  transform: translate(0);
}

.shark-map-container .mapboxgl-ctrl-group button {
  width: 29px;
  height: 29px;
  display: block;
  padding: 0;
  outline: none;
  border: 0;
  box-sizing: border-box;
  background-color: transparent;
  cursor: pointer;
}

.shark-map-container
  .mapboxgl-ctrl
  button.mapboxgl-ctrl-zoom-in
  .mapboxgl-ctrl-icon {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='29' height='29' viewBox='0 0 29 29' xmlns='http://www.w3.org/2000/svg' fill='%23333'%3E%3Cpath d='M14.5 8.5c-.75 0-1.5.75-1.5 1.5v3h-3c-.75 0-1.5.75-1.5 1.5S9.25 16 10 16h3v3c0 .75.75 1.5 1.5 1.5S16 19.75 16 19v-3h3c.75 0 1.5-.75 1.5-1.5S19.75 13 19 13h-3v-3c0-.75-.75-1.5-1.5-1.5z'/%3E%3C/svg%3E");
}

.shark-map-container .mapboxgl-ctrl button .mapboxgl-ctrl-icon {
  display: block;
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-position: 50%;
}

.shark-map-container .mapboxgl-ctrl-group button + button {
  border-top: 1px solid #ddd;
}

.shark-map-container
  .mapboxgl-ctrl
  button.mapboxgl-ctrl-zoom-out
  .mapboxgl-ctrl-icon {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='29' height='29' viewBox='0 0 29 29' xmlns='http://www.w3.org/2000/svg' fill='%23333'%3E%3Cpath d='M10 13c-.75 0-1.5.75-1.5 1.5S9.25 16 10 16h9c.75 0 1.5-.75 1.5-1.5S19.75 13 19 13h-9z'/%3E%3C/svg%3E");
}

.shark-map-container .mapboxgl-canvas-container.mapboxgl-interactive,
.mapboxgl-ctrl-group button.mapboxgl-ctrl-compass {
  cursor: -webkit-grab;
  cursor: -moz-grab;
  cursor: grab;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.shark-map-container
  .mapboxgl-ctrl
  button.mapboxgl-ctrl-compass
  .mapboxgl-ctrl-icon {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='29' height='29' viewBox='0 0 29 29' xmlns='http://www.w3.org/2000/svg' fill='%23333'%3E%3Cpath d='M10.5 14l4-8 4 8h-8z'/%3E%3Cpath d='M10.5 16l4 8 4-8h-8z' fill='%23ccc'/%3E%3C/svg%3E");
}

.shark-map-container .mapboxgl-ctrl.mapboxgl-ctrl-attrib {
  padding: 0 5px;
  background-color: hsla(0, 0%, 100%, 0.5);
  margin: 0;
}

.shark-map-container .mapboxgl-ctrl-bottom-right .mapboxgl-ctrl {
  margin: 0 10px 10px 0;
  float: right;
}

.shark-map-container .mapboxgl-ctrl {
  clear: both;
  pointer-events: auto;
  transform: translate(0);
}

.shark-switch .ant-switch-checked {
  background: #fa1b1b !important;
}

.shark-switch .ant-switch {
  background: #b9b9b9;
}

.nude-switch .ant-switch {
  background: #b9b9b9;
}

.nude-switch .ant-switch-checked {
  background: #00aae3 !important;
}

.header-submenu a:hover {
  color: #00aae3 !important;
}

.shark-modal .ant-collapse-expand-icon {
  color: #ff1616;
}

/* Customize Tooltip Background and Text */
.ant-tooltip-inner {
  background-color: #00aae3 !important; /* Change background color */
  color: #fff !important; /* Change text color */
}
.ant-tooltip .ant-tooltip-arrow:before {
  background: #00aae3;
}

/* Optional: Customize Tooltip Arrow */
.ant-tooltip-arrow-content {
  background-color: #00aae3 !important; /* Match arrow color to the tooltip */
}
.ant-tooltip-placement-bottom .ant-tooltip-arrow,
.ant-tooltip-placement-bottomLeft .ant-tooltip-arrow,
.ant-tooltip-placement-bottomRight .ant-tooltip-arrow {
  border-bottom-color: #00aae3;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

:where(.css-dev-only-do-not-override-11xg00t).ant-picker-dropdown
  .ant-picker-cell-in-view.ant-picker-cell-selected:not(
    .ant-picker-cell-disabled
  )
  .ant-picker-cell-inner,
:where(.css-dev-only-do-not-override-11xg00t).ant-picker-dropdown
  .ant-picker-cell-in-view.ant-picker-cell-range-start:not(
    .ant-picker-cell-disabled
  )
  .ant-picker-cell-inner,
:where(.css-dev-only-do-not-override-11xg00t).ant-picker-dropdown
  .ant-picker-cell-in-view.ant-picker-cell-range-end:not(
    .ant-picker-cell-disabled
  )
  .ant-picker-cell-inner {
  background: #00aae3 !important;
}

:where(.css-11xg00t).ant-picker-dropdown
  .ant-picker-cell-in-view.ant-picker-cell-selected:not(
    .ant-picker-cell-disabled
  )
  .ant-picker-cell-inner {
  background: #00aae3;
}

.mapboxgl-ctrl .mapboxgl-ctrl-logo {
  display: none !important;
}

/* .beach-detail-map .ant-drawer-body {
  border-radius: 24px 24px 0 0;
} */

.ant-drawer-content-wrapper {
  box-shadow: none !important;
  padding: 10px;
}

/*progress bar*/
.progress-bar {
  width: 100%;
  height: 10px;
  background-color: #e4e4e4;
  border-radius: 10px;
  overflow: hidden;
}

.progress {
  height: 10px;
  background-color: #1a1a1a;
  transition: width 0.5s ease-in-out;
}

.customab .mySwiper .swiper-slide {
  height: 80% !important;
}

/* Slide in from top */
@keyframes slideIn {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Slide out to top */
@keyframes slideOut {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(-100%);
    opacity: 0;
  }
}

.slide-in {
  animation: slideIn 0.5s ease-out forwards;
}

.slide-out {
  animation: slideOut 0.5s ease-out forwards;
}

@keyframes dotPulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.5);
  }
}

.dot-animation {
  animation: dotPulse 1.2s infinite;
}

.dot-animation.delay-200 {
  animation-delay: 0.2s;
}

.dot-animation.delay-400 {
  animation-delay: 0.4s;
}

.boat-answer a {
  color: #00aae3;
  font-weight: 600;
}
.cursor-col-resize {
  cursor: col-resize;
}

/* Prevent text selection during drag */
.no-select {
  user-select: none;
  -webkit-user-select: none;
}

:where(.css-dev-only-do-not-override-11xg00t).ant-tabs .ant-tabs-ink-bar {
  background: #00aae3 !important;
}

:where(.css-dev-only-do-not-override-11xg00t).ant-tabs
  .ant-tabs-tab.ant-tabs-tab-active
  .ant-tabs-tab-btn {
  color: #00aae3 !important;
}
:where(.css-dev-only-do-not-override-11xg00t).ant-tabs .ant-tabs-tab:hover {
  color: #00aae3 !important;
}

:where(.css-dev-only-do-not-override-11xg00t).ant-tabs .ant-tabs-tab-btn {
  /* color: #00aae3 !important; */
}
