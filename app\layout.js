import "./globals.css";
import "../public/ckeditor5-content.css";

import { siteMetadata } from "@/data/siteMetadata";
import { Suspense } from "react";
import NotFound from "@/app/not-found";
import Footer from "@/components/Footer";
import CircularProgressBar from "@/components/Common/CircularPrgress";
import Script from "next/script";
import TopNProgressBar from "@/components/TopNProgressBar";
import HomeSkeleton from "@/components/HomeSkeleton";
import ValProvider from "@/helper/context/ValContext";
import MobileBottomNav from "@/components/MobileBottomNav";

export default function RootLayout(props) {
  const { children } = props;

  return (
    <html
      lang={siteMetadata.language}
      className={` scroll-smooth`}
      suppressHydrationWarning
    >
      <link
        rel="apple-touch-icon"
        sizes="76x76"
        href="/static/favicons/Sandee.png"
      />
      <link
        rel="icon"
        type="image/png"
        sizes="32x32"
        href="/static/favicons/Sandee.png"
      />
      <link
        rel="icon"
        type="image/png"
        sizes="16x16"
        href="/static/favicons/Sandee.png"
      />
      <link rel="manifest" href="/static/favicons/site.webmanifest" />
      <link
        rel="mask-icon"
        href="/static/favicons/Sandee.png"
        color="#5bbad5"
      />
      <link
        rel="icon"
        type="image/png"
        sizes="16x16"
        href="/static/favicons/Sandee.png"
      />

      <link rel="icon" href="/static/favicons/Sandee.png" />
      <link rel="apple-touch-icon" href="/static/favicons/Sandee.png" />
      <meta name="msapplication-TileColor" content="#000000" />
      <meta
        name="theme-color"
        media="(prefers-color-scheme: light)"
        content="#fff"
      />
      <meta
        name="theme-color"
        media="(prefers-color-scheme: dark)"
        content="#000"
      />
      <link rel="alternate" type="application/rss+xml" href="/feed.xml" />

      <body
        id="pagebody"
      // className="!bg-[rgba(255,255,255,1)] text-black "
      // style={{ background: "white" }}
      >
        <Script
          strategy="lazyOnload"
          id="googletag"
          async
          dangerouslySetInnerHTML={{
            __html: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-WT6TPZDD');`,
          }}
        ></Script>
        <Script
          strategy="lazyOnload"
          id="googleanalytic"
          async
          dangerouslySetInnerHTML={{
            __html: `window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());

    gtag('config', 'G-QMTZQN6JXS');`,
          }}
        ></Script>
        <Script
          strategy="lazyOnload"
          async
          src="https://www.googletagmanager.com/gtag/js?id=G-QMTZQN6JXS"
        />

        <Script
          strategy="lazyOnload"
          id="hotjar"
          async
          dangerouslySetInnerHTML={{
            __html: `(function (h, o, t, j, a, r) {
      h.hj = h.hj || function () { (h.hj.q = h.hj.q || []).push(arguments) };
      h._hjSettings = { hjid: 2948421, hjsv: 6 };
      a = o.getElementsByTagName('head')[0];
      r = o.createElement('script'); r.async = 1;
      r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
      a.appendChild(r);
    })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv=');`,
          }}
        ></Script>

        <div className="flex min-h-screen flex-col justify-between">
          <ValProvider>
            <Suspense fallback={<HomeSkeleton />}>{children}</Suspense>
            <MobileBottomNav />
            {/* <Footer /> */}
          </ValProvider>
        </div>

        <CircularProgressBar />
        <TopNProgressBar />
      </body>
    </html>
  );
}
