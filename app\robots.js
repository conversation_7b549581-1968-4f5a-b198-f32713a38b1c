export default function robots() {
  const rules = [];
  const URL =
    process.env.NEXT_PUBLIC_ENV == "production"
      ? process.env.NEXT_PUBLIC_WEB_URL
      : process.env.NEXT_PUBLIC_DEV_WEB_URL;
  if (process.env.NEXT_PUBLIC_ENV == "production") {
    rules.push({
      userAgent: "*",
      // disallow: ["/iframes/"],
      allow: "/",
    });
    // Allow PerplexityBot specifically
    rules.push({
      userAgent: "PerplexityBot",
      allow: "/",
    });

    // Allow Perplexity-User (optional, as it generally ignores robots.txt)
    rules.push({
      userAgent: "Perplexity-User",
      allow: "/",
    });
  } else {
    rules.push({
      userAgent: "*",
      disallow: "/",
    });
  }
  return {
    rules,
    // rules: [
    //   {
    //     userAgent: "*",
    //     disallow: "/",
    //     allow: "/",
    //   },
    //   //   {
    //   //     userAgent: "Googlebot",
    //   //     allow: ["/"],
    //   //     disallow: ["/private/"],
    //   //   },
    //   //   {
    //   //     userAgent: ["Applebot", "Bingbot"],
    //   //     disallow: ["/"],
    //   //   },
    // ],
    sitemap: `${URL}/sitemap.xml`,
  };
}
