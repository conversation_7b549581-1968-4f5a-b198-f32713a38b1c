import SearchBar from "@/components/SearchBar";
import React from "react";
import Header from "@/components/Header";
import Link from "next/link";
import Image from "next/image";

const SearchPage = (props) => {
  return props?.searchParams?.q ? (
    <div className=" w-full flex justify-center items-start mt-10 min-h-[450px]">
      <SearchBar initialquery={props?.searchParams?.q ?? ""} />
    </div>
  ) : (
    <div className="">
      <Header type={2} />
      <div className="flex flex-col items-center justify-center md:my-24 md:flex-row md:items-center md:justify-center md:space-x-6">
        <div className=" flex justify-center items-center flex-col">
          <Link href="/">
            <div className="relative  w-[200px] h-[200px]">
              <Image
                src={"/static/images/NoBeach.svg"}
                fill
                alt="Sandee logo Blue"
              />
            </div>
          </Link>
          <p className="text-sandee-24 font-bold text-gray-600">
            Result Not Found
          </p>
        </div>

        <div className="max-w-md text-center text-sandee-24 font-normal text-gray-600">
          <p className="mb-4  leading-normal">
            {`Looks like you've hit a dead end, mate.`}
          </p>
          <p className="mb-3">
            {`The page you're searching for might be out of reach.`}
          </p>
          <p className="mb-3">Happy Beach Discoveries!</p>
          <div className="flex justify-center mb-6">
            <Link href={"/"}>
              <button className=" text-sandee-blue active:text-white active:bg-sandee-blue border-2 border-sandee-blue  py-2 px-5 transition ease-in-out duration-300  text-xs font-medium  rounded-full active:scale-[0.7] hover:scale-110">
                Choose Your Beach
              </button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SearchPage;
