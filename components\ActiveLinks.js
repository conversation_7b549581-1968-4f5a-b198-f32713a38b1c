"use client";
import Link from "next/link";
import { usePathname } from "next/navigation";
import React, { useEffect, useState } from "react";

const ActiveLinks = ({
  children,
  href,
  className,
  ActiveClassName = "",

  ...rest
}) => {
  const pathname = usePathname();
  const [hash, setHAsh] = useState();
  const isAnchorLink = href && href.startsWith("#");
  useEffect(() => {
    const handleScroll = () => {
      setHAsh(window.location.hash);
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <Link
      {...rest}
      href={href}
      className={`${className} ${isAnchorLink
        ? hash === href
          ? ActiveClassName
          : ""
        : pathname === href || pathname.startsWith(`${href}/`)
          ? ActiveClassName
          : ""
        }`}

    >
      {children}
    </Link>
  );
};

export default ActiveLinks;
