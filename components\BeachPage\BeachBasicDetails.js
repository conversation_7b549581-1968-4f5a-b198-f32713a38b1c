import React from "react";
import NameTitle from "../Common/NameTitle";
import Image from "next/image";
import Link from "next/link";

const BeachBasicDetails_Section = ({ beachData }) => {
  const BasicArray = [
    // {
    //   IconKey: "phone",
    //   SVG: `https://images.sandee.com/Icons/phone.svg`,
    //   detail: beachData?.beachBasicDetail?.phoneNumber,
    //   display: beachData?.beachBasicDetail?.phoneNumber,
    // },
    // {
    //   IconKey: "ruler",
    //   SVG: `https://images.sandee.com/Icons/ruler.svg`,
    //   detail: `${beachData?.beachBasicDetail?.size} ${beachData?.beachBasicDetail?.unit} `,
    //   display:
    //     beachData?.beachBasicDetail?.size && beachData?.beachBasicDetail?.unit,
    // },
    // {
    //   IconKey: "offer",
    //   detail: "Entry Fee",
    //   SVG: `https://images.sandee.com/Icons/offer.svg`,
    //   display: beachData?.beachCategory?.restaurant,
    // },

    {
      detail: "Parking",
      IconKey: "parking",
      SVG: `https://images.sandee.com/Icons/parking.svg`,
      display: beachData?.beachBasicDetail?.parking,
    },

    {
      IconKey: "clock",
      SVG: `https://images.sandee.com/Icons/clock.svg`,
      detail: beachData?.beachBasicDetail?.hours,
      display: beachData?.beachBasicDetail?.hours,
    },
    {
      IconKey: "toilet",
      detail: "Restroom Available",
      SVG: `https://images.sandee.com/Icons/toilet.svg`,
      display: beachData?.beachCategory?.restRooms,
    },
    {
      IconKey: "sand",
      SVG: `https://images.sandee.com/Icons/sand.svg`,
      detail:
        (beachData?.beachBasicDetail?.sandColor &&
          Array.isArray(beachData?.beachBasicDetail?.sandColor)
          ? beachData?.beachBasicDetail?.sandColor?.[0]
          : beachData?.beachBasicDetail?.sandColor?.[0]) || "",
      display:
        (beachData?.beachBasicDetail?.sandColor &&
          Array.isArray(beachData?.beachBasicDetail?.sandColor)
          ? beachData?.beachBasicDetail?.sandColor?.[0]
          : beachData?.beachBasicDetail?.sandColor?.[0]) || "",
    },

    // {
    //   IconKey: "shower",
    //   SVG: `https://images.sandee.com/Icons/shower.svg`,
    //   detail: beachData?.beachBasicDetail?.shower || "Shower",
    //   display: beachData?.beachBasicDetail?.shower,
    // },

    {
      IconKey: "pin",
      SVG: `https://images.sandee.com/Icons/pin.svg`,
      detail: beachData?.address,
      display: beachData?.address,
      link: `https://maps.google.com/?q=${beachData?.address}`,
    },
  ];
  return (
    <>
      {BasicArray?.filter((data) => data?.display)?.length > 0 && (
        <div className="mt-3" data-testid="beach-basic-details-section">
          <NameTitle
            className="mt-5 mb-[6px] uppercase"
            // name={`Top Amenities at ${beachData?.name}`}
            name={`Basic Details`}
            // description={"Basic details beach have"}
            dataTestid="beach-basic-details-title"
            type={3}
          />
          <div data-testid="beach-basic-details-grid" className=" grid grid-cols-1  sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 2xl:grid-cols-5 gap-x-3 gap-y-4">
            {BasicArray?.filter((data) => data?.display)?.map((data, index) => (
              <div
                key={data?.IconKey}
                className={`${data?.link ? "col-span-2" : ""}`}
                data-testid={`beach-basic-detail-item-${index}`}
              >
                <p
                  data-testid={`beach-basic-detail-text-${index}`}
                  className="capitalize flex items-start gap-3 font-normal text-black">
                  <Image
                    src={`/static/icons/BasicDetails/${data?.IconKey}Blue.svg`}
                    width={20}
                    height={20}
                    alt="IN"
                    data-testid={`beach-basic-detail-icon-${index}`}
                  // className="p-1"
                  />
                  {data?.link ? (
                    <span className="w-5/6 " data-testid={`beach-basic-detail-link-${index}`}>
                      <Link
                        target="_blank"
                        href={data?.link}
                        className=" hover:underline"
                      >
                        {data?.detail}
                      </Link>
                    </span>
                  ) : (
                    <span className="w-5/6 line-clamp-1 text-start leading-snug" data-testid={`beach-basic-detail-value-${index}`}>
                      {Array.isArray(data?.detail)
                        ? data?.detail[0]
                        : data?.detail}
                    </span>
                  )}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}
    </>
  );
};

export default BeachBasicDetails_Section;
