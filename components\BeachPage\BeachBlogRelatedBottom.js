import React from "react";
import { CustomGrid } from "../Custom-Display";
import BlogCard from "../Cards/BlogCard";
import NameTitle from "../Common/NameTitle";
import { ReadBookIcon } from "../social-icons/icons";
import { HomePageData } from "@/data/HomePageData";
import Link from "next/link";

const BeachBlog_Related_Bottom_Section = ({ data = [], dataTestid = "" }) => {
  return (
    <div data-testid={dataTestid}>
      <NameTitle
        // className="mt-8"
        description={"EXPERT ADVICE AND TIPS"}
        name={"Beach Blogs"}
        dataTestid="beach-blog-related-title"
        extraButton={
          <div data-testid="beach-blog-related-extra-button" className=" hidden md:flex justify-end items-start w-3/12">
            <Link
              href={HomePageData.BlogSection.button_link}
              className=" custom-hover-slide-button group"
              data-testid="beach-blog-related-link"
            >
              <span className="custom-hover-slide group-hover:h-full"></span>
              <span className="custom-hover-slide-text group-hover:text-white font-semibold ">
                <ReadBookIcon className=" me-2 fill-transparent stroke-sandee-orange group-hover:stroke-white  h-5 w-5 -mt-0.5" />
                {HomePageData.BlogSection.button_text}
              </span>
            </Link>
          </div>
        }
        type={2}
      />
      <CustomGrid
        data={data}
        className="gap-8 mt-[6px] mb-6 "
        Component={({ data: dataProps, index }) => {
          dataProps.link = `/blog/${dataProps?.slug}`; //
          dataProps.imageSrc = dataProps?.image;
          dataProps.name = dataProps?.title;
          return BlogCard({
            data: { ...dataProps },
            copyRightsData: dataProps?.image,
            dataTestid: `beach-blog-related-grid-item-${index}`,
            index: index
          });
        }}
        dataTestid="beach-blog-related-grid"
        xs={1}
        sm={2}
        md={2}
        lg={3}
        xl={4}
      />
      <div className=" md:hidden my-5" data-testid="beach-blog-related-mobile-button">
        <Link
          href={HomePageData.BlogSection.button_link}
          className=" custom-hover-slide-button group"
          data-testid="beach-blog-related-mobile-link"
        >
          <span className="custom-hover-slide group-hover:h-full"></span>
          <span className="custom-hover-slide-text group-hover:text-white font-medium">
            <ReadBookIcon className=" me-2 fill-transparent stroke-sandee-orange group-hover:stroke-white h-5 w-5 -mt-0.5" />
            {HomePageData.BlogSection.button_text}
          </span>
        </Link>
      </div>
    </div>
  );
};

export default BeachBlog_Related_Bottom_Section;
