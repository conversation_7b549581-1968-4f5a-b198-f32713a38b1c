import React from "react";
import ListCard from "../Cards/ListCard";
import { CustomGrid } from "../Custom-Display";
import NameTitle from "../Common/NameTitle";

const BeachBlog_Related_Top_Section = ({ data = [] }) => {
  return (
    <div>
      <NameTitle
        // className="mt-8"
        description={"EXPERT ADVICE AND TIPS"}
        name={"Beach Blogs"}
        type={2}
      />
      <CustomGrid
        data={data}
        className="gap-4 sm:gap-8 my-6 mb-12"
        Component={({ data: dataProps }) => {
          dataProps.link = `/blog/${dataProps?.slug}`; //
          dataProps.imageSrc = dataProps?.image;
          return ListCard({ data: { ...dataProps, name: dataProps?.title } });
        }}
        xs={1}
        // sm={2}
        // md={2}
        // lg={3}
        // xl={3}
      />
    </div>
  );
};

export default BeachBlog_Related_Top_Section;
