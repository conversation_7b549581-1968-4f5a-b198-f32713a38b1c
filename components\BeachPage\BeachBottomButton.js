"use client";
import { scrollToTop } from "@/helper/functions";
import Link from "next/link";
import React from "react";
import CustomButton from "../Custom-Button";
import { CustomButtonGrid } from "../Custom-Display";

const Beach_Bottom_Button = ({ beachData, dataTestid }) => {
  return (
    <CustomButtonGrid dataTestid={dataTestid}>
      <Link href={`/countries`} data-testid={`${dataTestid}-all-countries-link`}>
        <CustomButton dataTestid={`${dataTestid}-all-countries-button`} type={5}>All Countries</CustomButton>
      </Link>
      <Link href={`/${beachData?.country?.slug}`}
        data-testid={`${dataTestid}-country-link`}>
        <CustomButton dataTestid={`${dataTestid}-country-button`} type={5}>{beachData?.country?.name}</CustomButton>
      </Link>
      <Link
        data-testid={`${dataTestid}-state-or-island-link`}
        href={
          beachData?.islandSlug && beachData?.island?.name
            ? `/island/${beachData?.islandSlug}`
            : `/${beachData?.country?.slug}/${beachData?.state?.slug}`
        }
      >
        <CustomButton dataTestid={`${dataTestid}-state-or-island-button`} type={5}>
          {beachData?.islandSlug && beachData?.island?.name
            ? beachData?.island?.name
            : beachData?.state?.name}
        </CustomButton>
      </Link>
      <Link
        data-testid={`${dataTestid}-city-link`}
        href={`/${beachData?.country?.slug}/${beachData?.state?.slug}/${beachData?.city?.slug}`}
      >
        <CustomButton dataTestid={`${dataTestid}-city-button`} type={5}>{beachData?.city?.name}</CustomButton>
      </Link>
      <CustomButton dataTestid={`${dataTestid}-back-to-top-button`} type={5} onClick={scrollToTop}>
        Back to Top
      </CustomButton>
    </CustomButtonGrid>
  );
};

export default Beach_Bottom_Button;
