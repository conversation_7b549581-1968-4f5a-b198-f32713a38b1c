"use client";
import React from "react";
import CustomRating from "./CustomRating";
import { postReview } from "@/app/(HomeHeader)/action";
import useLoggedIn from "@/helper/hook/useLoggedIn";

const BeachBottomReviewSection = ({ beachData }) => {
  const { token } = useLoggedIn();

  return (
    <div>
      <p className=" text-sandee-32 font-semibold mb-10">
        {`Add a Review for ${beachData?.name}`}
      </p>
      <form
        className="form"
        name="bottomform"
        onSubmit={async (e) => {
          e.preventDefault();

          const payload = {
            rate: e.target?.rate?.value,
            review: e.target?.review?.value,
            AllBeachId: beachData?.id,
          };
          //   logController(payload, token, e.target?.agree?.checked);
          const response = await postReview(payload, token);
          //   logController(response);
          if (response?.data && response?.status === "success") {
            // logController("Review Added Successfully");
            window.location.reload();
          } else {
            // logController("You are not Authorized");
          }

          // const payload = {
          //   email: e.target?.email?.value,
          //   password: e.target?.password?.value,
          // };
        }}
      >
        <div className=" flex gap-3 justify-start items-start md:items-center md:justify-between flex-col md:flex-row">
          <div className=" w-full md:w-1/2 lg:w-3/4 xl:w-4/6">
            {/* <textarea
              className="input h-auto text-sandee-12 align-text-top text-wrap text-start bg-gray-100 w-full "
              name="review"
              type="textarea"
              placeholder="Share your best moments, tips, and anything you wish you'd known before going."
              required
            /> */}
            <input
              className="input h-auto text-sandee-12 align-text-top text-wrap text-start bg-gray-100 w-full px-4 py-3 rounded-full"
              name="review"
              type="text"
              placeholder="Share your best moments, tips, and anything you wish you'd known before going."
              required
            />
          </div>
          <div className=" w-full md:w-1/2 lg:w-1/4 xl:w-1/6">
            <CustomRating radioGroup="RadioAlpha" />
          </div>

          <div>
            <div className=" justify-center items-center  py-2 px-8 flex relative   overflow-hidden font-medium  text-white border-2 border-sandee-blue bg-sandee-blue active:text-white text-sandee-sm h-auto rounded-full group">
              <span className="absolute bottom-0 left-0 flex w-full h-0 mb-0 transition-all duration-200 ease-out transform translate-y-0 bg-white text-sandee-blue opacity-100 group-hover:h-full"></span>
              <span className="relative flex items-center group-hover:text-sandee-blue ">
                <input
                  defaultValue="Post"
                  //   disabled={loading}
                  type="submit"
                  className={` login-button ${""}`}
                />
              </span>
            </div>
            {/* <input
              defaultValue="Post"
              //   disabled={loading}
              type="submit"
              className={` login-button ${""}`}
            /> */}
          </div>
        </div>
      </form>
    </div>
  );
};

export default BeachBottomReviewSection;
