import React from "react";
import BreadCumber from "@/components/Common/BreadCumber";

const Beach_BreadCumber_Section = ({ beachData }) => {
  const data =
    // beachData?.islandSlug && beachData?.island?.name
    //   ? [
    //       {
    //         title: beachData?.country?.name,
    //         to: `/${beachData?.country?.slug}`,
    //       },
    //       {
    //         title: beachData?.island?.name,
    //         to: `/island/${beachData?.islandSlug}`,
    //       },
    //       {
    //         title: beachData?.city?.name,
    //         to: `/${beachData?.country?.slug}/${beachData?.state?.slug}/${beachData?.city?.slug}`,
    //       },
    //       {
    //         title: beachData?.name,
    //       },
    //     ]
    //   :
    [
      {
        title: beachData?.country?.name,
        to: `/${beachData?.country?.slug}`,
        dataTestid: "country-breadcrumb",
      },
      {
        title: beachData?.state?.name,
        to: `/${beachData?.country?.slug}/${beachData?.state?.slug}`,
        dataTestid: "state-breadcrumb",
      },
      {
        title: beachData?.city?.name,
        to: `/${beachData?.country?.slug}/${beachData?.state?.slug}/${beachData?.city?.slug}`,
        dataTestid: "city-breadcrumb",
      },
      {
        title: beachData?.name,
      },
    ];

  return <BreadCumber dataTestid="beach-breadcrumb" data={data} />;
};

export default Beach_BreadCumber_Section;
