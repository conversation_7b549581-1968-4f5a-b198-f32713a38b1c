import React from "react";
import NameTitle from "../Common/NameTitle";
import { getEmbedUrl } from "@/helper/functions";

const BeachCamsSection = ({ beachData, dataTestid = "beach-cams-section" }) => {
  return beachData?.beachCamLink ? (
    <div data-testid={dataTestid} className=" lg:bg-[#FAFAFA] lg:pt-8 lg:pb-3 md:px-4 my-5 rounded-sandee lg:min-h-[400px]" id={"beachCams"}>
      <div data-testid="beach-cams-container" className="relative flex flex-col justify-center items-center">
        <div data-testid="beach-cams-header" className=" w-full lg:w-2/5 flex lg:flex-row flex-col gap-5 items-center justify-between">
          <p data-testid="beach-cams-live-indicator" className="text-red-500 bg-white rounded-full px-4 py-2 flex items-center justify-center gap-2 lg:text-xl text-sm shadow-md">
            <span data-testid="beach-cams-live-dot" className="aspect-square lg:w-4 lg:h-4 w-2 h-2 rounded-full bg-red-500 "></span>
            LIVE
          </p>
          {/* <NameTitle
            name={`${
              beachData?.beachCamTitle ??
              `${beachData?.name ?? "Beach"} Live Cam`
            } `}
          /> */}
          <p data-testid="beach-cams-title" className="font-semibold text-center lg:text-sandee-24 text-lg">
            {`${beachData?.name ?? "Beach"} - Live Beach Cam`}
          </p>
          {/* <NameTitle name={`${beachData?.name ?? "Beach"} - Live Beach Cam`} /> */}
        </div>
        <div data-testid="beach-cams-video-container" className="w-full lg:w-3/5 md:flex items-center justify-center my-5 lg:px-5">
          {beachData?.beachCamLink && (
            <div data-testid="beach-cams-video-wrapper" className=" w-full  lg:min-h-[20%]  flex flex-col  rounded-sandee bg-gray-200 shadow-lg aspect-auto md:aspect-video">
              <iframe
                src={`${getEmbedUrl(beachData?.beachCamLink)}`}
                // src={`${beachData?.beachCamLink}${
                //   beachData?.beachCamLink?.includes("?")
                //     ? ""
                //     : "?autoplay=1&mute=1"
                // }`}
                allowFullScreen
                data-testid="beach-cams-iframe"
                loading="lazy"
                className=" bg-gray-200 w-full h-full rounded-sandee min-h-[230px]"
              ></iframe>
            </div>
          )}
        </div>
      </div>
    </div>
  ) : (
    ""
  );
};

export default BeachCamsSection;
