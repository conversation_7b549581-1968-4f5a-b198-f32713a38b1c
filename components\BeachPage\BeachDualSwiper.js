"use client";
import React, { useState } from "react";
// Import Swiper React components
import { Swiper, SwiperSlide } from "swiper/react";

// Import Swiper styles
import "swiper/css";
import "swiper/css/free-mode";
import "swiper/css/navigation";
import "swiper/css/thumbs";

import { FreeMode, Navigation, Thumbs } from "swiper";
import Image from "next/image";
import {
  FinalImageGenerator,
  WeatherIconLink,
  altText,
  blurDataURL,
  convertUnixTimeTo12HourFormat,
  generateBreakpoints,
  isEmptyObject,
} from "@/helper/functions";
import CopyRight, { CustomLink } from "../Common/CopyRight";
import BeachPhotoUploadModal from "./BeachPhotoUploadModal";
import {
  AddPhotosSwiper,
  LocationDirectionIcon,
  PinLocation,
} from "../social-icons/icons";
import AuthWrapper from "../Common/AuthWrapper";
import Link from "next/link";
import BeachListModal from "./BeachListModal";
import useLoggedIn from "@/helper/hook/useLoggedIn";
import CustomeImage from "../Common/CustomeImage";
export default function BeachDualSwiper({ dataTestid = "beach-dual-swiper", MultipleImages, AllImages, beachData }) {
  const location =
    beachData?.islandSlug && beachData?.island?.name
      ? [
        {
          title: beachData?.country?.name,
          to: `/${beachData?.country?.slug}`,
        },
        {
          title: beachData?.island?.name,
          to: `/island/${beachData?.islandSlug}`,
        },
        {
          title: beachData?.city?.name,
          to: `/${beachData?.country?.slug}/${beachData?.state?.slug}/${beachData?.city?.slug}`,
        },
        {
          title: beachData?.name,
        },
      ]
      : [
        {
          title: beachData?.country?.name,
          to: `/${beachData?.country?.slug}`,
        },
        {
          title: beachData?.state?.name,
          to: `/${beachData?.country?.slug}/${beachData?.state?.slug}`,
        },
        {
          title: beachData?.city?.name,
          to: `/${beachData?.country?.slug}/${beachData?.state?.slug}/${beachData?.city?.slug}`,
        },
        {
          title: beachData?.name,
        },
      ];
  const [thumbsSwiper, setThumbsSwiper] = useState(null);
  const [open, setOpen] = useState(null);
  const data = useLoggedIn();

  return (
    <div data-testid={dataTestid} className="customab lg:h-full w-full 425:h-[52vh] h-[53vh]">
      <Swiper
        init
        style={{
          "--swiper-navigation-color": "#fff",
          "--swiper-pagination-color": "#fff",
        }}
        data-testid="beach-main-swiper"
        slidesPerView={1}
        slidesPerGroup={1}
        slidesPerGroupSkip={0}
        hashNavigation
        // loop={true}
        spaceBetween={10}
        navigation={{
          // nextEl: ".swiper-button-next-major",
          // prevEl: ".swiper-button-prev-major",
          enabled: true,
        }}
        pagination={{
          clickable: true,
          dynamicBullets: true,
          dynamicMainBullets: 20,
        }}
        //   injectStyles={`
        //     .swiper-button-next,
        //     .swiper-button-prev {
        //       background-color: white;
        //       background-position: center;
        //       background-size: 40px;
        //       background-repeat: no-repeat;
        //       padding: 8px 16px;
        //       border-radius: 100%;
        //       border: 2px solid black;
        //       color: red;
        //     }

        //     .swiper-button-prev {
        //       background-image: url("https://images.sandee.com/images/Icons/PrevIcon.svg");
        //     }

        //     .swiper-button-next {
        //       background-image: url("https://images.sandee.com/images/Icons/NextIcon.svg");
        //     }

        //     .swiper-button-next::after,
        //     .swiper-button-prev::after {
        //       content: "";
        //     }

        //     .swiper-pagination-bullet{
        //       width: 40px;
        //       height: 40px;
        //       background-color: red;
        //     }
        // `}
        thumbs={{ swiper: thumbsSwiper }}
        // modules={[FreeMode, Navigation, Thumbs, Pagination]}
        className={`${MultipleImages?.length <= 1 ? "!h-full" : ""
          } mySwiper2 h-[60vh]"`}
      >
        {MultipleImages?.length &&
          !isEmptyObject(MultipleImages[0]) &&
          MultipleImages?.map((el, index) => (
            <SwiperSlide data-testid={`beach-swiper-slide-${index}`} key={el?.id} className=" lg:rounded-sandee ">
              <div data-testid={`beach-swiper-image-container-${index}`} className="relative  overflow-hidden  w-full h-full  rounded-sandee">
                {MultipleImages?.length > 1 ?
                  <div
                    data-testid="beach-all-photos-button"
                    className="absolute z-50 top-2 right-2  backdrop-blur-[1px] p-2 rounded-md border-white border-[1px] cursor-pointer bg-black !bg-opacity-40 flex justify-center items-center">
                    <BeachListModal MultipleImages={AllImages} beachData={beachData} Other={(props) => {
                      return (
                        <p {...props} className="w-full h-full text-center justify-center fill-white text-white text-base flex items-center">All Photos</p>
                      );
                    }} />
                  </div> : <></>}
                <CustomeImage
                  // priority
                  className=" lg:rounded-sandee transition-transform duration-1000 ease-in-out transform group-hover:scale-125 w-full"
                  src={FinalImageGenerator(el, 1600, 3)}
                  title={`${beachData?.name} Image - ${index + 1}`}
                  alt={altText(el)}
                  fill
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  blurDataURL={blurDataURL(1650, 1100)}
                  placeholder="blur"
                  data-testid={`beach-swiper-image-${index}`}
                />
              </div>

              <CopyRight
                copyRightsData={[el]}
                background={true}
                classNameExtra={'bottom-0 left-1'}
                customSize="xs:text-[6px] text-[8px]"
              // styleExtra={{ bottom: "1px", left: "3px" }}
              />
            </SwiperSlide>
          ))}
        {MultipleImages?.length && isEmptyObject(MultipleImages[0]) ? (
          <SwiperSlide data-testid={`beach-swiper-slide-last-add`} className="rounded-sandee">
            {/* <SatelightMap location={[beachData?.lat,beachData?.lon]}/> */}
            <iframe
              className=" bg-gray-200 w-full h-full rounded-sandee min-h-[280px]"
              loading="lazy"
              data-testid={`beach-swiper-image-last-add`}
              src={`https://maps.google.com/?q=${beachData?.lat},${beachData?.lon}&ie=UTF8&iwloc=&output=embed&t=k`}
            ></iframe>
            {/* <Image
              className="bg-gray-200 w-full h-full rounded-sandee min-h-[280px]"
              fill
              src={`https://maps.googleapis.com/maps/api/staticmap?center=${beachData?.lat},${beachData?.lon}&zoom=15&size=1600x1600&maptype=satellite&key=AIzaSyDLfO8bHqEoNNmnyh3scit7hHUQr7Jx36Q`}
              alt="Satellite view of the beach"
            />{" "} */}
          </SwiperSlide>
        ) : null}
      </Swiper>
      <div className="lg:hidden mt-4 h-fit">
        <p className="text-2xl truncate font-bold">{beachData?.name}</p>
        <p className=" text-sandee-sm flex-wrap flex items-center  font-normal py-1">
          <PinLocation className=" fill-black  h-4 w-4 mr-2" />
          {location?.map((item, i) => {
            if (item?.to && item?.title) {
              return (
                <Link
                  hrefLang="en-us"
                  href={item?.to}
                  key={`breadcrumbLink${i}`}
                  className=" decoration-0 text-[#000000] text-sandee-base"
                >
                  {item?.title}
                  {location?.length - 2 !== i && ","}&nbsp;
                </Link>
              );
            }
          })}
        </p>
      </div>
      {MultipleImages?.length > 1 && (
        <Swiper
          onSwiper={setThumbsSwiper}
          // loop={true}
          breakpoints={{
            ...generateBreakpoints(50, 800, 50, 80),
            ...generateBreakpoints(800, 1000, 50, 120),
            ...generateBreakpoints(1000, 1200, 50, 180),
            ...generateBreakpoints(1200, 1500, 50, 220),
            ...generateBreakpoints(1500, 2400, 50, 320),
          }}
          spaceBetween={15}
          // slidesPerView={7}
          freeMode={true}
          watchSlidesProgress={true}
          modules={[FreeMode, Navigation, Thumbs]}
          className="mySwiper lg:!mx-auto lg:!pt-[26px] !pt-4"
          data-testid="beach-thumbnail-swiper"
        >
          {MultipleImages?.map((el, index) => (
            <SwiperSlide
              key={el?.id}
              className=" rounded-md max-w-[75px] md:max-w-[90px] lg:max-w-[90px] xl:max-w-[100px]"
              data-testid={`beach-thumbnail-slide-${index}`}
            >
              {/* <div className="relative h-[60vh] aspect-square  rounded-md ">
              <div className="relative  overflow-hidden  w-full h-full  rounded-md"> */}
              <CustomeImage
                // priority
                className=" rounded-md transition-transform duration-1000 ease-in-out transform group-hover:scale-125 w-full"
                src={FinalImageGenerator(el, 400)}
                title={`${beachData?.name} Image - ${index + 1}`}
                alt={altText(el)}
                fill
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                blurDataURL={blurDataURL(1650, 1100)}
                placeholder="blur"
                data-testid={`beach-thumbnail-image-${index}`}
              />

              {/* <CopyRight
                copyRightsData={[el]}
                background={false}
                styleExtra={{ bottom: "1px", left: "3px" }}
                copyRightExtraStyle={{ fontSize: "4px" }}
              /> */}
              {/* </div>
            </div> */}
            </SwiperSlide>
          ))}
          <SwiperSlide data-testid={`beach-thumbnail-slide-add-last`} className=" rounded-md ">
            <Image
              // priority
              className=" rounded-md transition-transform duration-1000 ease-in-out transform group-hover:scale-125 w-full"
              src={FinalImageGenerator({}, 400)}
              title={`${beachData?.name} Image`}
              alt={altText(beachData)}
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              blurDataURL={blurDataURL(1650, 1100)}
              placeholder="blur"
              data-testid={`beach-thumbnail-image-add-last`}
            />
            <div className=" absolute top-0 left-0 h-full w-full backdrop-blur-[1px] p-2 rounded-md cursor-pointer bg-black !bg-opacity-40 flex justify-center items-center">
              {MultipleImages?.length >= 10 ?
                <BeachListModal MultipleImages={AllImages} beachData={beachData} Other={(props) => {
                  return (
                    <p {...props} className="w-full h-full text-center justify-center fill-white text-white text-base flex items-center">See All</p>
                  );
                }} /> : <></>
                // <AddPhotosSwiper className=" w-full h-full fill-white" />
              }
              {MultipleImages?.length < 10 ?
                <AuthWrapper
                  WithoutLogIn={<AddPhotosSwiper className=" w-full h-full fill-white" />}
                  WithLogIn={
                    MultipleImages?.length >= 10 ?
                      <BeachListModal MultipleImages={AllImages} beachData={beachData} Other={(props) => {
                        return (
                          // <AddPhotosSwiper
                          //   {...props}
                          //   className=" w-full h-full fill-white "
                          // />
                          <p {...props} className="w-full h-full fill-white justify-center text-white text-base flex items-center">See All</p>
                        );
                      }} /> :
                      <BeachPhotoUploadModal
                        beachData={beachData}
                        Other={(props) => {
                          return (
                            <AddPhotosSwiper
                              {...props}
                              className=" w-full h-full fill-white "
                            />
                          );
                        }}
                      />
                  }
                /> : <></>}
            </div>
          </SwiperSlide>
        </Swiper>
      )}
    </div>
  );
}
export function BeachWeatherMiniSwiper({ WeatherData, celcius }) {
  const WeatherDataList = WeatherData?.list;
  const timeZone = WeatherData?.city?.timezone;
  return (
    <div className=" h-full w-full">
      <Swiper
        init
        // loop={true}
        breakpoints={{
          ...generateBreakpoints(1460, 2400, 50, 310),
          ...generateBreakpoints(770, 1460, 50, 275),
          ...generateBreakpoints(50, 770, 50, 85),
        }}
        spaceBetween={0}
        freeMode={true}
        watchSlidesProgress={true}
        modules={[FreeMode, Navigation, Thumbs]}
        className="MiniWeatherSwiper"
      >
        {WeatherDataList?.map((el) => {
          return (
            <SwiperSlide
              key={`${el?.dt_txt}_${el?.dt}`}
            // className=" rounded-md max-w-[75px] lg:max-w-[90px] xl:max-w-[100px]"
            >
              <p
                className=" text-sandee-14 text-sandee-grey text-center m-0 p-0"
                suppressHydrationWarning
              >
                {convertUnixTimeTo12HourFormat(el?.dt, timeZone)}
                {/* {moment.utc(el?.dt_txt).format("LT") } */}
              </p>
              <div className="flex justify-center items-center">
                <Image
                  src={WeatherIconLink(el?.weather?.[0]?.icon)}
                  width={60}
                  height={60}
                  alt="Sandee Weather Icon"
                  className="p-2"
                />
              </div>
              <p className=" text-center">
                <span
                  className={`${celcius ? "w-0 hidden" : "text-sandee-blue "} `}
                >
                  {`${(1.8 * (el?.main?.temp - 273) + 32)?.toFixed(1)} °F`}
                </span>
                <span
                  className={`  ${!celcius ? " w-0 hidden " : "text-sandee-blue "
                    } `}
                >
                  {`${(el?.main?.temp - 273.15)?.toFixed(0)} °C`}
                </span>
              </p>
            </SwiperSlide>
          );
        })}
      </Swiper>
    </div>
  );
}
