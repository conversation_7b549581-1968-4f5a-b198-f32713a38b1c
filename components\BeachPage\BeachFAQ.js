"use client";
import React, { useState } from "react";
import { CustomContainer } from "../Custom-Display";
import { Activities } from "@/data/beachPageData";
import { EditorContent, isHTML, processContent } from "@/helper/functions";

const BeachFAQ = ({ beachData, extraFaq = [], dataTestid = "faq" }) => {
  const beachNameAnswer = "this Beach";
  const {
    name: beachName,
    // rating100: rating,
    beachCategory: FAQAnswer,
  } = beachData;
  const availableActivities = Activities.filter(
    (item) => FAQAnswer?.[item?.id]
  )?.map((el) => el?.label);
  const qaPairs = [
    {
      id: "1",
      question: `Does ${beachName} have parking? If so, is it free or paid?`,
      answer: FAQAnswer?.freeParking
        ? FAQAnswer?.paidParking
          ? `Yes, ${beachNameAnswer} provides paid parking at the entrance, although spaces are limited. Visitors can also find street parking nearby for easy access to the beach.`
          : `Yes, ${beachNameAnswer} provides free parking at the entrance, although spaces are limited. Visitors can also find street parking nearby for easy access to the beach.`
        : `No, ${beachNameAnswer} does not have dedicated parking but has street parking near the entrance.`,
    },
    {
      id: "2",
      question: `Does ${beachName} have lifeguards?`,
      answer: FAQAnswer?.lifeguard
        ? `Yes, ${beachNameAnswer} ensures safety with professional lifeguards on duty throughout the day, offering peace of mind for beachgoers.`
        : `No, ${beachNameAnswer} does not have lifeguards monitoring the beach - please be careful when swimming on this beach.`,
    },
    {
      id: "3",
      question: `Is ${beachName} a family-friendly beach?`,
      answer:
        FAQAnswer?.restRooms && FAQAnswer?.lifeguard && FAQAnswer?.restaurant
          ? `Yes, ${beachNameAnswer} is the perfect family destination, offering clean restrooms, vigilant lifeguards, and a variety of nearby food options, ensuring an enjoyable day for all.`
          : `No, ${beachNameAnswer} is not family-friendly and does not have restrooms, lifeguards, and nearby food.`,
    },
    {
      id: "4",
      question: `Is ${beachName} good for swimming?`,
      answer: FAQAnswer?.swimming
        ? `Yes, ${beachNameAnswer} boasts excellent swimming conditions, attracting swimmers of all skill levels with its gentle waves and clear waters.`
        : `No, ${beachNameAnswer} is not generally considered a good swimming beach.`,
    },
    {
      id: "5",
      question: `Does ${beachName} have restrooms?`,
      answer: FAQAnswer?.restRooms
        ? `Yes, Restrooms are conveniently available on or adjacent to the beach at ${beachNameAnswer}.`
        : `No, ${beachNameAnswer} does not have restrooms on or near the beach.`,
    },
    {
      id: "6",
      question: `Is ${beachName} a nude beach?`,
      answer: FAQAnswer?.nude
        ? `Yes, ${beachNameAnswer} is known for being a nude beach, providing a liberating experience for those seeking clothing-optional sunbathing and swimming.`
        : `No, ${beachNameAnswer} maintains a family-friendly atmosphere and does not allow nude bathing.`,
    },
    {
      id: "7",
      question: `Is there food near ${beachName}?`,
      answer:
        FAQAnswer?.restaurant && FAQAnswer?.beachVendors
          ? `Yes, ${beachNameAnswer} has multiple food options near or on the beach, including restaurants and beach vendors.`
          : FAQAnswer?.restaurant
            ? `Yes, ${beachNameAnswer} has multiple restaurant options near or on the beach.`
            : FAQAnswer?.beachVendors
              ? `No, ${beachNameAnswer} does not have nearby restaurants but frequently has beach vendors selling food on or near the beach.`
              : `No, ${beachNameAnswer} does not have nearby food options - please plan your beach day accordingly.`,
    },
    {
      id: "8",
      question: `What are the activities at ${beachName}?`,
      answer: availableActivities?.length
        ? availableActivities?.length === 1
          ? `Yes, ${beachNameAnswer} is a good place to ${availableActivities}`
          : `Yes, ${beachNameAnswer} is renowned for its vibrant beach scene, offering various activities for everyone to enjoy ${availableActivities?.join(
            ", "
          )}.`
        : `No, ${beachNameAnswer} is not known for any activities in particular.`,
    },
    {
      id: "9",
      question: `Does ${beachName} have accommodations for people with disabilities?`,
      answer: FAQAnswer?.disability
        ? `Yes, ${beachNameAnswer} offers accommodations for people with disabilities, allowing everyone to relish the coastal experience.`
        : `No, ${beachNameAnswer} does not have accommodations for people with disabilities.`,
    },
    {
      id: "10",
      question: `Is ${beachName} a dog-friendly beach?`,
      answer: FAQAnswer?.dogs
        ? `Yes, bring your dog with you to ${beachNameAnswer}!`
        : `No, ${beachNameAnswer} is not dog-friendly.`,
    },
    {
      id: "11",
      question: `Is ${beachName} good for surfing?`,
      answer: FAQAnswer?.surfing
        ? `Yes, surfing is an activity that is frequently enjoyed at ${beachNameAnswer}.`
        : `No, ${beachNameAnswer} is not generally considered to be good for surfing.`,
    },
    ...extraFaq
  ];
  const [accordions, setAccordions] = useState(qaPairs);
  // const CollapseAll = () => {
  //   setAccordions(
  //     accordions.map((accordion) => ({ ...accordion, isActive: false }))
  //   );
  // };
  // const ExpandAll = () => {
  //   setAccordions(
  //     accordions.map((accordion) => ({ ...accordion, isActive: true }))
  //   );
  // };
  const AccordionToggle = (index) => {
    setAccordions((prevState) =>
      prevState.map((accordion) => {
        if (accordion?.id === index) {
          return { ...accordion, isActive: !!!accordion?.isActive };
        }
        return accordion;
      })
    );
  };

  return (
    <div data-testid={dataTestid} className=" bg-[#FAFAFA] pt-8 pb-3 md:px-4 my-5 rounded-sandee">
      <div className="flex  flex-col md:flex-row" data-testid="beach-faq-header">
        <div className=" w-full   flex flex-col  gap-3 text-center" data-testid="beach-faq-title-container">
          <p data-testid="beach-faq-subtitle" className=" lg:text-xl text-lg font-bold text-sandee-orange uppercase">
            Explore Top Destinations
            {/* Top Traveler Questions */}
          </p>

          <p data-testid="beach-faq-title" className=" md:text-sandee-32 text-2xl font-semibold leading-none -mt-1">
            Frequently Asked Questions
          </p>
          <p data-testid="beach-faq-description" className=" text-[#374151B2] lg:text-[22px] text-sandee-sm font-normal leading-none px-12">
            Surf through our FAQs to help you find your perfect beach!
          </p>
        </div>
        {/* <div className=" w-full lg:w-3/12 flex justify-center items-center md:items-end flex-col gap-3">
          <div className="flex flex-wrap gap-5">
            <button
              id="ExpandAll"
              className=" text-sandee-blue active:text-white active:bg-sandee-blue border-2 border-sandee-blue  py-2 px-5 transition ease-in-out duration-300  px-10 text-xs font-medium  rounded-full active:scale-[0.7] hover:scale-110"
            >
              Expand All
            </button>
            <button
              id="CollapseAll"
              className=" text-sandee-blue active:text-white active:bg-sandee-blue border-2 border-sandee-blue  py-2 px-5 transition ease-in-out duration-300  px-10 text-xs font-medium  rounded-full active:scale-[0.7] hover:scale-110"
            >
              Collapse All
            </button>
          </div>
        </div> */}
      </div>
      <CustomContainer dataTestid="beach-faq-container" className=" lg:!px-20 xl:!px-20 2xl:!px-20 3xl:!px-20 !px-2">
        <div className="FAQ mb-8 mt-10" data-testid="beach-faq-list">
          {accordions?.map((singleFAQ, index) => (
            <div
              className={`accordion  ${singleFAQ?.isActive ? "accordion__active" : ""
                }`}
              key={singleFAQ?.id}
              onClick={() => {
                AccordionToggle(singleFAQ?.id);
              }}
              data-testid={`beach-faq-item-${index}`}
            >
              <div className="accordion__intro flex justify-between" data-testid={`beach-faq-question-container-${index}`}>
                <p data-testid={`beach-faq-question-${index}`} className="!text-base font-medium text-[#1A1A1A] me-3">
                  {" "}
                  {singleFAQ?.question}
                </p>
                <div data-testid={`beach-faq-toggle-icon-${index}`} className="icon__content flex justify-center items-center">
                  {singleFAQ?.isActive ? (
                    <svg
                      width={23}
                      height={23}
                      viewBox="0 0 23 23"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M7.5 11.0742H15.5M21.5 11.0742C21.5 16.5971 17.0228 21.0742 11.5 21.0742C5.97715 21.0742 1.5 16.5971 1.5 11.0742C1.5 5.55137 5.97715 1.07422 11.5 1.07422C17.0228 1.07422 21.5 5.55137 21.5 11.0742Z"
                        stroke="#00AAE3"
                        strokeWidth={2}
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  ) : (
                    <svg
                      width={25}
                      height={25}
                      viewBox="0 0 25 25"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M12.5 8.07422V16.0742M8.5 12.0742H16.5M22.5 12.0742C22.5 17.5971 18.0228 22.0742 12.5 22.0742C6.97715 22.0742 2.5 17.5971 2.5 12.0742C2.5 6.55137 6.97715 2.07422 12.5 2.07422C18.0228 2.07422 22.5 6.55137 22.5 12.0742Z"
                        stroke="#00AAE3"
                        strokeWidth={2}
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  )}
                  {/* <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="10"
                    viewBox="0 0 24 14"
                    fill="none"
                  >
                    <path
                      d="M10.9393 13.0607C11.5251 13.6464 12.4749 13.6464 13.0607 13.0607L22.6066 3.51472C23.1924 2.92893 23.1924 1.97919 22.6066 1.3934C22.0208 0.807611 21.0711 0.807611 20.4853 1.3934L12 9.87868L3.51472 1.3934C2.92893 0.807611 1.97919 0.807611 1.3934 1.3934C0.807611 1.97919 0.807611 2.92893 1.3934 3.51472L10.9393 13.0607ZM10.5 11V12H13.5V11H10.5Z"
                      fill="#00AAE3"
                    />
                  </svg> */}
                </div>
              </div>
              <div data-testid={`beach-faq-answer-${index}`} className="accordion__content ">
                <EditorContent value={isHTML(singleFAQ?.answer)
                  ? processContent(singleFAQ?.answer)
                  : `${singleFAQ?.answer}`} />
                {/* <p className="!text-base">{singleFAQ?.answer}</p> */}
              </div>
              {index < qaPairs?.length - 1 && (
                <div data-testid={`beach-faq-divider-${index}`} className=" border-b border-sandee-grey border-opacity-15 w-full h-1"></div>
              )}
            </div>
          ))}
        </div>
      </CustomContainer>
    </div>
  );
};

export default BeachFAQ;
