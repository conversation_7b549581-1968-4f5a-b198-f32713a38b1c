"use client";
import { Modal, notification } from "antd";
import React, { useState } from "react";
import { FlagIcon } from "../social-icons/icons";
import { postSuggestion } from "@/app/(HomeHeader)/action";
import useLoggedIn from "@/helper/hook/useLoggedIn";
import { isMobileView } from "@/helper/functions";

const checkboxes = [
  { id: "access", label: "Access", value: "access" },
  { id: "address", label: "Address", value: "address" },
  { id: "amenities", label: "Amenities", value: "amenities" },
  { id: "beach-map", label: "Beach Map", value: "beach-map" },
  { id: "beach-name", label: "Beach Name", value: "beach-name" },
  { id: "blog", label: "Blog", value: "blog" },
  { id: "city", label: "City", value: "city" },
  { id: "country", label: "Country", value: "country" },
  { id: "description", label: "Description", value: "description" },
  { id: "faq", label: "FAQ", value: "faq" },
  { id: "food", label: "Food", value: "food" },
  { id: "others", label: "Others", value: "others" },
  { id: "rentals", label: "Rentals", value: "rentals" },
  { id: "state", label: "State", value: "state" },
  { id: "video", label: "Video", value: "video" },
];
// const sortedCheckboxes = checkboxes.sort((a, b) =>
//   a.label.localeCompare(b.label)
// );

// // Step 2: Group checkboxes by the starting letter of their label
// const groupedCheckboxes = sortedCheckboxes.reduce((acc, checkbox) => {
//   const firstLetter = checkbox.label[0].toUpperCase();
//   if (!acc[firstLetter]) {
//     acc[firstLetter] = [];
//   }
//   acc[firstLetter].push(checkbox);
//   return acc;
// }, {});
const BeachFlagInformation = ({ beachData, customBtn }) => {
  const { token } = useLoggedIn();
  const [open, setOpen] = useState(false);
  const isMobileViews = isMobileView()
  const toggle = () => {
    setOpen((prev) => !prev);
  };

  return (
    <>
      {customBtn ? (
        <div onClick={toggle}>{customBtn}</div>
      ) : (
        <div className="flex items-center justify-center cursor-pointer">
          <FlagIcon className="w-5 h-5 fill-sandee-blue" onClick={toggle} />
        </div>
      )}
      <Modal
        role="dialog"
        aria-modal="true"
        open={open}
        onCancel={() => toggle()}
        footer={null}
        closable={isMobileViews}
        okButtonProps={{
          style: {
            display: "none",
          },
        }}
        style={{
          padding: 0,
        }}
        width={450}
        centered
        wrapClassName=" !p-0"
        styles={{
          body: {
            padding: "0px !important",
          },
        }}
        cancelButtonProps={{
          style: {
            display: "none",
          },
        }}
      >
        <div
          className="custom-auth "
        // style={{
        //   border: "none",
        //   borderRadius: "30pt",
        //   textAlign: "center",
        //   padding: "40px",
        // }}
        >
          <div className={` container ${open ? "!max-w-[500px]" : ""}`}>
            <form
              className="form"
              onSubmit={async (e) => {
                e.preventDefault();
                const topics = [];

                e.target.suggestions.forEach((checkbox) => {
                  if (checkbox?.checked) {
                    topics.push(checkbox.value);
                  }
                });

                if (topics?.length) {
                  const payload = {
                    topics,
                    suggestion: e.target?.suggestion?.value,
                    AllBeachId: beachData?.id,
                  };

                  const response = await postSuggestion(payload, token);
                  if (response?.status === "success") {
                    // logController("Review Added Successfully");
                    notification.success({
                      message: "Suggestion Added Successfully",
                      duration: 3,
                    });
                    e.target.reset();
                    toggle();
                  } else {
                    notification.error({
                      message: "Something went wrong",
                      duration: 3,
                    });
                    // logController("You are not Authorized");
                  }
                } else {
                  notification.error({
                    message: "Please Select at least one Topic",
                    duration: 3,
                  });
                }
              }}
            >
              <p className=" text-center text-sandee-18 font-bold">
                🌟 Please Help us improve Our Beach Data for {beachData?.name}!
                🌟
              </p>
              <p className=" text-center text-sandee-12 font-normal mt-3 mb-3">
                Please let us know how we can improve our data - your
                contribution and feedback are important to us!
              </p>
              <div className=" grid grid-rows-5 grid-flow-col-dense ">
                {/* <div className=" grid grid-flow-col auto-cols-max grid-rows-5"> */}

                {checkboxes?.map((singleCheck) => (
                  <div
                    className="flex justify-start items-center"
                    key={singleCheck?.label}
                  >
                    <input
                      className="form-check-input mr-1"
                      type="checkbox"
                      name="suggestions"
                      value={singleCheck?.value}
                      id={singleCheck?.value}
                    />
                    <label
                      className="form-check-label text-sandee-14"
                      htmlFor={singleCheck?.value}
                    >
                      {singleCheck?.label}{" "}
                    </label>
                  </div>
                ))}
                {/* {Object.keys(groupedCheckboxes).map((letter, colIndex) => (
                  <div key={colIndex} className="flex flex-col  pb-3 mb-4">
                    {groupedCheckboxes[letter].map((singleCheck) => (
                      <div
                        className="flex justify-start items-center"
                        key={singleCheck.label}
                      >
                        <input
                          className="form-check-input mr-1"
                          type="checkbox"
                          name="suggestions"
                          value={singleCheck.value}
                          id={singleCheck.value}
                        />
                        <label
                          className="form-check-label text-sandee-14"
                          htmlFor={singleCheck.value}
                        >
                          {singleCheck.label}
                        </label>
                      </div>
                    ))}
                  </div>
                ))} */}
              </div>
              <Divider>
                <p className=" text-black text-sandee-12 font-bold">
                  Write Your message
                </p>
              </Divider>
              <textarea
                className="input !w-full !h-full min-h-[120px] text-sandee-12 align-text-top text-wrap text-start"
                name="suggestion"
                type="textarea"
                placeholder="Enter a Message"
                required
              />

              <div className="flex justify-center items-center ">
                <input
                  defaultValue="Submit a Suggestion"
                  //   disabled={loading}
                  type="submit"
                  className={` login-button `}
                />
              </div>
            </form>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default BeachFlagInformation;
export const Divider = ({ children }) => {
  return (
    <div className="relative flex py-2 items-center">
      <div className="flex-grow border-t border-gray-400"></div>
      <span className="flex-shrink mx-4 text-gray-400">{children}</span>
      <div className="flex-grow border-t border-gray-400"></div>
    </div>
  );
};
