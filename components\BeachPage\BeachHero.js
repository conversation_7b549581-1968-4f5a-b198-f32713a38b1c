import React from "react";
// import BeachWeatherCard from "./BeachWeatherCard";
import BeachDualSwiper from "./BeachDualSwiper";
import Link from "next/link";
import { PinLocation } from "../social-icons/icons";
import BeachWeatherCard from "./BeachWeatherCard";
import { getEmbedUrl } from "@/helper/functions";
// import BeachListModal from "./BeachListModal";
// import { getEmbedUrl } from "@/helper/functions";


const BeachPage_BeachHero_Section = ({
  beachData,
  MultipleImages,
  AllImages,
  WeatherDeatils,
}) => {
  const location =
    beachData?.islandSlug && beachData?.island?.name
      ? [
        {
          title: beachData?.country?.name,
          to: `/${beachData?.country?.slug}`,
        },
        {
          title: beachData?.island?.name,
          to: `/island/${beachData?.islandSlug}`,
        },
        {
          title: beachData?.city?.name,
          to: `/${beachData?.country?.slug}/${beachData?.state?.slug}/${beachData?.city?.slug}`,
        },
        {
          title: beachData?.name,
        },
      ]
      : [
        {
          title: beachData?.country?.name,
          to: `/${beachData?.country?.slug}`,
        },
        {
          title: beachData?.state?.name,
          to: `/${beachData?.country?.slug}/${beachData?.state?.slug}`,
        },
        {
          title: beachData?.city?.name,
          to: `/${beachData?.country?.slug}/${beachData?.state?.slug}/${beachData?.city?.slug}`,
        },
        {
          title: beachData?.name,
        },
      ];

  return (
    <>
      {/* <div className="lg:flex lg:justify-center lg:items-center lg:my-[1.5rem]">
        <div className="xl:w-1/2 lg:w-2/3 lg:h-[60vh] h-[62vh] ">
          <BeachDualSwiper
            MultipleImages={MultipleImages}
            beachData={beachData}
          />
        </div>
      </div> */}
      {/* <div className="lg:hidden mt-7">
        <p className="text-2xl truncate font-bold">{beachData?.name}</p>
        <p className=" text-sandee-sm flex-wrap flex items-center  font-normal py-1">
          <PinLocation className=" fill-black  h-4 w-4 mr-2" />
          {location?.map((item, i) => {
            if (item?.to && item?.title) {
              return (
                <Link
                  hrefLang="en-us"
                  href={item?.to}
                  key={`breadcrumbLink${i}`}
                  className=" decoration-0 text-[#000000] text-sandee-base"
                >
                  {item?.title}
                  {location?.length - 2 !== i && ","}&nbsp;
                </Link>
              );
            }
          })}
        </p>
      </div> */}

      <div className="w-full flex  flex-col lg:flex-row gap-5 my-5"
        data-testid="beach-hero-section">
        <div
          data-testid="beach-hero-swiper-container"
          className="realtive lg:min-h-[400px] xl:min-h-[500px] 4xl:min-h-[650px]  w-full lg:w-7/12 xl:w-7/12 4xl:w-8/12 ">
          <div
            data-testid="beach-hero-swiper-wrapper"
            className="relative h-[60vh] lg:h-full  w-full  ">
            <BeachDualSwiper
              MultipleImages={MultipleImages} a
              AllImages={AllImages}
              beachData={beachData}
              dataTestid="beach-hero-swiper"
            />
          </div>
        </div>
        <div
          data-testid="beach-hero-right-section"
          className={`w-full   lg:w-5/12 xl:w-5/12 4xl:w-4/12 gap-5 flex-col lg:pr-5 flex pt-11 lg:pt-0 `}>
          <div
            data-testid="beach-weather-card-container"
            className="rounded-sandee shadow-md">
            <BeachWeatherCard
              WeatherDeatils={WeatherDeatils}
              beachData={beachData}
              dataTestid="beach-weather-card"
            />
          </div>

          {beachData?.droneVideo && (
            <div
              data-testid="beach-drone-video-container"
              className=" w-full max-h-[290px] aspect-video flex flex-col  rounded-sandee bg-gray-200 shadow-lg">
              <iframe
                src={`${getEmbedUrl(beachData?.droneVideo)}`}
                allowFullScreen
                loading="lazy"
                className=" bg-gray-200 h-full rounded-sandee  lg:aspect-video "
                data-testid="beach-drone-video"
              ></iframe>
            </div>
          )}
        </div>
        {/* <BeachListModal MultipleImages={MultipleImages} beachData={beachData} /> */}
        {/* <BeachAllPhotos MultipleImages={MultipleImages} /> */}
      </div>
    </>
  );
};

export default BeachPage_BeachHero_Section;
