"use client";
import { BeachPageData } from "@/data/beachPageData";
import React, { useState } from "react";
import NameTitle from "../Common/NameTitle";
import CustomButton from "../Custom-Button";
import Image from "next/image";
import BeachFlagInformation from "./BeachFlagInformation";

const BeachInformation_Section = ({ beachData }) => {
  const DATA = BeachPageData?.beachInformation?.map((el) => ({
    title: el?.title,
    items: el?.items,
    display:
      el?.items.filter((elp) => beachData?.beachCategory?.[elp?.id])?.length !==
      0,
  }));
  // const DATATOACESS = [];
  // BeachPageData?.beachInformation?.forEach((el) => {
  //   DATATOACESS.push(...el?.items);
  // });
  // logController(DATATOACESS?.filter((el) => !el?.icon));
  const [dataPointsVisible, setDataPointsVisible] = useState(false);
  // logController(
  //   DATA?.map((el) => el?.display).some((el) => el === true),
  //   beachData?.name,
  //   "\n\n",
  //   DATA
  // );
  return DATA?.map((el) => el?.display).some((el) => el === true) ? (
    <div className="mt-3" data-testid="beach-information-section">
      <NameTitle
        className="mt-5 uppercase"
        name={`Know Before You Go`}
        // name={`All Amenities at ${beachData?.name}`}
        // description={"Amenities this beach have"}
        type={3}
        dataTestid="beach-information-title"
        extraButton={
          <div className=" hidden md:flex justify-end items-start  !text-nowrap">
            <CustomButton
              type={4}
              id="beach-info"
              onClick={() => {
                setDataPointsVisible((prev) => !prev);
              }}
            >
              {dataPointsVisible ? "See Less" : "See All"}
            </CustomButton>
          </div>
        }
      />
      <div data-testid="beach-information-grid" className=" grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-6 ">
        {DATA.map(({ title, items, display }, index) => (
          <div
            key={title}
            data-testid={`beach-information-category-${index}`}
            className={`${!display && !dataPointsVisible
              ? "hidden list-should-hide "
              : "flex flex-col gap-x-3 gap-y-4"
              }`}
          >
            <>
              <p
                className=" text-black text-[20px] font-semibold"
                data-testid={`beach-information-category-title-${index}`}
              // id={title}
              >
                {title}
              </p>
              {
                // beachData?.beachCategory &&
                // items?.slice(0, itemsToShow)?.map((link) => {
                items?.map((link, itemIndex) => {
                  return (
                    <li
                      key={link?.id}
                      data-testid={`beach-information-item-${index}-${itemIndex}`}
                      className={` list-none ${!beachData?.beachCategory?.[link?.id] &&
                        !dataPointsVisible
                        ? "hidden list-should-hide "
                        : ""
                        }`}
                    >
                      <p
                        className={`capitalize m-0 gap-3 text-wrap flex text-ellipsis overflow-hidden  items-center py-0  font-normal transition-colors ${beachData?.beachCategory?.[link?.id]
                          ? "text-black"
                          : "text-[#898888]"
                          } leading-snug`}
                        data-testid={`beach-information-item-text-${index}-${itemIndex}`}
                      >
                        <span className="w-[20px]" data-testid={`beach-information-item-icon-${index}-${itemIndex}`}>
                          {beachData?.beachCategory?.[link?.id] ? (
                            link?.icon ? (
                              <Image
                                src={`/static/icons/BeachAmenities/${link?.id}Blue.svg`}
                                width={20}
                                height={20}
                                alt="Icon for Beach Amenities"
                              />
                            ) : (
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width={20}
                                height={20}
                                viewBox="0 0 16 16"
                                fill="none"
                                className=" p-[2px]"
                              >
                                <path
                                  d="M15.75 8C15.75 12.2802 12.2802 15.75 8 15.75C3.71978 15.75 0.25 12.2802 0.25 8C0.25 3.71978 3.71978 0.25 8 0.25C12.2802 0.25 15.75 3.71978 15.75 8ZM7.10356 12.1036L12.8536 6.35356C13.0488 6.15831 13.0488 5.84172 12.8536 5.64647L12.1465 4.93937C11.9512 4.74409 11.6346 4.74409 11.4393 4.93937L6.75 9.62869L4.56066 7.43934C4.36541 7.24409 4.04881 7.24409 3.85353 7.43934L3.14644 8.14644C2.95119 8.34169 2.95119 8.65828 3.14644 8.85353L6.39644 12.1035C6.59172 12.2988 6.90828 12.2988 7.10356 12.1036Z"
                                  fill="#00AAE3"
                                />
                              </svg>
                            )
                          ) : (
                            <svg
                              fill="#dddddd"
                              width={20}
                              height={20}
                              version="1.1"
                              id="Layer_1"
                              xmlns="http://www.w3.org/2000/svg"
                              xmlnsXlink="http://www.w3.org/1999/xlink"
                              viewBox="0 0 300.003 300.003"
                              xmlSpace="preserve"
                              stroke="#898888"
                              className=" p-[2px]"
                            >
                              <g id="SVGRepo_bgCarrier" strokeWidth={0} />
                              <g
                                id="SVGRepo_tracerCarrier"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                              <g id="SVGRepo_iconCarrier">
                                <g>
                                  <g>
                                    <path d="M150,0C67.159,0,0.001,67.159,0.001,150c0,82.838,67.157,150.003,149.997,150.003S300.002,232.838,300.002,150 C300.002,67.159,232.839,0,150,0z M206.584,207.171c-5.989,5.984-15.691,5.984-21.675,0l-34.132-34.132l-35.686,35.686 c-5.986,5.984-15.689,5.984-21.672,0c-5.989-5.991-5.989-15.691,0-21.68l35.683-35.683L95.878,118.14 c-5.984-5.991-5.984-15.691,0-21.678c5.986-5.986,15.691-5.986,21.678,0l33.222,33.222l31.671-31.673 c5.986-5.984,15.694-5.986,21.675,0c5.989,5.991,5.989,15.697,0,21.678l-31.668,31.671l34.13,34.132 C212.57,191.475,212.573,201.183,206.584,207.171z" />{" "}
                                  </g>
                                </g>
                              </g>
                            </svg>
                          )}
                        </span>

                        {link?.label}
                      </p>
                    </li>
                  );
                })
              }
            </>
          </div>
        ))}
      </div>

      <div data-testid="beach-information-see-all-button-mobile" className="  md:hidden  my-5  !text-nowrap">
        <CustomButton
          type={6}
          id="beach-info-bottom"
          onClick={() => {
            setDataPointsVisible((prev) => !prev);
          }}
        >
          {dataPointsVisible ? "See Less" : "See All"}
        </CustomButton>
      </div>
      <div data-testid="beach-information-suggest-edit" className="mt-7 mb-2 flex justify-center">
        <BeachFlagInformation
          beachData={beachData}
          customBtn={
            <CustomButton
              type={5}
              id="beach-info-bottom"
            >
              Suggest an edit?
            </CustomButton>
          }
        />
      </div>
    </div>
  ) : (
    ""
  );
};

export default BeachInformation_Section;
