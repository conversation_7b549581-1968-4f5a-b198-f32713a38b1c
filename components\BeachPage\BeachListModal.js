"use client"
import { <PERSON><PERSON>, <PERSON>, Spin } from 'antd'
import React, { useEffect, useMemo, useState } from 'react'
import { CancelIcon, EyeIcon } from '../social-icons/icons'
import Image from 'next/image'
import CopyRight from '../Common/CopyRight'
import { altText, blurDataURL, FinalImageGenerator, isEmptyObject, defaultImage } from '@/helper/functions'
import { CustomGrid } from '../Custom-Display'
import { BeachCustomButton } from './BeachReviewAndPhoto'
import CustomeImage from '../Common/CustomeImage'


const BeachListModal = ({ MultipleImages, beachData, Other }) => {

    const [isLoading, setIsLoading] = useState(false);
    const [open, setOpen] = useState(false);
    const [previewImageIndex, setPreviewImageIndex] = useState(null);
    const [isClient, setIsClient] = useState(false);
    const [imgSrc, setImgSrc] = useState("");


    const handleImageClick = (index) => {
        // console.log(index, imgSrc, MultipleImages[index - 1])
        setPreviewImageIndex(index);
        setImgSrc(FinalImageGenerator(MultipleImages[index], 1600, 3));
    };

    const handleClosePreview = () => {
        setPreviewImageIndex(null);
    };

    const handleNext = async () => {
        // console.log(previewImageIndex, MultipleImages.length - 1, "next")
        if (previewImageIndex < MultipleImages.length - 1) {
            triggerAnimation(async () => {
                const nextIndex = previewImageIndex + 1;
                setPreviewImageIndex(nextIndex);
                setImgSrc(FinalImageGenerator(MultipleImages[nextIndex], 1600, 3));
            });
        }
    };

    const handlePrev = async () => {
        if (previewImageIndex > 0) {
            triggerAnimation(async () => {
                const prevIndex = previewImageIndex - 1;
                setPreviewImageIndex(prevIndex);
                setImgSrc(FinalImageGenerator(MultipleImages[prevIndex], 1600, 3));
            });
        }
    };

    const triggerAnimation = async (updateIndex) => {
        await updateIndex(); // Update the index after preloading the image
    };

    const toggle = (e) => {
        e?.stopPropagation();

        // Close the photo preview if it's open
        if (previewImageIndex !== null) {
            setPreviewImageIndex(null);
            return;
        }

        // Toggle the modal state
        setOpen((prev) => !prev);
    };

    useEffect(() => {
        const handleKeyDown = (e) => {
            if (e.key === "Escape") {
                if (previewImageIndex !== null) {
                    // Close the photo preview first
                    setPreviewImageIndex(null);
                } else if (open) {
                    // Close the modal only if it is open
                    setOpen(false);
                }
            }
        };

        if (open) {
            window.addEventListener("keydown", handleKeyDown);
        } else {
            window.removeEventListener("keydown", handleKeyDown);
        }

        // Cleanup the listener
        return () => {
            window.removeEventListener("keydown", handleKeyDown);
        };
    }, [open, previewImageIndex]); // Depend on `open` and `previewImageIndex`

    useEffect(() => {
        setIsClient(true);
    }, []);

    // const previewImage = useMemo(() => {

    //     console.log(FinalImageGenerator(MultipleImages[previewImageIndex], 1600, 3), imgSrc)
    //     return (<div data-testid="beach-preview-modal" className="fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center">
    //         <button
    //             data-testid="beach-close-preview-button"
    //             className="absolute top-5 right-5 text-white text-2xl"
    //             onClick={handleClosePreview}
    //         >
    //             &times;
    //         </button>
    //         <div data-testid="beach-preview-container" className="relative w-3/4 h-3/4">
    //             {isLoading ? (
    //                 <Spin className="absolute inset-0 m-auto flex justify-center items-center" size="large" />
    //             ) : (
    //                 <div
    //                     className={`relative w-full h-full transition-all duration-300 opacity-100 translate-x-0
    //                     `}
    //                 >
    //                     <Image
    //                         src={imgSrc}
    //                         alt={altText(beachData)}
    //                         fill
    //                         className="rounded-lg"
    //                         onError={() => {
    //                             if (imgSrc !== defaultImages) {
    //                                 return setImgSrc(defaultImages);
    //                             }
    //                         }}
    //                     />
    //                 </div>
    //             )}
    //         </div>
    //         {previewImageIndex > 0 && (
    //             <button
    //                 data-testid="beach-prev-image-button"
    //                 className="absolute left-5 text-white text-3xl"
    //                 onClick={handlePrev}
    //             >
    //                 &#10094;
    //             </button>
    //         )}
    //         {previewImageIndex < MultipleImages.length - 1 && (
    //             <button
    //                 data-testid="beach-next-image-button"
    //                 className="absolute right-5 text-white text-3xl"
    //                 onClick={handleNext}
    //             >
    //                 &#10095;
    //             </button>
    //         )}
    //     </div>)
    // }, [previewImageIndex, imgSrc]);
    if (!isClient) {
        return null;
    }



    return (
        <>
            {!Other ? (
                <BeachCustomButton dataTestid="beach-show-all-button" onClick={toggle}>Show All</BeachCustomButton>
            ) : (
                <Other data-testid="beach-custom-trigger-button" onClick={toggle} />
            )}
            <Modal
                role="dialog"
                aria-modal="true"
                data-testid="beach-gallery-modal"
                open={open}
                onCancel={() => toggle()}
                footer={null}
                closable={false}
                okButtonProps={{ style: { display: "none" } }}
                width={1200}
                className="shark-modal top-[35px]"
                wrapClassName="!p-0"
                cancelButtonProps={{ style: { display: "none" } }}
            >
                <div className="bg-white rounded-xl h-full">
                    <Space direction="vertical" className="px-4 pb-4 w-full">
                        <div className="flex justify-between pt-4">
                            <div>
                                <p data-testid="beach-gallery-title" className="font-extrabold text-2xl">{"Photo Gallery"}</p>
                            </div>
                            <CancelIcon data-testid={`beach-close-modal-button`} className="w-7 h-7 opacity-50 cursor-pointer" onClick={toggle} />
                        </div>
                        <div data-testid="beach-gallery-container" className="h-[580px] overflow-y-scroll overflow-hidden">
                            {MultipleImages?.length && !isEmptyObject(MultipleImages?.[0]) ? (
                                <div className="mr-2 space-y-3">
                                    <CustomGrid
                                        data={MultipleImages}
                                        dataTestid={`beach-gallery`}
                                        className="gap-2 sm:gap-4"
                                        Component={({ data: dataProps, index }) => (
                                            <div
                                                data-testid={`beach-gallery-image-wrapper-${index}`}
                                                onClick={() => handleImageClick(index)}
                                                className="relative hover:cursor-pointer overflow-hidden rounded-lg aspect-[3/2] w-full h-full group"
                                            >
                                                <CustomeImage
                                                    className="rounded-lg transition-transform duration-1000 ease-in-out transform group-hover:scale-125 w-full"
                                                    src={FinalImageGenerator(dataProps, 1600, 3)}
                                                    data-testid={`beach-gallery-image-${index}`}
                                                    title={`${beachData?.name} Image - ${index + 1}`}
                                                    alt={altText(beachData)}
                                                    fill
                                                    blurDataURL={blurDataURL(1650, 1100)}
                                                    placeholder="blur"
                                                // onError={(e) => {
                                                //     console.log("first error", e);

                                                // }}
                                                />
                                                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-40 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                                    <EyeIcon width={24} height={24} stroke={"#ffffff"} className="text-white text-4xl" />
                                                </div>
                                                <CopyRight
                                                    copyRightsData={[dataProps]}
                                                    background={true}
                                                    classNameExtra="bottom-0 left-1"
                                                    customSize="xs:text-[6px] text-[8px]"
                                                />
                                            </div>
                                        )}
                                        xs={1}
                                        sm={2}
                                        md={3}
                                        lg={4}
                                    />
                                </div>
                            ) : (
                                <p data-testid="beach-gallery-empty-message" className="flex items-center h-full justify-center text-xl">Beach Image data not found</p>
                            )}
                        </div>
                    </Space>
                </div>
                {previewImageIndex !== null && (
                    <div data-testid="beach-preview-modal" className="fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center">
                        <button
                            data-testid="beach-close-preview-button"
                            className="absolute top-5 right-5 text-white text-2xl"
                            onClick={handleClosePreview}
                        >
                            &times;
                        </button>
                        <div data-testid="beach-preview-container" className="relative w-3/4 h-3/4">
                            {isLoading ? (
                                <Spin className="absolute inset-0 m-auto flex justify-center items-center" size="large" />
                            ) : (
                                <div
                                    className={`relative w-full h-full transition-all duration-300 opacity-100 translate-x-0
                                `}
                                >
                                    <Image
                                        src={imgSrc}
                                        alt={altText(beachData)}
                                        fill
                                        className="rounded-lg"
                                        onError={() => {
                                            if (imgSrc !== "https://images.sandee.com/images/header/Default-Header.avif") {
                                                return setImgSrc("https://images.sandee.com/images/header/Default-Header.avif");
                                            }
                                        }}
                                    />
                                </div>
                            )}
                        </div>
                        {previewImageIndex > 0 && (
                            <button
                                data-testid="beach-prev-image-button"
                                className="absolute left-5 text-white text-3xl"
                                onClick={handlePrev}
                            >
                                &#10094;
                            </button>
                        )}
                        {previewImageIndex < MultipleImages.length - 1 && (
                            <button
                                data-testid="beach-next-image-button"
                                className="absolute right-5 text-white text-3xl"
                                onClick={handleNext}
                            >
                                &#10095;
                            </button>
                        )}
                    </div>
                )}
            </Modal>
        </>
    );
}

export default BeachListModal