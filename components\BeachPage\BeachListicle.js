import React, { Fragment } from "react";
import SingleListicle from "../SingleListicle";
import NameTitle from "../Common/NameTitle";

const Beach_Listcle_Section = ({ data }) => {
  return (
    <>
      {data?.map((el) => (
        <Fragment key={el?.id}>
          <NameTitle name={el?.name} description={el?.description} />
          <SingleListicle
            data={el?.listiclesBeaches}
            listSlug={el?.nameSlug}
            id={el?.id}
          />
        </Fragment>
      ))}
    </>
  );
};

export default Beach_Listcle_Section;
