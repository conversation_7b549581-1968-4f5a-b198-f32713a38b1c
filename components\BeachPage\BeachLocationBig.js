import React from "react";
import NameTitle from "../Common/NameTitle";

const BeachLocationBig = ({ beachData }) => {
  return (
    <>
      <div className=" bg-[#FAFAFA] pt-8 pb-3 px-4 my-5 rounded-sandee min-h-[400px]">
        <div className="relative flex flex-col justify-center items-center">
          <div className=" w-full md:w-2/5 flex flex-col gap-5 items-center justify-center">
            <NameTitle name={`${beachData?.name ?? "Beach"} - Beach Map`} />
          </div>
          <div className="w-full md:w-3/5 md:flex items-center justify-center my-5 px-5">
            <div className=" w-full  lg:min-h-[20%]  flex flex-col  rounded-sandee bg-gray-200 shadow-lg aspect-auto md:aspect-video">
              <iframe
                className=" bg-gray-200 w-full h-full rounded-sandee min-h-[280px]"
                loading="lazy"
                src={`https://maps.google.com/?q=${beachData?.lat},${beachData?.lon}&ie=UTF8&iwloc=&output=embed`}
              ></iframe>
            </div>
          </div>
        </div>
      </div>

      {/* <div className=" bg-[#FAFAFA] pt-8 pb-3 px-4 my-5 rounded-sandee min-h-[400px]">
        <div className="relative flex flex-col md:flex-row">
          <div className=" w-full md:w-2/5 flex flex-col gap-5 items-center justify-center">
            <NameTitle name={`${beachData?.name ?? "Beach"} - Beach 360View`} />
          </div>
          <div className="w-full md:w-3/5 md:flex items-center justify-center my-5 px-5">
            <div className=" w-full  lg:min-h-[20%]  flex flex-col  rounded-sandee bg-gray-200 shadow-lg aspect-auto md:aspect-video ">
              <iframe
                src="https://www.google.com/maps/embed?pb=!4v1718000549713!6m8!1m7!1sCAoSLEFGMVFpcFBpWmJxazF0T0w1MldrcjlwQWhKbjhPM3pwS0FPWjV3M1NlMDV2!2m2!1d34.0083722!2d-118.4978617!3f186.66331250103534!4f49.625712256926846!5f0.4000000000000002"
                // style="border:0;"
                allowFullScreen
                loading="lazy"
                className=" bg-gray-200 w-full h-full rounded-sandee min-h-[280px]"
                referrerPolicy="no-referrer-when-downgrade"
              ></iframe>
            </div>
          </div>
        </div>
      </div> */}
    </>
  );
};

export default BeachLocationBig;
