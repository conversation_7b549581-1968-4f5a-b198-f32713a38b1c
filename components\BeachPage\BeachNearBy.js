import { FinalImageGenerator } from "@/helper/functions";
import React from "react";
import { CustomGrid } from "../Custom-Display";
import BeachCard from "../Cards/BeachCard";
import NameTitle from "../Common/NameTitle";

const Beach_NearBy_Section = ({ data = [], beachData }) => {
  return (
    <div className="my-5 " id='nearbybeaches' data-testid="beach-nearby-section">
      <NameTitle
        name={`Beaches Near ${beachData?.name}`}
        // description={
        //   " This is the ultimate beach bucket list - conquer each of them across the world's most stunning coastlines. With over 100,000 beaches in the world, this is the definitive list on the Top 50 Most Beautiful Beaches in the World."
        // }
        type={1}
        dataTestid="beach-nearby-title"
      />
      <CustomGrid
        data={data}
        className="gap-4 sm:gap-8 mt-[13px] mb-5"
        data-testid="beach-nearby-grid"
        Component={({ data: dataProps }) => {
          if (dataProps?.city?.state?.country?.slug) {
            dataProps.link = `/${dataProps?.city?.state?.country?.slug}/${dataProps?.city?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.nameSlug}`; //
            dataProps.location = `${dataProps?.city?.name}, ${dataProps?.city?.state?.country?.code}`; //
          } else {
            dataProps.link = `/${dataProps?.country?.slug}/${dataProps?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.nameSlug}`; //
            dataProps.location = `${dataProps?.city?.name}, ${dataProps?.country?.code}`; //
          }

          dataProps.imageSrc = FinalImageGenerator(dataProps?.images?.[0]);
          return (
            <BeachCard
              data={{ ...dataProps }}
              copyRightsData={dataProps?.images}
              dataTestid={`beach-nearby-card-${dataProps?.index}`}
            // className="h-[180px] xl:h-[190px]"
            />
          );
          // return BeachCard({
          //   data: { ...dataProps, ...dataProps },
          //   copyRightsData: dataProps?.images,
          // });
        }}
        xs={2}
        // sm={2}
        md={3}
        lg={4}
        xl={5}
      />
    </div>
  );
};

export default Beach_NearBy_Section;
