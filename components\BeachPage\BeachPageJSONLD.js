import { Activities } from "@/data/beachPageData";
import { getEmbedUrl } from "@/helper/functions";
import React from "react";

const BeachPageJSONLD = ({ beachData, SEOData, params }) => {
  // logController(SEOData);
  const { name: beachName, beachCategory: FAQAnswer } = beachData;
  const availableActivities = Activities.filter(
    (item) => FAQAnswer?.[item?.id]
  )?.map((el) => el?.label);
  const BeachNewScehma = {
    "@context": "https://schema.org",
    "@type": "TouristAttraction",
    "name": SEOData?.title ?? beachData?.name,
    "description": SEOData?.description,
    "url": `https://sandee.com/${params?.countrySlug}/${params?.stateSlug}/${params?.citySlug}/${params?.nameSlug}`,
    "image": beachData?.images?.[0]?.imageUrl,
    "address": {
      "@type": "PostalAddress",
      "addressLocality": `${beachData?.city?.name || ""} ${beachData?.state?.name || ""}`,
      "addressRegion": `${beachData?.address}`,
      "addressCountry": beachData?.country?.name
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": `${beachData?.lat}`,
      "longitude": `${beachData?.lon}`
    },
    "isAccessibleForFree": true,
    "touristType": {
      "@type": "Audience",
      "audienceType": ["Families", "Couples", "Solo Travelers"]
    },
    "hasMap": `https://maps.google.com/?q=${beachData?.lat},${beachData?.lon}`,
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `https://sandee.com/${params?.countrySlug}/${params?.stateSlug}/${params?.citySlug}/${params?.nameSlug}`
    }
  }
  const BeachJSON = {
    "@context": "https://schema.org",
    "@type": "Beach",
    // name: `${beachData?.name}`,
    name: SEOData?.title ?? beachData?.name,
    url: `https://sandee.com/${params?.countrySlug}/${params?.stateSlug}/${params?.citySlug}/${params?.nameSlug}`,
    address: `${beachData?.address} `,
    // description: `Learn about and discover ${beachData?.name}, ${beachData?.city}, ${beachData?.state}, ${beachData?.country}}. - we have over 100 categories of data including activities, parking, photos, attractions, hotels, and restaurants.`,
    description: SEOData?.description,
    hasMap: `https://maps.google.com/?q=${beachData?.lat},${beachData?.lon}`,
    // image:
    //   "https://sandee.com/_next/image?url=https%3A%2F%2Fcdn.sandee.com%2F30207_1650_1100.avif&w=1920&q=75",
    image: beachData?.images?.[0]?.imageUrl,
    // keywords:
    //   "North Star Beach , North Star Beach Photos and Reviews,Dog Friendly Beach",
    keywords: beachData?.altName?.length
      ? `${beachData?.altName?.join(",")}`
      : `${beachData?.name}`,
    latitude: `${beachData?.lat}`,
    longitude: `${beachData?.lon}`,
    openingHours: beachData?.beachBasicDetail?.hours,
    telephone: beachData?.beachBasicDetail?.phoneNumber,
  };
  const jsonLDFAQ = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    mainEntity: [
      {
        "@type": "Question",
        name: `Does ${beachName} have parking? If so, is it free or paid?`,
        acceptedAnswer: {
          "@type": "Answer",
          text: FAQAnswer?.freeParking
            ? FAQAnswer?.paidParking
              ? `${beachName} provides paid parking at the entrance, although spaces are limited. Visitors can also find street parking nearby for easy access to the beach.`
              : `${beachName} provides free parking at the entrance, although spaces are limited. Visitors can also find street parking nearby for easy access to the beach.`
            : `${beachName} does not have dedicated parking but has street parking near the entrance.`,
        },
      },
      {
        "@type": "Question",
        name: `Does ${beachName} have lifeguards?`,
        acceptedAnswer: {
          "@type": "Answer",
          text: FAQAnswer?.lifeguard
            ? `Yes, ${beachName} ensures safety with professional lifeguards on duty throughout the day, offering peace of mind for beachgoers.`
            : `No, ${beachName} does not have lifeguards monitoring the beach – please be careful when swimming on this beach.`,
        },
      },
      {
        "@type": "Question",
        name: `Is ${beachName} a family-friendly beach?`,
        acceptedAnswer: {
          "@type": "Answer",
          text:
            FAQAnswer?.restRooms &&
              FAQAnswer?.lifeguard &&
              FAQAnswer?.restaurant
              ? `${beachName} is the perfect family destination, offering clean restrooms, vigilant lifeguards, and a variety of nearby food options, ensuring an enjoyable day for all.`
              : `${beachName} is not family-friendly and does not have restrooms, lifeguards, and nearby food.`,
        },
      },
      {
        "@type": "Question",
        name: `Is ${beachName} good for swimming?`,
        acceptedAnswer: {
          "@type": "Answer",
          text: FAQAnswer?.swimming
            ? `${beachName} boasts excellent swimming conditions, attracting swimmers of all skill levels with its gentle waves and clear waters.`
            : `${beachName} is not generally considered a good swimming beach.`,
        },
      },
      {
        "@type": "Question",
        name: `Does ${beachName} have restrooms?`,
        acceptedAnswer: {
          "@type": "Answer",
          text: FAQAnswer?.restRooms
            ? `Restrooms are conveniently available on or adjacent to the beach at ${beachName}.`
            : `${beachName} does not have restrooms on or near the beach.`,
        },
      },
      {
        "@type": "Question",
        name: `Is ${beachName} a nude beach?`,
        acceptedAnswer: {
          "@type": "Answer",
          text: FAQAnswer?.nude
            ? `Yes, ${beachName} is known for being a nude beach, providing a liberating experience for those seeking clothing-optional sunbathing and swimming.`
            : `No, ${beachName} maintains a family-friendly atmosphere and does not allow nude bathing.`,
        },
      },
      {
        "@type": "Question",
        name: `Is there food near ${beachName}?`,
        acceptedAnswer: {
          "@type": "Answer",
          text:
            FAQAnswer?.restaurant && FAQAnswer?.beachVendors
              ? `${beachName} has multiple food options near or on the beach, including restaurants and beach vendors.`
              : FAQAnswer?.restaurant
                ? `${beachName} has multiple restaurant options near or on the beach.`
                : FAQAnswer?.beachVendors
                  ? `${beachName} does not have nearby restaurants but frequently has beach vendors selling food on or near the beach.`
                  : `${beachName} does not have nearby food options – please plan your beach day accordingly.`,
        },
      },
      {
        "@type": "Question",
        name: `What are the activities at ${beachName}?`,
        acceptedAnswer: {
          "@type": "Answer",
          text: availableActivities?.length
            ? availableActivities?.length === 1
              ? `${beachName} is a good place to ${availableActivities}`
              : `${beachName} is renowned for its vibrant beach scene, offering various activities for everyone to enjoy ${availableActivities?.join(
                ", "
              )}.`
            : `${beachName} is not known for any activities in particular.`,
        },
      },
      {
        "@type": "Question",
        name: `Does ${beachName} have accommodations for people with disabilities?`,
        acceptedAnswer: {
          "@type": "Answer",
          text: FAQAnswer?.disability
            ? `Yes, ${beachName} offers accommodations for people with disabilities, allowing everyone to relish the coastal experience.`
            : `${beachName} does not have accommodations for people with disabilities.`,
        },
      },
      {
        "@type": "Question",
        name: `Is ${beachName} a dog-friendly beach?`,
        acceptedAnswer: {
          "@type": "Answer",
          text: FAQAnswer?.dogs
            ? `Yes, bring your dog with you to ${beachName}!`
            : `${beachName} is not dog-friendly.`,
        },
      },
      {
        "@type": "Question",
        name: `Is ${beachName} good for surfing?`,
        acceptedAnswer: {
          "@type": "Answer",
          text: FAQAnswer?.surfing
            ? `Yes, surfing is an activity that is frequently enjoyed at ${beachName}.`
            : `${beachName} is not generally considered to be good for surfing.`,
        },
      },
    ],
  };

  const BeachBreadCumber = {
    "@context": "https://schema.org/",
    "@type": "BreadcrumbList",
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        name: "Home",
        item: `https://sandee.com/`,
      },
      {
        "@type": "ListItem",
        position: 2,
        name: beachData?.country?.name,
        item: `https://sandee.com/${beachData?.country?.slug}`,
      },
      {
        "@type": "ListItem",
        position: 3,
        name: beachData?.state?.name,
        item: `https://sandee.com/${beachData?.country?.slug}/${beachData?.state?.slug}`,
      },
      {
        "@type": "ListItem",
        position: 4,
        name: beachData?.city?.name,
        item: `https://sandee.com/${beachData?.country?.slug}/${beachData?.state?.slug}/${beachData?.city?.slug}`,
      },
      {
        "@type": "ListItem",
        position: 5,
        name: beachData?.name,
        item: `https://sandee.com/${beachData?.country?.slug}/${beachData?.state?.slug}/${beachData?.city?.slug}/${beachData?.nameSlug}`,
      },
    ],
  };

  const BeachVideoSchema = {
    "@context": "http://schema.org",
    "@type": "VideoObject",
    // "name": "Santa Monica Pier 4K Drone",
    // "description": "Santa Monica, California USA Drone: DJI PHANTOM4 PRO  Music: Blue Danube by Strauss",
    name: `${beachData?.name} Drone`,
    description: `${beachData?.name}, ${beachData?.state?.name}, ${beachData?.country?.name} Video `,
    thumbnailUrl: beachData?.droneVideo
      ? getEmbedUrl(beachData?.droneVideo)
      : "",
    uploadDate: beachData?.updatedAt,
    // duration: "PT8M43S",
    embedUrl: beachData?.droneVideo ? getEmbedUrl(beachData?.droneVideo) : "",
    // interactionCount: "587",
  };
  return (
    <>
      <script
        type="application/ld+json"
        id="application/ld+jsonBeachDetails"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(BeachNewScehma) }}
      ></script>
      {/* <script
        type="application/ld+json"
        id="application/ld+jsonBeachDetails"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(BeachJSON) }}
      ></script> */}
      <script
        type="application/ld+json"
        id="application/ld+jsonBeachFAQ"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLDFAQ) }}
      ></script>
      <script
        type="application/ld+json"
        id="application/ld+jsonBeachBreadCumber"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(BeachBreadCumber) }}
      ></script>
      {beachData?.droneVideo && (
        <script
          type="application/ld+json"
          id="application/ld+jsonBeachVideo"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(BeachVideoSchema) }}
        ></script>
      )}
    </>
  );
};

export default BeachPageJSONLD;
