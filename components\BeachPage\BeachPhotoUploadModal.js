"use client";
import React, { useEffect, useState } from "react";
import CustomButton from "../Custom-Button";
import BeachPhotoUploader from "../Common/BeachPhotoUploader";
import { Modal } from "antd";
import { CancelIcon } from "../social-icons/icons";
import { BeachCustomButton } from "./BeachReviewAndPhoto";
import { isMobileView } from "@/helper/functions";

const BeachPhotoUploadModal = ({ beachData, Other }) => {
  const [open, setOpen] = useState(false);
  const isMobileViews = isMobileView()

  const toggle = (e) => {
    e?.stopPropagation();
    setOpen((prev) => !prev);
    localStorage?.setItem("popupKey", null);
  };


  useEffect(() => {
    if (window !== undefined) {
      localStorage?.getItem("popupKey") === "addBeachPhoto" && setOpen(true);
    }
  }, []);
  return (
    <>
      {!Other ? (
        <BeachCustomButton onClick={toggle}>
          Add Photo
          {/* <AddPhoto className="w-5 h-5 ml-1 fill-sandee-blue group-hover:fill-white" /> */}
        </BeachCustomButton>
      ) : (
        <Other onClick={toggle} />
      )}
      <Modal
        role="dialog"
        aria-modal="true"
        open={open}
        onCancel={() => toggle()}
        footer={null}
        closable={false}
        okButtonProps={{
          style: {
            display: "none",
          },
        }}
        style={{
          padding: 0,
        }}
        // width={800}
        centered
        wrapClassName=" md:!py-10 py-10"
        styles={{
          body: {
            padding: "0px !important",
          },
        }}
        cancelButtonProps={{
          style: {
            display: "none",
          },
        }}
        className="xl:!w-[800px] lg:!w-[700px] md:!w-[650px]"
      >
        <div className="bg-white rounded-[26px] overflow-hidden ">
          <div className="flex justify-end me-2 mt-3 cursor-pointer" onClick={() => toggle()}>
            <CancelIcon className="w-7 h-7 opacity-50" />
          </div>
          <div className="lg:max-h-[83vh] lg:w-[98%] lg:overflow-y-auto add-photo-modal-custom-scrollbar pb-6">
            <div className={` container lg:px-20 px-5 ${open ? "!max-w-[800px]" : ""}`}>
              <BeachPhotoUploader beachData={beachData} onSubmit={toggle} />
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default BeachPhotoUploadModal;
