"use client";
import React from "react";
import AuthWrapper from "../Common/AuthWrapper";
// import CustomButton from "../Custom-Button";
import BeachReviewModal from "./BeachReviewModal";
import BeachPhotoUploadModal from "./BeachPhotoUploadModal";
import BeachFlagInformation from "./BeachFlagInformation";
import { scrollToSection } from "@/helper/functions";

export const BeachCustomButton = ({ children, dataTestid = "beach-custom-button", ...props }) => {
  return (
    <button data-testid={dataTestid} className="custom-hover-slide-button !bg-white !text-sandee-orange border !border-sandee-orange active:!text-white group font-bold !px-2" {...props}>
      <span className="custom-hover-slide group-hover:h-full !bg-sandee-orange"></span>
      <span className="custom-hover-slide-text group-hover:text-white ">
        {children}
      </span>
    </button>
  );
};
const BeachReviewAndPhoto = ({ beachData }) => {
  return (
    <>
      <div data-testid="beach-review-photo-section" className="flex gap-2 !text-nowrap w-auto flex-wrap">
        {/* <BeachFlagInformation beachData={beachData} /> */}
        <AuthWrapper
          WithoutLogIn={
            <BeachCustomButton dataTestid="beach-write-review-button">
              Write Review{" "}
              {/* <Pencil className="w-5 h-5 ml-1 fill-sandee-blue group-hover:fill-white " /> */}
            </BeachCustomButton>
          }
          WithLogIn={<BeachReviewModal data-testid="beach-review-modal" beachData={beachData} />}
          popupKey={"beachReview"}
        />
        <AuthWrapper
          WithoutLogIn={
            <BeachCustomButton dataTestid="beach-add-photo-button">
              Add Photo
              {/* <AddPhoto className="w-5 h-5 ml-1 fill-sandee-blue group-hover:fill-white " /> */}
            </BeachCustomButton>
          }
          WithLogIn={
            <BeachPhotoUploadModal data-testid="beach-photo-upload-modal" beachData={beachData} Other={null} />
          }
          popupKey={"addBeachPhoto"}
        />
        <BeachCustomButton
          dataTestid="beach-nearby-beaches-button"
          onClick={() => {
            scrollToSection("nearbybeaches", 90);
          }}
        >
          Nearby Beaches
        </BeachCustomButton>
        {beachData?.beachCamLink && <BeachCustomButton
          dataTestid="beach-cams-button"
          onClick={() => {
            scrollToSection("beachCams", 90);
          }}
        >
          Beach Cams
        </BeachCustomButton>}
      </div>
    </>
  );
};

export default BeachReviewAndPhoto;
