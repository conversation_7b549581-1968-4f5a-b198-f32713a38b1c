import React from "react";
import NameTitle from "../Common/NameTitle";
import BeachReviewGrid from "./BeachReviewGrid";

const BeachReviewBottomDisplay = ({ beachData }) => {
  const ReviewsArray = [
    {
      lable: "5 Star",
      total: 3000,
      value: 2546,
    },
    {
      lable: "4 Star",
      total: 3000,
      value: 1178,
    },
    {
      lable: "3 Star",
      total: 3000,
      value: 684,
    },
    {
      lable: "2 Star",
      total: 3000,
      value: 67,
    },
    {
      lable: "1 Star",
      total: 3000,
      value: 237,
    },
  ];
  return (
    <div>
      <NameTitle
        className="my-5"
        description={"Showing 47 verified comments"}
        name={`Reviews of ${beachData?.name}`}
        type={2}
      />
      <div className="flex flex-col md:flex-row gap-5">
        <div className="w-full md:w-1/2">
          <p className=" flex gap-1 text-sandee-20 font-bold text-sandee-grey items-center justify-start">
            <Star className=" fill-[#ffa723]" />
            <Star className=" fill-[#ffa723]" />
            <Star className=" fill-[#ffa723]" />
            <Star className=" fill-[#ffa723]" />
            <Star className=" fill-[#E8E8E8]" />
            <span> {`(4.8 out of 5)`}</span>
          </p>
          <p className="  font-bold text-sandee-grey ">
            Based on 3,645 reviews
          </p>
        </div>
        <div className="w-full md:w-1/2 flex flex-col gap-3">
          {ReviewsArray?.map((el) => {
            return (
              <div key={el?.lable} className=" w-full">
                <div className=" w-full flex gap-2 items-center text-sandee-18 font-bold">
                  <p className="w-14">{el?.lable}</p>

                  <div className=" flex-1 max-w-96 w-bn64 bg-gray-300 rounded-full h-2 overflow-hidden">
                    <div
                      className="bg-black h-full flex items-center justify-center text-white transition-all duration-300"
                      style={{ width: `${(el?.value / el?.total) * 100}%` }}
                    ></div>
                  </div>
                  <p className="w-14">{el?.value?.toLocaleString()}</p>
                </div>
              </div>
            );
          })}
        </div>
      </div>
      <BeachReviewGrid
        AllReviewsInitial={[
          {
            id: 1,
            rating: 5,
            name: "John Deo",
            date: "16-1-2024",
            review:
              "lorem ispsum dolor sit amet, consectetur adipiscing el aspect et",
            isVerified: true,
          },
          {
            id: 2,
            rating: 4,
            name: "Jane Smith",
            date: "20-1-2024",
            review:
              "Beautiful beach with clear water and soft sand. It can get crowded during weekends.",
            isVerified: true,
          },
          {
            id: 3,
            rating: 5,
            name: "Emily Johnson",
            date: "22-1-2024",
            review:
              "Absolutely loved the sunset view. Perfect place for a family picnic.",
            isVerified: true,
          },
          {
            id: 4,
            rating: 3,
            name: "Michael Brown",
            date: "25-1-2024",
            review:
              "Nice beach but lacking in facilities like restrooms and food stalls.",
            isVerified: false,
          },
          {
            id: 5,
            rating: 4,
            name: "Sophia Davis",
            date: "28-1-2024",
            review:
              "Great for swimming and water sports. Clean and well-maintained.",
            isVerified: true,
          },
          {
            id: 6,
            rating: 2,
            name: "David Wilson",
            date: "30-1-2024",
            review: "Too much seaweed and litter. Not the best experience.",
            isVerified: false,
          },
          {
            id: 7,
            rating: 1,
            name: "David Wilson 2",
            date: "30-1-2024",
            review: "Too much seaweed and litter. Not the best experience.",
            isVerified: false,
          },
        ]}
        FullCount={{
          limit: 5,
          page: 1,
          count: 30,
          totalpages: 6,
          query: {
            limit: 5,
          },
        }}
        // data={b}
      />
    </div>
  );
};

export default BeachReviewBottomDisplay;

export const Star = (pros) => {
  return (
    <svg
      viewBox="0 0 576 512"
      height="1em"
      xmlns="http://www.w3.org/2000/svg"
      {...pros}
    >
      <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z" />
    </svg>
  );
};
