"use client";
import React, { useEffect, useState } from "react";
import { CustomGrid } from "../Custom-Display";
import ReviewCard, { ReviewCardSkeleton } from "../Cards/ReviewCard";
import { getAllReviewsForUserViaQuery } from "@/app/(HeaderSlim)/(Single Country Page)/[countrySlug]/action";
import Pagination from "../Common/Pagination";

const BeachReviewGrid = ({ AllReviewsInitial, FullCount }) => {
  const total = FullCount?.count;
  const [AllReviews, setAllReviews] = useState([
    AllReviewsInitial,
    [],
    AllReviewsInitial,
  ]);
  const [CurrentReview, setCurrentReview] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);

  const [loading, setLoading] = useState(true);

  const FetchOrSetReview = async () => {
    setLoading(true);
    if (currentPage == 1) {
      setCurrentReview(AllReviewsInitial);
      return setLoading(false);
    }

    if (AllReviews?.[currentPage - 1]?.length) {
      setCurrentReview(AllReviews?.[currentPage - 1]);
      return setLoading(false);
    }
    const AllBeachesResponse = await getAllReviewsForUserViaQuery({
      ...FullCount?.query,
      page: currentPage,
    });

    setAllReviews((prev) => {
      prev[currentPage - 1] = AllBeachesResponse?.data?.rows;
      return prev;
    });

    setCurrentReview(AllBeachesResponse?.data?.rows);
    return setLoading(false);
  };
  useEffect(() => {
    FetchOrSetReview();
  }, [currentPage]);
  return (
    <section>
      {loading ? (
        <CustomGrid
          className="!my-5 gap-4 sm:gap-8"
          data={Array(FullCount?.limit).fill(1)}
          Component={ReviewCardSkeleton}
          xs={1}
          //   xs={2}
          //   sm={2}
          //   md={3}
          //   lg={4}
          //   xl={5}
          //   xxl={5}
          //   xxxl={5}
        />
      ) : total ? (
        <>
          <CustomGrid
            data={CurrentReview}
            className="!my-5 gap-8"
            Component={ReviewCard}
            // Component={({ data: dataProps }) => {
            //   if (dataProps?.city?.state?.country?.slug) {
            //     dataProps.link = `/${dataProps?.city?.state?.country?.slug}/${dataProps?.city?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.nameSlug}`; //
            //     dataProps.location = `${dataProps?.city?.state?.country?.name}, ${dataProps?.city?.state?.name}, ${dataProps?.city?.name}`; //
            //   } else {
            //     dataProps.link = `/${dataProps?.country?.slug}/${dataProps?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.nameSlug}`; //
            //     dataProps.location = `${dataProps?.country?.name}, ${dataProps?.state?.name}, ${dataProps?.city?.name}`; //
            //   }
            //   dataProps.imageSrc = FinalImageGenerator(dataProps?.images?.[0]);
            //   return (
            //     <BeachCard
            //       data={{ ...dataProps }}
            //       copyRightsData={dataProps?.images}
            //       // className="h-[180px] xl:h-[190px]"
            //     />
            //   );
            //   // return BeachCard({
            //   //   data: { ...dataProps, ...dataProps },
            //   //   copyRightsData: dataProps?.images,
            //   // });
            // }}

            xs={1}
            // xs={2}
            // sm={2}
            // md={3}
            // lg={4}
            // xl={5}
            // xxl={5}
            // xxxl={5}
          />
        </>
      ) : (
        <p className=" px-2 py-5 text-error-red-600  border-error-red-400 bg-error-red-50 border-2 rounded-sandee text-center mt-6 mb-10">
          No, Reviews avilable for this beach
        </p>
      )}
      <Pagination
        className="pagination-bar"
        currentPage={currentPage}
        totalCount={total}
        pageSize={FullCount?.limit}
        onPageChange={(page) => {
          setCurrentPage(page);
        }}
      />
    </section>
  );
};

export default BeachReviewGrid;
