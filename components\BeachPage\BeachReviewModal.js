"use client";
import React, { useEffect, useState } from "react";
import CustomButton from "../Custom-Button";
import CustomRating from "./CustomRating";
import useLoggedIn from "@/helper/hook/useLoggedIn";
import { postReview } from "@/app/(HomeHeader)/action";
import { Modal, notification } from "antd";
import { BeachCustomButton } from "./BeachReviewAndPhoto";
import { isMobileView } from "@/helper/functions";

const BeachReviewModal = ({ beachData, Other }) => {
  const { token } = useLoggedIn();
  const [open, setOpen] = useState(false);
  const isMobileViews = isMobileView();

  useEffect(() => {
    if (window !== undefined) {
      localStorage?.getItem("popupKey") === "beachReview" && setOpen(true);
    }
  }, []);

  const toggle = () => {
    setOpen((prev) => !prev);
    localStorage?.setItem("popupKey", null);
  };

  return (
    <>
      {!Other ? (
        <BeachCustomButton onClick={toggle}>
          Write Review
          {/* <Pencil className="w-5 h-5 ml-1 fill-sandee-blue group-hover:fill-white" /> */}
        </BeachCustomButton>
      ) : (
        <Other onClick={toggle} />
      )}

      <Modal
        role="dialog"
        aria-modal="true"
        open={open}
        onCancel={() => toggle()}
        footer={null}
        closable={isMobileViews}
        okButtonProps={{
          style: {
            display: "none",
          },
        }}
        style={{
          padding: 0,
        }}
        width={450}
        centered
        wrapClassName=" !p-0"
        styles={{
          body: {
            padding: "0px !important",
          },
        }}
        cancelButtonProps={{
          style: {
            display: "none",
          },
        }}
      >
        <div
          className="custom-auth "
          // style={{
          //   border: "none",
          //   borderRadius: "30pt",
          //   textAlign: "center",
          //   padding: "40px",
          // }}
        >
          <div className={` container ${open ? "!max-w-[500px]" : ""}`}>
            <form
              className="form"
              onSubmit={async (e) => {
                e.preventDefault();

                if (e.target?.agree?.checked) {
                  const payload = {
                    rate: e.target?.rate?.value,
                    review: e.target?.review?.value,
                    AllBeachId: beachData?.id,
                  };
                  //   logController(payload, token, e.target?.agree?.checked);
                  const response = await postReview(payload, token);
                  //   logController(response);
                  if (response?.data && response?.status === "success") {
                    // logController("Review Added Successfully");
                    notification.success({
                      message: "Review Added Successfully",
                      duration: 3,
                    });
                    window.location.reload();
                    toggle();
                  } else {
                    notification.error({
                      message: "You are not Authorized",
                      duration: 3,
                    });
                    // logController("You are not Authorized");
                  }
                }
                // const payload = {
                //   email: e.target?.email?.value,
                //   password: e.target?.password?.value,
                // };
              }}
            >
              <p className=" text-center text-sandee-18 font-bold">
                🌟 Share Your Experience at {beachData?.name}! 🌟
              </p>
              <p className=" text-center text-sandee-12 font-normal">
                Hey beach lover! You&apos;ve soaked up the sun, surf, and sand
                at &nbsp;{beachData?.name} — now we want to hear all about it!
                Your reviews help fellow travelers make informed choices and
                inspire the Sandee community.
              </p>
              <Divider>
                <p className=" text-black text-sandee-12 font-bold">
                  Rate Your Experience
                </p>
              </Divider>
              <div className=" flex justify-center items-center my-1 mx-1 ">
                <CustomRating />
              </div>
              <Divider>
                <p className=" text-black text-sandee-12 font-bold">
                  Write Your Review
                </p>
              </Divider>
              <textarea
                className="input !w-full !h-full min-h-[120px] text-sandee-12 align-text-top text-wrap text-start"
                name="review"
                type="textarea"
                placeholder="Share your best moments, tips, and anything you wish you'd known before going."
                required
              />
              <div className="flex text-start items-start mt-5 gap-2">
                <input
                  type="checkbox"
                  className=" !m-0 "
                  id="agree"
                  name="agree"
                  required
                />
                <p className=" text-black text-sandee-12 font-normal leading-tight">
                  {` I certify that, All information submitted in this review is
                  based on my own experience and is my genuine opinion of this
                  beach. I have no personal or business relationship with this
                  establishment, and have not been offered any incentive or
                  payment to write this review. I understand that Sandee
                  reserves the right to remove reviews that do not adhere to
                  these guidelines or terms of service.`}
                </p>
              </div>
              <div className="flex justify-center items-center ">
                <input
                  defaultValue="Submit a review"
                  //   disabled={loading}
                  type="submit"
                  className={` login-button ${""}`}
                />
              </div>
            </form>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default BeachReviewModal;
export const Divider = ({ children }) => {
  return (
    <div className="relative flex py-2 items-center">
      <div className="flex-grow border-t border-gray-400"></div>
      <span className="flex-shrink mx-4 text-gray-400">{children}</span>
      <div className="flex-grow border-t border-gray-400"></div>
    </div>
  );
};
