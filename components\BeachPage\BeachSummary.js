// "use client"
// import { BeachPageData } from "@/data/beachPageData";
// import React from "react";

// function sanitizeHTMLHref(htmlString) {
//   // Use a regex to match href attributes inside <a> tags
//   return htmlString.replace(
//     /<a\b[^>]*\bhref=["|'](.*?)["|']/g,
//     (match, hrefValue) => {
//       // Clean the href value by replacing curly quotes and other problematic characters
//       const cleanedHref = hrefValue
//         .replace(/“|”/g, '"') // Replace curly quotes with straight quotes
//         .replace(/["']/g, "") // Remove any remaining quotes around the URL
//         .trim(); // Trim any extra spaces if needed

//       // Return the <a> tag with the cleaned href value
//       return match.replace(hrefValue, cleanedHref);
//     }
//   );
// }
// const BeachSummary_Section = ({ beachData }) => {
//   console.log(beachData?.beachDescription)
//   // Ensure beachData and beachDescription are defined
//   if (!beachData || !beachData.beachDescription) {
//     return;
//   }

//   // const dataB = BeachPageData?.Summary?.filter(
//   //   (el) =>
//   //     beachData?.beachDescription?.[el.id] &&
//   //     beachData?.beachDescription?.[el.id] != "<p><br></p>"
//   // );

//   const emptyValue = "<p><br></p>";

//   // Filter BeachPageData.Summary based on data object and empty values
//   let filteredSummary = BeachPageData.Summary?.filter(
//     ({ id }) =>
//       beachData?.beachDescription[id] &&
//       beachData?.beachDescription[id] !== emptyValue
//   )?.map((item) => ({ ...item }));

//   // Handle specific key conflict
//   const hasSummary = filteredSummary?.some((item) => item?.id === "summary");
//   const hasIntroduction = filteredSummary?.some(
//     (item) => item.id === "introduction"
//   );

//   if (hasSummary && hasIntroduction) {
//     filteredSummary = filteredSummary?.filter((item) => item?.id !== "summary");
//   }

//   return (
//     <>
//       {/* <div className="mt-5">
//         {filteredSummary?.map((tab, i) => (
//           <div id={`tab-${i + 1}`} key={tab?.id}>
//             <p className=" text-sandee-24 font-semibold">{tab?.tabLabel}</p>
//             <div className={` tab-paneB beachDescription `}>
//               <div
//                 dangerouslySetInnerHTML={{
//                   __html: sanitizeHTMLHref(
//                     beachData?.beachDescription?.[tab?.id]?.includes("<p>")
//                       ? beachData?.beachDescription?.[tab?.id]
//                       : `<p>${beachData?.beachDescription?.[tab?.id]}</p>`
//                   ),
//                 }}
//               ></div>
//             </div>
//           </div>
//         ))}
//       </div> */}
//     </>
//   );
// };

// export default BeachSummary_Section;

"use client";
import { BeachPageData } from "@/data/beachPageData";
import { EditorContent, processContent, processContentOnePTag } from "@/helper/functions";
import React, { useState } from "react";
function sanitizeHTMLHref(htmlString) {
  // Use a regex to match href attributes inside <a> tags
  const str = htmlString.replace(
    /<a\b[^>]*\bhref=["|'](.*?)["|']/g,
    (match, hrefValue) => {
      // console.log(hrefValue, "bvgdjmfdbk")
      // Clean the href value by replacing curly quotes and other problematic characters
      const cleanedHref = hrefValue
        .replace(/“|”/g, '"') // Replace curly quotes with straight quotes
        .replace(/["']/g, "") // Remove any remaining quotes around the URL 
        .trim(); // Trim any extra spaces if needed

      // Return the <a> tag with the cleaned href value
      return match.replace(hrefValue, cleanedHref);
    }
  )
  // console.log(str, str?.replace(/<\/br>/g, "<br>")
  //   .replace(/<br\s*\/?>/g, "<p><br><br></p>"), "bvgdjmfdbk")
  return str
  // ?.replace(/<\/br>/g, "<br>")
  // .replace(/<br\s*\/?>/g, '<span class="spacing_overview"></span>')
}
const BeachSummary_Section = ({ beachData }) => {
  const [activeTab, setActiveTab] = useState(0); // start with the first tab active
  if (!beachData || !beachData.beachDescription) {
    return;
  }

  const dataB = BeachPageData?.Summary?.filter(
    (el) =>
      beachData?.beachDescription?.[el.id] &&
      beachData?.beachDescription?.[el.id] != "<p><br></p>"
  );

  const emptyValue = "<p><br></p>";

  let filteredSummary = BeachPageData.Summary?.filter(
    ({ id }) =>
      beachData?.beachDescription[id] &&
      beachData?.beachDescription[id] !== emptyValue
  )?.map((item) => ({ ...item }));

  const hasSummary = filteredSummary?.some((item) => item?.id === "summary");
  const hasIntroduction = filteredSummary?.some(
    (item) => item.id === "introduction"
  );

  if (hasSummary && hasIntroduction) {
    filteredSummary = filteredSummary?.filter((item) => item?.id !== "summary");
  }

  const hasMultiplePTags = (description) => {
    if (!description) return false; // Handle undefined or null

    // Use a regular expression to match all <p> tags
    const pTagMatches = description.match(/<p>/g);
    // console.log(pTagMatches, "pTagMatches", description)
    // If there are multiple matches, return true
    return pTagMatches && pTagMatches.length > 1;
  };

  return (
    <div className="container--tabs" data-testid="beach-summary-section">
      <section className="row" data-testid="beach-summary-row">
        {/* Tab Navigation */}
        <ul
          data-testid="beach-summary-tabs"
          className="flex nav nav-tabs overflow-x-auto !flex-nowrap overflow-y-hidden whitespace-nowrap no-scrollbar">
          {filteredSummary?.map((tab, index) => (
            <li key={tab?.tabLabel}
              data-testid={`beach-summary-tab-${index}`}
              className={`flex w-auto`}>
              <button
                onClick={() => {
                  // const currentUrl = window.location.href.split('#')[0]; // Get current URL without hash
                  // window.location.href = `${currentUrl}#${tab?.tabLabel}`; // Add new hash to the URL
                  setActiveTab(index)
                }}
                data-testid={`beach-summary-tab-button-${index}`}
                className={`tab-button text-lg pb-3 font-semibold ${index === activeTab
                  ? "active border-b-[3px] border-black" // Ensures a bottom border is added
                  : "" // Ensures non-active tabs have a transparent border
                  }`}
              >
                {tab?.tabLabel}
              </button>
            </li>
          ))}
        </ul>

        {/* Tab Content */}
        <div className="tab-content" data-testid="beach-summary-tab-content">
          {filteredSummary.map((tab, index) => (
            <div
              key={tab?.id}
              className={`tab-pane !mt-0 beachDescription ${index === activeTab ? "active" : "hidden"
                }`}
              data-testid={`beach-summary-tab-pane-${index}`}
            >
              {/* <EditorContent value={sanitizeHTMLHref(
                beachData?.beachDescription?.[tab?.id]?.includes("<p>")
                  ? processContent(beachData?.beachDescription?.[tab?.id])
                  : `<p>${beachData?.beachDescription?.[tab?.id]}</p>`
              )} /> */}

              {/* {console.log(hasMultiplePTags(beachData?.beachDescription?.[tab?.id]))} */}
              <EditorContent
                dataTestid={`beach-summary-editor-content-${index}`}
                value={sanitizeHTMLHref(
                  beachData?.beachDescription?.[tab?.id]?.includes("<p>")
                    ? processContent(beachData?.beachDescription?.[tab?.id])
                    : `<p>${processContentOnePTag(beachData?.beachDescription?.[tab?.id])}</p>`
                )} />
              {/* <div
                dangerouslySetInnerHTML={{
                  __html: sanitizeHTMLHref(
                    beachData?.beachDescription?.[tab?.id]?.includes("<p>")
                      ? beachData?.beachDescription?.[tab?.id]
                      : `<p>${beachData?.beachDescription?.[tab?.id]}</p>`
                  ),
                }}
              ></div> */}
            </div>
          ))}
        </div>
      </section>
    </div>
  );
};

export default BeachSummary_Section;
