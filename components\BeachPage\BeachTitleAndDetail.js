import Link from "next/link";
import React from "react";
import BeachReviewAndPhoto from "./BeachReviewAndPhoto";

const BeachTitle_And_Detail_Section = ({ beachData }) => {
  const data =
    beachData?.islandSlug && beachData?.island?.name
      ? [
        {
          title: beachData?.country?.name,
          to: `/${beachData?.country?.slug}`,
        },
        {
          title: beachData?.island?.name,
          to: `/island/${beachData?.islandSlug}`,
        },
        {
          title: beachData?.city?.name,
          to: `/${beachData?.country?.slug}/${beachData?.state?.slug}/${beachData?.city?.slug}`,
        },
        {
          title: beachData?.name,
        },
      ]
      : [
        {
          title: beachData?.country?.name,
          to: `/${beachData?.country?.slug}`,
        },
        {
          title: beachData?.state?.name,
          to: `/${beachData?.country?.slug}/${beachData?.state?.slug}`,
        },
        {
          title: beachData?.city?.name,
          to: `/${beachData?.country?.slug}/${beachData?.state?.slug}/${beachData?.city?.slug}`,
        },
        {
          title: beachData?.name,
        },
      ];
  return (
    <div data-testid="beach-title-detail-section" className="grid lg:grid-flow-col grid-flow-row mb-2">
      <div
        className="flex flex-col col-span-6"
        data-testid="beach-title-detail-left"
      >
        <h1
          className="text-sandee-32 font-semibold !leading-none"
          data-testid="beach-title"
        >
          {beachData?.name}
          {beachData?.altName?.length ? <span
            className="text-sandee-18 text-gray-500 mx-2"
            data-testid="beach-alt-name"
          >
            {beachData?.altName?.filter(name => name)?.length
              ? `(${beachData?.altName?.filter(name => name)?.join(", ")})`
              : ""}
          </span> : ""}
        </h1>
        <p
          className="text-sandee-sm flex-wrap flex items-center font-normal py-[0.5rem]"
          data-testid="beach-address-links"
        >
          {/* <PinLocation className=" fill-black  h-4 w-4 mr-2" /> */}

          {data?.map((item, i) => {
            if (item?.to && item?.title) {
              return (
                <Link
                  hrefLang="en-us"
                  href={item?.to}
                  key={`address-link-${i}`}
                  className="decoration-0 text-[#000000] text-sandee-20"
                  data-testid={`beach-address-link-${i}`}
                >
                  {item?.title}
                  {data?.length - 2 !== i && ","}&nbsp;
                </Link>
              );
            }
          })}
        </p>
      </div>
      <div data-testid="beach-title-detail-right" className=" w-full col-span-6 flex justify-center items-center sm:items-end flex-col gap-3 sm:mt-3">
        <BeachReviewAndPhoto beachData={beachData}
          data-testid="beach-review-photo"
        />
        {/* <p className=" decoration-0 text-[#000000] text-[20px]">
          {`Sandee Rating : ${beachData?.rating100?.toFixed(0)}/100`}
        </p> */}
      </div>
    </div>
  );
};

export default BeachTitle_And_Detail_Section;
