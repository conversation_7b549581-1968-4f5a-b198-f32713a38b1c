"use client";
import Image from "next/image";
import React, { useState } from "react";
import {
  WeatherHumidity,
  WeatherSpeed,
  WeatherVisibilty,
} from "../social-icons/icons";
import { BeachWeatherMiniSwiper } from "./BeachDualSwiper";
import { WeatherIconLink } from "@/helper/functions";
// COMMENT
const BeachWeatherCard = ({ WeatherDeatils, dataTestid = "beach-weather-card" }) => {
  const WeatherDeatilLive = WeatherDeatils?.list?.[0];
  const [celcius, setCelcius] = useState(false);
  return (
    <div data-testid={dataTestid} className="p-4">
      <div className=" flex flex-col justify-around items-center gap-4">
        {/* <p className=" text-sandee-24 text-center font-bold">City</p> */}
        <div className="flex flex-row justify-around items-center w-full">
          <div className="  flex justify-center items-center">
            <div className=" flex  justify-start items-center  text-[40px] font-extrabold text-sandee-blue transition-all duration-700">
              <p className=" mx-3">
                <span
                  className={`  ${celcius ? "w-0 hidden" : "text-sandee-blue "
                    } `}
                >
                  {(1.8 * (WeatherDeatilLive?.main?.temp - 273) + 32)?.toFixed(
                    1
                  )}
                </span>
                <span
                  className={`  ${!celcius ? " w-0 hidden " : "text-sandee-blue "
                    } `}
                >
                  {(WeatherDeatilLive?.main?.temp - 273.15)?.toFixed(0)}
                </span>
              </p>
              <p
                className={` mx-1 cursor-pointer   ${celcius ? "text-gray-400" : "text-sandee-blue "
                  }`}
                id="fahrenheit"
                onClick={() => {
                  setCelcius(false);
                }}
              >
                °F
              </p>
              <p className=" text-gray-400  font-semibold">|</p>
              <p
                id="celsius"
                className={` mx-1 cursor-pointer text-black  ${celcius ? "text-sandee-blue " : "text-gray-400"
                  }`}
                onClick={() => {
                  setCelcius(true);
                }}
              >
                °C
              </p>
            </div>
          </div>
          <div className="hidden md:w-1/3 md:flex justify-center items-center">
            <Image
              src={WeatherIconLink(WeatherDeatilLive?.weather?.[0]?.icon)}
              width={60}
              height={60}
              alt="Icon"
            />
          </div>
          <div className=" hidden md:w-1/3 md:flex justify-center items-center">
            <p className=" text-sandee-24 text-sandee-grey font-bold">{WeatherDeatilLive?.weather?.[0]?.main}</p>
          </div>
        </div>
        <div className="flex w-full flex-row justify-around gap-5 items-center text-sandee-18 font-medium">
          {WeatherDeatilLive?.visibility ? (
            <span className=" flex gap-1">
              <WeatherVisibilty className="h-6 w-6 stroke-black fill-black" />
              {`${+WeatherDeatilLive?.visibility / 1000} Km`}
            </span>
          ) : (
            ""
          )}
          {WeatherDeatilLive?.main?.humidity ? (
            <span className=" flex gap-1">
              <WeatherHumidity className="h-6 w-6 stroke-black" />

              {`${WeatherDeatilLive?.main?.humidity}%`}
            </span>
          ) : (
            ""
          )}
          {WeatherDeatilLive?.wind?.speed ? (
            <span className=" flex gap-1">
              <WeatherSpeed className="h-6 w-6 stroke-black" />
              {`${WeatherDeatilLive?.wind?.speed} m/s`}
            </span>
          ) : (
            ""
          )}
        </div>
        <BeachWeatherMiniSwiper
          WeatherData={WeatherDeatils}
          celcius={celcius}
        />
      </div>
    </div>
  );
};

export default BeachWeatherCard;
