import React from 'react'
// import { CustomGrid } from '../Custom-Display';
import { ApproveIcon, StarIcon } from '../social-icons/icons';
// import NameTitle from '../Common/NameTitle';
import moment from 'moment';

const ReviewCard = ({ data, index = "0" }) => {
    const renderStars = (rate) => {
        const stars = [];
        for (let i = 0; i < 5; i++) {
            stars.push(<StarIcon key={i} fill={i < rate ? "#E49D3E" : "#E8E8E8"} />);
        }
        return stars;
    };

    return (
        <>
            <div data-testid={`review-card-${index}`} className='flex py-6 border-b-[1px] border-[#F0F0F0] gap-14'>
                <div className='flex flex-col gap-3 w-[40%]'>
                    <div data-testid={`review-card-stars-${index}`} className='flex items-center gap-1'>
                        {renderStars(data?.rate)}
                        <div className='ml-2 text-[#7D7D7D] font-semibold'>({data?.rate} out of 5)</div>
                    </div>
                    <div className='py-5'>
                        <p className=' font-semibold'>{data?.user?.name || ""}</p>
                        <span className='text-[#7D7D7D]'>{moment(data?.createdAt)?.format("MMM DD YYYY")}</span>
                    </div>
                    {/* <div>
                        <div className='flex gap-2'>
                            <ApproveIcon />
                            <span className='text-[#7D7D7D] font-semibold text-lg'>Verified</span>
                        </div>
                    </div> */}
                </div>
                <div className='w-full'>
                    <div className='bg-[#FBFBFB] p-5 w-full'>
                        {/* <NameTitle
                            htag={3}
                            name={"Great Beach, Clean and beautiful"}
                        /> */}
                        <p className='text-[#7D7D7D]'>{data?.review}</p>
                    </div>
                </div>
            </div>
        </>
    )
}

export default ReviewCard