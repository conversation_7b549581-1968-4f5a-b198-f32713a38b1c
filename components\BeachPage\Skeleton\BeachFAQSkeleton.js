import { CustomContainer } from "@/components/Custom-Display";
import React from "react";

const BeachFAQSkeleton = () => {
  return (
    <div className=" bg-[#FAFAFA] pt-8 pb-3 px-4 my-5 rounded-sandee">
      <div className="heading">
        <div className="animate-pulse h-8 bg-slate-200 my-4"></div>
        <div className="animate-pulse h-5 bg-slate-200 my-4"></div>
        <div className="animate-pulse h-5 bg-slate-200 my-4"></div>
      </div>
      <CustomContainer className=" lg:!px-20 xl:!px-20 2xl:!px-20 3xl:!px-20">
        {Array(10)
          .fill(1)
          .map((el, id) => {
            return (
              <div className={`accordion `} key={id}>
                <div className="accordion__intro flex justify-between">
                  <p className=" w-full h-10 bg-slate-200"></p>
                </div>
                <div className="accordion__content ">
                  <p className=" w-full h-4 bg-slate-100"></p>
                </div>
              </div>
            );
          })}
      </CustomContainer>
    </div>
  );
};

export default BeachFAQSkeleton;
