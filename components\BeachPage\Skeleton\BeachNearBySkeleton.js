import { CustomGrid } from "@/components/Custom-Display";
import React from "react";

const BeachNearBySkeleton = () => {
  return (
    // <div className="sm:container sm:mx-auto px-4 sm:px-4 lg:px-10 xl:px-40 2xl:px-40 3xl:px-40  4xl:px-48">
    <div className=" my-5">
      <div className="heading my-2">
        <div className="animate-pulse h-5 bg-slate-200 my-4"></div>
        <div className="animate-pulse h-6 bg-slate-200 my-4"></div>
      </div>
      <CustomGrid
        className="gap-4 sm:gap-8 mt-5 !mb-0"
        data={Array(10).fill(1)}
        Component={() => {
          return (
            <div className="relative  rounded-2xl sm:rounded-sandee group aspect-square sm:aspect-auto md:aspect-[180/140] group  bg-slate-200 animate-pulse"></div>
          );
        }}
        xs={2}
        // sm={2}
        md={3}
        lg={4}
        xl={5}
      />
    </div>
    // </div>
  );
};

export default BeachNearBySkeleton;
