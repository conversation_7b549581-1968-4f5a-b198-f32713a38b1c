import React, { Fragment } from "react";

const BeachSummaryBasicInfoSkeleton = () => {
  return (
    <Fragment>
      <div className="mt-5 space-y-5">
        {/* Overview Section */}
        <div className="flex flex-col space-y-2">
          <div className="animate-pulse h-6 bg-slate-200 w-full"></div> {/* "Overview" Tab */}
          {/* <div className="animate-pulse h-4 bg-slate-200 w-3/4"></div> Overview description */}
        </div>

        {/* Basic Details Section */}
        <div className="space-y-3">
          <div className="flex w-full">
            {/* <div className="animate-pulse h-6 bg-slate-200 w-1/3"></div> {/* Basic Details Heading */}
            {/* <div className="animate-pulse h-6 bg-slate-200 w-1/4"></div> Basic Details */}
            <div className="animate-pulse h-24 bg-slate-200 w-full"></div> {/* "Overview" Tab */}

          </div>
          {/* <div className="flex  w-full">
            <div className="animate-pulse h-8 bg-slate-200 w-full"></div>
            <div className="animate-pulse h-5 bg-slate-200 w-full"></div>
            <div className="animate-pulse h-5 bg-slate-200 w-full"></div>
            <div className="animate-pulse h-5 bg-slate-200 w-full"></div>
            <div className="animate-pulse h-5 bg-slate-200 w-full"></div>
            <div className="animate-pulse h-5 bg-slate-200 w-full"></div>

          </div> */}
        </div>

        {/* Know Before You Go Section */}
        <div className="space-y-3">
          <div className="flex flex-col space-y-3">
            {/* <div className="animate-pulse h-6 bg-slate-200 w-1/3"></div> Know Before You Go Heading */}
            <div className="animate-pulse h-8 bg-slate-200 w-full "></div> {/* Know Before You Go */}
            <div className="flex flex-col w-full space-y-2">
              <div className="animate-pulse h-5 bg-slate-200 w-full"></div>
              <div className="animate-pulse h-5 bg-slate-200 w-full"></div>
              <div className="animate-pulse h-5 bg-slate-200 w-full"></div>
              <div className="animate-pulse h-5 bg-slate-200 w-full"></div>
              {/* <div className="animate-pulse h-5 bg-slate-200 w-full"></div>
              <div className="animate-pulse h-5 bg-slate-200 w-full"></div> */}

            </div>
          </div>

          <div className="space-y-3">
            <div>
              <div className="animate-pulse h-8 bg-slate-200 w-full"></div>

            </div>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-6 ">
              {Array(6)
                .fill(1)
                .map((el, i) => {
                  return (
                    <div key={i} className="flex flex-col gap-x-3 gap-y-4">
                      {
                        Array(10)
                          .fill(1)
                          .map((el, i) => {
                            return (
                              <div key={i} className={`animate-pulse ${i == 0 ? "h-8" : "h-5"} bg-slate-200`}></div>
                            )
                          })
                      }
                    </div>
                  )
                })}
            </div>
          </div>

        </div>

        {/* Footer */}
        <div className="flex justify-center items-center mt-4">
          <div className="animate-pulse h-8 bg-slate-200 w-44"></div> {/* Suggest an Edit Button */}
          {/* <div className="animate-pulse h-8 bg-slate-200 w-1/5"></div> See All Button */}
        </div>
      </div>
    </Fragment>
  );
};

export default BeachSummaryBasicInfoSkeleton;
