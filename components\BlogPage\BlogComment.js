"use client";
import { postComment } from "@/app/(HomeHeader)/action";
import { Avatar, Drawer, List, notification } from "antd";
import React, { useState } from "react";

const BlogComment = ({
  open,
  onClose,
  totalComments,
  BlogData,
  token,
  refresher,
  allComments,
}) => {
  const [loading, setLoading] = useState(false);

  return (
    <Drawer
      title={`Top Comments (${totalComments})`}
      onClose={onClose}
      open={open}
    >
      <div className="custom-auth !rounded-md  !m-0">
        <div className="">
          <form
            className="form !mt-0"
            id="comment-form"
            onSubmit={async (e) => {
              e.preventDefault();
              const payload = {
                comment: e.target?.comment?.value,
                blogId: BlogData?.id,
              };

              setLoading(true);
              if (BlogData?.id) {
                const profile = await postComment(payload, token);
                if (profile?.data) {
                  notification.success({
                    message: "comment added successfully",
                  });
                  refresher();
                  e.target.reset();
                }
              }
              setLoading(false);
            }}
          >
            <textarea
              rows={4}
              placeholder="Comment"
              id="comment"
              name="comment"
              type=""
              className="input"
              required
            />
            <input
              defaultValue="Submit"
              disabled={loading}
              type="submit"
              className={` login-button ${
                loading ? " animate-pulse !bg-sandee-grey" : ""
              }`}
            />
          </form>
        </div>
      </div>
      <List
        className="comment-list"
        header={`${allComments?.length} Comments`}
        itemLayout="horizontal"
        dataSource={allComments}
        renderItem={(item) => (
          <List.Item key={item.title}>
            <List.Item.Meta
              avatar={<Avatar src={item?.user?.photo} />}
              title={<p>{item?.user?.name}</p>}
              description={item?.comment}
            />
            {/* {item?.comment} */}
          </List.Item>
        )}
      />
    </Drawer>
  );
};

export default BlogComment;
