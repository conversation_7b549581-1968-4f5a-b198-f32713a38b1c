import { SiteDataPageWise } from "@/data/siteMetadata";
import React from "react";

const BlogPageJSONLD = () => {
  const BlogBreadCumber = {
    "@context": "https://schema.org/",
    "@type": "BreadcrumbList",
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        name: "Home",
        item: `https://sandee.com/`,
      },
      {
        "@type": "ListItem",
        position: 2,
        name: "All Blogs",
        item: `https://sandee.com/blog`,
      },
    ],
  };
  const BlogSchema = {
    "@context": "https://schema.org",
    "@type": "Blog",
    name: SiteDataPageWise?.blogs?.title || "Blog | Sandee",
    headline: SiteDataPageWise?.blogs?.title || "Blog | Sandee",
    url: "https://sandee.com/blog",
    description: SiteDataPageWise?.blogs?.description || "Latest articles, stories, and updates from <PERSON><PERSON>.",
    publisher: {
      "@type": "Organization",
      name: "<PERSON><PERSON>",
    },
  };


  return (
    <>
      <script
        type="application/ld+json"
        id="application/ld+jsonBeachBreadCumber"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(BlogBreadCumber) }}
      ></script>
      <script
        type="application/ld+json"
        id="application/ld+jsonBeach"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(BlogSchema) }}
      ></script>
    </>
  );
};

export default BlogPageJSONLD;
