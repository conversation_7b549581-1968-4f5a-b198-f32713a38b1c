import { HomePageData } from "@/data/HomePageData";
import React from "react";
import { CustomGrid } from "../Custom-Display";
import ListCard from "../Cards/ListCard";
import NameTitle from "../Common/NameTitle";

const Blog_Related_Blog_Section = ({ data }) => {
  return (
    <>
      <NameTitle
        // className="mt-8"
        description={"EXPERT ADVICE AND TIPS"}
        name={HomePageData.BlogSection.title}
        type={2}
      />
      <CustomGrid
        data={data}
        className="gap-4 sm:gap-8 my-6 mb-12"
        Component={({ data: dataProps }) => {
          dataProps.link = dataProps?.link ?? `/blog/${dataProps?.slug}`; //
          dataProps.imageSrc = dataProps?.imageSrc ?? dataProps?.image;
          return ListCard({ data: { ...dataProps, name: dataProps?.title } });
        }}
        xs={1}
        sm={2}
        // md={2}
        lg={3}
        // xl={3}
      />
      {/* <div className="flex justify-center mb-6">
        <Link href={HomePageData.BlogSection.button_link}>
          <button className=" text-sandee-blue active:text-white active:bg-sandee-blue border-2 border-sandee-blue  py-2 px-5 transition ease-in-out duration-300  text-xs font-medium  rounded-full active:scale-[0.7] hover:scale-110">
            {HomePageData.BlogSection.button_text}
          </button>
        </Link>
      </div> */}
    </>
  );
};

export default Blog_Related_Blog_Section;
