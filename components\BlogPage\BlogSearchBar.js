"use client";
import React, { useEffect, useState } from "react";
import CustomBlogFilter from "./CustomBlogFilter";
import { API_BASE_URL } from "@/helper/functions";
import axios from "axios";
import SelectBox from "../Common/SelectBox";

const Blog_SearchBar_Section = ({
  selectedCategory,
  setSelectedCategory,
  setSelection,
  query,
  setQuery,
  scrollLoader = false
}) => {
  // const [selectedCategory, setSelectedCategory] = useState([]);
  const [categories, setCategories] = useState([]);

  const handelRefresher = () => {
    //   clearTimeout(debounce.current);
    //   debounce.current = setTimeout(() => {
    //     setPagination((prev) => ({ ...prev, page: 1 }));
    //   }, 500);
  };
  useEffect(() => {
    const getTagData = async () => {
      const CATEGORY = await getTestCategories();
      if (CATEGORY?.data?.length) {
        setCategories([
          ...CATEGORY?.data?.map((el) => ({
            label: el?.name,
            value: el?.id,
            id: el?.id,
          })),
        ]);
      }
    };
    getTagData();
  }, []);
  return (
    <>
      <h1 className="text-sandee-32 mb-3 mt-2 font-bold text-center md:text-start">
        Blogs
      </h1>
      <div className="  grid lg:grid-flow-col gap-4">

        <div className="relative col-span-6 ">
          <div className="absolute inset-y-0 start-0 flex items-center ps-2 pe-2 ms-2 pointer-events-none rounded-full">
            <svg
              className="w-4 h-4 text-[#7D7D7D]  "
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 20 20"
            >
              <path
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"
              />
            </svg>
          </div>
          <input
            type="text"
            id="simple-search"
            className={`transition-all duration-500  focus:outline-none outline-none  focus:ring-0 ring-0 focus:border-sandee-blue  bg-white border border-sandee-blue block w-full ps-10 sm:text-[20px]  rounded-xl text-gray-600 !pl-[50px] lg:text-sandee-sm  !text-sandee-sm p-2 `}
            placeholder="Search for Beach Blogs"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
          />
        </div>
        <div className="w-full h-full items-center flex gap-3 lg:justify-end flex-wrap col-span-6">
          <CustomBlogFilter
            categories={categories}
            setSelectedCategory={setSelectedCategory}
            selectCategory={selectedCategory}
            refresher={handelRefresher}
            scrollLoader={scrollLoader}
          />
          <SelectBox
            options={[
              {
                value: "DESC",
                label: "Latest",
              },
              {
                value: "ASC",
                label: "Oldest",
              },
            ]}
            disabled={scrollLoader}
            deafultSelectSort={{
              value: "DESC",
              label: "Latest",
            }}
            getSortingVal={(v) => {
              setSelection(v);
            }}
            menuClassName="w-full"
          />
          {/* <select
          placeholder="Latest"
          //   value="Latest"
          className=" min-w-[150px] h-10 p-[10px]  text-sandee-blue   border-sandee-blue border rounded-sandee"
        >
          <option value="Latest ">Latest</option>
          <option value="Oldest ">Oldest</option>
        </select> */}
        </div>
      </div>
    </>
  );
};

export default Blog_SearchBar_Section;

export const getTestCategories = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/category`);
    return response?.data;
  } catch (error) {
    // logController(error);
  }
};
