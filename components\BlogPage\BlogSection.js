// "use client";
// import React, { useEffect, useRef, useState } from "react";
// import { CustomContainer, CustomGrid } from "../Custom-Display";
// import Blog_SearchBar_Section from "./BlogSearchBar";
// import { getTestBlog } from "@/app/(HeaderSlim)/(Single Country Page)/[countrySlug]/action";
// import BlogCard from "../Cards/BlogCard";
// import ScrollBar from "../ScrollBar";

// const BlogSection = ({ initialData }) => {
//   const [blogData, setBlogData] = useState(initialData);
//   const [selectedCategory, setSelectedCategory] = useState([]);
//   const [selection, setSelection] = useState("DESC");
//   const [query, setQuery] = useState("");
//   const [scrollLoader, setScrollLoader] = useState(false);
//   const [loadoff, setLoadOff] = useState(false);
//   const [pagination, setPagination] = useState({
//     limit: 20,
//     page: 1,
//   });
//   const [refresh, setRefresh] = useState(false);

//   const prevSelection = useRef(selection);
//   const prevSelectedCategory = useRef(selectedCategory);
//   const prevQuery = useRef(query);

//   const delayedSearch = async (
//     queryValue,
//     selectionValue,
//     selectedCategoryValue
//   ) => {
//     const query = { ...pagination };
//     if (queryValue?.trim()?.length) {
//       query.searchQuery = queryValue;
//     }

//     if (selectedCategoryValue?.length) {
//       query.categoryIds = selectedCategoryValue?.join(",");
//     }
//     if (Object.keys(query)?.length > 0) {
//       if (!!selectionValue) {
//         query.sortBy = "createdAt";
//         query.sortOrder = selectionValue;
//       }
//     }
//     const { data } = await getTestBlog({ ...query });
//     if (data?.rows?.length && pagination.page > 1) {
//       setBlogData((prev) => [...prev, ...data?.rows]);
//       setLoadOff(false);
//     } else if (data?.rows?.length && pagination.page === 1) {
//       setBlogData((prev) => [...data?.rows]);
//       setLoadOff(false);
//     } else {
//       setLoadOff(true);
//     }
//     setScrollLoader(false);
//   };

//   useEffect(() => {
//     // if (pagination?.page > 2) {
//     setScrollLoader(true);
//     delayedSearch(query, selection, selectedCategory);
//     // }
//   }, [pagination.page, refresh]);

//   useEffect(() => {
//     if (
//       prevSelection.current !== selection ||
//       prevSelectedCategory.current !== selectedCategory ||
//       prevQuery.current !== query
//     ) {
//       const getData = setTimeout(() => {
//         setPagination((prev) => {
//           if (prev.page === 1) {
//             setRefresh((prevR) => !prevR);
//             setBlogData([]);
//           }
//           return { ...prev, page: 1 };
//         });
//       }, 400);

//       // Update previous values
//       prevSelection.current = selection;
//       prevSelectedCategory.current = selectedCategory;
//       prevQuery.current = query;
//       return () => clearTimeout(getData);
//     }
//   }, [selection, selectedCategory, query]);
//   return (
//     <>
//       <CustomContainer>
//         <Blog_SearchBar_Section
//           selectedCategory={selectedCategory}
//           setSelectedCategory={setSelectedCategory}
//           setSelection={setSelection}
//           setQuery={setQuery}
//           query={query}
//         />
//       </CustomContainer>
//       <CustomContainer className="">
//         {!blogData?.length && !scrollLoader ? (
//           <p className=" px-2 py-5 text-error-red-600  border-error-red-400 bg-error-red-50 border-2 rounded-sandee text-center">
//             No Data Found for your applied filters.
//           </p>
//         ) : (
//           <CustomGrid
//             data={blogData}
//             className=" gap-4 sm:gap-8 my-6 mb-12"
//             // Component={({ data: dataProps }) => {
//             //   dataProps.link = `/blog/${dataProps?.slug}`; //
//             //   dataProps.imageSrc = dataProps?.image;
//             //   return ListCard({
//             //     data: { ...dataProps, name: dataProps?.title },
//             //   });
//             // }}
//             Component={({ data: dataProps }) => {
//               dataProps.link = `/blog/${dataProps?.slug}`; //
//               // dataProps.imageSrc = FinalImageGenerator(dataProps?.listicleImage);
//               dataProps.imageSrc = dataProps?.image;
//               dataProps.name = dataProps?.title;
//               return BlogCard({
//                 data: { ...dataProps },
//                 copyRightsData: dataProps?.image,
//               });
//             }}
//             xs={1}
//             sm={2}
//             md={2}
//             lg={3}
//             xl={4}
//           />
//         )}
//         <ScrollBar
//           threshold={60}
//           loadMoreFunction={() => {
//             setPagination((prev) => ({ ...prev, page: prev?.page + 1 }));
//           }}
//           isLoading={scrollLoader}
//           loadoff={loadoff}
//           timeout={10}
//         />
//       </CustomContainer>
//     </>
//   );
// };

// export default BlogSection;


"use client";
import React, { useEffect, useRef, useState } from "react";
import { CustomContainer, CustomGrid } from "../Custom-Display";
import Blog_SearchBar_Section from "./BlogSearchBar";
import { getTestBlog } from "@/app/(HeaderSlim)/(Single Country Page)/[countrySlug]/action";
import BlogCard from "../Cards/BlogCard";
import ScrollBar from "../ScrollBar";

const BlogSection = ({ initialData }) => {
  const [blogData, setBlogData] = useState(initialData);
  const [selectedCategory, setSelectedCategory] = useState([]);
  const [selection, setSelection] = useState("DESC");
  const [query, setQuery] = useState("");
  const [scrollLoader, setScrollLoader] = useState(false);
  const [loadoff, setLoadOff] = useState(false);
  const [pagination, setPagination] = useState({
    limit: 20,
    page: 1,
  });
  const [refresh, setRefresh] = useState(false);

  const prevSelection = useRef(selection);
  const prevSelectedCategory = useRef(selectedCategory);
  const prevQuery = useRef(query);

  // Async function for delayed search
  const delayedSearch = async (
    queryValue,
    selectionValue,
    selectedCategoryValue
  ) => {
    const query = { ...pagination };
    if (queryValue?.trim()?.length) {
      query.searchQuery = queryValue;
    }

    if (selectedCategoryValue?.length) {
      query.categoryIds = selectedCategoryValue?.join(",");
    }
    if (Object.keys(query)?.length > 0) {
      if (!!selectionValue) {
        query.sortBy = "createdAt";
        query.sortOrder = selectionValue;
      }
    }

    try {
      const { data } = await getTestBlog({ ...query });
      if (data?.rows?.length && pagination.page > 1) {
        setBlogData((prev) => [...prev, ...data?.rows]);
        setLoadOff(false);
      } else if (data?.rows?.length && pagination.page === 1) {
        setBlogData((prev) => [...data?.rows]);
        setLoadOff(false);
      } else {
        setLoadOff(true);
      }
    } catch (error) {
      console.error("Error fetching blog data:", error);
    } finally {
      setScrollLoader(false);
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      setScrollLoader(true);
      await delayedSearch(query, selection, selectedCategory);
    };

    fetchData();
  }, [pagination.page, refresh]);

  useEffect(() => {
    if (
      prevSelection.current !== selection ||
      prevSelectedCategory.current !== selectedCategory ||
      prevQuery.current !== query
    ) {
      const getData = setTimeout(() => {
        setPagination((prev) => {
          if (prev.page === 1) {
            setRefresh((prevR) => !prevR);
            setBlogData([]);
          }
          return { ...prev, page: 1 };
        });
      }, 800);

      // Update previous values
      prevSelection.current = selection;
      prevSelectedCategory.current = selectedCategory;
      prevQuery.current = query;
      return () => clearTimeout(getData);
    }
  }, [selection, selectedCategory, query]);

  return (
    <>
      <CustomContainer>
        <Blog_SearchBar_Section
          selectedCategory={selectedCategory}
          setSelectedCategory={setSelectedCategory}
          setSelection={setSelection}
          setQuery={setQuery}
          query={query}
          scrollLoader={scrollLoader}
        />
      </CustomContainer>
      <CustomContainer className="min-h-screen">
        {!blogData?.length && !scrollLoader ? (
          <p className="px-2 py-5 text-error-red-600 border-error-red-400 bg-error-red-50 border-2 rounded-sandee text-center">
            No Data Found for your applied filters.
          </p>
        ) : (
          <CustomGrid
            data={blogData}
            className="gap-4 sm:gap-8 my-6 mb-12"
            Component={({ data: dataProps }) => {
              dataProps.link = `/blog/${dataProps?.slug}`; //
              dataProps.imageSrc = dataProps?.image;
              dataProps.name = dataProps?.title;
              return BlogCard({
                data: { ...dataProps },
                copyRightsData: dataProps?.image,
              });
            }}
            xs={1}
            sm={2}
            md={2}
            lg={3}
            xl={4}
          />
        )}
        <ScrollBar
          threshold={60}
          loadMoreFunction={() => {
            setPagination((prev) => ({ ...prev, page: prev?.page + 1 }));
          }}
          isLoading={scrollLoader}
          loadoff={loadoff}
          timeout={10}
        />
      </CustomContainer>
    </>
  );
};

export default BlogSection;

