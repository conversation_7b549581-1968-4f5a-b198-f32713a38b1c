"use client";
import React, { useEffect, useRef, useState } from "react";
// import { FaCaretDown, FaRegDotCircle } from "react-icons/fa";

const CustomBlogFilter = ({
  selectCategory = [],
  categories,
  setSelectedCategory,
  refresher = () => { },
  scrollLoader = false
}) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [categorySearch, setCategorySearch] = useState("");
  const filterBox = useRef(null);

  const toggle = () => {
    setDropdownOpen((prevState) => !prevState);
  };

  const handleClickOutside = (event) => {
    if (filterBox.current && !filterBox.current.contains(event.target)) {
      setDropdownOpen(false);
    }
  };

  const handleInputChange = (event) => {
    setCategorySearch(event.target.value);
  };

  useEffect(() => {
    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);

  const filteredCategories = categories.filter((el) =>
    el.label.toLowerCase().includes(categorySearch.toLowerCase())
  );

  return (
    <div className="relative z-10  h-10" ref={filterBox}>
      <div className="relative inline-block  h-10">
        <button
          onClick={toggle}
          className="bg-white  h-10 text-sandee-blue border border-sandee-blue px-4 py-2  rounded-xl flex items-center justify-between w-full min-w-[150px]"
        >
          <span className="flex items-center gap-4">
            Filters{" "}
            {selectCategory?.length > 0 && (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                height={20}
                width={20}
                className=" fill-sandee-blue mx-2"
                viewBox="0 0 512 512"
              >
                <path d="M256 8C119 8 8 119 8 256s111 248 248 248 248-111 248-248S393 8 256 8zm80 248c0 44.1-35.9 80-80 80s-80-35.9-80-80 35.9-80 80-80 80 35.9 80 80z" />
              </svg>
            )}
            {/* {selectCategory?.length > 0 && <FaRegDotCircle className="ml-2" />} */}
          </span>
          <svg
            fill="#00aae3"
            height="15"
            width="10"
            className={` outline-1 outline-sandee-blue ml-2 transition-all duration-100 ${dropdownOpen ? " -rotate-90" : ""
              }`}
            version="1.1"
            id="Layer_1"
            xmlns="http://www.w3.org/2000/svg"
            xmlnsXlink="http://www.w3.org/1999/xlink"
            viewBox="0 0 330.002 330.002"
            xmlSpace="preserve"
            transform="rotate(180)"
          >
            <g id="SVGRepo_bgCarrier" strokeWidth={0} />
            <g
              id="SVGRepo_tracerCarrier"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <g id="SVGRepo_iconCarrier">
              {" "}
              <path
                id="XMLID_105_"
                d="M324.001,209.25L173.997,96.75c-5.334-4-12.667-4-18,0L6.001,209.25c-6.627,4.971-7.971,14.373-3,21 c2.947,3.93,7.451,6.001,12.012,6.001c3.131,0,6.29-0.978,8.988-3.001L164.998,127.5l141.003,105.75c6.629,4.972,16.03,3.627,21-3 C331.972,223.623,330.628,214.221,324.001,209.25z"
              />{" "}
            </g>
          </svg>
        </button>
        {dropdownOpen && (
          <div
            className="absolute z-10  rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none p-4 mt-2 flex gap-3 flex-wrap"
            style={{
              minWidth: "300px",
              maxHeight: "500px",
              overflowY: "scroll",
            }}
          >
            <input
              type="text"
              placeholder="Search..."
              value={categorySearch}
              onChange={handleInputChange}
              className="p-2 my-2 w-full outline-sandee-blue outline-2 border-sandee-blue border  rounded-xl"
            />
            {filteredCategories.map((el) => (
              <div
                key={el?.id}
                className={`p-2 px-4 rounded-full active:scale-75 ${scrollLoader && "cursor-not-allowed"} ${selectCategory.includes(el?.id)
                  ? "text-white bg-sandee-blue"
                  : "text-sandee-blue bg-white"
                  } border border-sandee-blue cursor-pointer`}
                onClick={() => {
                  if (scrollLoader) return;
                  setSelectedCategory((prev) => {
                    if (prev?.includes(el?.id)) {
                      return prev.filter((pel) => el?.id !== pel);
                    }
                    return [...prev, el?.id];
                  });
                  refresher();
                }}
              >
                {el?.label}
              </div>
            ))}
            {selectCategory?.length ? (
              <div className="p-2">
                <div
                  className="text-red-500 bg-white border border-red-500 rounded cursor-pointer p-2"
                  onClick={() => {
                    setSelectedCategory((prev) => []);
                    refresher();
                  }}
                >
                  Clear All
                </div>
              </div>
            ) : null}
          </div>
        )}
      </div>
    </div>
  );
};

export default CustomBlogFilter;
