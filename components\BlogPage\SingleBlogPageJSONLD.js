import { siteMetadata } from "@/data/siteMetadata";
import { WEBSITE_URL } from "@/helper/functions";
import React from "react";

const SingleBlogPageJSONLD = ({ blogData }) => {
  const BlogBreadCumber = {
    "@context": "https://schema.org/",
    "@type": "BreadcrumbList",
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        name: "Home",
        item: `https://sandee.com/`,
      },
      {
        "@type": "ListItem",
        position: 2,
        name: "All Blogs",
        item: `https://sandee.com/blog`,
      },
      {
        "@type": "ListItem",
        position: 3,
        name: blogData?.name,
        item: `https://sandee.com/blog/${blogData?.slug}`,
      },
    ],
  };
  const BlogPostingSchema = {
    "@context": "https://schema.org",
    "@type": "BlogPosting",
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `https://sandee.com/blog/${blogData?.slug}`
    },
    "headline": blogData?.headline,
    "description": blogData?.description,
    "author": {
      "@type": "Person",
      "name": "Mr. Beach",
      "url": `https://sandee.com/mr-beach`
    },
    "publisher": {
      "@type": "Organization",
      "name": siteMetadata?.title || "Sandee",
      "logo": {
        "@type": "ImageObject",
        "url": `https://sandee.com/_next/image?url=%2Fstatic%2Fimages%2FSandee-Blue.webp&w=1920&q=75`
      }
    },
    "datePublished": blogData?.datePublished,
    "dateModified": blogData?.dateModified
  };


  return (
    <>
      <script
        type="application/ld+json"
        id="application/ld+jsonBeachBreadCumber"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(BlogBreadCumber) }}
      ></script>
      <script
        type="application/ld+json"
        id="application/ld+jsonBlogPosting"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(BlogPostingSchema) }}
      ></script>
    </>
  );
};

export default SingleBlogPageJSONLD;
