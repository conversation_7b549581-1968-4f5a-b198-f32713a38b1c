"use client";
import { scrollToSection } from "@/helper/functions";
import useScrollContent from "@/helper/hook/useScrollContent";
import React from "react";

const Blog_TableOfContent = ({ headings, h2Ids }) => {
  const { activeId } = useScrollContent(h2Ids);
  return (
    <div className="table-of-contents  font-semibold text-center ">
      <p className="text-sandee-18 font-semibold ">Table Of Contents</p>
      <p className="text-sandee-blue h-px bg-sandee-blue border border-sandee-blue mt-2" />
      <ul className="text-sandee-sm font-semibold text-start my-list list-none  position-sticky sticky-top top-5">
        {headings?.map((heading, index) => (
          <li
            key={index + 1}
            className={` my-2 ${activeId === `#${index + 1}` ? "active" : ""}`}
            onClick={() => {
              scrollToSection(`${index + 1}`);
            }}
          >
            <p
              className="  no-underline hover:lg:underline m-0 cursor-pointer"
              title={`#heading-${index + 1}`}
              // id={index + 1}
            >
              {heading}
            </p>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default Blog_TableOfContent;
