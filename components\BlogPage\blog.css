.blog-content .ql-editor li {
  font-size: 18px !important;
}
.blog-content ul {
  list-style-type: disc !important;
}

.blog-content ol {
  list-style-type: decimal !important;
}
.blog-content .ql-editor p {
  font-size: 18px !important;
  line-height: 30px !important;
  margin: 4px 0px !important;
}
.blog-editor p {
  margin-block-start: 0em !important;
  margin-block-end: 0em !important;
}
.blog-editor h2 {
  margin-block-start: 0.6em !important;
  margin-block-end: 0.4em !important;
}
.blog-editor h3 {
  margin-block-start: 0.6em !important;
  margin-block-end: 0.4em !important;
}

.ql-editor p {
  display: block;
  font-weight: normal;
  margin-block-start: 0em;
  margin-block-end: 0em;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
}
.ql-editor h2 {
  font-size: 1.5em;
  margin-block-start: 0em;
  margin-block-end: 0em;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  font-weight: bold;
}
.ql-editor h3 {
  display: block;
  font-size: 1.17em;
  margin-block-start: 0em;
  margin-block-end: 0em;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  font-weight: bold;
}
.ql-editor ul > li {
  list-style-type: disc !important;
}

.ql-editor ol > li {
  list-style-type: decimal !important;
}

.ql-editor a {
  text-decoration: underline;
}
.ql-editor ol,
.ql-editor ul {
  padding-left: 1.5em;
}

.ql-editor ul > li::before {
  content: unset;
}
.ql-editor ol li:not(.ql-direction-rtl),
.ql-editor ul li:not(.ql-direction-rtl) {
  padding-left: 0 !important;
  margin-left: 1.5em !important;
}
.ql-editor li.ql-indent-2:not(.ql-direction-rtl) {
  padding-left: 0 !important;
  margin-left: 7.5em !important;
}
.ql-editor li.ql-indent-1:not(.ql-direction-rtl) {
  padding-left: 0 !important;
  margin-left: 3em !important;
}
.ql-align-center {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}
@media (max-width: 768px) and (min-width: 426px) {
  .ql-editor ol li:not(.ql-direction-rtl),
  .ql-editor ul li:not(.ql-direction-rtl) {
    padding-left: 0 !important;
    margin-left: 1em !important;
  }
  .ql-editor li.ql-indent-2:not(.ql-direction-rtl) {
    padding-left: 0 !important;
    margin-left: 2.5em !important;
  }
  .ql-editor li.ql-indent-1:not(.ql-direction-rtl) {
    padding-left: 0 !important;
    margin-left: 3em !important;
  }
  .ql-editor .ql-indent-1 {
    padding-left: 0 !important;
    margin-left: 1em !important;
  }
  .ql-editor .ql-indent-2 {
    padding-left: 0 !important;
    margin-left: 2em !important;
  }
}
@media (max-width: 425px) {
  .ql-editor ol li:not(.ql-direction-rtl),
  .ql-editor ul li:not(.ql-direction-rtl) {
    padding-left: 0 !important;
    margin-left: 0em !important;
  }
  .ql-editor li.ql-indent-2:not(.ql-direction-rtl) {
    padding-left: 0 !important;
    margin-left: 0em !important;
  }
  .ql-editor li.ql-indent-1:not(.ql-direction-rtl) {
    padding-left: 0 !important;
    margin-left: 0em !important;
  }
  .ql-editor li.ql-indent-2 {
    padding-left: 0 !important;
    margin-left: 0em !important;
  }
  .ql-editor li.ql-indent-1 {
    padding-left: 0 !important;
    margin-left: 0em !important;
  }
  .ql-editor .ql-indent-1 {
    padding-left: 0 !important;
    margin-left: 0em !important;
  }
  .ql-editor .ql-indent-2 {
    padding-left: 0 !important;
    margin-left: 0em !important;
  }
}

.sidenav__sublist {
  text-align: start;
  list-style-type: none;
  padding: 0;
}

/* .sidenav__sublist li {
  position: relative;
  margin-left: 20px;
} */
/* li.sidenav__item.current a {
  color: #00aae3;
  font-weight: 700;
}
li.sidenav__item.current {
  color: #00aae3;
  font-weight: 700;
  list-style: disc;
}
li.sidenav__item a {
  text-decoration: none;
  color: #00aae3;
  font-weight: 400;
}
li.sidenav__item {
  text-align: start;
  color: #00aae3;
  font-weight: 400;
  list-style: none;
} */

.my-list {
  list-style-type: none;
  padding: 0;
}

.my-list li {
  @apply text-sandee-blue;
  position: relative;
  margin-left: 20px;
}
/* .my-list li >p:hover{
    text-decoration: underline!important;
  } */
.my-list li.active::before {
  content: "\2022"; /* Bullet character */
  position: absolute;
  /* font-size: 20px; */
  left: -22px;
  /* top: -18px; */
}

.my-list li.active {
  @apply text-sandee-blue;
  font-weight: bolder; /* Optional: Style the active link */
}

/* .share {
  @apply absolute w-[400px] ml-[-200px] -mt-10 rounded-[80px] left-2/4 top-2/4;
}
.share span {
  @apply w-[200px] leading-[80px] inline-block font-bold uppercase absolute ml-[-100px] opacity-100 transition-opacity duration-[0.3s] ease-[ease-in-out] pointer-events-none left-2/4;
}
.share nav {
  @apply text-[0];
}
.share a {
  @apply leading-[80px] w-20 text-center inline-block bg-white text-white overflow-hidden opacity-100 transition-all duration-[0.3s] ease-[ease-in-out] shadow-[3px_1px_3px_rgba(0,0,0,0.1)] -mx-5 my-0;
} */
/* .share a:nth-child(1) {
  @apply ml-0 rounded-tl-[40px] rounded-bl-[40px] hover:bg-[#61c5ec];
}
.share a:nth-child(2):hover {
  @apply bg-[#3b5998];
}
.share a:nth-child(3):hover {
  @apply bg-[#ea4335];
}
.share a:nth-child(4) {
  @apply mr-0 rounded-tr-[40px] rounded-br-[40px] hover:bg-black;
} */
/* .share:hover span,
.share.hover span {
  @apply opacity-0;
}
.share:hover a,
.share.hover a {
  @apply text-[#f1ce64] text-[28px] mx-2.5 my-0 rounded-[50%];
}
.share:hover a:hover,
.share.hover a:hover {
  @apply text-white;
} */

/* This is an example, feel free to delete this code */
.custom .tooltip-container {
  /* background: rgb(3, 169, 244);
  background: linear-gradient(
    138deg,
    rgba(3, 169, 244, 1) 15%,
    rgba(63, 180, 233, 1) 65%
  ); */
  position: relative;
  cursor: pointer;
  font-size: 17px;
  padding: 0.7em 0.7em;
  border-radius: 50px;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
}
.custom .tooltip-container:hover {
  background: #fff;
  transition: all 2s;
}
.custom .tooltip-container .text {
  display: flex;
  align-items: center;
  justify-content: center;
  fill: #fff;
  transition: all 0.2s;
}
.custom .tooltip-container:hover .text {
  fill: rgb(3, 169, 244);
  transition: all 2s;
}

/* Inicio do tooltip twitter */
.custom .tooltip1 {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  visibility: hidden;
  background: #fff;
  fill: #03a9f4;
  padding: 10px;
  border-radius: 50px;
  transition: opacity 0.3s, visibility 0.3s, top 0.3s, background 0.3s;
  z-index: 1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.custom .tooltip-container:hover .tooltip1 {
  top: 150%;
  opacity: 1;
  visibility: visible;
  background: #fff;
  border-radius: 50px;
  transform: translate(-50%, -5px);
  display: flex;
  align-items: center;
  justify-content: center;
  fill: #0462df;
}
/* .custom .tooltip-container:hover .tooltip1:hover {
  background: #0462df;
  fill: #fff;
} */
/* Fim do tooltip twitter */

/* Inicio do tooltip facebook */
.custom .tooltip2 {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  visibility: hidden;
  background: #fff;
  fill: #0462df;
  padding: 10px;
  border-radius: 50px;
  transition: opacity 0.3s, visibility 0.3s, top 0.3s, background 0.3s;
  z-index: 1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.custom .tooltip-container:hover .tooltip2 {
  top: -120%;
  opacity: 1;
  visibility: visible;
  background: #fff;
  transform: translate(-50%, -5px);
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* .custom .tooltip-container:hover .tooltip2:hover {
  background: #ea4c89;
  fill: #fff;
} */
/* Fim do tooltip facebook */

/* Inicio do tooltip whatsApp */
.custom .tooltip3 {
  position: absolute;
  top: 100%;
  left: 60%;
  transform: translateX(80%);
  opacity: 0;
  visibility: hidden;
  background: #fff;
  fill: #1db954;
  padding: 10px;
  border-radius: 50px;
  transition: opacity 0.3s, visibility 0.3s, top 0.3s, background 0.3s;
  z-index: 1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.custom .tooltip-container:hover .tooltip3 {
  top: 10%;
  opacity: 1;
  visibility: visible;
  background: #fff;
  transform: translate(85%, -5px);
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}
/* .custom .tooltip-container:hover .tooltip3:hover {
  background: #a4a4a4;
  fill: #fff;
} */
/* Fim do tooltip whatsApp */

/* Inicio do tooltip Discord */
.custom .tooltip4 {
  position: absolute;
  top: 100%;
  left: -190%;
  transform: translateX(70%);
  opacity: 0;
  visibility: hidden;
  background: #fff;
  fill: #8c9eff;
  padding: 10px;
  border-radius: 50px;
  transition: opacity 0.3s, visibility 0.3s, top 0.3s, background 0.3s;
  z-index: 1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.custom .tooltip-container:hover .tooltip4 {
  top: 10%;
  opacity: 1;
  visibility: visible;
  background: #fff;
  transform: translate(70%, -5px);
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}
/* .custom .tooltip-container:hover .tooltip4:hover {
  background: #fff;
  fill: #fff;
} */
/* Fim do tooltip Discord */

/* Inicio do tooltip pinterest */
.custom .tooltip5 {
  position: absolute;
  top: 100%;
  left: -145%;
  transform: translateX(70%);
  opacity: 0;
  visibility: hidden;
  background: #fff;
  fill: #bd081c;
  padding: 10px;
  border-radius: 50px;
  transition: opacity 0.3s, visibility 0.3s, top 0.3s, background 0.3s;
  z-index: 1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.custom .tooltip-container:hover .tooltip5 {
  top: -78%;
  opacity: 1;
  visibility: visible;
  background: #fff;
  transform: translate(70%, -5px);
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.custom .tooltip-container:hover .tooltip5:hover {
  background: #bd081c;
  fill: #fff;
}
/* Fim do tooltip pinterest */

/* Inicio do tooltip dribbble */
.custom .tooltip6 {
  position: absolute;
  top: 100%;
  left: 35%;
  transform: translateX(70%);
  opacity: 0;
  visibility: hidden;
  background: #fff;
  fill: #ea4c89;
  padding: 10px;
  border-radius: 50px;
  transition: opacity 0.3s, visibility 0.3s, top 0.3s, background 0.3s;
  z-index: 1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.custom .tooltip-container:hover .tooltip6 {
  top: -79%;
  opacity: 1;
  visibility: visible;
  background: #fff;
  transform: translate(70%, -5px);
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.custom .tooltip-container:hover .tooltip6:hover {
  background: #ea4c89;
  fill: #fff;
}
/* Fim do tooltip dribbble */

/* Inicio do tooltip github */
.custom .tooltip7 {
  position: absolute;
  top: 100%;
  left: 39%;
  transform: translateX(70%);
  opacity: 0;
  visibility: hidden;
  background: #fff;
  fill: #000;
  padding: 10px;
  border-radius: 50px;
  transition: opacity 0.3s, visibility 0.3s, top 0.3s, background 0.3s;
  z-index: 1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.custom .tooltip-container:hover .tooltip7 {
  top: 104%;
  opacity: 1;
  visibility: visible;
  background: #fff;
  transform: translate(70%, -5px);
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.custom .tooltip-container:hover .tooltip7:hover {
  background: #000;
  fill: #fff;
}
/* Fim do tooltip github */

/* Inicio do tooltip reddit */
.custom .tooltip8 {
  position: absolute;
  top: 100%;
  left: -150%;
  transform: translateX(70%);
  opacity: 0;
  visibility: hidden;
  background: #fff;
  fill: #ff4500;
  padding: 10px;
  border-radius: 50px;
  transition: opacity 0.3s, visibility 0.3s, top 0.3s, background 0.3s;
  z-index: 1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.custom .tooltip-container:hover .tooltip8 {
  top: 101%;
  opacity: 1;
  visibility: visible;
  background: #fff;
  transform: translate(70%, -5px);
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.custom .tooltip-container:hover .tooltip8:hover {
  background: #ff4500;
  fill: #fff;
}
/* Fim do tooltip reddit */

/* Inicio do tooltip fixo */
.custom .tooltip9 {
  position: absolute;
  top: 0;
  left: -115%;
  opacity: 0;
  visibility: hidden;
  width: 150px;
  height: 150px;
  z-index: -1;
}

.custom .tooltip-container:hover .tooltip9 {
  top: -110%;
  opacity: 1;
  visibility: visible;
  border-radius: 50%;
  z-index: -1;
}
/* Fim do tooltip fixo */
/* Por meio desse ultimo tooltip todos os outros podem
ficar fixos quando houver no principal, para vê-lo dê um
background black acima*/
