import React from "react";
import Blog_TableOfContent from "./TableOfContent";
import Image from "next/image";
import BlogLikeShare from "./BlogLikeShare";
import moment from "moment";
import { altText, EditorContent, processContent } from "@/helper/functions";
import CustomeImage from "../Common/CustomeImage";
const BlogPage_Overview_Section = ({
  BlogData,
  h2Ids,
  headings,
  modifiedHtmlString,
}) => {
  return (
    <div className=" mb-5 ">
      <div style={{ display: "block", position: "sticky", top: 0 }}>
        <div className="flex  flex-col sm:flex-row ">
          <div className="col block sm:sticky top-0 w-full lg:w-4/12 xl:w-3/12 px-5 ">
            <div className=" sticky top-0 bg-white z-[500] pt-8">
              <Blog_TableOfContent h2Ids={h2Ids} headings={headings} />
            </div>
          </div>
          <div className="col bg-white block w-full lg:w-8/12 xl:w-9/12">
            <div className=" bg-white z-[500] pb-3 px-3 pt-8">
              {/* <div className=" sticky top-0 bg-white z-[500] pb-3 px-3 pt-8"> */}
              <div className=" bg-white">
                <h1 className=" text-sandee-24 font-semibold ">
                  {BlogData?.title}
                </h1>

                <div className=" block sm:flex justify-between text-sandee-sm text-sandee-grey font-normal">
                  <p className="m-0">{BlogData?.readTime} read</p>
                  <p className="m-0">
                    {/* {new Date(BlogData?.createdAt).toDateString()} */}
                    {moment(BlogData?.createdAt?.split("T")?.[0])?.format(
                      "MMM DD, YYYY"
                    ) ?? "Nov, 09 2023"}
                  </p>
                </div>
              </div>
              <div className="flex gap-4 px-3 justify-center sm:justify-start flex-wrap mt-4">
                {BlogData?.blogCategories
                  ?.map((tag) => tag?.category?.name)
                  ?.map((el) => (
                    <p
                      key={el}
                      className=" text-sandee-blue "
                    // className=" text-sandee-blue active:text-white active:bg-sandee-blue border-2 border-sandee-blue  py-2 px-5 transition ease-in-out duration-300  px-10 text-xs font-medium  rounded-full active:scale-[0.7] hover:scale-110"
                    >
                      {`#${el}`}
                    </p>
                  ))}
              </div>
            </div>
            <div className=" bg-white px-3 w-full flex md:hidden gap-3  !cursor-pointer ">
              <BlogLikeShare slug={BlogData?.slug} BlogData={BlogData} />
            </div>
            <div className="flex gap-4 justify-center">
              <div className="col h-[430px] w-full lg:w-9/12">
                <div className=" relative w-full h-full">
                  {BlogData?.image && (
                    <CustomeImage
                      src={BlogData?.image}
                      fill
                      alt={altText(BlogData)}
                      //   blurDataURL={blurDataURL(1000, 600)}
                      //   placeholder="blur"
                      className="w-full h-full header-static object-cover rounded-sandee"
                      priority
                    />
                  )}

                  {/* <LazyCopyRight images={singleBeachData?.images} /> */}
                </div>
              </div>
              <EditorContent
                id="content"
                className=" min-w-full px-2 ql-container ql-editor blog-editor"
                value={processContent(modifiedHtmlString)}
              />
              {/* <div
                id="content"
                className=" min-w-full px-2 ql-container ql-editor blog-editor"
                dangerouslySetInnerHTML={{
                  __html: modifiedHtmlString,
                }}
              /> */}
            </div>
          </div>
          <div className="col bg-white w-full hidden md:block lg:w-2/12 xl:w-1/12">
            <BlogLikeShare
              slug={BlogData?.slug}
              BlogData={BlogData}
              className="flex flex-col mt-8 !cursor-pointer"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlogPage_Overview_Section;
