import { SiteDataPageWise } from "@/data/siteMetadata";
import React from "react";

const BreadCumberJSONLD = ({ name, slug, title = "", description = "" }) => {
  const webPageSchema = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    url: `https://sandee.com/${slug}`,
    name: title,
    headline: title,
    description: description,
    publisher: {
      "@type": "Organization",
      name: "<PERSON><PERSON>",
    },
  };
  const ListBreadCumber = {
    "@context": "https://schema.org/",
    "@type": "BreadcrumbList",
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        name: "Home",
        item: `https://sandee.com/`,
      },
      {
        "@type": "ListItem",
        position: 2,
        name,
        item: `https://sandee.com/${slug}`,
      },
    ],
  };

  return (
    <>
      <script
        type="application/ld+json"
        id="webPageSchema"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(webPageSchema) }}
      ></script>
      <script
        type="application/ld+json"
        id="application/ld+jsonBreadCumber"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(ListBreadCumber) }}
      ></script>
    </>
  );
};

export default BreadCumberJSONLD;
