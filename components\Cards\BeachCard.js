import Image from "next/image";
import Link from "next/link";
import React from "react";
import {
  altText,
  blurDataURL,
  defaultImage,
  FinalImageGenerator,
} from "@/helper/functions";
import {
  CampingIcon,
  DisabilityIcon,
  DogIcon,
  FamilyIcon,
  GayIcon,
  NudeIcon,
  SurfingIcon,
} from "../social-icons/icons";
import CustomToolTip from "../Common/CustomToolTip";
import CopyRight from "../Common/CopyRight";
import { BeachCategoryTags } from "@/helper/constant";
import CustomeImage from "../Common/CustomeImage";
const BeachCard = ({
  data,
  dataTestid = "beach-card",
  // className = "h-[200px]",
  className = "md:aspect-[180/140] aspect-square ",
  // className = "md:aspect-[390/303] aspect-square"
}) => {
  data.lat = data?.GeoLoc?.coordinates?.length
    ? data?.GeoLoc?.coordinates?.[1]
    : 0;
  data.lon = data?.GeoLoc?.coordinates?.length
    ? data?.GeoLoc?.coordinates?.[0]
    : 0;

  // Check if restrooms, showers, and lifeguard are all true
  // const hasAllCategories =
  //   data?.beachCategory?.restRooms &&
  //   data?.beachCategory?.showers &&
  //   data?.beachCategory?.lifeguard;

  // const amenitiesTag = BeachCategoryTags?.find((tag) => {
  //   if (tag?.value === "family" && hasAllCategories) {
  //     // Prioritize the Family tag only when all categories are true
  //     return true;
  //   }
  //   // For other tags, check if they exist in beachCategory and are true
  //   return data?.beachCategory?.[tag?.value];
  // });

  // const selectedTag = amenitiesTag || {
  //   id: 0,
  //   label: "Family",
  //   value: "family",
  //   icon: <FamilyIcon className="w-[18px] h-[18px]" />,
  // };
  // const dummy = {
  //   15988: true,
  //   15786: true,
  //   15801: true,
  //   21159: true,

  // }

  return (
    <>
      <Link
        href={`${data?.link}`}
        className=""
        data-testid={dataTestid}
      // target={`${data?.target}` ?? '_self'}
      >
        <div className="flex flex-col gap-y-[10px]">
          <div
            className={`relative overflow-hidden aspect-square rounded-[25px] sm:rounded-sandee ${className} `}
          >
            {/* {data?.imageSrc && data?.imageSrc !== defaultImage ? ( */}
            <div
              className={`relative overflow-hidden aspect-square rounded-[25px] sm:rounded-sandee ${className} `}
            >
              <CustomeImage
                className="w-full object-cover"
                src={FinalImageGenerator(data?.images[0])}
                // alt={`${data?.location
                //   ? `Sandee ${data?.name} Photo`
                //   : `${data?.name}`
                //   }`}
                alt={altText(data)}
                fill
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                blurDataURL={blurDataURL(300, 200)}
                placeholder="blur"
              />

              <CopyRight
                copyRightsData={data?.images}
                // background={true}
                // styleExtra={{ bottom: "1px", left: "3px" }}
                classNameExtra={"bottom-0"}
              />
            </div>
            {/* ) : (
              <iframe
                className=" bg-gray-200 w-full h-full rounded-sandee min-h-[280px] pointer-events-none"
                loading="lazy"
                src={`https://maps.google.com/?q=${data?.lat},${data?.lon}&ie=UTF8&iwloc=&output=embed&t=k&disableDefaultUI=true`}
              ></iframe>
            )} */}
          </div>
          <div className="flex flex-col gap-y-1 ms-[5px]">
            {/* <div className="flex gap-x-2 items-center text-sm">
              {selectedTag?.icon}
              <span className="font-medium">{selectedTag?.label}</span>
              <span>|</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width={18}
                height={18}
                viewBox="0 0 16 16"
                fill="none"
              >
                <path
                  d="M7.99956 11.5135L10.7662 13.1868C11.2729 13.4935 11.8929 13.0402 11.7596 12.4668L11.0262 9.32017L13.4729 7.20017C13.9196 6.8135 13.6796 6.08017 13.0929 6.0335L9.87289 5.76017L8.61289 2.78684C8.38623 2.24684 7.61289 2.24684 7.38623 2.78684L6.12623 5.7535L2.90623 6.02684C2.31956 6.0735 2.07956 6.80684 2.52623 7.1935L4.97289 9.3135L4.23956 12.4602C4.10623 13.0335 4.72623 13.4868 5.23289 13.1802L7.99956 11.5135Z"
                  fill="#1F1F1F"
                />
              </svg>
              <span className="font-medium">
                {((data?.rating100 ?? 80) / 20)?.toFixed(2)}
              </span>
            </div> */}
            <CustomToolTip title={data?.name}>
              <h3 className="text-lg font-bold line-clamp-1 mb-0 leading-[24px]">
                {data?.name}
              </h3>
            </CustomToolTip>
            <span className="uppercase opacity-[0.8] text-[#656565] line-clamp-2 md:text-base text-sm font-semibold -mt-[5px]">
              {data?.location}
            </span>
            {data?.distance && (
              <span className=" text-sandee-12 ms-1">{`(${data?.distance.toFixed(
                2
              )} Miles)`}</span>
            )}
          </div>
        </div>
      </Link>
    </>

    // <div
    //   className={`relative ${className} rounded-2xl sm:rounded-sandee group`}
    // >
    //   <Link
    //     href={`${data?.link}`}
    //     className="w-full h-full group rounded-2xl sm:rounded-sandee shadow-[0_3px_10px_rgb(0,0,0,0.2)]"
    //     // target={`${data?.target}` ?? '_self'}
    //   >
    //     <div
    //       className={`absolute left-0 top-0  ${className} w-full rounded-2xl sm:rounded-sandee`}
    //     >
    //       <div className="relative  overflow-hidden  w-full h-full rounded-2xl sm:rounded-sandee ">
    //         {data?.imageSrc && data?.imageSrc !== defaultImage ? (
    //           <Image
    //             // priority
    //             className="transition-transform duration-1000 ease-in-out transform md:scale-100 scale-125 group-hover:scale-125 w-full object-cover"
    //             src={data?.imageSrc}
    //             // src={data?.imageSrc ? data?.imageSrc :`https://maps.googleapis.com/maps/api/staticmap?center=${data?.lat},${data?.lon}&zoom=15&size=600x400&maptype=satellite&key=AIzaSyDLfO8bHqEoNNmnyh3scit7hHUQr7Jx36Q`}
    //             alt={`${
    //               data?.location
    //                 ? `Sandee ${data?.name} Photo`
    //                 : `${data?.name}`
    //             }`}
    //             fill
    //             // onError={(event) => {
    //             //   event.target.src =
    //             //     `https://maps.googleapis.com/maps/api/staticmap?center=${data?.lat},${data?.lon}&zoom=15&size=600x400&maptype=satellite&key=AIzaSyDLfO8bHqEoNNmnyh3scit7hHUQr7Jx36Q`
    //             //     // "https://images.sandee.com/images/header/Default-Header.avif";
    //             //   event.target.alt = "Image Not Found";
    //             // }}
    //             sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
    //             blurDataURL={blurDataURL(300, 200)}
    //             placeholder="blur"
    //           />
    //         ) : (
    //           <iframe
    //             className=" bg-gray-200 w-full h-full rounded-sandee min-h-[280px] pointer-events-none"
    //             loading="lazy"
    //             src={`https://maps.google.com/?q=${data?.lat},${data?.lon}&ie=UTF8&iwloc=&output=embed&t=k&disableDefaultUI=true`}
    //           ></iframe>
    //         )}
    //       </div>
    //     </div>
    //     <div className="absolute left-0 top-0 flex group-hover:sm:translate-x-[0%] md:translate-x-[150%] sm:p-3 group-hover:w-full w-full md:w-1 rounded-2xl sm:rounded-sandee md:opacity-0 group-hover:opacity-100 transition-all duration-[10000] ease-in-out">
    //       <div className="py-2 px-2 mt-2 ml-1 sm:mt-0 sm:ml-0 sm:px-3 rounded-full bg-[#ffffff33]  text-white flex justify-between items-center backdrop-blur-sm">
    //         <p className=" flex justify-around items-center text-xs font-semibold">
    //           <span className=" mr-1 sm:mr-2">
    //             <svg
    //               xmlns="http://www.w3.org/2000/svg"
    //               width="17"
    //               height="16"
    //               viewBox="0 0 17 16"
    //               fill="none"
    //               className=" stroke-white"
    //             >
    //               <path
    //                 d="M14.8714 7.9998C14.8714 11.6798 11.8848 14.6665 8.20475 14.6665C4.52475 14.6665 1.53809 11.6798 1.53809 7.9998C1.53809 4.3198 4.52475 1.33313 8.20475 1.33313C11.8848 1.33313 14.8714 4.3198 14.8714 7.9998Z"
    //                 strokeWidth="1.07684"
    //                 strokeLinecap="round"
    //                 strokeLinejoin="round"
    //               />
    //               <path
    //                 d="M10.678 10.1203L8.61134 8.88696C8.25134 8.67362 7.95801 8.16029 7.95801 7.74029V5.00696"
    //                 strokeWidth="1.07684"
    //                 strokeLinecap="round"
    //                 strokeLinejoin="round"
    //               />
    //             </svg>
    //           </span>
    //           24 / 7
    //         </p>
    //         <div className="  border-r-2 border-white h-full  mx-2"></div>
    //         <p className=" flex justify-around items-center text-xs font-semibold">
    //           <span className=" mr-1 sm:mr-2">
    //             <svg
    //               xmlns="http://www.w3.org/2000/svg"
    //               width="14"
    //               height="14"
    //               viewBox="0 0 14 14"
    //               fill="none"
    //               className=" fill-white"
    //             >
    //               <path d="M8.35835 1.33977L9.53168 3.68643C9.69169 4.0131 10.1184 4.32643 10.4784 4.38643L12.605 4.73976C13.965 4.96643 14.285 5.9531 13.305 6.92643L11.6517 8.57976C11.3717 8.85976 11.2184 9.39976 11.305 9.78643L11.7784 11.8331C12.1517 13.4531 11.2917 14.0798 9.85835 13.2331L7.86502 12.0531C7.50502 11.8398 6.91169 11.8398 6.54502 12.0531L4.55168 13.2331C3.12502 14.0798 2.25835 13.4464 2.63168 11.8331L3.10502 9.78643C3.19168 9.39976 3.03835 8.85976 2.75835 8.57976L1.10502 6.92643C0.131684 5.9531 0.445018 4.96643 1.80502 4.73976L3.93168 4.38643C4.28502 4.32643 4.71168 4.0131 4.87168 3.68643L6.04502 1.33977C6.68502 0.0664324 7.72502 0.0664324 8.35835 1.33977Z" />
    //             </svg>
    //           </span>
    //           {(data?.rating100 ?? 80) / 20}
    //         </p>
    //       </div>
    //     </div>
    //     <div
    //       className={`absolute content-gradiant-black left-0 bottom-0 pb-1 ${className} w-full  rounded-2xl sm:rounded-sandee hover:cardgradianthovered  px-3 flex flex-col justify-end md:cardgradiant cardgradiant group-hover:hoverfullcardonhoverB  transition-all duration-1000 `}
    //       // className={`absolute content-gradiant-black left-0 bottom-0 pb-1 ${className} w-full  rounded-2xl sm:rounded-sandee  px-3 flex flex-col justify-end hoverfullcard transition-all duration-1000 `}
    //     >
    //       <div className=" text-white ">
    //         <h3 className=" text-sandee-base  sm:text-sandee-18 py-[3px] font-medium line-clamp-2 leading-tight">
    //           {data?.name}
    // {data?.distance && (
    //   <span className=" text-sandee-12 ms-1">{`(${data?.distance.toFixed(
    //     2
    //   )} miles)`}</span>
    // )}
    //         </h3>
    //         {/* <div className="has-tooltip  text-sandee-12 text-white font-medium line-clamp-1">
    //           <span className="tooltip rounded-lg shadow-lg p-1 px-4 bg-white border-gray-200 border text-sandee-blue -mt-5 scale-105">
    //             {data?.location}
    //           </span>
    //           {data?.location}
    //         </div> */}
    //         <div className=" text-sandee-10 sm:text-[11px] py-[3px] text-white font-medium flex ">
    //           {/* <span>
    //             {" "}
    //             <PinLocation className=" mr-1 fill-white h-4 w-4" />
    //           </span> */}
    //           <p className="md:line-clamp-1 group-hover:line-clamp-6  leading-tight">
    //             {data?.location}
    //           </p>
    //         </div>
    //       </div>
    //       <div className="md:opacity-0  group-hover:opacity-100 transition-all duration-100  ease-in-out mb-1 md:mb-0 group-hover:mb-1">
    //         {/* <p className="h-0 text-sandee-sm group-hover:h-auto text-xs text-white font-normal line-clamp-3 ">
    //           {data?.decription ??
    //             " Centrally located in Miami, South Beach is not the place for beachgoers that want an escape from the big city"}
    //         </p> */}
    //         {/* <button className="hidden  backdrop-blur-[2px] transition-all duration-100 group-hover:flex items-center text-white  active:bg-sandee-blue  bg-[#ffffff33] shadow-xl py-2 px-5   text-xs font-medium  rounded-full  hover:bg-sandee-blue">
    //           Explore
    //           <ExploreMoreArrow className=" ml-1 fill-white h-4 w-4" />
    //         </button> */}
    //       </div>
    //     </div>
    //     {/* {copyRightsData?.length || data?.images?.length ? (
    //       <CopyRight
    //         copyRightsData={
    //           copyRightsData?.length ? copyRightsData : data?.images
    //         }
    //         background={false}
    //         styleExtra={{ bottom: "1px", left: "3px" }}
    //       />
    //     ) : (
    //       ""
    //     )} */}
    //   </Link>
    // </div>
  );
};

export default BeachCard;

// export function convertDistance(distance) {
//   const miles = +distance * 0.621371;
//   if (miles < 1) {
//     return `${(miles * 5280).toFixed(0)} feet`;
//   } else {
//     return `${miles.toFixed(3)} miles`;
//   }
// }

// export function convertDistance(lat1, lon1, lat2, lon2) {
//   const R = 3958.8; // Radius of the Earth in miles
//   const dLat = (lat2 - lat1) * Math.PI / 180;
//   const dLon = (lon2 - lon1) * Math.PI / 180;

//   const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
//             Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
//             Math.sin(dLon / 2) * Math.sin(dLon / 2);

//   const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
//   const distance = R * c;

//   return distance;
// }
export const BeachCardSkeleton = () => {
  return (
    <div className="relative aspect-square sm:aspect-auto sm:h-[200px] lg:h-[190px] rounded-2xl sm:rounded-sandee">
      <div className="relative aspect-square sm:aspect-auto sm:h-[200px] lg:h-[190px] rounded-2xl sm:rounded-sandee bg-slate-200 animate-pulse"></div>
    </div>
  );
};
