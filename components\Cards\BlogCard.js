// import { blurDataURL } from "@/helper/functions";
// import Image from "next/image";
// import Link from "next/link";
// import React from "react";
// import CopyRight from "../Common/CopyRight";
// import moment from "moment";

// const BlogCard = ({ data, copyRightsData }) => {
//   return (
//     <Link
//       href={`${data?.link}`}
//       className="relative group h-[300px]  rounded-sandee cursor-pointer shadow-lg transition-all duration-500"
//     >
//       <div className="absolute left-0 top-0  h-[140px] w-full  rounded-t-sandee  shadow-[0_3px_10px_rgb(0,0,0,0.2)]">
//         <div className="relative  overflow-hidden  w-full h-full  rounded-t-sandee">
//           <Image
//             // priority
//             className="transition-transform duration-1000 ease-in-out transform group-hover:scale-125 w-full object-cover object-center"
//             src={data?.imageSrc}
//             alt={`Sandee - Blog / ${data?.name}`}
//             fill
//             sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
//             blurDataURL={blurDataURL(300, 200)}
//             placeholder="blur"
//           />
//         </div>
//       </div>
//       <div className="  bg-white absolute left-0 bottom-0  h-[160px] w-full  rounded-b-sandee flex flex-col items-start justify-center text-center px-4 gap-1  pb-1">
//         <p className=" truncate text-[#7D7D7D] text-xs font-normal flex items-center gap-3" suppressHydrationWarning                                     >
//           {/* <BecahIcon className=" h-5 w-5 fill-[#7D7D7D] mr-1 items-center" /> */}
//           {moment(data?.createdAt?.split("T")?.[0]).format("MMMM Do, YYYY") ??
//             " 09 Nov, 2023"}
//           <span className="h-1 w-1 bg-[#7D7D7D] rounded-full"></span> Sandee
//         </p>
//         <h3 className=" text-black text-[20px]  text-start line-clamp-2 leading-6 ">
//           {data?.name}
//         </h3>
//         <p className=" text-[#7D7D7D] text-xs font-normal  line-clamp-3 text-start ">
//           {/* <BecahIcon className=" h-5 w-5 fill-[#7D7D7D] mr-1 items-center" /> */}
//           {data?.description}
//         </p>
//       </div>
//       {copyRightsData?.length || data?.images?.length ? (
//         <CopyRight
//           copyRightsData={
//             copyRightsData?.length ? copyRightsData : data?.images
//           }
//           background={false}
//           styleExtra={{ bottom: "1px", left: "3px" }}
//         />
//       ) : (
//         ""
//       )}
//       {/* <div className="absolute  left-0 top-0    h-1/2  w-full  rounded-sandee">
//         <div className="relative  overflow-hidden  w-full h-full  rounded-sandee">
//           <Image
//             // priority
//             className="w-full"
//             src={data?.imageSrc}
//             alt={`Sandee - Listicles / ${data?.name}`}
//             fill
//             sizes="100vw"
//           />
//         </div>
//       </div>
//       <div className="absolute left-0 bottom-0 px-3 py-2  h-1/2  w-full  rounded-sandee flex items-start justify-end text-start flex-col ">
//         <p className=" text-white text-sandee-18 font-medium line-clamp-2">
//           {data?.name}
//         </p>
//         <p className=" text-sandee-blue text-sandee-18 font-medium hover:underline underline-offset-2 mt-2 mb-1">
//           Read More &gt;
//         </p>
//       </div> */}
//     </Link>
//   );
// };

// export default BlogCard;

import { altText, blurDataURL } from "@/helper/functions";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import CopyRight from "../Common/CopyRight";
import moment from "moment";
import CustomToolTip from "../Common/CustomToolTip";

const BlogCard = ({ data, copyRightsData, dataTestid, index = "0" }) => {
  return (
    <Link
      href={`${data?.link}`}
      className="relative rounded-sandee cursor-pointer"
      data-testid={dataTestid}
    >
      <div data-testid={`blog-card-image-container-${index}`} className="relative overflow-hidden aspect-video rounded-sandee">
        <Image
          // priority
          // className="transition-transform duration-1000 ease-in-out transform group-hover:scale-125 w-full object-cover object-center"
          src={data?.imageSrc}
          data-testid={`blog-card-image-${index}`}
          alt={altText(data, `Sandee - Blog /`)}
          // alt={`Sandee - Blog / ${data?.name}`}
          fill
          // sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          blurDataURL={blurDataURL(300, 200)}
          placeholder="blur"
        />
      </div>
      <div data-testid={`blog-card-text-container-${index}`} className="flex flex-col gap-y-[0.1rem] mt-[0.7rem] ms-[5px]">
        <p
          className=" truncate text-xs font-medium flex items-center gap-3 uppercase"
          suppressHydrationWarning
          data-testid={`blog-card-date-${index}`}
        >
          {moment(data?.createdAt?.split("T")?.[0]).format('MMM DD, YYYY') ??
            "Nov, 09 2023"}
          <span data-testid={`blog-card-date-separator-${index}`} className="h-1 w-1 bg-[#7D7D7D] rounded-full "></span> SANDEE
        </p>
        <CustomToolTip title={data?.name}>
          <h3 className=" text-black text-[18px]  text-start line-clamp-2 leading-6 font-semibold ">
            {data?.name}
          </h3>
        </CustomToolTip>
        <p data-testid={`blog-card-description-${index}`} className=" text-[#7D7D7D] text-sm font-normal  line-clamp-2 text-start ">
          {/* <BecahIcon className=" h-5 w-5 fill-[#7D7D7D] mr-1 items-center" /> */}
          {data?.description}
        </p>
      </div>
    </Link>
  );
};

export default BlogCard;
