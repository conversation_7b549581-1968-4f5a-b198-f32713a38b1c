// import Image from "next/image";
// import Link from "next/link";
// import React from "react";
// import { blurDataURL } from "@/helper/functions";

// const CountryCard = ({
//   data,
//   copyRightsData,
//   // className = "h-[200px] lg:h-[190px]",
//   className = "aspect-square sm:aspect-auto sm:h-[200px] lg:h-[190px]",
// }) => {
//   return (
//     <Link
//       href={`${data?.link}`}
//       className={`relative ${className}  group rounded-xl shadow-[0_3px_10px_rgb(0,0,0,0.2)]  hover:md:-mt-2 duration-150 transition-all `}
//     >
//       <div
//         className={`absolute left-0 top-0  ${className}  w-full  rounded-xl`}
//       >
//         <div className="relative  overflow-hidden  w-full h-full  rounded-xl ">
//           <Image
//             // priority
//             className="transition-all duration-1000 group-hover:scale-125 w-full object-cover object-center"
//             src={data?.image}
//             alt={`Sandee ${data?.name}`}
//             fill
//             sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
//             blurDataURL={blurDataURL(300, 200)}
//             placeholder="blur"
//           />
//         </div>
//       </div>
//       <div
//         className={`absolute content-gradiant-black left-0 bottom-0  ${className}   w-full   rounded-xl  px-3 flex flex-col justify-end hoverfullcard transition-all duration-1000 ${
//           "" // copyRightsData?.length || data?.images?.length ? "pb-3" : ""
//         }`}
//       >
//         <h3 className=" text-white text-sandee-18 sm:text-sandee-24 font-medium">
//           {data?.name}
//         </h3>
//         <div className="opacity-0  group-hover:opacity-100 transition-all duration-1000 pb-2 ease-in-out">
//           <p className="h-0 group-hover:h-auto text-xs text-white font-normal line-clamp-3 ">
//             {data?.description ??
//               ` ${data?.name}, a captivating country nestled in the Southern
//             Hemisphere.`}
//           </p>
//           {/* <button className="hidden my-1 group-hover:flex items-center text-white  active:bg-sandee-blue  bg-[#ffffff33] shadow-xl py-2 px-5   text-xs font-medium  rounded-full  hover:bg-sandee-blue">
//             Explore
//             <ExploreMoreArrow className=" ml-1 fill-white" />
//           </button> */}
//         </div>
//       </div>
//       {/* {copyRightsData?.length || data?.images?.length ? (
//         <CopyRight
//           copyRightsData={
//             copyRightsData?.length ? copyRightsData : data?.images
//           }
//           background={false}
//           styleExtra={{ bottom: "1px", left: "3px" }}
//         />
//       ) : (
//         ""
//       )} */}
//     </Link>
//   );
// };

// export default CountryCard;

import Image from "next/image";
import Link from "next/link";
import React from "react";
import { altText, blurDataURL } from "@/helper/functions";
import CopyRight from "../Common/CopyRight";
import CustomeImage from "../Common/CustomeImage";

const CountryCard = ({
  data,
  copyRightsData,
  // className = "h-[200px] lg:h-[190px]",
  className = "md:aspect-[180/140] aspect-square",
  // className = "md:aspect-[390/303] aspect-square"
}) => {
  return (
    <Link
      href={`${data?.link}`}
      // className={`relative ${className}  group rounded-xl shadow-[0_3px_10px_rgb(0,0,0,0.2)]  hover:md:-mt-2 duration-150 transition-all `}
      className={`${className}`}
    >
      <div
        className={`${className}  w-full rounded-[18px]`}
      // className={`absolute left-0 top-0  ${className}  w-full rounded-[18px]`}
      >
        <div className="relative  overflow-hidden  w-full h-full rounded-[18px] ">
          <CustomeImage
            // className="transition-all duration-1000 group-hover:scale-125 w-full object-cover object-center"
            src={data?.imageurl || data?.image}
            alt={altText(data)}
            fill
            // sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            blurDataURL={blurDataURL(300, 200)}
            placeholder="blur"
          />
          <CopyRight
            copyRightsData={copyRightsData}
            // background={true}
            // styleExtra={{ bottom: "1px", left: "3px" }}
            classNameExtra={"bottom-0"}
          />
        </div>
        <h3 className="text-sandee-20 font-bold mt-2 ms-[5px]">{data?.name}</h3>
        <p className="uppercase text-sm -mt-[3px] ms-[5px]">
          {Number(data?.beachcount)?.toLocaleString()}+ Beaches
        </p>
        {/* <span className="uppercase text-sm">{data?.totalBeach?.toLocaleString()}+ Beaches</span> */}
      </div>
    </Link>
  );
};

export default CountryCard;
