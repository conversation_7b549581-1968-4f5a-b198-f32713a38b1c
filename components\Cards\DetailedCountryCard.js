import { altText, blurDataUR<PERSON>, EditorContent, isHTML } from "@/helper/functions";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import CopyRight from "../Common/CopyRight";

const DetailedCountryCard = ({
  data,
  copyRightsData,
  className = "h-[190px]  sm:h-[200px] lg:h-[300px]",
}) => {
  return (
    <Link
      href={`${data?.link}`}
      className={`relative group ${className}  rounded-sandee cursor-pointer shadow-lg transition-all duration-500`}
    >
      <div className="absolute left-0 top-0 h-[120px]  lg:h-[200px] w-full  rounded-t-sandee  ">
        <div className="relative  overflow-hidden  w-full h-full  rounded-t-sandee">
          <Image
            // priority
            className="transition-transform duration-1000 ease-in-out transform group-hover:scale-125 w-full object-cover object-center  "
            src={data?.image}
            alt={altText(data, `Sandee - Country /`)}
            fill
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            blurDataURL={blurDataURL(300, 200)}
            placeholder="blur"
          />
        </div>
        {copyRightsData?.length || data?.images?.length ? (
          <CopyRight
            copyRightsData={
              copyRightsData?.length ? copyRightsData : data?.images
            }
            background={false}
            styleExtra={{ bottom: "1px", left: "3px" }}
          />
        ) : (
          ""
        )}
      </div>
      <div className="  bg-white absolute left-0 bottom-0   w-full  rounded-b-sandee flex flex-col items-start justify-center text-center px-2 sm:px-4 sm:gap-1 pb-2 sm:pb-3">
        {/* <p className=" text-[#7D7D7D] text-xs font-normal flex items-center gap-3">
          {data?.createdAt ?? " 09 Nov, 2023"}
          <span className="h-1 w-1 bg-[#7D7D7D] rounded-full"></span> Sandee
        </p> */}
        <h3 className=" text-black text-[20px]  text-start line-clamp-1">
          {data?.name}
        </h3>
        {isHTML(data?.description) ? (
          <EditorContent className="text-[#7D7D7D] text-xs font-normal  line-clamp-3  text-start leading-5"
            value={data?.description} />
          // <div className="text-[#7D7D7D] text-xs font-normal  line-clamp-3  text-start leading-5" dangerouslySetInnerHTML={{ __html: data?.description }}></div>
        ) :
          <p className=" text-[#7D7D7D] text-xs font-normal  line-clamp-3  text-start leading-5">
            {data?.description ??
              "boasts over 5,000 kilometers of coastline, offering a vibrant mix of beach experiences. From the legendary party scene of Ibiza to the secluded coves of Menorca"}
          </p>
        }
      </div>
    </Link>
  );
};

export default DetailedCountryCard;
