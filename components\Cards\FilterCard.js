import Image from "next/image";
import Link from "next/link";
import React from "react";
import { BecahIcon } from "../social-icons/icons";
import { altText, blurDataURL } from "@/helper/functions";

const FilterCard = ({ data }) => {
  return (
    <Link
      href={`${data?.link}`}
      className="relative min-h-[160px] sm:min-h-[200px] group rounded-sandee hover:shadow-[0_3px_10px_rgb(0,0,0,0.2)] transition-all duration-500  shadow-[0_3px_10px_rgb(0,0,0,0.2)] md:shadow-md cursor-pointer"
    >
      <div className="absolute left-0 top-0 min-h-[100px] sm:min-h-[130px] w-full  rounded-t-sandee  shadow-[0_3px_10px_rgb(0,0,0,0.2)]">
        <div className="relative  overflow-hidden aspect-video min-h-[100px] w-full h-full  sm:min-h-[130px] rounded-t-sandee">
          <Image
            // priority
            className="transition-transform duration-1000 ease-in-out transform group-hover:scale-110 scale-110 md:scale-100 w-full object-cover object-center"
            src={data?.image}
            alt={altText(data)}
            fill
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            blurDataURL={blurDataURL(300, 200)}
            placeholder="blur"
          />
        </div>
      </div>
      <div className="  bg-white absolute left-0 bottom-0  min-h-[70px] w-full  rounded-b-sandee flex flex-col items-start justify-center text-center px-2 sm:px-4 gap-1 ">
        <h3 className=" text-black text-sandee-base font-medium line-clamp-1">
          {data?.name}
        </h3>
        <p className=" text-[#7D7D7D] text-xs font-normal flex justify-center align-text-bottom items-end text-center">
          {/* <BecahIcon className=" h-4 w-4 fill-[#7D7D7D] mr-1 items-center" /> */}
          {(+data?.count)?.toLocaleString() ?? 0} BEACHES
        </p>
      </div>
    </Link>
  );
};

export default FilterCard;
