// import { blurDataURL } from "@/helper/functions";
// import Image from "next/image";
// import Link from "next/link";
// import React from "react";

// const ListCard = ({ data }) => {
//   return (
//     <Link
//       href={`${data?.link}`}
//       className="relative flex h-[110px] group  rounded-sandee shadow-[0_3px_10px_rgb(0,0,0,0.2)]"
//     >
//       <div className="    h-[110px] w-[110px] sm:w-[130px] rounded-s-sandee">
//         <div className=" h-[110px] w-[110px] sm:w-[130px] relative  overflow-hidden  rounded-s-sandee ">
//           <Image
//             // priority
//             className=" h-[110px] w-[110px] sm:w-[130px] transition-transform duration-1000 ease-in-out object-center transform group-hover:scale-125 object-cover"
//             src={data?.imageSrc}
//             alt={`<PERSON><PERSON> ${data?.name}`}
//             fill
//             sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
//             blurDataURL={blurDataURL(300, 200)}
//             placeholder="blur"
//           />
//         </div>
//       </div>
//       <div className=" px-4 rounded-sandee flex gap-2 justify-center items-start flex-col">
//         <h3 className=" text-sandee-base  line-clamp-2">{data?.name}</h3>
//         {/* <p className=" text-sandee-12 line-clamp-2 text-sandee-grey">
//           {data?.description}
//         </p> */}
//       </div>
//     </Link>
//   );
// };

// export default ListCard;
import { altText, blurDataURL } from "@/helper/functions";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import CustomToolTip from "../Common/CustomToolTip";
import CustomeImage from "../Common/CustomeImage";

const ListCard = ({ data }) => {
  return (
    <Link href={`${data?.link}`} className="">
      <div className="grid grid-flow-col">
        <div className=" h-[110px] w-[110px] sm:w-[155px] relative  overflow-hidden  rounded-sandee col-span-3">
          <CustomeImage
            // priority
            className=" h-[110px] w-[110px] sm:w-[130px] transition-transform duration-1000 ease-in-out object-center transform group-hover:scale-125 object-cover"
            src={data?.imageSrc}
            alt={altText(data)}
            fill
            // sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            blurDataURL={blurDataURL(300, 200)}
            placeholder="blur"
          />
        </div>

        <div className=" px-4 rounded-sandee flex gap-1 justify-center items-start flex-col col-span-9">
          <CustomToolTip title={data?.name}>
            <h3 className=" text-sandee-18  font-bold line-clamp-2">
              {data?.name}
            </h3>
          </CustomToolTip>

          <p className=" text-sm line-clamp-2 text-sandee-grey">
            {data?.description}
          </p>
        </div>
      </div>
    </Link>
  );
};

export default ListCard;
