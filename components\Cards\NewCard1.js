"use client";
// import { blurDataURL, iconComponents } from "@/utils/function";
import moment from "moment";
import Image from "next/image";
import Link from "next/link";
import React, { useState } from "react";
// import { LinkRef } from "@/utils/constant";
// import { getSingleNewsView } from "@/app/action";
// import useApiRequest from "@/hooks/useApiRequest";
import ShareModel from "../Common/shareModal";
import CustomToolTip from "../Common/CustomToolTip";
import defaultImage from "../../public/static/images/header/Countries.avif";
import { NextArrowIcon, ShareIcon, ShareIcon1 } from "../social-icons/icons";
import { altText, blurDataURL } from "@/helper/functions";
import NewsdetailModal from "../NewsPage/newsdetailModal";
import { InfoCircleOutlined } from "@ant-design/icons";
import { Tooltip } from "antd";
import { CustomLink } from "../Common/CopyRight";
import CustomeImage from "../Common/CustomeImage";

const NewsCard1 = ({ data }) => {
  const [showShareIcons, setShowShareIcons] = useState(false);
  const [newDetailOpen, setNewDetailOpen] = useState(false);

  // const api = useApiRequest();
  const icons = [
    {
      name: "Share",
      isFilled: false,
      onClick: (id, slug) => {
        handleShare(id, slug);
      },
    },
  ];
  const handleShare = () => {
    setShowShareIcons((prv) => !prv);
  };

  // const handleViews = (id) => {
  //     api.sendRequest(
  //         getSingleNewsView,
  //         (res) => {
  //             // console.log(res, "News View Success");
  //         },
  //         id
  //     );
  // };

  return (
    <div className="relative">
      <div className="grid md:grid-flow-col grid-flow-row grid-cols-12 gap-5 p-5 rounded-[30px] 320:rounded-[20px] text-inherit bg-white shadow-[5px_5px_24px_0px_rgba(0,0,0,0.10)]">
        <div className="relative 3xl:h-[203px] 2xl:h-[179px] xl:h-[197px] lg:h-[205px] md:h-[193px] 425:h-[228px] 375:h-[195px] h-[177px] 320:h-[158px] 2xl:col-span-4 xl:col-span-5 lg:col-span-6 md:col-span-5 col-span-12">
          <CustomeImage
            src={
              typeof data?.imageSrc === "string" && data?.imageSrc !== "object"
                ? !data.imageSrc.includes("parade.com")
                  ? data.imageSrc
                  : defaultImage
                : defaultImage
            }
            defaultImages={defaultImage}
            fill
            alt={altText(data, `Sandee - News /`)}
            className="rounded-[14px] border-gray-color overflow-hidden object-cover relative"
            title={data?.name ? data?.name : "News"}
            sizes="auto"
            blurDataURL={blurDataURL(400, 250)}
            placeholder="blur"
          />
          {/* <Image
            src={'https://parade.com/.image/t_share/MTk5NTA4MTE2MjY4NjU1NzE5/chen-mizrach-jl6ptwi7h18-unsplash.jpg'}
            // src={profileData?.image}
            alt="User"
            fill
            className="tw-object-cover tw-rounded-full"
          /> */}
          {data?.source !== null && data?.sourceLink !== null &&
            <Tooltip
              title={<>
                <CustomLink
                  href={data?.sourceLink}
                  target="_blank"
                  aria-label="Copyrights"
                >
                  {data?.source}
                </CustomLink>
              </>}
              placement="top"
              className="absolute flex bottom-2 left-2 z-50"
              color="#00aae3"
              overlayInnerStyle={{
                minHeight: 0,
              }}
            >

              <InfoCircleOutlined className="text-[17px] text-white" />
            </Tooltip>}
        </div>
        <div className="2xl:col-span-8 xl:col-span-7 lg:col-span-6 md:col-span-7 col-span-12">
          <div className="flex flex-col 2xl:gap-y-1 xl:gap-y-2 lg:gap-y-2.5 gap-y-2">
            <Link
              href={`/news/${data?.slug}`}
              className="tw-no-underline tw-text-inherit "
              hrefLang="en-us"
              target="_blank"
              rel="nofollow"
              title={data?.title}
              onClick={() => {
                // handleViews(data?.id);
              }}
            >
              <h3 className="3xl:text-2xl xl:text-xl text-lg font-semibold line-clamp-2 hover:underline">
                {data?.title}
              </h3>
            </Link>
            <p className="line-clamp-2 xl:text-base text-sm ">
              {data?.shortDescription}
            </p>
            <div className="flex justify-between items-center ">
              {data?.source !== null && data?.sourceLink !== null ? (
                <Link
                  href={data?.sourceLink}
                  className="hover:text-sandee-blue py-2 px-3 text-gray-800  bg-gray-100 rounded-full font-inter text-sm my-1 no-underline flex gap-x-1 w-fit "
                  hrefLang="en-us"
                  target="_blank"
                  rel="nofollow"
                  title={data?.source}
                  onClick={() => {
                    // handleViews(data?.id);
                  }}
                >
                  {/* <div className="  ">
                                        <span className=" "> */}
                  {data?.source}
                  {/* </span>
                                    </div> */}
                </Link>
              ) : (
                <div className="my-[22px]"></div>
              )}
              <span className="xl:text-base text-sm flex justify-end text-light-gray-900">
                {moment(data?.date?.split("T")?.[0]).format("MMM DD, YYYY") ??
                  "Nov, 09 2023"}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <div className="flex gap-x-2 items-center">
                <button
                  onClick={() => {
                    // window.open(`${data?.link}${LinkRef}`, "_blank");
                    // handleViews(data?.id);
                    //popupp
                    setNewDetailOpen((pr) => !pr);
                  }}
                  className="py-2 px-4 rounded-[28px] bg-sandee-blue border-0 text-sm font-semibold text-white flex gap-x-2 hover:lg:scale-[1.03]"
                >
                  <p>Read More </p>
                  <span className="mt-1">
                    <NextArrowIcon stroke="#FFFFFF" />
                  </span>
                </button>
              </div>
              <div className="flex gap-x-2 items-center">
                {/* // <CustomToolTip title={icon?.name} key={index}> */}
                {icons?.map((icon, index) => (
                  <div
                    key={`news-card-icon${index}`}
                    onClick={() => {
                      icon && icon?.onClick && icon?.onClick(data?.newsId, "");
                    }}
                    className=" cursor-pointer"
                  >
                    <ShareIcon1 />
                  </div>
                ))}
                {/* </CustomToolTip> */}
              </div>
            </div>
          </div>
        </div>
      </div>
      {showShareIcons ? (
        <ShareModel
          modalOpen={showShareIcons}
          setModalOpen={setShowShareIcons}
          link={`/news/${data?.slug}`}
        //   top={"lg:top-[30%] top-[28%]"}
        //   iconHeight={"2xl:py-5 py-2"}
        />
      ) : null}
      {
        <NewsdetailModal
          modalOpen={newDetailOpen}
          setModalOpen={setNewDetailOpen}
          data={data}
        />
      }
    </div>
  );
};

export default NewsCard1;
