import { altText, blurDataURL } from "@/helper/functions";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import moment from "moment";
import CustomToolTip from "../Common/CustomToolTip";
import defaultImage from "../../public/static/images/header/Countries.avif";
import CustomeImage from "../Common/CustomeImage";

const NewsCard = ({ data, copyRightsData }) => {
  return (
    // <Link
    //     href={`${data?.link}`}
    //     className="relative rounded-sandee cursor-pointer"
    // >
    //     {data?.imageSrc ? <div className="relative  overflow-hidden  aspect-video rounded-sandee">
    //         <Image
    //             // priority
    //             // className="transition-transform duration-1000 ease-in-out transform group-hover:scale-125 w-full object-cover object-center"
    //             src={data?.imageSrc ?? defaultImage}
    //             alt={`Sandee - News / ${data?.name}`}
    //             fill
    //             // sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
    //             blurDataURL={blurDataURL(300, 200)}
    //             placeholder="blur"
    //         />
    //     </div> : <></>}
    //     <div className="flex flex-col gap-y-[0.1rem] mt-[0.7rem] ms-[5px]">
    //         <p
    //             className=" truncate text-xs font-medium flex items-center gap-3 uppercase"
    //             suppressHydrationWarning
    //         >
    //             {moment(data?.createdAt?.split("T")?.[0]).format('MMM DD, YYYY') ??
    //                 "Nov, 09 2023"}
    //             <span className="h-1 w-1 bg-[#7D7D7D] rounded-full "></span> SANDEE
    //         </p>
    //         <CustomToolTip title={data?.name}>
    //             <h3 className=" text-black text-[18px]  text-start line-clamp-2 leading-6 font-semibold ">
    //                 {data?.name}
    //             </h3>
    //         </CustomToolTip>
    //         <p className=" text-[#7D7D7D] text-sm font-normal  line-clamp-2 text-start ">
    //             {/* <BecahIcon className=" h-5 w-5 fill-[#7D7D7D] mr-1 items-center" /> */}
    //             {data?.shortDescription}
    //         </p>
    //     </div>
    // </Link>
    <div className="relative">
      <div className="flex flex-col sm:flex-row gap-x-5 lg:gap-y-3 gap-y-1 bg-white p-5 rounded-[20px] shadow-[4px_4px_14px_0px_rgba(0,0,0,0.05)] relative">
        <div className="w-full sm:w-auto flex items-center justify-center sm:justify-start">
          <div className="relative aspect-auto xl:grid-cols-4 w-64 md:w-56 lg:w-64 lg:h-[168px] xl:w-[310px] xl:h-[188px]  md:h-[148px] h-[168px]  hover:lg:scale-[1.03]">
            <CustomeImage
              src={data?.imageSrc ?? defaultImage}
              defaultImages={defaultImage}
              fill
              alt={altText(data, `Sandee - News /`)}
              className="rounded-[14px] border-gray-color overflow-hidden object-cover relative"
              title={`Sandee - News / ${data?.name}`}
              sizes="auto"
              blurDataURL={blurDataURL(400, 250)}
              placeholder="blur"
            />
          </div>
        </div>

        {/* <Link
                    hrefLang="en-us"
                    href={`/blog/${data?.slug}`}
                    title={data?.title}
                    target="_blank"
                    rel="noopener noreferrer"
                > */}
        {/* <h3 className="text-primary-p-blue-500 text-lg line-clamp-2 font-inter font-semibold min-h-[55px] hover:underline">
              {realData?.title}
            </h3> */}
        {/* </Link> */}
        <div className="xl:grid-cols-8 flex items-start justify-start flex-col">
          <CustomToolTip title={data?.name}>
            <Link
              className="cursor-pointer "
              hrefLang="en-us"
              href={`/news/${data?.slug}`}
              title={data?.title}
              // target="_blank"
              rel="noopener noreferrer"
            > <h2 className=" text-black text-2xl hover:underline text-start line-clamp-3 leading-8 font-semibold ">
                {data?.name}
              </h2>
            </Link>
          </CustomToolTip>

          {/* <div className="h-[54px] overflow-hidden">
            <div className="flex gap-x-2 flex-wrap">
              {realData?.blogCategories?.length > 0 &&
                realData?.blogCategories?.map((i, idx) => {
                  return (
                    <Link
                      hrefLang="en-us"
                      href={`/tools/${i?.categoryOfBlog?.slug}`}
                      className="text-primary-p-blue-500 py-0.5 px-2  bg-light-gray-100 rounded-full font-inter text-12px my-0.5 no-underline"
                      key={idx}
                      title={i?.categoryOfBlog?.name}
                    >
                      {i?.categoryOfBlog?.name}
                    </Link>
                  );
                })}
            </div>
          </div> */}
          <div className="grid grid-flow-col gap-x-5 items-center ">
            <div className="flex gap-2 text-light-gray-900 xl:text-base lg:text-sm text-12px">
              {data?.readTime ? <><span className="overflow-hidden whitespace-nowrap text-ellipsis">

                {data?.readTime} min Read
              </span>
                <span className="">|</span></> : <></>}
              <span className="overflow-hidden whitespace-nowrap text-ellipsis truncate text-xs font-medium flex items-center gap-3 uppercase"> {moment(data?.createdAt?.split("T")?.[0]).format('MMM DD, YYYY') ??
                "Nov, 09 2023"}</span>
              {/* <span className="h-1 text-center w-1 bg-[#7D7D7D] rounded-full flex ite"></span> SANDEE */}
              {/* <span className="overflow-hidden whitespace-nowrap text-ellipsis">
                                {realData?.views} 
                                Read
                            </span> */}
            </div>

            {/* <CustomToolTip title={"Updated"}>
                            <div className="xl:text-base lg:text-sm text-12px flex justify-end text-light-gray-900 cursor-default items-center">
                                <span className="-mt-1 me-1">
                                    <CalendarIcon />
                                </span>
                                {moment(data?.createdAt?.split("T")?.[0]).format('MMM DD, YYYY') ??
                                    "Nov, 09 2023"}
                            </div>
                        </CustomToolTip> */}
          </div>
          <p className="line-clamp-3 font-inter min-h-[60px]  text-[#7D7D7D] mt-1">
            {data?.shortDescription}
          </p>
          <div className="flex justify-between">
            {/* <div className="flex gap-x-2 items-center hover:lg:scale-[1.03] cursor-pointer">
              <Link
                hrefLang="en-us"
                href={`/blog/${realData?.slug}`}
                title={realData?.title}
                target="_blank"
                rel="noopener noreferrer"
              >
                <p className="text-primary-p-blue-500 font-semibold text-sm">
                  Continue Reading
                </p>
              </Link>
              <span className="mt-1">
                <NextArrowIcon />
              </span>
            </div> */}
            {/* <div className="flex gap-x-2 cursor-pointer">
              {icons?.map((icon, index) => (
                <div
                  key={index}
                  onClick={() => {
                    icon &&
                      icon?.onClick &&
                      icon?.onClick(realData?.id, realData?.slug);
                  }}
                >
                  {icon.isFilled
                    ? iconComponents[icon.name].filled
                    : iconComponents[icon.name].outlined}
                </div>
              ))}
            </div> */}
          </div>
        </div>
      </div>
      {/* {showShareIcons ? (
          <ShareCard
            showShareIcons={showShareIcons}
            setShowShareIcons={setShowShareIcons}
            url={`/blog/${realData?.slug}`}
            top={"lg:top-[33%] top-[28%]"}
            iconHeight={"2xl:py-5 py-2"}
          />
        ) : null} */}
    </div>
  );
};

export default NewsCard;
