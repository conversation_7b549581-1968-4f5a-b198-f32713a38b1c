import { altText, blurDataURL } from "@/helper/functions";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import moment from "moment";
import CustomToolTip from "../Common/CustomToolTip";
import defaultImage from "../../public/static/images/header/Countries.avif";
import defaultImageLogo from "../../public/static/images/Sandee_Logo.png";
import NewsDesc from "../NewsPage/newsDesc";
import NewsDesNewDesign from "../NewsPage/newsDesNewDesign";

const NewsNewCard = ({ data, copyRightsData }) => {
    return (
        <div className="relative">
            <div className=" flex flex-col gap-x-5 lg:gap-y-3 gap-y-2 bg-white  pt-5 pb-5 rounded-[2px] border-[1px] border-gray-color hover:shadow-[rgba(0,0,0,0.16)_0px_1px_4px] relative transition-shadow duration-300 ease-in-out">
                <div className=" px-6 w-full sm:w-auto gap-8 flex items-start justify-start">
                    <div className="relative aspect-auto w-14 h-14 hover:lg:scale-[1.03]">
                        <Image
                            src={defaultImageLogo}
                            // src={data?.imageSrc ?? defaultImage}
                            fill
                            alt={altText(data, `Sandee - News /`)}
                            className="rounded-full border-gray-color overflow-hidden object-cover relative"
                            title={`Sandee - News / ${data?.name}`}
                            sizes="auto"
                            blurDataURL={blurDataURL(400, 250)}
                            placeholder="blur"
                        />
                    </div>
                    <div className="">
                        <p className=" text-base font-medium flex items-center gap-3 mb-1 uppercase">
                            Sandee
                        </p>
                        <p className="text-sm text-gray-400 font-semibold text-end">{moment(data?.createdAt?.split("T")?.[0]).format('MMM DD, YYYY') ?? "Nov, 09 2023"}</p>
                    </div>
                </div>
                <div className="border-b-[2px] w-full mt-4 mb-4"></div>
                <NewsDesNewDesign data={data} />
                {/* <NewsDesc
                    data={data}
                /> */}

            </div>

        </div>
    );
};

export default NewsNewCard;
