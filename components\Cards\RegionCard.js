import { altText, blurDataURL } from "@/helper/functions";
import Image from "next/image";
import Link from "next/link";
import React from "react";

const RegionCard = ({ data }) => {
  return (
    <Link
      href={`${data?.link}`}
      className="relative group flex h-[180px]  rounded-sandee shadow-[0_3px_10px_rgb(0,0,0,0.2)]"
    >
      <div className="    h-[180px] w-[140px] group-hover:w-[145px] ease-in duration-100 rounded-s-sandee">
        <div className=" h-[180px] w-[140px]  group-hover:w-[145px] ease-in duration-100 relative  overflow-hidden  rounded-s-sandee ">
          <Image
            // priority
            className=" h-[180px] w-[140px] group-hover:w-[145px] ease-in duration-1000 transition-transform object-cover object-center transform group-hover:scale-125"
            src={data?.imageSrc}
            alt={altText(data)}
            fill
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            blurDataURL={blurDataURL(300, 200)}
            placeholder="blur"
          />
        </div>
      </div>
      <div className=" px-5 rounded-sandee flex justify-start items-center">
        <p className=" text-sandee-sm font-normal line-clamp-6 text-start">
          <span className=" font-bold">{data?.name}</span>
          {` boasts over 5,000 kilometers of coastline, offering a vibrant mix of beach experiences. From the legendary party scene of Ibiza to`}
        </p>
      </div>
    </Link>
  );
};

export default RegionCard;
