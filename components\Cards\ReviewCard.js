import React from "react";
import { Star } from "../BeachPage/BeachReviewBottomDisplay";
import moment from "moment";

const ReviewCard = ({ data }) => {
  return (
    <div className="flex flex-col md:flex-row gap-5">
      <div className="flex flex-col gap-3 p-3">
        <p className=" flex gap-1  items-center justify-start">
          {Array(5)
            .fill(1)
            ?.map((rate, rateIndex) => {
              return (
                <Star
                  key={rateIndex}
                  className={`${
                    data?.rating > rateIndex
                      ? "fill-[#E49D3E]"
                      : //   ? "fill-[#ffa723]"
                        "fill-[#E8E8E8]"
                  }`}
                />
              );
            })}
        </p>
        <p className="  text-sandee-14 font-bold">{data?.name}</p>
        {/* <p>{moment(data?.date)?.format("MMMM Do, YYYY")}</p> */}
        {/* <p>{new Date(data?.date)?.toDateString()}</p> */}
        <p className=" text-sandee-grey text-sandee-14 font-bold">
          {moment(data?.date, "D-M-YYYY")?.format("MMM D, YYYY")}
        </p>
        {/* <p className=" text-sandee-grey text-sandee-14 font-bold">
          {data?.date}
        </p> */}
      </div>
      <div className=" flex-1 p-3">
        <div className=" bg-[#FBFBFB] w-full p-3">
          <p className=" text-sandee-grey text-sandee-18 font-medium">
            {" "}
            {data?.review} {data?.review}
            {data?.review}
            {data?.review}
            {data?.review}
          </p>
        </div>
      </div>
    </div>
  );
};

export default ReviewCard;

export const ReviewCardSkeleton = () => {
  return <div className=" bg-slate-200 h-32 animate-pulse"></div>;
};
