"use client";
import { scrollToTop } from "@/helper/functions";
import Link from "next/link";
import React from "react";
import CustomButton from "../Custom-Button";
import { CustomButtonGrid } from "../Custom-Display";

const City_Bottom_Button = ({ cityHeaderImageResponse }) => {
  return (
    <CustomButtonGrid>
      <Link href={`/countries`}>
        <CustomButton type={4}>All Countries</CustomButton>
      </Link>
      <Link href={`/${cityHeaderImageResponse?.country?.slug}`}>
        <CustomButton type={4}>
          {cityHeaderImageResponse?.country?.name}
        </CustomButton>
      </Link>
      <Link
        href={`/${cityHeaderImageResponse?.country?.slug}/${cityHeaderImageResponse?.state?.slug}`}
      >
        <CustomButton type={4}>
          {cityHeaderImageResponse?.state?.name}
        </CustomButton>
      </Link>
      <CustomButton type={4} onClick={scrollToTop}>
        Back to Top
      </CustomButton>
    </CustomButtonGrid>
  );
};

export default City_Bottom_Button;
