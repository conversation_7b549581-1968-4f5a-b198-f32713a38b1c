"use client";
import React, { useEffect, useState } from "react";
import HeroSectionCSC from "../Common/HeroSectionCSC";
import { CustomContainer } from "../Custom-Display";
import LocationMap from "../Common/LocationMap";
import { getCoordinatesFromNominatim } from "@/helper/functions";
import NameTitle from "../Common/NameTitle";

const City_Hero_Section = ({ data, breadCumberData, CityAnalyticsData }) => {
  // const { counts, beaches } = CityAnalyticsData;

  data.lat = data?.GeoLoc?.coordinates?.length
    ? data?.GeoLoc?.coordinates?.[1]
    : 0;
  data.lon = data?.GeoLoc?.coordinates?.length
    ? data?.GeoLoc?.coordinates?.[0]
    : 0;

  // const [coordinates,setCoordinates]=useState()
  // useEffect (()=>{
  //   getCoordinatesFromNominatim(data?.country?.name,data?.state?.name,data?.name)
  //   .then(result => {
  //     setCoordinates(result);
  //     return result
  //   })
  //   .catch(error => {
  //     console.error('Error fetching coordinates:', error);
  //   });

  // },[data?.name])
  // const CityDetails = [
  //   {
  //     title: `Beaches in ${data?.name}`,
  //     icon: <BecahIcon className=" fill-sandee-blue  h-6 w-6" />,
  //     details: [
  //       {
  //         title: `${+(counts?.beaches ?? 0)?.toLocaleString()}+ Beaches`,
  //       },
  //       // {
  //       //   title: `3700Km Coastline`,
  //       // },
  //       {
  //         title: `${(+(
  //           counts?.dogBeaches ?? 0
  //         ))?.toLocaleString()} Dog Beaches`,
  //       },
  //       {
  //         title: `${+(
  //           counts?.nudeBeaches ?? 0
  //         )?.toLocaleString()} Nude Beaches`,
  //       },
  //     ],
  //   },
  //   {
  //     title: `Best Beaches in ${data?.name}`,
  //     icon: <PinLocation className=" fill-sandee-blue  h-6 w-6" />,
  //     details: beaches?.map((beach) => ({
  //       title: beach?.name,
  //       link: `/${beach?.country?.slug}/${beach?.state?.slug}/${beach?.city?.slug}/${beach?.nameSlug}`,
  //     })),
  //   },
  // ];
  return (
    <>
      <HeroSectionCSC data={data} breadCumberData={breadCumberData} />
      <CustomContainer>
        <NameTitle
          name={`Map of Beaches in ${data?.name ?? "city"}`}
          type={4}
          className="mt-4"
        />
        <LocationMap latStr={data?.lat} longStr={data?.lon} zoom={11} />
      </CustomContainer>
      {/* <div className="w-full py-4 flex items-center">
        <CustomContainer>
          <BreadCumber
            data={[
              {
                title: "All Countries",
                to: `/countries`,
              },
              {
                title: `${data?.country?.name}`,
                to: `/${data?.country?.slug}`,
              },
              {
                title: `${data?.state?.name}`,
                to: `/${data?.country?.slug}/${data?.state?.slug}`,
              },
              {
                title: `${data?.name ?? "City"}`,
              },
            ]}
          />
        </CustomContainer>
      </div> */}
    </>
  );
};

export default City_Hero_Section;
