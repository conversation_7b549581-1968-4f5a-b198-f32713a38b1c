import { siteMetadata } from "@/data/siteMetadata";
import { slugConverter } from "@/helper/functions";
import React from "react";

const CityPageJSONLD = ({ cityData, params }) => {
  console.log(cityData)
  const CityBreadCumber = {
    "@context": "https://schema.org/",
    "@type": "BreadcrumbList",
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        name: "Home",
        item: `https://sandee.com/`,
      },
      {
        "@type": "ListItem",
        position: 2,
        name: cityData?.country?.name,
        item: `https://sandee.com/${cityData?.country?.slug}`,
      },
      {
        "@type": "ListItem",
        position: 3,
        name: cityData?.state?.name,
        item: `https://sandee.com/${cityData?.country?.slug}/${cityData?.state?.slug}`,
      },
      {
        "@type": "ListItem",
        position: 4,
        name: cityData?.name,
        item: `https://sandee.com/${cityData?.country?.slug}/${cityData?.state?.slug}/${cityData?.slug}`,
      },
    ],
  };

  const CityPageSchema = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `https://sandee.com/${cityData?.country?.slug}/${cityData?.state?.slug}/${cityData?.slug}`
    },
    "headline": !!cityData?.title ? cityData?.title : `Best Beaches in  ${slugConverter(
      params?.citySlug,
      true
    )}, ${slugConverter(params?.stateSlug, true)}, ${slugConverter(
      params?.countrySlug,
      true
    )} `,
    "description": !!cityData?.metaDescription ? cityData?.metaDescription : `Discover the complete list of beaches in the ${slugConverter(
      params?.citySlug,
      true
    )}, ${slugConverter(params?.stateSlug, true)}, ${slugConverter(
      params?.countrySlug,
      true
    )}. Plan your ${slugConverter(
      params?.citySlug,
      true
    )} beach vacation with 100+ activities, photos, attractions, restaurants, and hotels.`,
    "author": {
      "@type": "Person",
      "name": "Mr. Beach",
      "url": `https://sandee.com/mr-beach`
    },
    "publisher": {
      "@type": "Organization",
      "name": siteMetadata?.title || "Sandee",
      "logo": {
        "@type": "ImageObject",
        "url": `https://sandee.com/_next/image?url=%2Fstatic%2Fimages%2FSandee-Blue.webp&w=1920&q=75`
      }
    },
    "datePublished": cityData?.createdAt,
    "dateModified": cityData?.updatedAt
  };

  return (
    <>
      <script
        type="application/ld+json"
        id="application/ld+jsonBeachBreadCumber"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(CityBreadCumber) }}
      ></script>
      <script
        type="application/ld+json"
        id="application/ld+jsonCountryPage"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(CityPageSchema) }}
      ></script>
    </>
  );
};

export default CityPageJSONLD;
