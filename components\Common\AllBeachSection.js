"use client";
import React, { useEffect, useState } from "react";
import { CustomGrid } from "../Custom-Display";
import BeachCard, { BeachCardSkeleton } from "../Cards/BeachCard";
import { FinalImageGenerator } from "@/helper/functions";
import Pagination from "../Common/Pagination";
import { getAllBeachesForUserViaQuery } from "@/app/(HeaderSlim)/(Single Country Page)/[countrySlug]/action";
import SelectBox from "./SelectBox";
import NameTitle from "./NameTitle";

const AllBeachSectionGlobalComponent = ({
  data,
  AllBeachesInitial,
  FullCount,
}) => {
  const [loading, setLoading] = useState(true);
  const [query, setQuery] = useState("");

  const [total, setTotal] = useState(FullCount?.count);

  const [AllBeachPage, setAllBeachPage] = useState([AllBeachesInitial]);
  const [CurrentBeach, setCurrentBeach] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [refresh, setRefresh] = useState(true);
  const [sortOrder, setsortOrder] = useState("ASC");

  const FetchOrSetBeach = async () => {
    setLoading(true);

    if (!!query) {
      const AllBeachesFilterResponse = await getAllBeachesForUserViaQuery({
        ...FullCount?.query,
        page: currentPage,
        searchQuery: query,
      });
      setTotal(AllBeachesFilterResponse?.data?.count);
      setCurrentBeach(AllBeachesFilterResponse?.data?.rows);
      return setLoading(false);
    }
    setTotal(FullCount?.count);
    if (currentPage == 1 && sortOrder === "ASC") {
      setCurrentBeach(AllBeachesInitial);
      return setLoading(false);
    }

    if (AllBeachPage?.[currentPage - 1]?.length && sortOrder === "ASC") {
      setCurrentBeach(AllBeachPage?.[currentPage - 1]);
      return setLoading(false);
    }
    const AllBeachesResponse = await getAllBeachesForUserViaQuery({
      ...FullCount?.query,
      page: currentPage,
      sortBy: "name",
      sortOrder,
    });
    if (sortOrder === "ASC") {
      setAllBeachPage((prev) => {
        prev[currentPage - 1] = AllBeachesResponse?.data?.rows;
        return prev;
      });
    }
    setCurrentBeach(AllBeachesResponse?.data?.rows);
    return setLoading(false);
  };

  useEffect(() => {
    FetchOrSetBeach();
  }, [refresh]);
  useEffect(() => {
    const getData = setTimeout(() => {
      // if (!!!query) {
      //   return;
      // }
      setRefresh((prev) => !prev);
    }, 400);

    return () => clearTimeout(getData);
  }, [query]);

  return (
    <section className=" my-5">
      <div className="flex justify-between flex-col md:flex-row mt-5 gap-5">
        <div className="flex flex-col gap-2 w-full md:w-6/12">
          <NameTitle
            type={2}
            name={`All Beaches in ${data?.name}`}
            description="EXPLORE BEACH DESTINATIONS"
          />
        </div>
        <div className=" relative  flex justify-start md:justify-end items-start gap-4">
          <div className="relative flex justify-center items-center max-w-[450px] w-full sm:w-auto md:min-w-[300px]">
            <div className="absolute inset-y-0 start-0 flex items-center ps-2 pe-2 ms-2 pointer-events-none rounded-full">
              <svg
                className="w-4 h-4 text-[#7D7D7D]  "
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 20 20"
              >
                <path
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"
                />
              </svg>
            </div>
            <input
              type="text"
              id="simple-search"
              className={`transition-all duration-500  focus:outline-none outline-none  focus:ring-0 ring-0 focus:border-sandee-blue  bg-white border-2 border-[#DEDEDE] block w-full ps-10 sm:text-[20px]  rounded-xl text-gray-600 !pl-[50px] lg:text-sandee-sm  !text-sandee-sm p-2 `}
              placeholder="Search"
              value={query}
              onChange={(e) => {
                setQuery(e.target.value);
                setCurrentPage(1);
              }}
            />
          </div>
          <div className=" ">
            <SelectBox
              options={[
                {
                  value: "ASC",
                  label: "A to Z",
                },
                {
                  value: "DESC",
                  label: "Z to A",
                },
              ]}
              deafultSelectSort={{
                value: "ASC",
                label: "A to Z",
              }}
              getSortingVal={(v) => {
                setsortOrder(v);
                setRefresh((prev) => !prev);
              }}
              menuClassName="w-full"
            />
          </div>
        </div>
      </div>
      {loading ? (
        <CustomGrid
          className="!mb-5 mt-[13px] gap-4 sm:gap-8"
          data={Array(FullCount?.limit).fill(1)}
          Component={BeachCardSkeleton}
          xs={2}
          sm={2}
          md={3}
          lg={4}
          xl={5}
          xxl={5}
          xxxl={5}
        />
      ) : total ? (
        <>
          <CustomGrid
            data={CurrentBeach}
            className="!mb-5 mt-[13px] gap-4 sm:gap-8 lg:gap-4"
            Component={({ data: dataProps }) => {

              if (dataProps?.city?.state?.country?.slug) {
                dataProps.link = `/${dataProps?.city?.state?.country?.slug}/${dataProps?.city?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.nameSlug}`; //
                dataProps.location = `${dataProps?.city?.name}, ${dataProps?.city?.state?.country?.code}`; //
              } else {
                dataProps.link = `/${dataProps?.country?.slug}/${dataProps?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.nameSlug}`; //
                dataProps.location = `${dataProps?.city?.name}, ${dataProps?.country?.code}`; //
              }

              dataProps.imageSrc = FinalImageGenerator(dataProps.images[0]);
              dataProps.alterText = dataProps?.images?.[0]?.alterText;
              return (
                <BeachCard
                  data={{ ...dataProps }}
                  copyRightsData={dataProps?.images}
                // className="h-[180px] xl:h-[190px]"
                />
              );
            }}
            xs={2}
            sm={2}
            md={3}
            lg={4}
            xl={5}
            xxl={5}
            xxxl={5}
          />
        </>
      ) : (
        <p className=" px-2 py-5 text-error-red-600  border-error-red-400 bg-error-red-50 border-2 rounded-sandee text-center mt-6 mb-10">
          No Data Found for your applied filters
        </p>
      )}
      <Pagination
        className="pagination-bar"
        currentPage={currentPage}
        totalCount={total}
        pageSize={FullCount?.limit}
        onPageChange={(page) => {
          setCurrentPage(page);
          setRefresh((prev) => !prev);
        }}
      />
    </section>
  );
};

export default AllBeachSectionGlobalComponent;
