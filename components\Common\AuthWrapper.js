"use client";
import React, { useEffect, useState } from "react";
import LogInForm from "./LogInForm";
import useLoggedIn from "@/helper/hook/useLoggedIn";
import SignUpForm from "./SignUpForm";
import { Modal } from "antd";

const AuthWrapper = ({ WithoutLogIn, WithLogIn, popupKey = null }) => {
  const [open, setOpen] = useState(false);
  const [login, setLogIn] = useState(true);
  const [AuthDisplay, SetAuthDisplay] = useState(<></>);
  const data = useLoggedIn();
  useEffect(() => {
    SetAuthDisplay(
      data?.isLoggedIn ? (
        WithLogIn
      ) : (
        <div>
          <div
            className="btn btn-success"
            onClick={() => {
              setOpen(true);
              localStorage?.setItem("popupKey", popupKey);
            }}
          >
            {WithoutLogIn}
          </div>
        </div>
      )
    );
  }, [data?.isLoggedIn, WithoutLogIn, WithLogIn]);
  return (
    <>
      {AuthDisplay}
      <Modal
        role="dialog"
        aria-modal="true"
        open={open}
        onCancel={() => setOpen(false)}
        footer={null}
        closable={false}
        okButtonProps={{
          style: {
            display: "none",
          },
        }}
        style={{
          padding: 0,
        }}
        width={350}
        centered
        wrapClassName=" !p-0"
        styles={{
          body: {
            padding: "0px !important",
          },
        }}
        cancelButtonProps={{
          style: {
            display: "none",
          },
        }}
      >
        <div className={`custom-auth ${login ? " " : "hidden"}`}>
          <LogInForm setLogIn={setLogIn} />
        </div>
        <div className={`custom-auth ${login ? "hidden" : ""}`}>
          <SignUpForm setLogIn={setLogIn} />
        </div>
      </Modal>
    </>
  );
};

export default AuthWrapper;
