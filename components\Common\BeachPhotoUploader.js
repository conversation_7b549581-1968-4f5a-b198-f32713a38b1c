import { useState } from "react";
import { Divider } from "../BeachPage/BeachReviewModal";
import Previews from "./Previews";
import CustomButton from "../Custom-Button";
import useLoggedIn from "@/helper/hook/useLoggedIn";
import { postImages } from "@/app/(HomeHeader)/action";
import { Spin, notification } from "antd";

const BeachPhotoUploader = ({ beachData = {}, onSubmit = () => {} }) => {
  const [files, setFiles] = useState([]);
  const [formData, setFormData] = useState({
    description: "",
    check: false,
  });
  const [loading, setLoading] = useState(false);
  const [countWords, setCountWords] = useState(0);
  const { token } = useLoggedIn();
  return (
    <div className="form ">
      <p className=" text-center md:text-[28px] text-2xl font-bold mb-3">
        Share Your Best Moments!
      </p>
      <p className=" text-center md:text-lg text-[#A3A3A3] font-normal">
        Capture the essence of your beach day at {beachData?.name} by sharing
        your favorite photos!
      </p>
      {/* <Divider>
        <p className="m-0 px-4  text-black text-sandee-12 font-bold">
          Upload Photos
        </p>
      </Divider> */}
      <Previews
        // onChange={(val) => {
        //   //   setFormData((prev) => ({ ...prev, files: [...val] }));
        // }}
        setFiles={setFiles}
        files={files}
      />
      {/* <Divider>
        <p className="m-0 px-4  text-black text-sandee-12 font-bold">
          Add Captions
        </p>
      </Divider> */}
      <p className="text-center font-bold text-lg my-3"> Add Captions</p>
      <div className="relative">
        <textarea
          className="relative input !w-full !h-full min-h-[127px] align-text-top text-wrap text-start border-2 rounded-md shadow-[0px_33px_33px_0px_#00000017] p-3 md:text-lg placeholder:text-[#959595] placeholder:font-semibold"
          name="review"
          type="textarea"
          placeholder="Optional but Encouraged! Give Some Content To your Photos."
          required
          onChange={(v) => {
            setCountWords(TotalWords(v?.target?.value));
            setFormData((prev) => ({
              ...prev,
              description: v?.target?.value,
            }));
          }}
        />
        <span className="absolute bottom-1 right-2 text-black/60 font-medium">
          {`${countWords}/1000`}
        </span>
      </div>

      {/* <Input
        name="description"
        type="textarea"
        placeholder="Optional, but encouraged! Give some context to your photos."
        style={{
          height: "120px",
          margin: "15px 0px",
          borderRadius: "12pt",
          fontSize: "12px",
          color: `${
            formData?.description?.trim()?.length
              ? "black"
              : "rgba(0, 0, 0, 0.4)"
          }`,
        }}
        required
        onChange={(v) => {
          setFormData((prev) => ({
            ...prev,
            description: v?.target?.value,
          }));
        }}
      /> */}
      <div className="flex text-start items-start mt-7 gap-2">
        <input
          type="checkbox"
          className="!m-0"
          value={formData?.check}
          onChange={() => {
            setFormData((prev) => ({
              ...prev,
              check: !prev?.check,
            }));
          }}
        />
        <p className="font-semibold text-[#5E5E5E] -mt-1">
         {`I certify that, I am the copyright holder of the uploaded photo(S). or have obtained permission from the copyright holder to share these images publicly. I grant Sandee the right to use my photo(s) for promotional or other purposes, as outlined in the term of services.`}       </p>
      </div>
      <div className="flex justify-center items-center mt-5">
        <CustomButton
          className=" w-3/4 justify-center items-center  py-2 px-8 flex relative   overflow-hidden font-medium  text-white border border-sandee-blue bg-sandee-blue active:text-white text-sandee-sm h-auto rounded-full group"
          //   className="font-normal text-base text-[#00AAE3]  w-36 h-10 border-2 border-[#00AAE3] rounded-full transition-colors duration-300 load-more-button hover:bg-[#00AAE3] hover:text-white hover:border-blue-500"
          //   style={{
          //     overflow: "hidden",
          //     textOverflow: "ellipsis",
          //     whiteSpace: "nowrap",
          //     width: "190px",
          //   }}
          onClick={async () => {
            // if (files.length < 1) {
            //   return toast.error("Please select Photo to add");
            // }
            // if (!formData?.check) {
            //   return toast.error("Please agree to our term and condition");
            // }
            if (files?.length && formData?.check && beachData?.id) {
              setLoading(true);

              const formDataPhoto = new FormData();
              formData.description &&
                formDataPhoto.append("description", formData.description);
              beachData?.id &&
                formDataPhoto.append("AllBeachId", beachData?.id);
              files.forEach((file) => {
                formDataPhoto.append("imageUrl", file);
              });

              const response = await postImages(formDataPhoto, token);
              if (response?.data && response?.status === "success") {
                // logController("Review Added Successfully");
                notification.success({
                  message: "Photos Added Successfully",
                  duration: 3,
                });
                onSubmit();
                window.location.reload();
                // toggle();
              } else {
                notification.error({
                  message: "You are not Authorized",
                  duration: 3,
                });
                // logController("You are not Authorized");
              }
              //   if (response) {
              //     toast.success("Photo(s) Added Successfully");
              //   } else {
              //     toast.error("You are not Authorized", {});
              //   }
              setLoading(false);
              //   toggle();
            } else {
              // logController(formData, "FORMDATAT INNNER", files);
            }
          }}
          disabled={loading}
        >
          {/* {loading && <Spinner size="sm" className="mx-4" />} */}
          {loading && <Spin size="large" className=" mx-2 " />}
          {loading ? "Uploading" : "Upload"}
        </CustomButton>
      </div>
    </div>
  );
};

export default BeachPhotoUploader;
export const TotalWords = (str) => {
  // Trim the string to remove leading/trailing spaces
  // Split by spaces, then filter out any empty strings
  const words = str?.trim().split(/\s+/);
  
  // Return the length of the filtered array (number of words)
  return words?.length;
};