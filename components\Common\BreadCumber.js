import Link from "next/link";
import React from "react";

const BreadCumber = ({ data, dataTestid = "breadcrumb" }) => {
  return (
    <div data-testid={dataTestid} className="flex flex-wrap items-center ">
      <Link
        href={`/`}
        hrefLang="en-us"
        key={`Home`}
        className=" text-black text-sandee-base hover:underline"
      >
        Home
      </Link>
      <span className=" text-black text-sandee-base ">
        &nbsp;&#62;&nbsp;&nbsp;
      </span>
      {data?.map((item, i) => {
        if (item?.to && item?.title) {
          return (
            <span
              key={`breadcrumbLink${i}`}
              className="flex justify-center items-center"
            >
              <Link
                className=" decoration-0 text-[#000] text-sandee-base  hover:underline "
                hrefLang="en-us"
                data-testid={item?.dataTestid || `breadcrumbLink-${i}`}
                href={item?.to}
              >
                {item?.title}
              </Link>
              &nbsp; {data?.length - 1 !== i && " > "}&nbsp;
            </span>
          );
        } else if (item?.title) {
          return (
            <p
              key={`breadcrumbLink${i}`}
              data-testid={item?.dataTestid || `breadcrumbLink-${i}`}
              className=" decoration-0 text-[#00AAE3BF] text-sandee-base  m-0 font-bold "
            >
              {item?.title}
            </p>
          );
        } else {
          return null;
        }
      })}
    </div>
  );
};

export default BreadCumber;
