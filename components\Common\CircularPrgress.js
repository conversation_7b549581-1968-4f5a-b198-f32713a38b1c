"use client";
import React, { useState, useEffect } from "react";

const CircularScrollProgressBar = () => {
  const [offset, setOffset] = useState(0);
  const [show, setShow] = useState(false);
  useEffect(() => {
    const handleScroll = () => {
      const scrollableHeight =
        document.documentElement.scrollHeight - window.innerHeight;
      const scrolled = window.scrollY;
      const percentage = (scrolled / scrollableHeight) * 100;
      setOffset(percentage);
      if (window.scrollY > 50) setShow(true);
      else setShow(false);
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <div className="fixed lg:bottom-0 bottom-11 right-0 z-[1000]">
      {show ? <Pie percentage={offset} /> : ""}
    </div>
  );
};

export default CircularScrollProgressBar;

export const cleanPercentage = (percentage) => {
  const tooLow = !Number.isFinite(+percentage) || percentage < 0;
  const tooHigh = percentage > 100;
  return tooLow ? 0 : tooHigh ? 100 : +percentage;
};

export const Circle = ({ colour, pct, r = 15, strokeWidth = 5 }) => {
  const circ = 2 * Math.PI * r;
  const strokePct = ((100 - pct) * circ) / 100;
  return (
    <circle
      r={r}
      //   cx={`${r * 2}`}
      //   cy={`${r * 2}`}
      cx={"50%"}
      cy={"50%"}
      fill="transparent"
      stroke={strokePct !== circ ? colour : "white"} // remove colour as 0% sets full circumference
      strokeWidth={strokeWidth}
      strokeDasharray={circ}
      strokeDashoffset={pct ? strokePct : 0}
      strokeLinecap="round"
    ></circle>
  );
};

export const Text = ({ percentage }) => {
  return (
    <text
      x="50%"
      y="50%"
      dominantBaseline="central"
      textAnchor="middle"
      fontSize={"0.5em"}
    >
      {percentage.toFixed(0)}%
    </text>
  );
};

export const Pie = ({ percentage = 0, colour = "#FF6B00" }) => {
  const pct = cleanPercentage(percentage);
  const r = 20;
  return (
    <svg
      width={r * 4}
      height={r * 4}
      onClick={() => {
        window.scrollTo({
          top: 0,
          behavior: "smooth",
        });
      }}
      className=" flex items-center justify-center cursor-pointer rounded-full "
    >
      <g transform={`rotate(-90 ${r * 2} ${r * 2})`}>
        <Circle colour="white" r={Math.round(r / 2)} strokeWidth={r} />
        <Circle colour="white" r={r} />
        <Circle colour={colour} r={r} pct={pct} />
        {/* <Circle colour="white" r={13} strokeWidth={1} /> */}
      </g>
      <Text percentage={percentage} />
    </svg>
  );
};
