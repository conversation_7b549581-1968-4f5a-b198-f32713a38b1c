// "use client";
// import React, { useEffect, useRef, useState } from "react";
// import { InfoIcon } from "../social-icons/icons";
// const CustomLink = ({ href, children, ...props }) => {
//   const handleClick = () => {
//     // window.location.href = href; // You can also use React Router for client-side navigation.
//     if (href && href !== "https://sandee.com") {
//       window.open(href, "_blank");
//     }
//   };

//   return (
//     <div className="custom-link " onClick={handleClick} {...props}>
//       {children}
//     </div>
//   );
// };
// const CopyRight = ({
//   copyRightsData,
//   background = true,
//   styleExtra,
//   copyRightExtraStyle = {},
// }) => {
//   const [isShow, setIsShow] = useState(false);
//   // const tooltipRef = useRef(null);
//   const isCopyRightVisible = copyRightsData
//     ? copyRightsData?.length != 0
//     : false;
//   let license = null;
//   let BeatchImage = null;

//   const copyrightTextStyle = {
//     fontSize: background ? "10px" : "6px",
//     color: background ? "black" : "white",
//   };

//   if (isCopyRightVisible && copyRightsData?.[0]) {
//     BeatchImage = copyRightsData[0];
//     if (
//       copyRightsData[0]?.license?.licenseName &&
//       copyRightsData[0]?.license?.licenseName != ""
//     ) {
//       license = copyRightsData[0]?.license;
//     }
//   }
//   // useEffect(() => {
//   //   const handleClickOutside = (event) => {
//   //     if (tooltipRef?.current && !tooltipRef?.current.contains(event.target)) {
//   //       setIsShow(false);
//   //     }
//   //   };

//   //   // Add mousedown event listener to detect outside clicks
//   //   document.addEventListener("mousedown", handleClickOutside);
//   //   return () => {
//   //     document.removeEventListener("mousedown", handleClickOutside);
//   //   };
//   // }, [tooltipRef]);
//   // console.log(copyRightsData, "copyRightsDatacopyRightsData");
//   return (
//     <>
//       <div
//         style={{
//           ...styleExtra,
//         }}
//         className=" absolute flex bottom-2 p-2 w-full z-50"
//         // className=" absolute flex bottom-2 px-2 w-full "
//       >
//         {/* <div className="relative w-full flex overflow-x-hidden ms-[25px] "> */}
//         <div className=" relative flex w-full !cursor-pointer z-10 ">
//           <button
//             className="text-white text-xl"
//             onMouseEnter={() => {
//               setIsShow(true);
//             }}
//             onMouseLeave={() => {
//               setIsShow(false);
//             }}
//           >
//           ⓘ
//           </button>
//           {isShow ? (

//             license != null ? (
//               <div
//                 // ref={tooltipRef}
//                 style={{
//                   background: background ? "#ffffffcc" : "transparent",
//                   color: background ? "#ffffffcc" : "white",
//                   width: background ? "auto" : "1000px",
//                   borderRadius: "50px",
//                   padding: background ? "0px 4px" : "0px 0px",
//                   // padding: background ? "0px 9px" : "0px 0px",
//                   marginLeft: background ? "4px" : "1px",
//                   display: "flex",
//                   alignItems: "center",
//                 }}
//               >
//                 {/* {isCopyRightVisible &&
//                 copyRightsData?.[0]?.createdByDisplayName && (
//                   <div className=" text-black  rounded-full m-auto font-extralight mx-1 text-sandee-sm">
//                     ©
//                   </div>
//                 )} */}
//                 <CustomLink
//                   style={copyrightTextStyle}
//                   href={
//                     license?.photoLink &&
//                     license?.photographerName !== "dronepicr"
//                       ? license?.photoLink.includes("http://") ||
//                         license?.photoLink.includes("https://")
//                         ? license?.photoLink
//                         : "https://" + license?.photoLink || "sandee.com"
//                       : "https://sandee.com"
//                   }
//                   target="_blank"
//                   aria-label="Copyrights"
//                 >
//                   {license?.photoName ? license?.photoName : ""}
//                   {/* {license?.photoName ? license?.photoName : "Null"} */}
//                 </CustomLink>
//                 {license?.photoName ? "" : ""}
//                 <CustomLink
//                   style={copyrightTextStyle}
//                   href={
//                     license?.photographerLink &&
//                     license?.photographerName !== "dronepicr"
//                       ? license?.photographerLink.includes("http://") ||
//                         license?.photographerLink.includes("https://")
//                         ? license?.photographerLink || "sandee.com"
//                         : "https://" + license?.photographerLink || "sandee.com"
//                       : "https://sandee.com"
//                   }
//                   aria-label="Copyrights"
//                   target="_blank"
//                 >
//                   <span className="ms-1">
//                     {license?.photographerName ? license?.photographerName : ""}
//                   </span>
//                   {/* {license?.photographerName ? license?.photographerName : "Null"} */}
//                 </CustomLink>
//                 {license?.photographerName ? "-" : ""}
//                 <CustomLink
//                   style={copyrightTextStyle}
//                   href={
//                     license?.licenseLink &&
//                     license?.photographerName !== "dronepicr"
//                       ? license?.licenseLink.includes("http://") ||
//                         license?.licenseLink.includes("https://")
//                         ? license?.licenseLink || "sandee.com"
//                         : "https://" + license?.licenseLink || "sandee.com"
//                       : "https://sandee.com"
//                   }
//                   aria-label="Copyrights"
//                   target="_blank"
//                 >
//                   {license?.licenseName + " " + "(Unmodified)"}
//                 </CustomLink>
//               </div>
//             ) : (
//               <div
//                 // ref={tooltipRef}
//                 style={{
//                   background: background ? "#ffffffcc" : "transparent",
//                   borderRadius: "50px",
//                   // padding: background ? "0px 9px" : "0px 2px",
//                   // padding: background ? "0px 9px" : "0px 2px",
//                   padding: background ? "1px 7px 1px 0px" : "0px 0px",
//                   marginLeft: background ? "4px" : "1px",
//                   display: copyRightsData?.[0]?.createdByDisplayName
//                     ? "flex"
//                     : "none",
//                   alignItems: "center",
//                   paddingLeft: "6px",
//                   paddingRight: "6px",
//                   ...copyrightTextStyle,
//                   ...copyRightExtraStyle,
//                 }}
//               >
//                 {/* {isCopyRightVisible &&
//                       copyRightsData?.[0]?.createdByDisplayName &&
//                       copyRightsData[0]?.createdByDisplayName ===
//                         "Randall Kaplan" && (
//                         <div
//                           style={{
//                             color: "white",
//                             // background: background ? "#00000063" : "transparent",
//                             borderRadius: "60px",
//                             margin: "auto",
//                             fontWeight: "200",
//                             // padding: "0 3px",
//                             // paddingRight: "0px",
//                             marginInline: "5px",
//                             fontSize: "16px",
//                           }}
//                         >
//                           ©
//                         </div>
//                       )} */}
//                 {/* {BeatchImage != null &&
//               copyRightsData[0]?.createdByDisplayName === "Randall Kaplan"
//                 ? ""
//                 : ""} */}
//                 {/* <div
//                 style={{
//                   display: "flex",
//                   paddingRight: "5px",
//                 }}
//               > */}
//                 {BeatchImage != null &&
//                   copyRightsData[0]?.createdByDisplayName &&
//                   copyRightsData[0]?.createdByDisplayName !== "Null" && (
//                     <>
//                       {copyRightsData[0]?.createdByDisplayName ===
//                       "Randall Kaplan"
//                         ? "Copyright "
//                         : ""}
//                       <CustomLink
//                         style={{
//                           ...copyrightTextStyle,
//                           marginInline: "0px",
//                           ...copyRightExtraStyle,
//                           padding: "4px",
//                         }}
//                         href={
//                           copyRightsData[0]?.createdByDisplayName ===
//                           "Randall Kaplan"
//                             ? "https://www.randallkaplan.com/biography"
//                             : `https://staging.sandee.com/profile/${copyRightsData[0]?.coverPhotoId?.id}`
//                         }
//                         target="_blank"
//                         aria-label="Copyrights"
//                       >
//                         {/* {copyRightsData[0]?.createdByDisplayName === "Randall Kaplan"
//                         ? " "
//                         : // : "All Rights Reserved. - "}
//                           "All Rights Reserved."} */}
//                         {copyRightsData[0]?.createdByDisplayName ===
//                         "Randall Kaplan" ? (
//                           <>&nbsp;{`Randall Kaplan. `}</>
//                         ) : (
//                           `Copyright ${copyRightsData[0]?.createdByDisplayName}. All Rights Reserved.`
//                         )}
//                       </CustomLink>
//                       {copyRightsData[0]?.createdByDisplayName ===
//                       "Randall Kaplan" ? (
//                         <>&nbsp;{`All Rights Reserved.`}</>
//                       ) : (
//                         ""
//                       )}
//                     </>
//                   )}
//                 {/* </div> */}
//               </div>
//             )
//           ) : null}
//         </div>
//       </div>
//       {/* </div> */}
//     </>
//   );
// };

// export default CopyRight;
"use client";
import React, { useState } from "react";
import { Tooltip } from "antd";
import { InfoCircleOutlined } from "@ant-design/icons";

export const CustomLink = ({ href, children, ...props }) => {
  const handleClick = () => {
    if (href && href !== "https://sandee.com") {
      window.open(href, "_blank");
    }
  };

  return (
    <div className="custom-link cursor-pointer" onClick={handleClick} {...props}>
      {children}
    </div>
  );
};

const CopyRight = ({
  copyRightsData,
  background = true,
  styleExtra,
  classNameExtra,
  copyRightExtraStyle = {},
  tooltipContentHTML = null,
  customSize = "xs:text-[6px] text-[6px]",
}) => {
  const isCopyRightVisible = copyRightsData && copyRightsData.length !== 0;
  let license = null;
  let BeatchImage = null;
  // const copyrightTextStyle = {
  //   fontSize: background ? "10px" : "6px",
  //   color: background ? "black" : "white",
  // };
  const copyrightTextStyle = {
    fontSize: "10px",
    color: "white",
  };

  if (isCopyRightVisible) {
    BeatchImage = copyRightsData[0];
    if (BeatchImage?.license?.licenseName) {
      license = BeatchImage.license;
    }
  }

  const tooltipContent = license ? (
    <div style={{ ...copyrightTextStyle, ...copyRightExtraStyle }}>
      <CustomLink
        href={
          license?.photoLink && license?.photographerName !== "dronepicr"
            ? license?.photoLink.includes("http://") ||
              license?.photoLink.includes("https://")
              ? license?.photoLink
              : "https://" + license?.photoLink || "sandee.com"
            : "https://sandee.com"
        }
        target="_blank"
        aria-label="Copyrights"
      >
        {license?.photoName ? license?.photoName : ""}
      </CustomLink>
      <CustomLink
        href={
          license?.photographerLink && license?.photographerName !== "dronepicr"
            ? license?.photographerLink.includes("http://") ||
              license?.photographerLink.includes("https://")
              ? license?.photographerLink || "sandee.com"
              : "https://" + license?.photographerLink || "sandee.com"
            : "https://sandee.com"
        }
        aria-label="Copyrights"
        target="_blank"
      >
        <span className="">
          {license?.photographerName ? license?.photographerName : ""}
        </span>
      </CustomLink>
      <CustomLink
        href={
          license?.licenseLink && license?.photographerName !== "dronepicr"
            ? license?.licenseLink.includes("http://") ||
              license?.licenseLink.includes("https://")
              ? license?.licenseLink || "sandee.com"
              : "https://" + license?.licenseLink || "sandee.com"
            : "https://sandee.com"
        }
        aria-label="Copyrights"
        target="_blank"
      >
        {license?.licenseName + " " + "(Unmodified)"}
      </CustomLink>
    </div>
  ) : (
    <div style={{ ...copyrightTextStyle, ...copyRightExtraStyle }}>
      {/* {BeatchImage?.createdByDisplayName === "Randall Kaplan" ? (
        <CustomLink href={"https://www.randallkaplan.com/biography"}>
          {`Copyright Randall Kaplan. All Rights Reserved.`}
        </CustomLink>
      ) : */}
      {BeatchImage?.createdByDisplayName ? (
        `Copyright ${BeatchImage?.createdByDisplayName || ""
        }. All Rights Reserved.`
      ) : (
        "Copyright. All Rights Reserved."
      )}
    </div>
  );

  return (
    <>
      {
        // BeatchImage?.createdByDisplayName === "Randall Kaplan" ? <div className={`absolute flex p-2 z-50 bottom-2 left-[2px] uppercase ${classNameExtra}`}>
        //   <span className={`text-white font-semibold opacity-70 ${customSize}`}>{"Copyright Randall Kaplan. All Rights Reserved."}</span>
        // </div> : 
        <div
          style={{ ...styleExtra }}
          className={`absolute flex p-2 z-50 ${classNameExtra}`}
        >
          <div className="relative flex z-10">
            {/* {isCopyRightVisible && ( */}
            <Tooltip
              title={tooltipContentHTML ?? tooltipContent}
              placement="rightBottom"
              color="#00aae3"
              // visible={isShow}
              // onVisibleChange={setIsShow}
              // overlayStyle={{
              //   background: background ? "#ffffffcc" : "transparent",
              //   borderRadius: "8px",
              //   padding: "4px 8px",
              //   maxWidth: "200px",
              // }}
              overlayInnerStyle={{
                minHeight: 0,
                // padding:"6px 8px 0px 6px"
              }}
            >
              <button
                className="text-white text-xl"
              // onMouseEnter={() => setIsShow(true)}
              // onMouseLeave={() => setIsShow(false)}
              >
                <InfoCircleOutlined className="text-[17px]" />
              </button>
            </Tooltip>
            {/* )} */}
          </div>
        </div>}
    </>
  );
};

export default CopyRight;
