import React, { memo } from "react";
const CustomToolTip = ({ title, children }) => {
  return (
    <>
      <div className="custome-tooltip group relative inline-block">
        {children}
        <span className="custome-tooltiptext opacity-0 group-hover:opacity-100 transition-opacity duration-300 ease-in-out transform translate-y-2 group-hover:translate-y-0 absolute z-10 text-sm rounded-md text-center text-white bg-sandee-blue w-auto py-[3px] px-[10px] left-0 -top-[calc(100%+10px)] before:content-[''] before:absolute before:left-1/2 before:transform before:-translate-x-1/2 before:top-full before:border-8 before:border-transparent before:border-t-sandee-blue">
          {title}
        </span>
      </div>
    </>
  );
};

export default memo(CustomToolTip);
