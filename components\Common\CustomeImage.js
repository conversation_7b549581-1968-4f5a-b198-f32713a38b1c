"use client"
import Image from 'next/image';
import { defaultImage } from '@/helper/functions'
import React, { useEffect, useState } from 'react'

const CustomeImage = ({
    src,
    alt,
    defaultImages = defaultImage,
    className = '',
    fill = false,
    sizes,
    blurDataURL,
    placeholder = 'empty',
    ...props
}) => {
    const [imgSrc, setImgSrc] = useState(src);

    useEffect(() => {
        if (!!src) {
            setImgSrc(src);
        }
    }, [src]);

    const handleError = () => {
        // Check if defaultImages is an array and has elements
        if (imgSrc !== defaultImages) {
            return setImgSrc(defaultImages);
        }
        // if (imgSrc !== defaultImage) {
        //     return setImgSrc(defaultImage);
        // }
    };

    return (
        <Image
            className={className}
            src={imgSrc}
            alt={alt}
            fill={fill}
            sizes={sizes}
            blurDataURL={blurDataURL}
            placeholder={placeholder}
            onError={handleError}
            {...props}
        />
    );
};

export default CustomeImage