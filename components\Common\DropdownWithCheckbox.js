"use client";
import { useEffect, useRef, useState } from "react";

const DropdownWithCheckbox = ({
  placeholder = "Select",
  subtextholder = "Option Selected",
  filter = [],
  initialOptions = [],
  onCancel = () => {},
  onSubmit = () => {},
  onClearAll = () => {},
}) => {
  const [options, setOptions] = useState(initialOptions);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const filterBox = useRef(null);

  const selectedOptions = options.filter((option) => option.checked);
  const handleClickOutside = (event) => {
    if (filterBox.current && !filterBox.current.contains(event.target)) {
      setIsDropdownOpen(false);
      // onCancel();
    }
  };
  useEffect(() => {
    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);
  useEffect(() => {
    setOptions((prev) => {
      const newOptions = prev?.map((opt) => ({
        ...opt,
        checked: filter?.findIndex((val) => val === opt?.id) !== -1,
      }));
      return newOptions;
    });
  }, [filter]);
  return (
    <div className="container-dropdown">
      {/* <p>
        Selected Option :
        {selectedOptions.map((option) => option.label).join(", ")}
      </p> */}
      <div
        ref={filterBox}
        className={`dropdown ${isDropdownOpen ? "open" : ""}`}
      >
        <div
          className="selected-options"
          onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        >
          {/* {selectedOptions.length === 0 ? (
            <span>{placeholder}</span>
          ) : selectedOptions.length === 1 ? (
            <span>{selectedOptions[0].label}</span>
          ) : (
            <span>
              {selectedOptions.length} {subtextholder}
            </span>
          )} */}
          <span className="flex items-center justify-center gap-1">
            {placeholder}{" "}
            {selectedOptions.length ? (
              <svg
                fill="#00aae3"
                className=" h-4 w-4"
                viewBox="-1.2 -1.2 26.40 26.40"
                xmlns="http://www.w3.org/2000/svg"
                stroke="#00aae3"
                strokeWidth="0.00024000000000000003"
              >
                <g id="SVGRepo_bgCarrier" strokeWidth={0} />
                <g
                  id="SVGRepo_tracerCarrier"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <g id="SVGRepo_iconCarrier">
                  <path d="m0 12c0-6.627 5.373-12 12-12s12 5.373 12 12-5.373 12-12 12c-6.624-.008-11.992-5.376-12-11.999zm2.4 0c0 5.302 4.298 9.6 9.6 9.6s9.6-4.298 9.6-9.6-4.298-9.6-9.6-9.6c-5.299.006-9.594 4.301-9.6 9.599v.001zm4 0c0-3.093 2.507-5.6 5.6-5.6s5.6 2.507 5.6 5.6-2.507 5.6-5.6 5.6c-3.093 0-5.6-2.507-5.6-5.6z" />
                </g>
              </svg>
            ) : (
              ""
            )}{" "}
          </span>
        </div>
        <div className={`dropdown-menu ${isDropdownOpen ? "show" : ""}`}>
          {options.map((option) => (
            <div
              className={`option cursor-pointer 
              }`}
              //  ${
              //   option.checked ? " rounded-sandee bg-sandee-blue !text-white" : ""
              // }`}
              // className=""
              key={option.value}
            >
              <input
                type="checkbox"
                className={` cursor-pointer  ${option.checked ? "" : ""}`}
                checked={option.checked}
                onClick={(e) => e.stopPropagation()} // Prevent the checkbox click from closing the dropdown
                onChange={() => {
                  setOptions((prevoption) => {
                    const updatedOptions = prevoption.map((opt) =>
                      opt.value === option.value
                        ? { ...opt, checked: !opt.checked }
                        : opt
                    );
                    return updatedOptions;
                  });
                }}
              />
              <label className={` ${option.checked ? "font-bold" : ""} `}>
                {option.label}
              </label>
            </div>
          ))}
          <div className="flex justify-around items-center flex-wrap my-3 gap-3">
            <button
              className=" text-white bg-sandee-blue border-sandee-blue  px-4  text-xs font-medium  rounded-full"
              onClick={() => {
                onSubmit(options);
                setIsDropdownOpen(false);
              }}
            >
              Ok
            </button>
            <button
              className=" text-white bg-error-red-600 border-errbg-error-red-600  px-4  text-xs font-medium  rounded-full"
              onClick={() => {
                onCancel();
                setIsDropdownOpen(false);
              }}
            >
              Cancel
            </button>
            <button
              className=" text-white bg-error-red-600 border-errbg-error-red-600  px-4  text-xs font-medium  rounded-full"
              onClick={() => {
                onClearAll();
                setIsDropdownOpen(false);
              }}
            >
              Clear All
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
export default DropdownWithCheckbox;
