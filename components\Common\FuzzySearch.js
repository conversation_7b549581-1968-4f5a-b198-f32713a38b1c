class TrieNode {
  constructor() {
    this.children = new Map();
    this.isEndOfWord = false;
    this.items = new Set(); // Store references to original items
  }
}

class Trie {
  constructor() {
    this.root = new TrieNode();
  }

  insert(word, item) {
    let node = this.root;
    for (const char of word) {
      if (!node.children.has(char)) {
        node.children.set(char, new TrieNode());
      }
      node = node.children.get(char);
    }
    node.isEndOfWord = true;
    node.items.add(item); // Add reference to original item
  }

  search(word) {
    let node = this.root;
    for (const char of word) {
      if (!node.children.has(char)) {
        return new Set(); // Return an empty set if word not found
      }
      node = node.children.get(char);
    }
    return node.items; // Return references to original items
  }

  fuzzySearch(query) {
    const result = new Set();
    const traverse = (node, str) => {
      if (node.isEndOfWord) {
        node.items.forEach((item) => result.add(item));
      }
      for (const [char, child] of node.children.entries()) {
        traverse(child, str + char);
      }
    };
    const searchNode = (node, str, i, errorsLeft) => {
      if (i === query.length) {
        traverse(node, str);
        return;
      }
      if (node.children.has(query[i])) {
        searchNode(
          node.children.get(query[i]),
          str + query[i],
          i + 1,
          errorsLeft
        );
      }
      if (errorsLeft > 0) {
        for (const [char, child] of node.children.entries()) {
          if (char !== query[i]) {
            searchNode(child, str + char, i + 1, errorsLeft - 1);
          }
        }
      }
    };
    searchNode(this.root, "", 0, 1);
    return Array.from(result); // Convert set to array
  }
}

export function fuzzySearchTrie(query, list) {
  const trie = new Trie();
  list?.forEach((item) => trie?.insert(item?.name?.toLowerCase(), item));
  return trie?.fuzzySearch(query?.toLowerCase());
}
