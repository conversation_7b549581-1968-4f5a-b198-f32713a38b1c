"use client";
import { EditorContent, FinalImageGenerator, altText, blurDataURL, processContent } from "@/helper/functions";
import Image from "next/image";
import React, { useEffect, useRef, useState } from "react";
import { CustomContainer } from "../Custom-Display";
import CopyRight from "./CopyRight";
import BreadCumber from "./BreadCumber";

const HeroSectionCSC = ({ data, breadCumberData, Details, className = "md:aspect-[1600/900] aspect-square" }) => {
  const [isShow, setIsShow] = useState(false);
  const [isOverflow, setIsOverflow] = useState(false);


  // Process content fields in order of priority
  const fullContent =
    data?.summary && data?.summary !== "<p><br></p>" ? processContent(data?.summary) :
      (processContent(data?.overview) ||
        processContent(data?.description) ||
        "");

  const contentRef = useRef(null);
  const sanitizeContent =
    fullContent?.trim()?.length !== 0 &&
    fullContent?.trim() !== '<div class="spacing_overview"></div>';
  // Check if the content exceeds 5 lines
  useEffect(() => {
    const contentEl = contentRef.current;
    if (contentEl) {
      const lineHeight = parseFloat(
        window.getComputedStyle(contentEl).lineHeight
      );
      const maxHeight = lineHeight * 5; // Height for 5 lines
      if (contentEl.scrollHeight > maxHeight) {
        setIsOverflow(true); // Content is more than 5 lines
      } else {
        setIsOverflow(false); // Content fits within 5 lines
      }
    }
  }, [fullContent]);
  return (
    <section className=" items-center  justify-around ">
      <div className="relative  md:h-[500px]  h-[260px]">
        <div className={`${className} absolute left-0 top-0  md:h-[500px] h-[260px] w-full `}>
          <Image
            priority
            src={FinalImageGenerator(data?.image, 1600, 3)}
            alt={altText(data, `Sandee - HomePage /`)}
            style={{
              objectPosition: `center ${data?.image?.position?.objectPositionY ? data?.image?.position?.objectPositionY
                : "50"}%`,
            }}
            className={`object-cover bg-blend-overlay lg:rounded-b-none rounded-b-[40px] object-[center ${data?.image?.position?.objectPositionY ? `${data?.image?.position?.objectPositionY}px` : "center"}]`}
            fill
            blurDataURL={blurDataURL(1600, 800)}
            placeholder="blur"
          />
        </div>

        <div className="absolute flex justify-center items-center  md:h-[500px] h-[260px] w-full lg:rounded-b-none rounded-b-[40px]">
          <h1 className=" text-white font-bold text-sandee-4xl w-full lg:w-1/2  sm:text-sandee-4xl md:text-sandee-6xl xl:text-sandee-7xl 3xl:text-sandee-8xl text-center">
            {data?.name}
          </h1>
          <CopyRight
            copyRightsData={[data?.image]}
            background={true}
            classNameExtra={"bottom-2 lg:left-0 left-6"}
            customSize={"xs:text-[8px] text-[12px]"}
          // styleExtra={{ left: "25px" }}
          />
        </div>
      </div>

      <div className="  w-full pt-5 flex items-center">
        <CustomContainer>
          <BreadCumber
            data={
              breadCumberData
                ? breadCumberData
                : [
                  {
                    title: "All Countries",
                    to: `/countries`,
                  },
                  {
                    title: data?.name,
                  },
                ]
            }
          />
          <p className="text-sandee-24 leading-[30px] font-bold mt-2">
            {data?.name}
          </p>
        </CustomContainer>
      </div>
      <CustomContainer>
        {(data?.summary || data?.overview || data?.description) &&
          sanitizeContent && (
            <div className="text-black font-normal text-sandee-18 pt-0">
              {/* <EditorContent ref={contentRef} className={`${!isShow
                ? "clamp-text" // Limit to 5 lines
                : ""
                }`} value={fullContent} id={""} />
              <br /> */}
              <div
                ref={contentRef}
                className={`${!isShow
                  ? "clamp-text" // Limit to 5 lines
                  : ""
                  } ck-content`}
                dangerouslySetInnerHTML={{
                  __html: fullContent,
                }}
              >

              </div>

              {sanitizeContent && isOverflow && (
                <button
                  onClick={() => setIsShow(!isShow)}
                  className="font-bold hover:underline text-sandee-orange"
                // className="font-bold hover:underline text-gray-500"
                >
                  {isShow ? "View Less" : "View More"}
                </button>
              )}
            </div>
          )}
      </CustomContainer>

      {/* <HeroSectionSCSDynamic Details={Details} /> */}
    </section>
  );
};

export default HeroSectionCSC;
