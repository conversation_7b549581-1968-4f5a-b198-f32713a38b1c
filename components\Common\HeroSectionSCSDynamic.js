"use client";
import Link from "next/link";
import React, { useState } from "react";

const HeroSectionSCSDynamic = ({ Details }) => {
  const [accordian, setAccordion] = useState("");
  return (
    <div className="relative z-50  h-auto md:h-0 -mt-[80px]">
      <div className=" w-full  h-auto flex justify-center items-end px-2">
        <div className="md:hidden  flex flex-col  items-center justify-center gap-5 bg-white rounded-sandee shadow-lg p-4 h-full mb-5">
          {Details?.map((element, index) => {
            return (
              <div
                key={element?.title}
                className={`accordion ${
                  element?.title === accordian ? "accordion__active" : ""
                } px-2 !bg-sandee-blue !bg-opacity-10 rounded-sandee flex flex-col`}
                onClick={() => {
                  setAccordion((prev) => {
                    if (prev === element?.title) {
                      return "";
                    }
                    return element?.title;
                  });
                }}
              >
                {/* <div className={`p-2 rounded-sandee flex flex-col`}> */}
                <div className="accordion__intro flex justify-between items-center !m-0 gap-2 w-full">
                  <div className="flex justify-between items-center gap-2">
                    <div className="p-2 bg-sandee-blue bg-opacity-25 rounded-full aspect-square w-[40px] h-[40px] flex items-center justify-center ">
                      {element?.icon}
                    </div>
                    <p className=" font-medium text-sandee-blue text-sandee-base ">
                      {element?.title}
                    </p>
                  </div>
                  <div className="icon__content flex justify-center items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="10"
                      viewBox="0 0 24 14"
                      fill="none"
                    >
                      <path
                        d="M10.9393 13.0607C11.5251 13.6464 12.4749 13.6464 13.0607 13.0607L22.6066 3.51472C23.1924 2.92893 23.1924 1.97919 22.6066 1.3934C22.0208 0.807611 21.0711 0.807611 20.4853 1.3934L12 9.87868L3.51472 1.3934C2.92893 0.807611 1.97919 0.807611 1.3934 1.3934C0.807611 1.97919 0.807611 2.92893 1.3934 3.51472L10.9393 13.0607ZM10.5 11V12H13.5V11H10.5Z"
                        fill="#00AAE3"
                      />
                    </svg>
                  </div>
                </div>
                <ul
                  className={` accordion__content flex-col text-sandee-sm text-sandee-grey  w-full list-none ps-5 gap-x-5 gap-y-[2px]  `}
                >
                  {element?.details?.map((el) => (
                    <li key={el?.title}>
                      {el?.link ? (
                        <Link
                          href={el?.link}
                          className=" underline   max-w-60 text-ellipsis line-clamp-1"
                        >
                          {el?.title}
                        </Link>
                      ) : (
                        el?.title
                      )}
                    </li>
                  ))}
                </ul>
                {/* </div> */}
                {/* <div className="  h-full w-0.5 bg-gray-200"></div> */}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default HeroSectionSCSDynamic;
