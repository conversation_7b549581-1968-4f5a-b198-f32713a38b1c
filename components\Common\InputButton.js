"use client"
import { Spin } from 'antd';
import React, { memo, } from 'react'

const InputButton = ({ title, className, classNameText, type, click, loading, loadingText, disabled }) => {
    return (
        <>
            <button type="submit" className={`${className}  main-button`} onClick={click} disabled={disabled}>
                {loading ? <span className='tw-me-2'><Spin size={"sm"} className='text-white' /></span> : null}
                <span className={`${classNameText} ${type == 'fill' ? 'text-white' : ""}`}>{loading ? loadingText : title}</span></button>
        </>
    )
}

export default memo(InputButton)

