"use client";
import React, { useEffect, useRef, useState } from "react";
import mapboxgl from "!mapbox-gl";
import { PostSearchForMap } from "@/app/(HomeHeader)/action";

mapboxgl.accessToken = process.env.NEXT_PUBLIC_MAPBOX_API_KEY;

const LocationMap = ({ dataTestid = "", latStr, longStr, zoom = 3, mapClass = "md:map-container w-full lg:h-[450px] h-[370px] p-0 rounded-[22px]" }) => {
  const mapContainer = useRef(null);
  const map = useRef(null);
  const timerRef = useRef(null);
  const [beachesCount, setBeachesCount] = useState([]);
  const [isLoadingCount, setIsLoadingCount] = useState(true);
  const [location, setLocation] = useState(null);
  const [loaded, setLoaded] = useState(false);
  const [isMapActive, setIsMapActive] = useState(false);



  const disablePageScroll = () => {
    document.body.style.overflow = "hidden";
  };

  const enablePageScroll = () => {
    document.body.style.overflow = "";
  };

  const enableMapScrollZoom = () => {
    if (map.current) {
      map.current.scrollZoom.enable();
      setIsMapActive(true);
      // disablePageScroll();
    }
  };

  const disableMapScrollZoom = () => {
    if (map.current) {
      map.current.scrollZoom.disable();
      setIsMapActive(false);
      enablePageScroll();
    }
  };

  const handleMouseEnter = () => {
    timerRef.current = setTimeout(enableMapScrollZoom, 1500); // Wait 1 seconds to enable zoom
  };

  const handleMouseLeave = () => {
    clearTimeout(timerRef.current);
    disableMapScrollZoom(); // Reset zoom state
  };

  // Function to fetch beaches count
  const FetchAndSetDelayedBeachesCount = async () => {
    if (!map.current) return;

    const bounds = map.current.getBounds();
    const payload = {
      point1: bounds.getSouthWest().toArray().reverse(),
      point2: bounds.getNorthEast().toArray().reverse(),
      point3: bounds.getNorthWest().toArray().reverse(),
      point4: bounds.getSouthEast().toArray().reverse(),
      imageAndDescriptions: false,
    };

    try {
      const Results = await PostSearchForMap(payload);
      const beachLists = Results?.data?.beaches?.rows;
      // Refine data for rendering beaches
      const AllBeachWithRefinedDataCount = beachLists?.map((el) => ({
        ...el,
        lat: el?.GeoLoc?.coordinates?.[1] ?? 0,
        long: el?.GeoLoc?.coordinates?.[0] ?? 0,
        locationAddress: {
          link: `/map/${el?.nameSlug}/@${el?.GeoLoc?.coordinates?.[1] ?? 0},${el?.GeoLoc?.coordinates?.[0] ?? 0
            }`,
        },
      }));

      setBeachesCount(AllBeachWithRefinedDataCount);
      setIsLoadingCount(false);
    } catch (error) {
      console.error("Error fetching beach data:", error);
    }
  };

  // Function to add beaches and clusters to the map
  const SetBeaches = () => {
    if (!mapContainer?.current || !beachesCount?.length || !loaded) return;

    if (map.current.getSource("beaches")) {
      map.current.getSource("beaches").setData({
        type: "FeatureCollection",
        features: beachesCount.map((beach) => ({
          type: "Feature",
          geometry: {
            type: "Point",
            coordinates: [beach.long, beach.lat],
          },
          properties: beach,
        })),
      });
    } else {
      map.current.addSource("beaches", {
        type: "geojson",
        data: {
          type: "FeatureCollection",
          features: beachesCount.map((beach) => ({
            type: "Feature",
            geometry: {
              type: "Point",
              coordinates: [beach.long, beach.lat],
            },
            properties: beach,
          })),
        },
        cluster: true,
        clusterMaxZoom: 14,
        clusterRadius: 50,
      });

      // Add cluster layer
      map.current.addLayer({
        id: "clusters",
        type: "circle",
        source: "beaches",
        filter: ["has", "point_count"],
        paint: {
          "circle-color": [
            "step",
            ["get", "point_count"],
            "#fff000",
            100,
            "#fff000",
            750,
            "#fff000",
          ],
          "circle-radius": [
            "step",
            ["get", "point_count"],
            25,
            100,
            25,
            750,
            30,
          ],
        },
      });

      // Add cluster count layer
      map.current.addLayer({
        id: "cluster-count",
        type: "symbol",
        source: "beaches",
        filter: ["has", "point_count"],
        layout: {
          "text-field": ["get", "point_count_abbreviated"],
          "text-font": ["DIN Offc Pro Medium", "Arial Unicode MS Bold"],
          "text-size": 16,
        },
      });

      // Load and add beach icons
      map.current.loadImage("/Marker4.png", (error, image) => {
        if (error) throw error;
        map.current.addImage("beach-icon", image);

        // Add unclustered points layer
        map.current.addLayer({
          id: "unclustered-point",
          type: "symbol",
          source: "beaches",
          filter: ["!", ["has", "point_count"]],
          layout: {
            "icon-image": "beach-icon",
            "icon-size": 1,
            "icon-allow-overlap": true,
            "text-field": ["get", "name"],
            "text-size": 18,
            "text-offset": [1, 0],
            "text-anchor": "left",
            "text-font": ["Open Sans Bold", "Arial Unicode MS Bold"]
          },
          paint: {
            "text-color": "#FF6B00", // Set text color FF6B00
            "text-halo-color": "#fff  ", // Optional: Add a halo around the text for better readability
            "text-halo-width": 1, // Optional: Set halo width
          },
        });
      });

      if (latStr && longStr) {
        new mapboxgl.Marker({ color: "red" })
          .setLngLat([longStr, latStr])
          .addTo(map.current);
        // map.current.flyTo({ center: [longStr, latStr], zoom: 10 });
      }

      // Handle cluster clicks
      map.current.on("click", "clusters", (e) => {
        const features = map.current.queryRenderedFeatures(e.point, {
          layers: ["clusters"],
        });
        const clusterId = features[0].properties.cluster_id;
        map.current
          .getSource("beaches")
          .getClusterExpansionZoom(clusterId, (err, zoom) => {
            if (err) return;
            map.current.easeTo({
              center: features[0].geometry.coordinates,
              zoom: zoom,
            });
          });
      });

      // Handle unclustered-point clicks
      map.current.on("click", "unclustered-point", (e) => {
        const feature = e.features[0];
        const address = JSON.parse(feature.properties.locationAddress);

        if (address?.link) {
          window.open(address.link, "_blank");
        }
      });

      // Cursor handling for clusters and unclustered points
      map.current.on("mouseenter", ["clusters", "unclustered-point"], () => {
        map.current.getCanvas().style.cursor = "pointer";
      });
      map.current.on("mouseleave", ["clusters", "unclustered-point"], () => {
        map.current.getCanvas().style.cursor = "";
      });

      map.current.addControl(new mapboxgl.ScaleControl());
    }
  };

  // Fetch beaches count when map bounds change
  const handleMoveEnd = () => {
    setIsLoadingCount(true);
    FetchAndSetDelayedBeachesCount();
  };

  // Set the initial location
  const getLocation = () => {
    if (latStr && longStr) {
      setLocation([longStr, latStr]);
    } else {
      setLocation([-118.4117325, 34.020479]); // los angeles coordinates
    }
  };

  // Initialize map when location is set
  useEffect(() => {
    if (!location) return;
    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: "mapbox://styles/mapbox/streets-v12",
      center: location,
      zoom: zoom,
      minZoom: 2,
      maxBounds: [
        [-180, -85],
        [180, 85],
      ],
      projection: { name: "mercator" },
    });

    map.current.on("load", () => {
      setLoaded(true);
      handleMoveEnd();
    });

    map.current.on("moveend", handleMoveEnd);

    return () => {
      map.current.off("moveend", handleMoveEnd);
      // clearTimeout(timerRef.current);
      // disableMapScrollZoom();

      map.current.remove();
      // enablePageScroll(); // Ensure page scrolling is enabled on cleanup
    };
  }, [location]);

  // Load beaches after data is fetched
  useEffect(() => {
    SetBeaches();
  }, [beachesCount, loaded]);

  // Fetch beach data after the map is refreshed
  useEffect(() => {
    getLocation();
  }, []);

  return (
    <div data-testid={dataTestid} className="relative rounded-[22px] overflow-hidden mt-1 mb-3"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div
        ref={mapContainer}
        className={`${mapClass}`}

      />
      {isLoadingCount ? (
        <div className="absolute md:top-5 right-4 bg-sandee-blue py-1 rounded-lg md:px-4 px-2 text-white md:text-base text-[12px] top-4">
          <span className="font-semibold">Loading Beaches...</span>
        </div>
      ) : null}
    </div>
  );
};

export default LocationMap;
