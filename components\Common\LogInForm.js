"use client";
import { getMyProfile, postlogIn } from "@/app/(HomeHeader)/action";
import React, { useState } from "react";
import SocialLogin from "./SocialLogIn";
import { notification } from "antd";

const LogInForm = ({ setLogIn }) => {
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (payload) => {
    const SignUpData = await postlogIn(payload);
    if (!SignUpData?.token) {
      return false;
    }
    const Profile = await getMyProfile(SignUpData?.token);
    localStorage.setItem("token", SignUpData?.token);
    localStorage.setItem("profile", JSON.stringify(Profile?.data));
    return true;
  };
  return (
    <div className="container">
      <div className="heading">Log In</div>
      <form
        className="form"
        // action=""
        onSubmit={async (e) => {
          e.preventDefault();
          const payload = {
            email: e.target?.email?.value,
            password: e.target?.password?.value,
          };
          setLoading(true);
          const profile = await handleSubmit(payload);
          if (profile) {
            notification.success({
              message: "LoggedIn successfully",
              duration: 3,
            });
            window.location.reload();
          } else {
            notification.error({
              message: "email or password incorrect",
              duration: 3,
            });
          }
          setLoading(false);
        }}
      >
        <input
          placeholder="E-mail"
          id="email"
          name="email"
          type="email"
          className="input"
          required
        />
        <input
          placeholder="Password"
          id="password"
          name="password"
          type="password"
          className="input"
          required
        />
        <span
          className="forgot-password !normal-case"
          onClick={() => {
            setLogIn(false);
          }}
        >
          {` Don't have an account ?`}
        </span>
        <input
          defaultValue="Sign In"
          disabled={loading}
          type="submit"
          className={` login-button ${loading ? " animate-pulse !bg-sandee-grey" : ""
            }`}
        />
      </form>
      <div className="social-account-container">
        <span className="title">Or</span>
        <div className="social-accounts">
          <SocialLogin
            Onsuccess={async (v) => {
              const Profile = await getMyProfile(v?.data?.token);
              localStorage.setItem("token", v?.data?.token);
              // Profile?.data &&
              //   localStorage.setItem("profile", JSON.stringify(Profile?.data));
              if (Profile?.data) {
                notification.success({
                  message: "LoggedIn successfully",
                  duration: 3,
                });
                localStorage.setItem("profile", JSON.stringify(Profile?.data));
              } else {
                notification.error({
                  message: "email or password incorrect",
                  duration: 3,
                });
              }
              window.location.reload();
            }}
          // loginButtons={[
          //   {
          //     providerName: "google",
          //     text: "Login With Google",
          //     icon: <FaGoogle className="tw-w-[26px] tw-h-[26px]" />,
          //   },
          // ]}
          />
        </div>
      </div>
      {/* <span className="agreement">
        <a href="#">Learn user licence agreement</a>
      </span> */}
    </div>
  );
};

export default LogInForm;
