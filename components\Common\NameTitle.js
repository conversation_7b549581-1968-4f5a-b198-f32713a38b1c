import React from "react";
import { HeadingTags } from "../Custom-Display";

const NameTitle = ({
  name = "",
  description = "",
  extraButton = null,
  type = 1,
  className = "",
  htag = 2,
  dataTestid = "name-title",
}) => {
  switch (type) {
    case 1:
      return (
        <div className={`flex justify-between ${className}`}
          data-testid={`${dataTestid}-type-${htag}`}>
          <div className="flex flex-col gap-0">
            <HeadingTags
              htag={htag}
              className=" text-sandee-20 sm:text-sandee-24 font-bold"
              dataTestid={`${dataTestid}-heading-${htag}`}
            >
              {name}
            </HeadingTags>

            {!!description ? (
              <p data-testid={`${dataTestid}-description-${htag}`} className=" text-[#374151B2] text-sandee-base font-normal">
                {description}
              </p>
            ) : (
              ""
            )}
          </div>
          {extraButton}
        </div>
      );
    case 2:
      return (
        <div data-testid={`${dataTestid}-type-${htag}`} className={`flex justify-between ${className}`}>
          <div className="flex flex-col gap-1">
            {!!description ? (
              <p data-testid={`${dataTestid}-description-${htag}`} className=" text-sandee-[17px] font-bold text-sandee-orange tracking-widest  uppercase">
                {description}
              </p>
            ) : (
              ""
            )}
            {!!name ? (
              <HeadingTags
                htag={htag}
                dataTestid={`${dataTestid}-heading-${htag}`}
                className=" text-sandee-20 sm:text-sandee-24 font-bold -mt-[7px]"
              >
                {name}
              </HeadingTags>
            ) : (
              ""
            )}
          </div>
          {extraButton}
        </div>
      );
    case 3:
      return (
        <div className={`flex justify-between ${className}`} data-testid={`${dataTestid}-type-${htag}`}>
          <div className="flex flex-col gap-0">
            <HeadingTags
              htag={htag}
              dataTestid={`${dataTestid}-heading-${htag}`}
              className=" text-sandee-20 sm:text-sandee-24 font-bold text-sandee-orange"
            >
              {name}
            </HeadingTags>

            {!!description ? (
              <p data-testid={`${dataTestid}-description-${htag}`} className=" text-[#374151B2] text-sandee-base font-normal">
                {description}
              </p>
            ) : (
              ""
            )}
          </div>
          {extraButton}
        </div>
      );
    // case 2:
    //   return (
    //     <div className="flex justify-between my-10">
    //       <div className="flex flex-col gap-2">
    //         <p className=" text-sandee-sm font-bold text-sandee-orange tracking-widest leading-none uppercase">
    //           {description}
    //         </p>
    //         <p className="text-sandee-24 font-semibold leading-none">{name}</p>
    //       </div>
    //       {extraButton}
    //     </div>
    //   );
    case 4:
      return (<div data-testid={`${dataTestid}-type-${htag}`} className={`flex justify-between ${className}`}>
        <div className="flex flex-col gap-2">
          <HeadingTags dataTestid={`${dataTestid}-heading-${htag}`} htag={htag} className=" text-sandee-24 font-semibold">
            {name}
          </HeadingTags>

          {!!description && (
            <p data-testid={`${dataTestid}-description-${htag}`} className=" text-[#1F1F1F] text-sandee-18 font-normal">
              {description}
            </p>
          )}
        </div>
        {extraButton}
      </div>)
    case 5:
      return (<div className={`flex justify-between ${className}`} data-testid={`${dataTestid}-type-${htag}`}>
        <div className="flex flex-col gap-2">
          <HeadingTags dataTestid={`${dataTestid}-heading-${htag}`} htag={htag} className=" text-sandee-20 font-semibold">
            {name}
          </HeadingTags>

          {!!description && (
            <p data-testid={`${dataTestid}-description-${htag}`} className=" text-[#1F1F1F] text-sandee-18 font-normal">
              {description}
            </p>
          )}
        </div>
        {extraButton}
      </div>)

    default:
      return (
        <div className={`flex justify-between ${className}`} data-testid={`${dataTestid}-default-${htag}`}>
          <div className="flex flex-col gap-2">
            <HeadingTags dataTestid={`${dataTestid}-heading-${htag}`} htag={htag} className=" text-sandee-24 font-semibold">
              {name}
            </HeadingTags>

            {!!description && (
              <p data-testid={`${dataTestid}-description-${htag}`} className=" text-[#374151B2] text-sandee-sm font-normal">
                {description}
              </p>
            )}
          </div>
          {extraButton}
        </div>
      );
  }
};

export default NameTitle;
{
  /* <div className=" hidden md:flex justify-end items-start w-3/12">
        <Link href={`/list/${el?.nameSlug}`} className=" !text-nowrap">
          <CustomButton type={4}>
            View All
            <ExploreMoreArrow className=" ml-2 fill-sandee-blue group-hover:fill-white  h-4 w-4" />
          </CustomButton>
        </Link>
      </div> */
}
