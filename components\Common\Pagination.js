import React from "react";
import { DOTS, usePagination } from "@/helper/hook/usePagination";
import Link from "next/link";

const Pagination = (props) => {
  const {
    onPageChange,
    totalCount,
    siblingCount = 1,
    currentPage,
    pageSize,
    className,
    isLink,
    baseLink = "/",
    dataTestid = "pagination",
  } = props;
  const paginationRange = usePagination({
    currentPage,
    totalCount,
    siblingCount: 0,
    pageSize,
  });

  if (currentPage === 0 || paginationRange?.length < 2) {
    return null;
  }

  const onNext = () => {
    onPageChange(currentPage + 1);
  };

  const onPrevious = () => {
    onPageChange(currentPage - 1);
  };

  let lastPage = paginationRange[paginationRange?.length - 1];

  return (
    <ul data-testid={dataTestid} className={`pagination-container flex list-none group ${className}`}>
      {isLink ? (
        <Link
          className={`${currentPage === 1 ? "!hidden" : ""
            } pagination-item group `}
          href={`${baseLink}${currentPage - 1}`}
        >
          {/* <div className="arrow left" /> */}
          <svg
            width={9}
            height={14}
            viewBox="0 0 9 14"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className=" mr-1"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M1.12274 7.71927C0.935265 7.53174 0.829949 7.27744 0.829949 7.01227C0.829949 6.74711 0.935265 6.4928 1.12274 6.30527L6.77973 0.648271C6.87198 0.55276 6.98233 0.476578 7.10433 0.424169C7.22633 0.371759 7.35755 0.344174 7.49033 0.34302C7.62311 0.341866 7.75479 0.367168 7.87769 0.417449C8.00059 0.46773 8.11224 0.541984 8.20613 0.635877C8.30002 0.72977 8.37428 0.84142 8.42456 0.964316C8.47484 1.08721 8.50014 1.21889 8.49899 1.35167C8.49783 1.48445 8.47025 1.61567 8.41784 1.73768C8.36543 1.85968 8.28925 1.97002 8.19374 2.06227L3.24373 7.01227L8.19374 11.9623C8.37589 12.1509 8.47669 12.4035 8.47441 12.6657C8.47213 12.9279 8.36696 13.1787 8.18155 13.3641C7.99615 13.5495 7.74533 13.6547 7.48314 13.6569C7.22094 13.6592 6.96834 13.5584 6.77973 13.3763L1.12274 7.71927Z"
              fill="#1A1A1A"
            />
          </svg>
          {/* Prev */}
        </Link>
      ) : (
        <li
          className={`${currentPage === 1 ? "!hidden" : ""
            } pagination-item group `}
          onClick={onPrevious}
        >
          {/* <div className="arrow left" /> */}
          <svg
            width={9}
            height={14}
            viewBox="0 0 9 14"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className=" mr-1"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M1.12274 7.71927C0.935265 7.53174 0.829949 7.27744 0.829949 7.01227C0.829949 6.74711 0.935265 6.4928 1.12274 6.30527L6.77973 0.648271C6.87198 0.55276 6.98233 0.476578 7.10433 0.424169C7.22633 0.371759 7.35755 0.344174 7.49033 0.34302C7.62311 0.341866 7.75479 0.367168 7.87769 0.417449C8.00059 0.46773 8.11224 0.541984 8.20613 0.635877C8.30002 0.72977 8.37428 0.84142 8.42456 0.964316C8.47484 1.08721 8.50014 1.21889 8.49899 1.35167C8.49783 1.48445 8.47025 1.61567 8.41784 1.73768C8.36543 1.85968 8.28925 1.97002 8.19374 2.06227L3.24373 7.01227L8.19374 11.9623C8.37589 12.1509 8.47669 12.4035 8.47441 12.6657C8.47213 12.9279 8.36696 13.1787 8.18155 13.3641C7.99615 13.5495 7.74533 13.6547 7.48314 13.6569C7.22094 13.6592 6.96834 13.5584 6.77973 13.3763L1.12274 7.71927Z"
              fill="#1A1A1A"
            />
          </svg>
          {/* Prev */}
        </li>
      )}

      {paginationRange.map((pageNumber, i) => {
        if (pageNumber === DOTS) {
          return (
            <li key={`${pageNumber} ${i}`} className="pagination-item dots">
              &#8230;
            </li>
          );
        }

        return isLink ? (
          <Link
            key={`${pageNumber} ${i}`}
            className={`pagination-item  ${pageNumber === currentPage ? " selected" : ""
              }`}
            href={`${baseLink}${pageNumber}`}
          >
            {pageNumber}
          </Link>
        ) : (
          <li
            key={`${pageNumber} ${i}`}
            className={`pagination-item  ${pageNumber === currentPage ? " selected" : ""
              }`}
            onClick={() => onPageChange(pageNumber)}
          >
            {pageNumber}
          </li>
        );
      })}
      {isLink ? (
        <Link
          className={`pagination-item group ${currentPage === lastPage ? "!hidden" : ""
            }`}
          href={`${baseLink}${currentPage + 1}`}
        >
          {/* Next */}
          <svg
            width={9}
            height={14}
            viewBox="0 0 9 14"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className=" ml-1"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M7.87531 6.28073C8.06278 6.46826 8.1681 6.72256 8.1681 6.98773C8.1681 7.25289 8.06278 7.5072 7.87531 7.69473L2.21831 13.3517C2.12607 13.4472 2.01572 13.5234 1.89372 13.5758C1.77171 13.6282 1.64049 13.6558 1.50771 13.657C1.37493 13.6581 1.24325 13.6328 1.12036 13.5826C0.997462 13.5323 0.885809 13.458 0.791916 13.3641C0.698024 13.2702 0.623771 13.1586 0.57349 13.0357C0.523209 12.9128 0.497907 12.7811 0.499061 12.6483C0.500215 12.5155 0.527801 12.3843 0.58021 12.2623C0.632619 12.1403 0.708801 12.03 0.804311 11.9377L5.75431 6.98773L0.804311 2.03773C0.622153 1.84913 0.521359 1.59652 0.523637 1.33433C0.525916 1.07213 0.631085 0.821319 0.816493 0.635911C1.0019 0.450503 1.25271 0.345334 1.51491 0.343055C1.77711 0.340777 2.02971 0.441571 2.21831 0.623729L7.87531 6.28073Z"
              fill="#1A1A1A"
            />
          </svg>
          {/* <div className="arrow right " /> */}
        </Link>
      ) : (
        <li
          className={`pagination-item group ${currentPage === lastPage ? "!hidden" : ""
            }`}
          onClick={onNext}
        >
          {/* Next */}
          <svg
            width={9}
            height={14}
            viewBox="0 0 9 14"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className=" ml-1"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M7.87531 6.28073C8.06278 6.46826 8.1681 6.72256 8.1681 6.98773C8.1681 7.25289 8.06278 7.5072 7.87531 7.69473L2.21831 13.3517C2.12607 13.4472 2.01572 13.5234 1.89372 13.5758C1.77171 13.6282 1.64049 13.6558 1.50771 13.657C1.37493 13.6581 1.24325 13.6328 1.12036 13.5826C0.997462 13.5323 0.885809 13.458 0.791916 13.3641C0.698024 13.2702 0.623771 13.1586 0.57349 13.0357C0.523209 12.9128 0.497907 12.7811 0.499061 12.6483C0.500215 12.5155 0.527801 12.3843 0.58021 12.2623C0.632619 12.1403 0.708801 12.03 0.804311 11.9377L5.75431 6.98773L0.804311 2.03773C0.622153 1.84913 0.521359 1.59652 0.523637 1.33433C0.525916 1.07213 0.631085 0.821319 0.816493 0.635911C1.0019 0.450503 1.25271 0.345334 1.51491 0.343055C1.77711 0.340777 2.02971 0.441571 2.21831 0.623729L7.87531 6.28073Z"
              fill="#1A1A1A"
            />
          </svg>
          {/* <div className="arrow right " /> */}
        </li>
      )}
    </ul>
  );
};

export default Pagination;
