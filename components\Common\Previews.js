// "use client";
// import React, { useEffect, useState } from "react";
// import { useDropzone } from "react-dropzone";
// import {
//   AddImageIcon,
//   CheckCircleIcon,
//   CloseCircleIcon,
//   DeleteIcon,
//   PauseCircleIcon,
// } from "../social-icons/icons";
// import Image from "next/image";
// import Picture from "../../public/static/images/Picture.png";
// import { Progress } from "antd";
// // const thumbsContainer = {
// //   rowGap: "10px",
// //   marginBottom: "10px",
// // };

// // const thumb = {
// //   //   display: "inline-flex",
// //   borderRadius: 2,
// //   padding: "0 10px",
// //   //   marginBottom: 8,
// //   //   marginRight: 8,
// //   //   width: 100,
// //   height: 100,
// //   //   padding: 4,
// //   //   boxSizing: "border-box",
// // };

// // const thumbInner = {
// //   position: "relative",
// //   width: "100%",
// //   height: "100%",

// //   border: "1px solid #dee2e6",
// // };

// // const img = {
// //   width: "100%",
// //   objectFit: "cover",
// //   height: "100%",
// //   position: "absolute",
// // };
// export function bytesToSize(bytes) {
//   var sizes = ["Bytes", "KB", "MB", "GB", "TB"];
//   if (bytes == 0) return "n/a";
//   var i = parseInt(Math.floor(Math.log(bytes) / Math.log(1024)));
//   if (i == 0) return bytes + " " + sizes[i];
//   return (bytes / Math.pow(1024, i)).toFixed(1) + " " + sizes[i];
// }
// export default function Previews({ onChange = (v) => {}, setFiles, files }) {
//   //   const [files, setFiles] = useState([]);
//   const [uploadProgress, setUploadProgress] = useState({});
//   const { getRootProps, getInputProps } = useDropzone({
//     accept: { "image/jpeg": [], "image/jpg": [], "image/png": [] },
//     onDrop: (acceptedFiles) => {
//       setFiles((prevFiles) => [...prevFiles, ...acceptedFiles]);
//       acceptedFiles.forEach((file) => simulateUploadProgress(file)); // Simulate the progress for each file
//     },
//   });
//   const simulateUploadProgress = (file) => {
//     let progress = 0;
//     const timer = setInterval(() => {
//       progress += 10; // Increment progress by 10% every interval
//       setUploadProgress((prevProgress) => ({
//         ...prevProgress,
//         [file.name]: progress,
//       }));

//       if (progress >= 100) {
//         clearInterval(timer); // Clear the interval once progress reaches 100%
//       }
//     }, 200); // Adjust the interval speed (200ms = 0.2 seconds)
//   };

//   const deleteFile = (file) => {
//     setFiles((prevFiles) => prevFiles.filter((f) => f !== file));
//     setUploadProgress((prevProgress) => {
//       const newProgress = { ...prevProgress };
//       delete newProgress[file.name];
//       return newProgress;
//     });
//   };

//   const thumbs = files.map((file, index) => (
//     <div
//       className="bg-[#F7F7F7] p-2 grid md:grid-flow-col grid-flow-row md:grid-cols-12 gap-y-2 my-4 rounded-lg"
//       key={file.name}
//     >
//       <div className="relative md:w-[70px] md:h-[70px] h-[150px] lg:col-span-2 md:col-span-3 ">
//         <Image
//           src={URL.createObjectURL(file)}
//           fill
//           alt={file.name}
//           className="object-cover rounded-md"
//           // Revoke data URI after image is loaded
//           onLoad={() => {
//             URL.revokeObjectURL(file.preview);
//           }}
//         />
//       </div>
//       <div className="w-full lg:col-span-10 md:col-span-9 ">
//         <div className="grid grid-flow-col grid-cols-12">
//           <p className="text-base font-medium truncate w-[90%] col-span-10">
//             {file.name}
//           </p>
//           <div className="flex justify-end gap-x-2 col-span-2">
//             <div>
//               {uploadProgress[file.name] === 100 ? (
//                 <CheckCircleIcon />
//               ) : (
//                 <PauseCircleIcon />
//               )}
//             </div>
//             <div
//               className="cursor-pointer hover:scale-110 "
//               onClick={() => deleteFile(file)}
//             >
//               <CloseCircleIcon />
//             </div>
//           </div>
//         </div>

//         <div className="grid grid-flow-col grid-cols-12 items-center mt-1">
//           <p className="text-black/60 text-sm truncate col-span-9 mb-0">
//             {bytesToSize(file?.size)}
//           </p>
//           <span className="text-black/60 text-[12px] truncate col-span-3 text-end ">
//             {`${uploadProgress[file.name]}%`}
//           </span>
//         </div>
//         <div className="">
//           {uploadProgress[file.name] && (
//             <Progress
//               className=""
//               showInfo={false}
//               percent={uploadProgress[file.name]}
//               status={uploadProgress[file.name] === 100 ? "success" : "active"}
//               strokeColor={"#00AAE3"}
//             />
//           )}
//         </div>
//       </div>
//     </div>
//     // <div style={thumb} key={file.name} className=" w-1/2 md:w-4/12 lg:w-3/12">
//     //   <div style={thumbInner}>
//     //     <img
//     //       src={URL.createObjectURL(file)}
//     //       style={img}
//     //       alt={file.name}
//     //       // Revoke data URI after image is loaded
//     //       onLoad={() => {
//     //         URL.revokeObjectURL(file.preview);
//     //       }}
//     //     />
//     //     <DeleteIcon
//     //       className="h-5 w-5 fill-sandee-grey absolute top-1 right-1"
//     //       onClick={() => deleteFile(file)}
//     //       // style={{ color: "gray", position: "absolute", right: 0 }}
//     //     />
//     //   </div>
//     // </div>
//   ));

//   useEffect(() => {
//     // Call the onChange prop with the updated files array

//     onChange(files);
//   }, [files, onChange]);

//   return (
//     <>
//       <div className="shadow-[0px_33px_33px_0px_#00000017]  my-5 text-sm min-h-40 rounded-lg p-5">
//         <p className="text-center text-lg font-bold mb-3">Upload Image</p>
//         <div {...getRootProps({ className: "dropzone" })} className="">
//           <input {...getInputProps()} />

//           <div className="border-2 border-[#B1BFD0] border-dashed rounded-lg py-3 flex justify-center items-center flex-col text-sm ">
//             <Image src={Picture} width={100} height={100} alt="Picture icon" />
//             <p className="font-medium">
//               Drop your image here, or{" "}
//               <span className="text-sandee-blue">browse</span>
//             </p>
//             <p className="text-[12px] text-black/60">
//               {" "}
//               Supports: PNG, JPG, JPEG, WEBP
//             </p>
//           </div>
//         </div>
//         <p className="text-lg text-[#959595] text-center mt-4 font-semibold">
//           • Recommended 1600*1800{" "}
//         </p>
//         {thumbs}
//       </div>
//     </>
//   );
// }
"use client";
import React, { useEffect, useState, useCallback } from "react";
import { useDropzone } from "react-dropzone";
import {
  AddImageIcon,
  CheckCircleIcon,
  CloseCircleIcon,
  DeleteIcon,
  PauseCircleIcon,
} from "../social-icons/icons";
import Image from "next/image";
import Picture from "../../public/static/images/Picture.png";
import { Progress } from "antd";
import { altText } from "@/helper/functions";

export function bytesToSize(bytes) {
  const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
  if (bytes === 0) return "n/a";
  const i = parseInt(Math.floor(Math.log(bytes) / Math.log(1024)), 10);
  return i === 0 ? `${bytes} ${sizes[i]}` : `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
}

export default function Previews({ onChange = (v) => { }, setFiles, files }) {
  const [uploadProgress, setUploadProgress] = useState({});
  const [filePreviews, setFilePreviews] = useState({});

  // Use dropzone to handle file drop
  const { getRootProps, getInputProps } = useDropzone({
    accept: { "image/jpeg": [], "image/jpg": [], "image/png": [] },
    onDrop: useCallback(
      (acceptedFiles) => {
        const newPreviews = {};

        acceptedFiles.forEach((file) => {
          const previewUrl = URL.createObjectURL(file);
          newPreviews[file.name] = previewUrl;
          simulateUploadProgress(file);
        });

        setFiles((prevFiles) => [...prevFiles, ...acceptedFiles]);
        setFilePreviews((prev) => ({ ...prev, ...newPreviews }));
      },
      [setFiles]
    ),
  });

  const simulateUploadProgress = (file) => {
    let progress = 0;
    const timer = setInterval(() => {
      progress += 10;
      setUploadProgress((prevProgress) => ({
        ...prevProgress,
        [file.name]: progress,
      }));

      if (progress >= 100) {
        clearInterval(timer);
      }
    }, 200);
  };

  const deleteFile = useCallback((file) => {
    setFiles((prevFiles) => prevFiles.filter((f) => f !== file));
    setUploadProgress((prevProgress) => {
      const newProgress = { ...prevProgress };
      delete newProgress[file.name];
      return newProgress;
    });

    setFilePreviews((prevPreviews) => {
      URL.revokeObjectURL(prevPreviews[file.name]);
      const newPreviews = { ...prevPreviews };
      delete newPreviews[file.name];
      return newPreviews;
    });
  }, [setFiles]);

  // Prevent unnecessary rerenders using useCallback for mapped JSX
  const thumbs = useCallback(
    files.map((file) => (
      <div
        className="bg-[#F7F7F7] p-2 grid md:grid-flow-col grid-flow-row md:grid-cols-12 gap-y-2 my-4 rounded-lg"
        key={file.name}
      >
        <div className="relative md:w-[70px] md:h-[70px] h-[150px]  md:col-span-2">
          <Image
            src={filePreviews[file.name]} // Use cached preview URL
            fill
            alt={altText(file)}
            className="object-cover rounded-md"
          />
        </div>
        <div className="w-full  md:col-span-10 ">
          <div className="grid grid-flow-col grid-cols-12">
            <p className="text-base font-medium truncate w-[90%] col-span-10">
              {file.name}
            </p>
            <div className="flex justify-end gap-x-2 col-span-2">
              <div>
                {uploadProgress[file.name] === 100 ? (
                  <CheckCircleIcon />
                ) : (
                  <PauseCircleIcon />
                )}
              </div>
              <div
                className="cursor-pointer hover:scale-110"
                onClick={() => deleteFile(file)}
              >
                <CloseCircleIcon />
              </div>
            </div>
          </div>

          <div className="grid grid-flow-col grid-cols-12 items-center mt-1">
            <p className="text-black/60 text-sm truncate col-span-9 mb-0">
              {bytesToSize(file?.size)}
            </p>
            <span className="text-black/60 text-[12px] truncate col-span-3 text-end">
              {`${uploadProgress[file.name]}%`}
            </span>
          </div>
          <div>
            {uploadProgress[file.name] && (
              <Progress
                className=""
                showInfo={false}
                percent={uploadProgress[file.name]}
                status={uploadProgress[file.name] === 100 ? "success" : "active"}
                strokeColor={"#00AAE3"}
              />
            )}
          </div>
        </div>
      </div>
    )),
    [files, filePreviews, uploadProgress, deleteFile]
  );

  // Callbacks to notify changes to parent component (if needed)
  useEffect(() => {
    onChange(files);

    // Cleanup object URLs when component unmounts
    return () => {
      Object.values(filePreviews).forEach((url) => URL.revokeObjectURL(url));
    };
  }, [files, onChange, filePreviews]);

  return (
    <>
      <div className="shadow-[0px_33px_33px_0px_#00000017]  my-5 text-sm min-h-40 rounded-lg p-5">
        <p className="text-center text-lg font-bold mb-3">Upload Image</p>
        <div {...getRootProps({ className: "dropzone" })} className="">
          <input {...getInputProps()} />
          <div className="border-2 border-[#B1BFD0] border-dashed rounded-lg py-3 flex justify-center items-center flex-col text-sm ">
            <Image src={Picture} width={100} height={100} alt="Sandee icon" />
            <p className="font-medium">
              Drop your image here, or{" "}
              <span className="text-sandee-blue">browse</span>
            </p>
            <p className="text-[12px] text-black/60">
              Supports: PNG, JPG, JPEG, WEBP
            </p>
          </div>
        </div>
        <p className="text-lg text-[#959595] text-center mt-4 font-semibold">
          • Recommended 1600*1800{" "}
        </p>
        {thumbs}
      </div>
    </>
  );
}
