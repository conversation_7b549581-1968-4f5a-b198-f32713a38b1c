"use client"
import React, { memo } from 'react';
import { FillStarIcon, HalfStarIcon, OutlineStar } from '../social-icons/icons';


const ReadOnlyRating = ({ rating ,width='20',height='20'}) => {
  const starCount = 5;
  const fullStars = Math?.floor(rating);
  const hasHalfStar = rating % 1 !== 0;
  return (
    <div className="rating flex gap-1">
      {[...Array(starCount)].map((_, idx) =>  hasHalfStar && idx === fullStars ? <HalfStarIcon key={idx} width={width} height={height} color='#FF9635'/> :  idx < fullStars ? <FillStarIcon key={idx} width={width} height={height} color='#FF9635'/> : <OutlineStar key={idx} width={width} height={height} color='#FF9635'/>)}
    </div>
  );
};

export default memo(ReadOnlyRating);