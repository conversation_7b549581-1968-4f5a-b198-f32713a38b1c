'use client'
import React, { useEffect, useState } from 'react'
import NameTitle from './NameTitle'
import ReviewCard from '../BeachPage/ReviewCard'
import { FillStarIcon, StarIcon } from '../social-icons/icons'
import CustomProgressBar from './CustomProgressBar'
import Pagination from './Pagination'
import { CustomGrid } from '../Custom-Display'
import { API_BASE_URL, buildQueryString, FinalImageGenerator } from '@/helper/functions'
import axios from 'axios'
import { BeachCardSkeleton } from '../Cards/BeachCard'


const getReviewList = async (query, params) => {
    // console.log(`${API_BASE_URL}beachReview/beaches/santa-monica-beach${buildQueryString(
    //     query
    // )}`)
    // /beachReview/beaches/india/gujarat/dwarka/dwarka-beach
    const APIShakiestCountries = `${API_BASE_URL}/beachReview/beaches/${params?.countrySlug}/${params?.stateSlug}/${params?.citySlug}/${params?.nameSlug}${buildQueryString(
        query
    )
        }`;
    const response = await axios.get(APIShakiestCountries);
    // console.log(response?.data)
    return response?.data;
};

// const getReviewAnalysis = async (query, params) => {
//     // console.log(`${API_BASE_URL}beachReview/beaches/santa-monica-beach${buildQueryString(
//     //     query
//     // )}`)
//     // //beachReview/getBeachReviewAnalytics/india/gujarat/dwarka/dwarka-beach
//     const APIShakiestCountries = `${API_BASE_URL}/beachReview/getBeachReviewAnalytics/${params?.countrySlug}/${params?.stateSlug}/${params?.citySlug}/${params?.nameSlug}${buildQueryString(
//         query
//     )}`;
//     const response = await axios.get(APIShakiestCountries);
//     // console.log(response?.data)
//     return response?.data;
// };
const ReviewSection = ({
    countryData = [],
    totalRecords = 0,
    params,
    beachData,
    reviewAnalysis = {},
    dataTestid = "review-section",

}) => {
    const [loading, setLoading] = useState(true);
    const [total, setTotal] = useState(totalRecords || 0);
    // const [query, setQuery] = useState("");
    const [CurrentBeach, setCurrentBeach] = useState(countryData);
    const [reviewAnalisisData, setReviewAnalisisData] = useState(reviewAnalysis);
    const [currentPage, setCurrentPage] = useState(1);
    // const [refresh, setRefresh] = useState(true);

    const FetchOrSetBeach = async () => {
        setLoading(true);
        // if (!!query) {
        //     const AllBeachesFilterResponse = await getReviewList({
        //         page: currentPage,
        //         // searchQuery: query,
        //         limit: 3,
        //     }, params);
        //     console.log(AllBeachesFilterResponse)
        //     setTotal(AllBeachesFilterResponse?.data?.count);
        //     setCurrentBeach(AllBeachesFilterResponse?.data?.rows);
        //     return setLoading(false);
        // }

        const AllBeachesResponse = await getReviewList({
            page: currentPage,
            limit: 3,
        }, params);

        setTotal(AllBeachesResponse?.data?.count);
        setCurrentBeach(AllBeachesResponse?.data?.rows);
        setLoading(false);
    };
    useEffect(() => {
        FetchOrSetBeach();
    }, [currentPage]);
    // useEffect(() => {
    //     const dataAnalysis = getReviewAnalysis({}, params);
    //     console.log(dataAnalysis, "dataAnalysis")
    //     setReviewAnalisisData(dataAnalysis?.data)
    // }, []);

    // useEffect(() => {
    //     const getData = setTimeout(() => {
    //         // if (!!!query) {
    //         //   return;
    //         // }
    //         setRefresh((prev) => !prev);
    //     }, 400);

    //     return () => clearTimeout(getData);
    // }, []);
    // const dummayData = [
    //     {
    //         rating: 5,
    //         percentage: 100,
    //         count: 10
    //     },
    //     {
    //         rating: 4,
    //         percentage: 50,
    //     },
    //     {
    //         rating: 3,
    //         percentage: 100,
    //     },
    //     {
    //         rating: 2,
    //         percentage: 70,
    //     },
    //     {
    //         rating: 1,
    //         percentage: 20,
    //     }
    // ]
    // Transform the ratings object into an array
    const ratingsArray =
        reviewAnalisisData?.ratings ? Object?.keys(reviewAnalisisData?.ratings)?.map((key) => ({
            rating: key,
            count: reviewAnalisisData?.ratings?.[key]?.count,
            percentage: reviewAnalisisData?.ratings?.[key]?.percentage
        })) : [];
    return (
        total > 0 ?
            <div data-testid={dataTestid}>
                <NameTitle
                    className="mt-8"
                    description={`Showing ${reviewAnalisisData?.totalReviews || 0} verified comments`}
                    name={`Reviews of  ${beachData?.name}`}
                    type={2}
                    dataTestid="review-section-title"
                />

                {/* <ReviewCard /> */}
                <div data-testid="review-section-summary" className='flex flex-row gap-y-5 py-6 w-full justify-between'>
                    <div className='flex flex-col gap-4' data-testid="review-section-summary-left">
                        <div className='flex items-center gap-1' data-testid="review-section-stars">
                            {/* Render stars based on the rounded average rating */}
                            {[...Array(5)]?.map((_, index) => {
                                const averageRatingRounded = Math?.round(parseFloat(reviewAnalisisData?.averageRating || 0));
                                return (
                                    <StarIcon
                                        data-testid={`review-section-star-${index}`}
                                        key={index} fill={index < averageRatingRounded ? "#E49D3E" : "#E8E8E8"} />
                                );
                            })}
                            <div data-testid="review-section-average-rating" className='ml-2 text-[#7D7D7D] font-semibold'>({reviewAnalisisData?.averageRating || 0} out of 5)</div>
                        </div>
                        {/* <span data-testid="review-section-total-reviews" className='text-[#7D7D7D] font-semibold'>Based on {reviewAnalisisData?.totalReviews || 0} {reviewAnalisisData?.totalReviews > 1 ? "reviews" : "review"}</span> */}
                        <p data-testid="review-section-total-reviews" className='text-[#7D7D7D] font-semibold'>Based on <span data-testid="review-section-total-reviews-count" className='font-bold'>{reviewAnalisisData?.totalReviews || 0}</span> {reviewAnalisisData?.totalReviews > 1 ? "reviews" : "review"}</p>
                    </div>
                    <div className='w-1/2' data-testid="review-section-summary-right">
                        {ratingsArray?.map((i, index) => {
                            return (
                                <div
                                    className="grid grid-flow-col items-center gap-x-3 my-2"
                                    key={index}
                                    data-testid={`review-section-rating-${index}`}
                                >
                                    <span className="mt-1 col-span-2 font-semibold"
                                        data-testid={`review-section-rating-label-${index}`}
                                    >{i?.rating} star</span>
                                    <div className="col-span-3"
                                        data-testid={`review-section-rating-bar-${index}`}
                                    >
                                        <CustomProgressBar percent={i?.percentage} />
                                    </div>
                                    <span className="font-semibold"
                                        data-testid={`review-section-rating-percentage-${index}`}
                                    >{`${i?.percentage?.toFixed(0)}%`}</span>
                                </div>
                            );
                        })}
                    </div>
                </div>

                <div>
                    {/* <ReviewCard /> */}
                    {loading ? (
                        <CustomGrid
                            className="!my-5 gap-4 sm:gap-8"
                            data={Array(3).fill(1)}
                            Component={BeachCardSkeleton}
                            dataTestid="review-section-loading"
                            xs={1}
                            sm={1}
                            md={1}
                            lg={1}
                            xl={1}
                            xxl={1}
                            xxxl={1}
                        />
                    ) : total ? (
                        <>
                            <CustomGrid
                                data={CurrentBeach}
                                className=" gap-4 sm:gap-8"
                                Component={({ data: dataB, index }) => {
                                    const dataProps = { ...dataB };
                                    //   dataProps.link = `/ shark / ${ dataProps?.slug }`;
                                    //   dataProps.imageSrc = FinalImageGenerator(dataProps?.image);
                                    //   dataProps.images = [dataProps?.image];
                                    //   dataProps.image = FinalImageGenerator(dataProps?.image);
                                    return ReviewCard({
                                        data: { ...dataProps },
                                        index
                                    });
                                }}
                                dataTestid="review-section-grid"
                                xs={1}
                                sm={1}
                                md={1}
                                lg={1}
                                xl={1}
                                xxl={1}
                                xxxl={1}
                            />
                        </>
                    ) : (
                        <p data-testid="review-section-no-data" className=" px-2 py-5 text-error-red-600  border-error-red-400 bg-error-red-50 border-2 rounded-sandee text-center mt-6 mb-10">
                            No Data Found for your applied filters
                        </p>
                    )}
                    <Pagination
                        className="pagination-bar"
                        currentPage={currentPage}
                        totalCount={total}
                        pageSize={3}
                        dataTestid="review-section-pagination"
                        onPageChange={(page) => {
                            setCurrentPage(page);
                            // setRefresh((prev) => !prev);
                        }}
                    />
                </div>
            </div>
            : <></>
    )
}

export default ReviewSection