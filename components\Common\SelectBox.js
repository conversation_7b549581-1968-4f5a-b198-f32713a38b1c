"use client";
import React, { useEffect, useRef, useState } from "react";
// import { ArrowDownIcon } from "../icons/Icons";
import Link from "next/link";
import { CancelIcon } from "../social-icons/icons";

const SelectBox = ({
  options,
  deafultSelectSort,
  placeholder,
  getSortingVal = () => { },
  isLink = false,
  className = "",
  menuClassName = "",
  baseLink = "/",
  buttonText = null,
  disabled = false,
}) => {
  const isOpenRef = useRef(null);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState(deafultSelectSort || "");
  const handleSelect = (data) => {
    if (data?.value !== selectedOption?.value) {
      setSelectedOption(data);
      getSortingVal(data?.value);
    }
    setIsOpen(false);
  };
  const handleClickOutside = (event) => {
    if (isOpenRef?.current && !isOpenRef.current.contains(event.target)) {
      setIsOpen(false);
    }
  };
  useEffect(() => {
    document?.addEventListener("mousedown", handleClickOutside);
    return () => {
      document?.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);
  return (
    <>
      <div className="relative">
        {/* <button
          type="button"
          //   className="relative w-[150px] cursor-default rounded-xl bg-transparent py-3 px-3 text-left border border-light-gray-900 text-gray-900 shadow-sm sm:text-sm sm:leading-6"
          className={`${className} w-[150px] h-10 px-3  text-sandee-blue   border-sandee-blue border rounded-xl`}
          aria-haspopup="listbox"
          aria-expanded="true"
          aria-labelledby="listbox-label"
          onClick={() => {
            setIsOpen((prv) => !prv);
          }}
        >
          <div className="flex items-center justify-between">
            <span
              className="block truncate "
              title={
                selectedOption?.label
                  ? selectedOption?.label
                  : deafultSelectSort?.label
                    ? deafultSelectSort?.label
                    : placeholder
              }
            >
              {selectedOption?.label
                ? selectedOption?.label
                : deafultSelectSort?.label
                  ? deafultSelectSort?.label
                  : placeholder}
            </span>
            <div className="flex items-center">
              {selectedOption && !deafultSelectSort ? <div onClick={(e) => { setSelectedOption(null); getSortingVal(""); e.stopPropagation(); }}> <CancelIcon
                width={14}
                height={14}
                fill={"black"}
                strokeWidth={16}
                className="bg-gray-300 rounded-lg transition-transform duration-300 transform hover:scale-105"
              /></div> : null}

              <svg
                fill="#00aae3"
                height="15"
                width="10"
                className={` outline-1 outline-sandee-blue ml-2 transition-all duration-100 ${isOpen ? " -rotate-90" : ""
                  }`}
                version="1.1"
                id="Layer_1"
                xmlns="http://www.w3.org/2000/svg"
                xmlnsXlink="http://www.w3.org/1999/xlink"
                viewBox="0 0 330.002 330.002"
                xmlSpace="preserve"
                transform="rotate(180)"
              >
                <g id="SVGRepo_bgCarrier" strokeWidth={0} />
                <g
                  id="SVGRepo_tracerCarrier"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <g id="SVGRepo_iconCarrier">
                  {" "}
                  <path
                    id="XMLID_105_"
                    d="M324.001,209.25L173.997,96.75c-5.334-4-12.667-4-18,0L6.001,209.25c-6.627,4.971-7.971,14.373-3,21 c2.947,3.93,7.451,6.001,12.012,6.001c3.131,0,6.29-0.978,8.988-3.001L164.998,127.5l141.003,105.75c6.629,4.972,16.03,3.627,21-3 C331.972,223.623,330.628,214.221,324.001,209.25z"
                  />{" "}
                </g>
              </svg>
            </div>
          </div>
        </button> */}
        {/* <button
          className={`${className} p-[13px] border-2 border-[#DEDEDE] rounded-[10px] focus:outline-none outline-none  focus:ring-0 ring-0 focus:border-sandee-blue`}
          onClick={() => {
            setIsOpen((prv) => !prv);
          }}
        >
          {buttonText ? buttonText : <svg
            width={19}
            height={11}
            viewBox="0 0 19 11"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M6.5 10.25C6.5 10.0511 6.57902 9.86032 6.71967 9.71967C6.86032 9.57902 7.05109 9.5 7.25 9.5H11.75C11.9489 9.5 12.1397 9.57902 12.2803 9.71967C12.421 9.86032 12.5 10.0511 12.5 10.25C12.5 10.4489 12.421 10.6397 12.2803 10.7803C12.1397 10.921 11.9489 11 11.75 11H7.25C7.05109 11 6.86032 10.921 6.71967 10.7803C6.57902 10.6397 6.5 10.4489 6.5 10.25ZM3.5 5.75C3.5 5.55109 3.57902 5.36032 3.71967 5.21967C3.86032 5.07902 4.05109 5 4.25 5H14.75C14.9489 5 15.1397 5.07902 15.2803 5.21967C15.421 5.36032 15.5 5.55109 15.5 5.75C15.5 5.94891 15.421 6.13968 15.2803 6.28033C15.1397 6.42098 14.9489 6.5 14.75 6.5H4.25C4.05109 6.5 3.86032 6.42098 3.71967 6.28033C3.57902 6.13968 3.5 5.94891 3.5 5.75ZM0.5 1.25C0.5 1.05109 0.579018 0.860322 0.71967 0.71967C0.860322 0.579018 1.05109 0.5 1.25 0.5H17.75C17.9489 0.5 18.1397 0.579018 18.2803 0.71967C18.421 0.860322 18.5 1.05109 18.5 1.25C18.5 1.44891 18.421 1.63968 18.2803 1.78033C18.1397 1.92098 17.9489 2 17.75 2H1.25C1.05109 2 0.860322 1.92098 0.71967 1.78033C0.579018 1.63968 0.5 1.44891 0.5 1.25Z"
              fill="#1A1A1A"
            />
          </svg>}
        </button> */}
        <button
          disabled={disabled}
          className={`${className} p-[13px] border-2 border-[#DEDEDE] rounded-[10px] focus:outline-none outline-none  focus:ring-0 ring-0 focus:border-sandee-blue`}
          onClick={() => {
            setIsOpen((prv) => !prv);
          }}
        >
          {buttonText ? buttonText : <svg
            width={19}
            height={11}
            viewBox="0 0 19 11"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M6.5 10.25C6.5 10.0511 6.57902 9.86032 6.71967 9.71967C6.86032 9.57902 7.05109 9.5 7.25 9.5H11.75C11.9489 9.5 12.1397 9.57902 12.2803 9.71967C12.421 9.86032 12.5 10.0511 12.5 10.25C12.5 10.4489 12.421 10.6397 12.2803 10.7803C12.1397 10.921 11.9489 11 11.75 11H7.25C7.05109 11 6.86032 10.921 6.71967 10.7803C6.57902 10.6397 6.5 10.4489 6.5 10.25ZM3.5 5.75C3.5 5.55109 3.57902 5.36032 3.71967 5.21967C3.86032 5.07902 4.05109 5 4.25 5H14.75C14.9489 5 15.1397 5.07902 15.2803 5.21967C15.421 5.36032 15.5 5.55109 15.5 5.75C15.5 5.94891 15.421 6.13968 15.2803 6.28033C15.1397 6.42098 14.9489 6.5 14.75 6.5H4.25C4.05109 6.5 3.86032 6.42098 3.71967 6.28033C3.57902 6.13968 3.5 5.94891 3.5 5.75ZM0.5 1.25C0.5 1.05109 0.579018 0.860322 0.71967 0.71967C0.860322 0.579018 1.05109 0.5 1.25 0.5H17.75C17.9489 0.5 18.1397 0.579018 18.2803 0.71967C18.421 0.860322 18.5 1.05109 18.5 1.25C18.5 1.44891 18.421 1.63968 18.2803 1.78033C18.1397 1.92098 17.9489 2 17.75 2H1.25C1.05109 2 0.860322 1.92098 0.71967 1.78033C0.579018 1.63968 0.5 1.44891 0.5 1.25Z"
              fill="#1A1A1A"
            />
          </svg>}
        </button>
        <ul
          ref={isOpenRef}
          className={`${isOpen ? "visible" : "invisible"
            } ${menuClassName}  absolute z-10 right-0 !w-[150px] mt-1 max-h-56 overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm`}
          tabIndex={-1}
          role="listbox"
          aria-labelledby="listbox-label"
          aria-activedescendant="listbox-option-3"
        >
          {options?.map((i, indx) => {
            return (
              <li
                className={`text-gray-900 relative cursor-pointer mb-0.5 select-none py-2 pl-3 pr-9 ${selectedOption?.value === i?.value
                  ? "bg-sandee-blue text-white"
                  : "hover:bg-sandee-blue hover:text-white "
                  }`}
                id="listbox-option-0"
                key={indx}
                onClick={() => {
                  handleSelect(i);
                }}
              >
                {isLink ? (
                  <Link
                    key={indx}
                    hrefLang="en-us"
                    href={`/${i?.value?.toLowerCase()}/tools`}
                  >
                    <span
                      className="font-normal ml-3 block truncate"
                      onClick={() => {
                        handleSelect(i);
                      }}
                    >
                      {i?.label}
                    </span>
                  </Link>
                ) : (
                  <span
                    className="font-normal ml-3 block truncate"
                    key={indx}
                    // onClick={() => {
                    //   handleSelect(i);
                    // }}
                    title={i?.label}
                  >
                    {i?.label}
                  </span>
                )}
              </li>
            );
          })}
        </ul>
      </div>
    </>
  );
};
export default SelectBox;
