"use client";
import {
  FacebookAuthProvider,
  GithubAuthProvider,
  GoogleAuthProvider,
  OAuthProvider,
  TwitterAuthProvider,
  signInWithPopup,
} from "firebase/auth";
import React, { useState } from "react";
// import { FaApple, FaFacebook, FaGithub, FaGoogle } from "react-icons/fa";
import { auth } from "./firebase";
import { GoogleIcon } from "../social-icons/icons";
import { postSocialAuth } from "@/app/(HomeHeader)/action";

const SocialLogin = ({
  loginButtons = [
    {
      providerName: "google",
      text: "Continue With Google",
      icon: <GoogleIcon className="h-6 w-6 fill-white" />,
    },
    // {
    //   providerName: "twitter",
    //   text: "Login With Twitter",
    //   icon: <FaTwitter className="tw-w-[26px] tw-h-[26px]" />,
    // },
    // {
    //   providerName: "facebook",
    //   text: "Login With Facebook",
    //   icon: <Facebook className="h-6 w-6 fill-white" />,
    // },
    // {
    //   providerName: "apple",
    //   text: "Login With APPle",
    //   icon: <FaApple className="tw-w-[26px] tw-h-[26px]" />,
    // },
    // {
    //   providerName: "github",
    //   text: "Login With Github",
    //   icon: <FaGithub className="tw-w-[26px] tw-h-[26px]" />,
    // },
  ],
  Onsuccess = () => {},
  OnError = () => {},
}) => {
  const [loading, setLoading] = useState(false);

  const authProviders = {
    google: {
      AuthProvider: GoogleAuthProvider,
      defaultScopes: ["email"],
    },
    github: {
      AuthProvider: GithubAuthProvider,
      defaultScopes: [],
    },
    apple: {
      AuthProvider: OAuthProvider,
      defaultScopes: [],
      callvalue: "apple.com",
    },
    facebook: {
      AuthProvider: FacebookAuthProvider,
      defaultScopes: [],
    },
    //   yahoo: {
    //     AuthProvider:Yahoo,
    //     defaultScopes: [],
    //   },
    twitter: {
      AuthProvider: TwitterAuthProvider,
      defaultScopes: [],
    },
    //   microsoft: {
    //     AuthProvider: Microso,
    //     defaultScopes: [],
    //   },
    // Add other providers as needed
  };
  // const [loading, setLoading] = useState(false);
  const handleDynamicLogin = async (config) => {
    const { providerName, customScopes } = config;
    const providerInfo = authProviders[providerName];

    if (!providerInfo) {
      throw new Error("Invalid provider name");
    }

    const { AuthProvider, defaultScopes, callvalue } = providerInfo;
    const provider = new AuthProvider(callvalue);
    const scopes = [...defaultScopes, ...(customScopes || [])];
    if (scopes.length > 0) {
      scopes.forEach((scope) => provider.addScope(scope));
    }

    try {
      setLoading(true);
      const result = await signInWithPopup(auth, provider);
      if (result && result.user && result.user.accessToken) {
        const user = result.user;
        const payload = {
          firebase_token: user?.accessToken,
        };
        localStorage.setItem("ItemToken", user.accessToken);

        const resultofSocialAuth = await postSocialAuth(payload);
        localStorage.setItem("token", resultofSocialAuth?.token);
        await Onsuccess({ data: resultofSocialAuth });
        setLoading(false);

        // API.sendRequest(
        //   CONSTANTS.API.auth.login,
        //   (res) => {
        //     if (res?.status == 200) {
        //       setAuthDetails(res?.token);
        //       window.location.assign(appRoot);
        //     }
        //   },
        //   payload,
        //   ""
        // );
      }
    } catch (error) {
      console.error(`${providerName} Login Error:`, error);
      // Handle the error as needed
    }
    setLoading(false);
  };

  return (
    <div className=" flex m-0 gap-3  flex-col">
      {loginButtons.map((button, index) => (
        <div
          key={index}
          className=" flex justify-center items-center align-middle"
        >
          <button
            className={`!flex !rounded-full !my-0 gap-3 px-4 login-button ${
              loading ? "animate-pulse" : ""
            }`}
            disabled={loading}
            onClick={() => {
              handleDynamicLogin({ providerName: button.providerName });
            }}
          >
            {button.icon}
            {button.text}
          </button>
        </div>
      ))}
    </div>
  );
};

export default SocialLogin;
