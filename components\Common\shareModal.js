"use client";
import React, { memo, useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Col } from "antd";
import useClipboard from "react-use-clipboard";
import Image from "next/image";
import InputButton from "./InputButton";
import {
  CancelIcon,
  CopyIcon,
  Facebook,
  Linkedin,
  Twitter,
  Youtube,
} from "../social-icons/icons";
import { blurDataURL, isMobileView } from "@/helper/functions";
import SocialIcon from "../social-icons";
import { CustomGrid } from "../Custom-Display";

const ShareModel = ({ modalOpen, setModalOpen, id, type = "", link = "#" }) => {
  const [copyUrl, setCopyUrl] = useState("");
  const [isCopied, setCopied] = useClipboard(copyUrl, {
    successDuration: 1500,
  });
  const [isClient, setIsClient] = useState(false); // Track if it's client-side
  // const [isMobile, setIsMobile] = useState(false); // Determine if it's mobile view
  const isMobileViews = isMobileView();
  useEffect(() => {
    // Mark as client-side
    setIsClient(true);

    // Determine if it's mobile view
    // setIsMobile(isMobileView());
    const url =
      process.env.NEXT_PUBLIC_ENV === "production"
        ? process.env.NEXT_PUBLIC_WEB_URL
        : process.env.NEXT_PUBLIC_DEV_WEB_URL;
    setCopyUrl(`${url}${link}`);
  }, [link, id]);

  // Share links
  const ShareLink = [
    {
      icon: "https://images.sandee.com/svg/email.svg", // Add Email icon
      link: `mailto:?subject=Sandee%20-%20Sharks%20Page%20|%20Map,%20Attacks,%20Species%20and%20More&body=${copyUrl}`, // Email sharing link with default subject
      name: "Email",
    },
    {
      icon: "https://images.sandee.com/svg/whatsapp.svg",
      link: !isMobileViews
        ? `https://web.whatsapp.com/send?text=${copyUrl}`
        : `https://api.whatsapp.com/send?text=${copyUrl}`,
      name: "WhatsApp",
    },
    {
      icon: "https://images.sandee.com/svg/instagram.svg", // Add Instagram icon
      link: `https://www.instagram.com/?url=${copyUrl}`, // Update Instagram link
      name: "Instagram",
    },
    {
      icon: "https://images.sandee.com/svg/facebook.svg",
      link: `https://facebook.com/sharer/sharer.php?u=${copyUrl}`,
      name: "Facebook",
    },

    {
      icon: "https://images.sandee.com/svg/linkedin.svg",
      link: `https://www.linkedin.com/cws/share?url=${copyUrl}`,
      name: "LinkedIn",
    },
    // {
    //     icon: "https://images.sandee.com/svg/x.svg",
    //     link: `http://www.twitter.com/share?url=${copyUrl}`,
    //     name: "X",
    // },
  ];

  // Toggle modal visibility
  const toggle = () => {
    setModalOpen((prev) => !prev);
  };

  if (!isClient) {
    return null; // Prevent server-side rendering
  }

  return (
    <Modal
      open={modalOpen}
      onCancel={toggle}
      footer={null}
      closable={false}
      centered
      width={460}
      className="authpage"
      bodyStyle={{ padding: 0 }}
    >
      <div className="bg-white rounded-xl h-full pb-6 px-4 pt-4">
        <div className="flex justify-between items-center pb-4">
          <p className="text-3xl font-semibold">Share</p>
          <div
            className="flex justify-end me-0  cursor-pointer"
            onClick={toggle}
          >
            <CancelIcon className="w-7 h-7 opacity-50" />
          </div>
        </div>
        <CustomGrid
          xs={5}
          sm={5}
          md={5}
          lg={5}
          xl={5}
          xxl={5}
          xxxl={5}
          data={ShareLink}
          className={"gap-2 xs:gap-2 "}
          Component={({ data: item }) => (
            <div>
              <a
                href={item.link}
                target="_blank"
                rel="noopener noreferrer"
                aria-label={item.name}
                title={item.name}
              >
                <div class="rounded-lg border border-light-gray-300 hover:lg:scale-[0.8] flex flex justify-center md:py-5 py-4">
                  <div className="relative w-8 h-8">
                    <Image
                      src={item.icon}
                      fill
                      className="relative"
                      alt="Sandee | Share Icon"
                      sizes="auto"
                      blurDataURL={blurDataURL(90, 90)}
                      placeholder="blur"
                    />
                  </div>
                  {/* <p class="text-light-gray-900 md:text-base text-12px mt-1 text-center">WhatsApp */}
                </div>
                <span className="hidden sm:flex md:text-[16px] 375:text-sm 320:text-[12px] flex justify-center mt-1">
                  {item.name}
                </span>
              </a>
            </div>
          )}
        />

        {/* <CustomRow gutter={[10, 10]} className=" min-w-full w-full">
                    {ShareLink.map((item, idx) => (
                        <CustomCol xs={5} sm={5} md={5} lg={5} total={5} key={idx} >
                            <div>
                                <a
                                    href={item.link}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    aria-label={item.name}
                                    title={item.name}
                                >
                                    <div class="rounded-lg border border-light-gray-300 hover:lg:scale-[0.8] flex flex justify-center md:py-5 py-4">
                                        <div className="relative w-8 h-8">
                                            <Image
                                                src={item.icon}
                                                fill
                                                className="relative"
                                                alt="Sandee | Share Icon"
                                                sizes="auto"
                                                blurDataURL={blurDataURL(90, 90)}
                                                placeholder="blur"
                                            />
                                        </div>
                                    </div>
                                    <span className="md:text-[16px] 375:text-sm 320:text-[12px] flex justify-center mt-1">
                                        {item.name}
                                    </span>
                                </a>
                            </div>
                        </CustomCol>
                    ))}
                   
                </CustomRow> */}
        <div className="w-full pt-2">
          {isCopied && (
            <Alert type="info" message="Copy to clipboard" className="py-1" />
          )}
          <div className="relative bg-gray-color rounded-[40px]">
            <div className="py-3 ps-3 pe-[5.5rem]">
              <p
                style={{
                  maxHeight: "30px", // Constrain height to a smaller size
                  lineHeight: "30px", // Align text properly if required
                }}
                className="mb-0 overflow-hidden whitespace-nowrap overflow-x-scroll hide-scrollbar-h-2"
              >
                {copyUrl}
              </p>
              <div
                className="absolute top-[15px] right-1 cursor-pointer"
                onClick={setCopied}
              >
                <CopyIcon />

                {/* <InputButton
                                        type="fill"
                                        title="Copy"
                                        className="bg-yellow-color px-4 rounded-full py-2"
                                        click={setCopied}
                                    /> */}
              </div>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default memo(ShareModel);
