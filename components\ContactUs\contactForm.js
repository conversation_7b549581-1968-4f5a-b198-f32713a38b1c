"use client";
import { ContactContactUS } from "@/app/(HomeHeader)/action";
import { notification } from "antd";
import React, { useState } from "react";

const ContactForm = () => {
  const [loading, setLoading] = useState(false);
  return (
    <div className="containerB">
      <h2 className="heading">Get in Touch</h2>
      <form
        className="form"
        id="contactUsForm"
        onSubmit={async (e) => {
          e.preventDefault();
          const payload = {
            firstName: e.target?.firstName?.value,
            lastName: e.target?.lastName?.value,
            email: e.target?.email?.value,
            message: e.target?.message?.value,
          };

          setLoading(true);
          const profile = await ContactContactUS(payload);
          if (profile?.data) {
            notification.success({ message: profile?.message });
            e.target.reset();
          }
          setLoading(false);
        }}
      >
        <input
          placeholder="First Name"
          id="firstName"
          name="firstName"
          type="text"
          className="input"
          required
        />
        <input
          placeholder="Last Name"
          id="lastName"
          name="lastName"
          type="text"
          className="input"
          required
        />
        <input
          placeholder="Email"
          id="email"
          name="email"
          type="email"
          pattern="[^@\s]+@[^@\s]+\.[^@\s]+"
          title="Invalid email address"
          className="input"
          required
        />
        <textarea
          rows={4}
          placeholder="Message"
          id="message"
          name="message"
          type=""
          className="input"
          required
        />
        <input
          defaultValue="Submit"
          disabled={loading}
          type="submit"
          className={` login-button ${
            loading ? " animate-pulse !bg-sandee-grey" : ""
          }`}
        />
      </form>
    </div>
  );
};

export default ContactForm;
