"use client";
import React, { useState } from "react";
import { CustomGrid } from "../Custom-Display";
import CountryCard from "../Cards/CountryCard";
import { FinalImageGenerator } from "@/helper/functions";
import Pagination from "../Common/Pagination";
import { fuzzySearchTrie } from "../Common/FuzzySearch";
import SelectBox from "../Common/SelectBox";
import NameTitle from "../Common/NameTitle";

function sortCountriesByAsc(countries) {
  return countries.slice().sort((a, b) => a.name.localeCompare(b.name));
}

function sortCountriesByDesc(countries) {
  return countries.slice().sort((a, b) => b.name.localeCompare(a.name));
}

const Countries_All_Country_Section = ({ FullCount, AllCountries }) => {
  const limit = FullCount?.limit ?? 20;
  // const total = FullCount?.count ?? 50;

  // const [searchedCountries , setSearchedCountries]= []
  const [query, setQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [sortOrder, setsortOrder] = useState("ASC");

  const globaloSorterFunction = (country) => {
    if (sortOrder === "ASC") {
      return sortCountriesByAsc(country);
    }
    return sortCountriesByDesc(country);
  };
  return (
    <section className=" my-5">
      <div className="flex justify-between flex-col md:flex-row mt-5 gap-5">
        <div className="flex flex-col gap-2 w-full md:w-6/12">
          <NameTitle
            type={2}
            name="All Beach Countries"
            description="EXPLORE BEACH DESTINATIONS"
          />
        </div>

        <div className=" relative  flex flex-wrap justify-between md:justify-end items-start gap-2">
          <div className="relative flex justify-between items-center max-w-[450px] min-w-[140px] md:min-w-[300px]">
            <div className="absolute inset-y-0 start-0 flex items-center ps-2 pe-2 ms-2 pointer-events-none rounded-full">
              <svg
                className="w-4 h-4 text-[#7D7D7D]  "
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 20 20"
              >
                <path
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"
                />
              </svg>
            </div>
            <input
              type="text"
              id="simple-search"
              className={`transition-all duration-500  focus:outline-none outline-none  focus:ring-0 ring-0 focus:border-sandee-blue  bg-white border-2 border-[#DEDEDE] block w-full ps-10 sm:text-[20px]  rounded-xl text-gray-600 !pl-[50px] lg:text-sandee-sm  !text-sandee-sm p-2 `}
              placeholder="Search"
              value={query}
              onChange={(e) => {
                setQuery(e.target.value);
                setCurrentPage(1);
                // setRefresh((prev) => !prev);
              }}
            />
          </div>

          <div className=" ">
            <SelectBox
              options={[
                {
                  value: "ASC",
                  label: "A to Z",
                },
                {
                  value: "DESC",
                  label: "Z to A",
                },
              ]}
              deafultSelectSort={{
                value: "ASC",
                label: "A to Z",
              }}
              getSortingVal={(v) => {
                setsortOrder(v);
              }}
              menuClassName="w-full"
            />
          </div>
        </div>
      </div>
      {fuzzySearchTrie(query, AllCountries)?.length ? (
        <CustomGrid
          className="!mb-5 mt-[13px] gap-4"
          Component={({ data }) => {
            const dataProps = { ...data };
            dataProps.images = [dataProps?.image];
            dataProps.imageurl = FinalImageGenerator(dataProps?.image);
            // dataProps.image = FinalImageGenerator(dataProps?.image);
            return (
              <CountryCard
                copyRightsData={dataProps.images}
                // className="md:aspect-[390/303] aspect-square"
                data={{
                  ...dataProps,
                  link: `/${dataProps?.slug}`,
                }}
              // className="h-[190px] lg:h-[180px]"
              />
            );
            // return CountryCard({
            //   data: {
            //     ...dataProps,
            //     link: `/${dataProps?.slug}`,
            //   },
            //   copyRightsData: [data?.image],
            //   className: "h-[190px] lg:h-[180px] ",
            // });
          }}
          // data={AllCountries?.filter((el) =>
          //   el?.name?.toLowerCase()?.includes(query?.toLowerCase())
          // )?.slice(limit * (currentPage - 1), limit * (currentPage - 1) + limit)}
          data={globaloSorterFunction([
            ...fuzzySearchTrie(query, AllCountries),
          ])?.slice(
            limit * (currentPage - 1),
            limit * (currentPage - 1) + limit
          )}
          xs={2}
          sm={2}
          md={3}
          lg={4}
          xl={5}
          xxl={5}
          xxxl={5}
        />
      ) : (
        <p className=" px-2 py-5 text-error-red-600  border-error-red-400 bg-error-red-50 border-2 rounded-sandee text-center mt-6 mb-10">
          No Data Found for your applied filters
        </p>
      )}
      <Pagination
        className="pagination-bar"
        currentPage={currentPage}
        // siblingCount={3}
        // totalCount={
        //   AllCountries?.filter((el) =>
        //     el?.name?.toLowerCase()?.includes(query?.toLowerCase())
        //   )?.length
        // }
        totalCount={fuzzySearchTrie(query, AllCountries)?.length}
        pageSize={FullCount?.limit}
        onPageChange={(page) => {
          setCurrentPage(page);
          // setRefresh((prev) => !prev);
        }}
      />
    </section>
  );
};

export default Countries_All_Country_Section;

export function fuzzySearch(query, list) {
  query = query.toLowerCase();
  return list.filter((item) => {
    const itemName = item.name.toLowerCase();
    let j = 0;
    for (let i = 0; i < itemName.length; i++) {
      if (itemName[i] === query[j]) {
        j++;
      }
      if (j === query.length) {
        return true;
      }
    }
    return false;
  });
}
