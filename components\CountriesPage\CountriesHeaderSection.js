// import Image from "next/image";
// import React from "react";
// import { CustomContainer } from "../Custom-Display";

// const CountriesHeaderSection = ({ imageSrc, name, description }) => {
//   return (
//     <header className=" items-center  justify-around  h-[550px] ">
//       <div className="relative h-[550px]">
//         <div className="absolute left-0 top-0  h-[550px] w-full">
//           <Image
//             priority
//             src={imageSrc}
//             alt="HomePage Sandee Photo "
//             className=" object-cover bg-blend-overlay rounded-b-[40px]"
//             fill
//           />
//         </div>
//         <div className="absolute flex justify-center items-end  h-[550px] w-full black-layer rounded-b-[40px]">
//           <div>
//             <CustomContainer className=" flex justify-center items-center flex-col md:flex-row   w-full">
//               <h1 className=" text-white font-bold text-sandee-4xl   w-full md:w-1/2  sm:text-sandee-4xl md:text-sandee-6xl xl:text-sandee-7xl 3xl:text-sandee-8xl uppercase text-center md:text-start">
//                 {name}
//               </h1>
//               <p className=" text-white font-normal  text-sandee-18   w-full md:w-1/2 text-center md:text-start">
//                 {description}
//               </p>
//             </CustomContainer>
//           </div>
//         </div>
//       </div>
//     </header>
//   );
// };

// export default CountriesHeaderSection;
import Image from "next/image";
import React from "react";
import { CustomContainer } from "../Custom-Display";

const CountriesHeaderSection = ({ imageSrc, name, description }) => {
  return (
    <header className=" items-center  justify-around  md:h-[550px] h-[260px] ">
      <div className="relative md:h-[550px] h-[260px]">
        <div className="absolute left-0 top-0  md:h-[550px] h-[260px] w-full">
          <Image
            priority
            src={imageSrc}
            alt="Sandee | HomePage Sandee Photo"
            className=" object-cover bg-blend-overlay lg:rounded-b-none rounded-b-[40px]"
            fill
          />
        </div>
        <div className="absolute flex justify-center items-center  md:h-[550px] h-[260px] w-full lg:rounded-b-none rounded-b-[40px]">
          <h1 className=" text-white font-bold text-sandee-4xl  w-full md:w-1/2  sm:text-sandee-4xl md:text-sandee-6xl xl:text-sandee-7xl 3xl:text-sandee-8xl text-center">
            {name}
          </h1>
        </div>

        {/* <div className="absolute flex justify-center items-end  h-[550px] w-full black-layer rounded-b-[40px]">
          <div>
            <CustomContainer className=" flex justify-center items-center flex-col md:flex-row   w-full">
              <h1 className=" text-white font-bold text-sandee-4xl   w-full md:w-1/2  sm:text-sandee-4xl md:text-sandee-6xl xl:text-sandee-7xl 3xl:text-sandee-8xl uppercase text-center md:text-start">
                {name}
              </h1>
          
            </CustomContainer>
          </div>
        </div> */}
      </div>
    </header>
  );
};

export default CountriesHeaderSection;
