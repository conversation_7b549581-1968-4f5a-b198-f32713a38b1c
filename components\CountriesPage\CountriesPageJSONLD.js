import { SiteDataPageWise } from "@/data/siteMetadata";
import { head } from "lodash";
import React from "react";

const CountriesPageJSONLD = () => {
  const webPageSchema = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    url: "https://sandee.com/countries",
    name: SiteDataPageWise?.countries?.title,
    headline: SiteDataPageWise?.countries?.title,
    description: SiteDataPageWise?.countries?.description,
    publisher: {
      "@type": "Organization",
      name: "<PERSON><PERSON>",
    },
  };

  const CountriesBreadCumber = {
    "@context": "https://schema.org/",
    "@type": "BreadcrumbList",
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        name: "Home",
        item: `https://sandee.com/`,
      },
      {
        "@type": "ListItem",
        position: 2,
        name: "All Countries",
        item: `https://sandee.com/countries`,
      },
    ],
  };

  return (
    <>
      <script
        type="application/ld+json"
        id="webPageSchema"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(webPageSchema) }}
      ></script>
      <script
        type="application/ld+json"
        id="application/ld+jsonBeachBreadCumber"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(CountriesBreadCumber),
        }}
      ></script>
    </>
  );
};

export default CountriesPageJSONLD;
