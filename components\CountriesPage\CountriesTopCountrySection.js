// import { HomePageData } from "@/data/HomePageData";
// import React from "react";
// import { CustomGrid } from "../Custom-Display";
// import DetailedCountryCard from "../Cards/DetailedCountryCard";
// import NameTitle from "../Common/NameTitle";
// const Countries_Top_Country_Section = () => {
//   return (
//     <>
//       <NameTitle
//         // className="mt-8"
//         description={"Explore the worlds best beach destinations"}
//         name={"Best Beach Countries in the World"}
//         type={2}
//       />
//       <CustomGrid
//         className="mt-10 gap-4 sm:gap-8"
//         Component={DetailedCountryCard}
//         data={HomePageData.CountriesSection.cards?.filter(
//           (el) => el?.countries
//         )}
//         xs={2}
//         sm={2}
//         md={3}
//         lg={3}
//         xxl={3}
//         // xxl={5}
//         // xxxl={5}
//       />
//       {/* <div className="flex justify-center mb-6">
//         <Link href={HomePageData.CountriesSection.button_link}>
//           <button className=" text-sandee-blue active:text-white active:bg-sandee-blue border-2 border-sandee-blue  py-2 px-5 transition ease-in-out duration-300  px-10 text-xs font-medium  rounded-full active:scale-[0.7] hover:scale-110">
//             {HomePageData.CountriesSection.button_text}
//           </button>
//         </Link>
//       </div> */}
//     </>
//   );
// };

// export default Countries_Top_Country_Section;
import { HomePageData } from "@/data/HomePageData";
import React from "react";
import { CustomGrid } from "../Custom-Display";
import DetailedCountryCard from "../Cards/DetailedCountryCard";
import NameTitle from "../Common/NameTitle";
import CountryCard from "../Cards/CountryCard";
const Countries_Top_Country_Section = ({ data }) => {
  return (
    <>
      <NameTitle
        // className="mt-8"
        description={"Explore the worlds best beach destinations"}
        name={"Best Beach Countries in the World"}
        type={2}
      />
      <CustomGrid
        className="lg:mt-[13px] mt-3 gap-4 sm:gap-8"
        Component={({ data: dataProps }) => {
          // dataProps.copyRightsData = dataProps?.image;
          // dataProps.image = dataProps?.image?.imageUrl;
          dataProps.imageurl = dataProps?.image?.imageUrl;
          dataProps.link = `/${dataProps?.slug}`
          return CountryCard({
            data: { ...dataProps },
            copyRightsData: [dataProps?.image]
            // copyRightsData: dataProps?.image,
            // className: "md:aspect-[390/303] aspect-square"
          });
        }}
        data={data}
        xs={2}
        sm={2}
        md={3}
        lg={4}
        xl={4}
        xxl={4}
        xxxl={4}
      />
    </>
  );
};

export default Countries_Top_Country_Section;
