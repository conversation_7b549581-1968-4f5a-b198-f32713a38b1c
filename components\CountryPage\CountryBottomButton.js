"use client";
import { scrollToTop } from "@/helper/functions";
import Link from "next/link";
import React from "react";
import CustomButton from "../Custom-Button";
import { CustomButtonGrid } from "../Custom-Display";

const Country_Bottom_Button = () => {
  return (
    <CustomButtonGrid>
      <Link href={`/countries`}>
        <CustomButton type={4}>All Countries</CustomButton>
      </Link>
      <CustomButton type={4} onClick={scrollToTop}>
        Back to Top
      </CustomButton>
    </CustomButtonGrid>
  );
};

export default Country_Bottom_Button;
