"use client";
import React, { useEffect, useState } from "react";
import HeroSectionCSC from "../Common/HeroSectionCSC";
import LocationMap from "../Common/LocationMap";
import { CustomContainer } from "../Custom-Display";
import { getCoordinatesFromNominatim } from "@/helper/functions";
import NameTitle from "../Common/NameTitle";

const Country_Hero_Section = ({ data, Topstates, CountryAnalyticsData }) => {
  data.lat = data?.GeoLoc?.coordinates?.length
    ? data?.GeoLoc?.coordinates?.[1]
    : 0;
  data.lon = data?.GeoLoc?.coordinates?.length
    ? data?.GeoLoc?.coordinates?.[0]
    : 0;
  // const {counts, beaches} = CountryAnalyticsData;
  // const [coordinates,setCoordinates]=useState()
  // useEffect(()=>{
  //   getCoordinatesFromNominatim(data?.name)
  //   .then(result => {
  //     setCoordinates(result);
  //     return result
  //   })
  //   .catch(error => {
  //     console.error('Error fetching coordinates:', error);
  //   });

  // },[data?.name])

  // const CountryDetails = [
  //   {
  //     title: `Beaches in ${data?.name}`,
  //     icon: <BecahIcon className=" fill-sandee-blue  h-6 w-6" />,
  //     details: [
  //       {
  //         title: `${(+(counts?.beaches ?? 0))?.toLocaleString()}+ Beaches`,
  //       },
  //       // {
  //       //   title: `3700 Coastline`,
  //       // },
  //       {
  //         title: `${(+(
  //           counts?.dogBeaches ?? 0
  //         ))?.toLocaleString()} Dog Beaches`,
  //       },
  //       {
  //         title: `${(+(
  //           counts?.blueFlagBeaches ?? 0
  //         ))?.toLocaleString()} Blue Flag Beaches`,
  //       },
  //       {
  //         title: `${(+(
  //           counts?.nudeBeaches ?? 0
  //         ))?.toLocaleString()} Nude Beaches`,
  //       },
  //     ],
  //   },
  //   {
  //     title: `Top Destinations`,
  //     icon: <StateIcon className=" fill-sandee-blue  h-6 w-6" />,

  //     details: Topstates?.map((state) => ({
  //       title: `${state?.name}`,
  //       link: `/${state?.country?.slug}/${state?.slug}`,
  //     }))?.slice(0, 4),
  //   },
  //   {
  //     title: `Best Beaches in ${data?.name}`,
  //     icon: <PinLocation className=" fill-sandee-blue  h-6 w-6" />,

  //     details: beaches?.map((beach) => ({
  //       title: beach?.name,
  //       link: `/${beach?.country?.slug}/${beach?.state?.slug}/${beach?.city?.slug}/${beach?.nameSlug}`,
  //     })),
  //   },
  // ];
  return (
    <>
      <HeroSectionCSC data={data} />

      <CustomContainer>
        <NameTitle
          name={`Map of Beaches in ${data?.name ?? "country"}`}
          type={4}
          className="mt-2"
        />
        <LocationMap latStr={data?.lat} longStr={data?.lon} zoom={3} />
      </CustomContainer>

      {/* <div className="w-full py-4 flex items-center">
        <CustomContainer>
          <BreadCumber
            data={[
              {
                title: "All Countries",
                to: `/countries`,
              },

              {
                title: `${data?.name ?? "Country"}`,
              },
            ]}
          />
        </CustomContainer>
      </div> */}
    </>
  );
};

export default Country_Hero_Section;
