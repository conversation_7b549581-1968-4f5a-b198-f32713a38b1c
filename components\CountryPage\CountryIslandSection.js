"use client";
import React, { useEffect, useState } from "react";
import { CustomGrid } from "../Custom-Display";
import {
  API_BASE_URL,
  FinalImageGenerator,
  altText,
  blurDataURL,
  buildQueryString,
} from "@/helper/functions";
import Link from "next/link";
import Image from "next/image";
import NameTitle from "../Common/NameTitle";
import CountryCard from "../Cards/CountryCard";
import axios from "axios";
import Pagination from "../Common/Pagination";
import { BeachCardSkeleton } from "../Cards/BeachCard";
import CustomeImage from "../Common/CustomeImage";
const CountrygetAllIceland = async (data = {}, query) => {
  const response = await axios.get(
    `${API_BASE_URL}/beachMain/getTopIslands/${data}${buildQueryString(query)}`
  );
  return response?.data;
};
const Country_Island_Section = ({ data, params, islandData, totalRecords }) => {
  const [loading, setLoading] = useState(true);
  const [query, setQuery] = useState("");
  const [total, setTotal] = useState(totalRecords);
  // const [AllBeachPage, setAllBeachPage] = useState([topStateData]);
  const [CurrentBeach, setCurrentBeach] = useState(islandData);
  const [currentPage, setCurrentPage] = useState(1);
  const [refresh, setRefresh] = useState(true);
  const FetchOrSetBeach = async () => {
    setLoading(true);
    if (!!query) {
      const AllBeachesFilterResponse = await CountrygetAllIceland(
        params?.countrySlug,
        {
          page: currentPage,
          searchQuery: query,
          limit: 8,
        }
      );
      setTotal(AllBeachesFilterResponse?.totalRecords);
      setCurrentBeach(AllBeachesFilterResponse?.data);
      return setLoading(false);
    }
    const AllBeachesResponse = await CountrygetAllIceland(params?.countrySlug, {
      page: currentPage,
      limit: 8,
    });
    setTotal(AllBeachesResponse?.totalRecords);
    setCurrentBeach(AllBeachesResponse?.data);
    return setLoading(false);
  };
  useEffect(() => {
    FetchOrSetBeach();
  }, [refresh]);
  useEffect(() => {
    const getData = setTimeout(() => {
      // if (!!!query) {
      //   return;
      // }
      setRefresh((prev) => !prev);
    }, 400);

    return () => clearTimeout(getData);
  }, [query]);
  return (
    <section className="my-5">
      <div className="flex justify-between flex-col md:flex-row mt-5 gap-5">
        <NameTitle
          className=""
          description={"EXPLORE TOP DESTINATIONS"}
          name={`Best Beach Islands in ${data?.name}`}
          type={2}
        />
        <div className=" relative  flex justify-start md:justify-end items-start gap-4">
          {totalRecords > 8 ? (
            <div className="relative flex justify-center items-center max-w-[450px] w-full sm:w-auto md:min-w-[300px]">
              <div className="absolute inset-y-0 start-0 flex items-center ps-2 pe-2 ms-2 pointer-events-none rounded-full">
                <svg
                  className="w-4 h-4 text-[#7D7D7D]  "
                  aria-hidden="true"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 20 20"
                >
                  <path
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"
                  />
                </svg>
              </div>
              <input
                type="text"
                id="simple-search"
                className={`transition-all duration-500  focus:outline-none outline-none  focus:ring-0 ring-0 focus:border-sandee-blue  bg-white border-2 border-[#DEDEDE] block w-full ps-10 sm:text-[20px]  rounded-xl text-gray-600 !pl-[50px] lg:text-sandee-sm  !text-sandee-sm p-2 `}
                placeholder="Search"
                value={query}
                onChange={(e) => {
                  setQuery(e.target.value);
                  setCurrentPage(1);
                }}
              />
            </div>
          ) : null}
        </div>
      </div>
      {loading ? (
        <CustomGrid
          className="!mb-5 mt-[6px] gap-4 sm:gap-8"
          data={Array(8).fill(1)}
          Component={BeachCardSkeleton}
          xs={2}
          sm={2}
          md={3}
          lg={4}
          xl={4}
          xxl={4}
          xxxl={4}
        />
      ) : total ? (
        <>
          <CustomGrid
            data={CurrentBeach}
            className="!mb-5 mt-[6px] gap-4 sm:gap-8"
            Component={({ data: dataB }) => {
              const dataProps = { ...dataB };
              dataProps.images = [dataProps?.image];
              dataProps.image = FinalImageGenerator(dataProps?.image);
              dataProps.link = `/island/${dataProps?.slug}`;
              return CountryCard({
                data: { ...dataProps },
                copyRightsData: [dataProps?.images],
              });
            }}
            xs={2}
            sm={2}
            md={3}
            lg={4}
            xl={4}
            xxl={4}
            xxxl={4}
          />
        </>
      ) : (
        <p className=" px-2 py-5 text-error-red-600  border-error-red-400 bg-error-red-50 border-2 rounded-sandee text-center mt-6 mb-10">
          No Data Found for your applied filters
        </p>
      )}
      <Pagination
        className="pagination-bar"
        currentPage={currentPage}
        totalCount={total}
        pageSize={8}
        onPageChange={(page) => {
          setCurrentPage(page);
          setRefresh((prev) => !prev);
        }}
      />
      {/* <div className="flex justify-center mb-6">
        <Link href={HomePageData.ListSection.button_link}>
          <button className=" text-sandee-blue active:text-white active:bg-sandee-blue border-2 border-sandee-blue  py-2 px-5 transition ease-in-out duration-300  px-10 text-xs font-medium  rounded-full active:scale-[0.7] hover:scale-110">
            {HomePageData.ListSection.button_text}
          </button>
        </Link>
      </div> */}
    </section>
  );
};

export default Country_Island_Section;

export const ListCard = ({ data }) => {
  return (
    <Link
      href={`${data?.link}`}
      className="relative group flex h-[180px]  rounded-sandee shadow-[0_3px_10px_rgb(0,0,0,0.2)]"
    >
      <div className="    h-[180px] w-[125px] rounded-s-sandee">
        <div className=" h-[180px] w-[125px] relative  overflow-hidden  rounded-s-sandee ">
          <CustomeImage
            // priority
            className=" h-[180px] w-[125px] transition-transform duration-1000 ease-in-out object-cover object-center transform group-hover:scale-125"
            src={data?.imageSrc}
            alt={altText(data)}
            fill
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            blurDataURL={blurDataURL(300, 200)}
            placeholder="blur"
          />
        </div>
      </div>
      <div className=" px-5 rounded-sandee flex justify-start items-center">
        <p className=" text-sandee-sm font-normal line-clamp-6">
          <span className=" font-bold">{data?.name}</span> <br></br>
          {` boasts over 5,000 kilometers of coastline, offering a vibrant mix of beach experiences. From the legendary party scene of Ibiza to`}
        </p>
      </div>
    </Link>
  );
};
