import { siteMetadata } from "@/data/siteMetadata";
import { slugConverter } from "@/helper/functions";
import React from "react";

const CountryPageJSONLD = ({ countryData }) => {

  const CountryBreadCumber = {
    "@context": "https://schema.org/",
    "@type": "BreadcrumbList",
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        name: "Home",
        item: `https://sandee.com/`,
      },
      {
        "@type": "ListItem",
        position: 2,
        name: countryData?.name,
        item: `https://sandee.com/${countryData?.slug}`,
      },
    ],
  };

  const CountryPageSchema = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `https://sandee.com/${countryData?.slug}`
    },
    "headline": countryData?.title || `Best Beaches in ${slugConverter(countryData?.slug, true)} - Sandee`,
    "description": !!countryData?.metaDescription ? countryData?.metaDescription :
      `Discover the complete list of beaches in the ${slugConverter(
        countryData?.slug,
        true
      )}. Plan your ${slugConverter(
        countryData?.slug,
        true
      )} beach vacation with 100+ activities, photos, attractions, restaurants, and hotels.`,
    "author": {
      "@type": "Person",
      "name": "Mr. Beach",
      "url": `https://sandee.com/mr-beach`
    },
    "publisher": {
      "@type": "Organization",
      "name": siteMetadata?.title || "Sandee",
      "logo": {
        "@type": "ImageObject",
        "url": `https://sandee.com/_next/image?url=%2Fstatic%2Fimages%2FSandee-Blue.webp&w=1920&q=75`
      }
    },
    "datePublished": countryData?.createdAt,
    "dateModified": countryData?.updatedAt
  };

  return (
    <>
      <script
        type="application/ld+json"
        id="application/ld+jsonBeachBreadCumber"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(CountryBreadCumber) }}
      ></script>
      <script
        type="application/ld+json"
        id="application/ld+jsonCountryPage"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(CountryPageSchema) }}
      ></script>
    </>
  );
};

export default CountryPageJSONLD;
