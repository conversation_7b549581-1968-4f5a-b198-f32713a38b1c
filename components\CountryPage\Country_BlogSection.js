import React from "react";
import { CustomGrid } from "../Custom-Display";
import BlogCard from "../Cards/BlogCard";
import NameTitle from "../Common/NameTitle";
import Link from "next/link";
import { HomePageData } from "@/data/HomePageData";
import { ReadBookIcon } from "../social-icons/icons";

const Country_Blog_Section = ({ data }) => {
  return (
    <section className="my-5">
      <NameTitle
        className="mt-5"
        description={"EXPERT ADVICE AND TIPS"}
        name={"Beach Blogs"}
        extraButton={
          <div className=" hidden md:flex justify-end items-start w-3/12">
            <Link
              href={HomePageData.BlogSection.button_link}
              className=" custom-hover-slide-button group"
            >
              <span className="custom-hover-slide group-hover:h-full"></span>
              <span className="custom-hover-slide-text group-hover:text-white font-bold ">
                <ReadBookIcon className=" me-2 fill-transparent stroke-sandee-orange group-hover:stroke-white  h-5 w-5 -mt-0.5" />
                {HomePageData.BlogSection.button_text}
              </span>
            </Link>
          </div>
        }
        type={2}
      />
      <CustomGrid
        data={data}
        className="gap-4 sm:gap-8 !mb-5 mt-[6px]"
        Component={({ data: dataProps }) => {
          // dataProps.link = `/blog/${dataProps?.slug}`; //
          // dataProps.imageSrc = dataProps?.image;
          // dataProps.name = dataProps?.title;
          // return BlogCard({ data: { ...dataProps } });
          dataProps.link = `/blog/${dataProps?.slug}`; //
          // dataProps.imageSrc = FinalImageGenerator(dataProps?.listicleImage);
          dataProps.imageSrc = dataProps?.image;
          dataProps.name = dataProps?.title;
          return BlogCard({
            data: { ...dataProps },
            copyRightsData: dataProps?.image,
          });
        }}
        xs={1}
        sm={2}
        md={2}
        lg={3}
        xl={4}
      />
      <div className=" md:hidden my-5">
        <Link
          href={HomePageData.BlogSection.button_link}
          className=" custom-hover-slide-button group"
        >
          <span className="custom-hover-slide group-hover:h-full"></span>
          <span className="custom-hover-slide-text group-hover:text-white font-bold">
            <ReadBookIcon className=" me-2 fill-transparent stroke-sandee-orange group-hover:stroke-white h-5 w-5 -mt-0.5" />
            {HomePageData.BlogSection.button_text}
          </span>
        </Link>
      </div>
    </section>
  );
};

export default Country_Blog_Section;

// export const BlogCard = ({ data }) => {
//   return (
//     <Link
//       href={`${data?.link}`}
//       className="relative flex h-[150px]  rounded-sandee shadow-[0_3px_10px_rgb(0,0,0,0.2)]"
//     >
//       <div className="    h-[150px] w-[120px] rounded-s-sandee">
//         <div className=" h-[150px] w-[120px] relative  overflow-hidden  rounded-s-sandee ">
//           <Image
//             // priority
//             className=" h-[150px] w-[120px] transition-transform duration-1000 ease-in-out transform group-hover:scale-125"
//             src={data?.imageSrc}
//             fill
//             sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
//             blurDataURL={blurDataURL(300, 200)}
//             placeholder="blur"
//           />
//         </div>
//       </div>
//       <div className=" px-5 rounded-sandee flex justify-center items-center flex-col">
//         <p className=" text-sandee-sm font-bold line-clamp-2">{data?.name}</p>
//         <p className=" text-sandee-12 font-normal text-gray-500 line-clamp-5">
//           {data?.description}
//         </p>
//       </div>
//     </Link>
//   );
// };
