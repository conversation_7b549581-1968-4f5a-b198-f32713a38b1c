import React from "react";

const CityHeroSectionSkeleton = () => {
  return (
    <div>
      <section className=" items-center  justify-around  min-h-[700px]  animate-pulse ">
        <div className="relative h-[560px] ">
          <div className="absolute left-0 top-0  h-[550px] w-full bg-sandee-grey"></div>
        </div>
        {/* <div className="relative h-0 md:h-[140px] ">
          <div className="absolute left-0 -top-[150px] w-full  h-[270px]  flex justify-center items-end px-3">
            <div className="hidden md:flex md:flex-row flex-col bg-white  items-center justify-center gap-5  rounded-sandee shadow-lg p-5 px-8 h-full">
              <div className="h-full bg-slate-200 border-r-2 border-gray-200 w-60"></div>

              <div className="h-full  bg-slate-200 w-60"></div>
            </div>
          </div>
        </div> */}
        <div className="relative z-50  h-auto md:h-0 -mt-[80px]">
          <div className=" w-full  h-auto flex justify-center items-end px-2">
            <div className="md:hidden  flex flex-col  items-center justify-center gap-5 bg-white rounded-sandee shadow-lg p-4 h-full"></div>
          </div>
        </div>
      </section>
      <div className="w-full py-4 flex items-center bg-slate-100 h-10"></div>
    </div>
  );
};

export default CityHeroSectionSkeleton;
