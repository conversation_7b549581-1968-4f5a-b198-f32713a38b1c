import React from "react";

const CustomButton = ({ type = 1, children, dataTestid = "custom-button", ...props }) => {
  switch (type) {
    case 1:
      return (
        <button
          data-testid={`${dataTestid}-${type}`}
          className=" justify-center items-center  py-2 px-8 flex relative   overflow-hidden font-bold  text-white border border-sandee-blue bg-sandee-blue active:text-white text-sandee-sm h-auto rounded-lg group"
          {...props}
        >
          <span className="absolute bottom-0 left-0 flex w-full h-0 mb-0 transition-all duration-200 ease-out transform translate-y-0 bg-white text-sandee-blue opacity-100 group-hover:h-full"></span>
          <span className="relative flex items-center group-hover:text-sandee-blue ">
            {children}
          </span>
        </button>
      );
    case 2:
      return (
        <button
          data-testid={`${dataTestid}-${type}`}
          className=" text-sandee-blue active:text-white active:bg-sandee-blue border border-sandee-blue  py-2 px-5 transition ease-in-out duration-300  text-sandee-sm font-bold  rounded-lg active:scale-[0.7] hover:scale-110"
          {...props}
        >
          {children}
        </button>
      );
    case 3:
      return (
        <button
          data-testid={`${dataTestid}-${type}`}
          className="relative inline-flex items-center justify-start  px-5 py-2 overflow-hidden  font-bold  rounded-lg group text-sandee-sm"
          {...props}
        >
          <span className="w-32 h-32 rotate-45 translate-x-12 -translate-y-2 absolute left-0 top-0 bg-sandee-blue opacity-[3%]"></span>
          <span className="absolute top-0 left-0 w-48 h-48 -mt-1 transition-all duration-500 ease-in-out rotate-45 -translate-x-56 -translate-y-24 bg-sandee-blue opacity-100 group-hover:-translate-x-8"></span>
          <span className="relative w-full text-left  text-sandee-blue transition-colors duration-200 ease-in-out group-hover:text-white">
            {children}
          </span>
          <span className="absolute inset-0 border border-sandee-blue rounded-lg"></span>
        </button>
      );
    case 4:
      return (
        <button className="custom-hover-slide-button group font-bold " {...props}>
          <span className="custom-hover-slide group-hover:h-full"></span>
          <span className="custom-hover-slide-text group-hover:text-white ">
            {children}
          </span>
        </button>
      );
    case 5:
      return (
        <button
          data-testid={`${dataTestid}-${type}`}
          className="custom-hover-slide-button !bg-white !text-sandee-orange border !border-sandee-orange active:!text-white group font-bold "
          {...props}
        >
          <span className="custom-hover-slide group-hover:h-full !bg-sandee-orange"></span>
          <span className="custom-hover-slide-text group-hover:text-white ">
            {children}
          </span>
        </button>
      );
    case 6:
      return (
        <button data-testid={`${dataTestid}-${type}`} className="custom-hover-slide-button group xs:w-full font-bold " {...props}>
          <span className="custom-hover-slide group-hover:h-full"></span>
          <span className="custom-hover-slide-text group-hover:text-white ">
            {children}
          </span>
        </button>
      );
    case 7:
      return (
        <button
          data-testid={`${dataTestid}-${type}`}
          className=" text-sandee-blue active:text-white active:bg-sandee-blue border border-sandee-blue  py-1 px-2 transition ease-in-out duration-300  text-sandee-sm  rounded-[35px] active:scale-[0.7] hover:scale-110 text-xs"
          {...props}
        >
          {children}
        </button>
      );
    default:
      return (
        <button
          data-testid={`${dataTestid}-${type}`}
          className=" text-sandee-blue active:text-white active:bg-sandee-blue border border-sandee-blue  py-2 px-5 transition ease-in-out duration-300   text-sandee-sm font-bold  rounded-lg active:scale-[0.7] hover:scale-110"
          {...props}
        >
          {children}
        </button>
      );
  }
};

export default CustomButton;
