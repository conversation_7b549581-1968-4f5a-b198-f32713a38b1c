"use client";

import Image from "next/image";
import SwiperComps, { Slide } from "./SwiperComps";
// import SwiperCore, {
//   Autoplay,
//   Navigation,
//   Pagination,
//   Thumbs,
//   EffectFade,
//   Grid,
// } from "swiper";
// import "swiper/css";
// import "swiper/css/pagination";
// import "swiper/css/navigation";
// import "swiper/css/grid";
import { CustomContainer } from "./Custom-Display";
import { Fragment } from "react";

// import NextIcon from "@/_assets/images/Icons/NextIcon.svg";
// import PrevIcon from "@/_assets/images/Icons/PrevIcon.svg";
const NextIcon = "https://images.sandee.com/images/Icons/NextIcon.svg";
const PrevIcon = "https://images.sandee.com/images/Icons/PrevIcon.svg";
const CustomSwiper = ({
  // cardType,
  id,
  data = [],
  extra,
  className = "",
  slideClassName = "",
  Component,
  cardHeight = 250,
  dataTestid = "custom-swiper",

  settingsExtra = {},
}) => {
  function generateBreakpoints(minWidth, maxWidth, step, widthDivisor) {
    const breakpoints = {};
    for (let width = minWidth; width <= maxWidth; width += step) {
      breakpoints[width] = {
        slidesPerView: width / widthDivisor < 1 ? 1.1 : width / widthDivisor,
      };
    }
    return breakpoints;
  }

  const settings = {
    spaceBetween: 0,
    pagination: false,
    navigation: true,
    // mousewheel:true,
    // navigation: {
    //   nextEl: `.project-button-next-${id}`,
    //   prevEl: `.project-button-prev-${id}`,
    // },
    loop: false,
    breakpoints: {
      // 1200: {
      //   slidesPerView: 1200 / getCardWidth(cardType),
      // },

      ...generateBreakpoints(400, 1800, 20, cardHeight + 100),
      ...generateBreakpoints(100, 480, 20, cardHeight + 50),

      // 310: {
      //   slidesPerView: 2,
      // },
    },
    ...settingsExtra,
  };
  const renderArrowPrev = (clickHandler, hasPrev) => {
    return (
      <button
        onClick={clickHandler}
        disabled={!hasPrev}
        className={`carousel-arrow project-button-prev-${id} d-none d-lg-block bg-white !z-[500] rounded-full`}
      >
        <Image
          src={PrevIcon}
          width={30}
          height={30}
          alt="Prev"
          className=" project-button-prev"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
      </button>
    );
  };

  const renderArrowNext = (clickHandler, hasNext) => {
    return (
      <button
        onClick={clickHandler}
        disabled={!hasNext}
        className={`carousel-arrow project-button-next-${id} d-none d-lg-block bg-white !z-[500] rounded-full`}
      >
        <Image
          src={NextIcon}
          width={30}
          height={30}
          alt="Next"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
      </button>
    );
  };
  if (!data?.length) {
    return null;
  }
  return (
    <Fragment >
      {/* <div
        style={{
          height: cardHeight,
        }}
        className={` absolute flex justify-between items-center left-0 w-full ${className}`}
      >
        <CustomContainer className="flex justify-between w-full ">
          {renderArrowPrev()}

          {renderArrowNext()}
        </CustomContainer>
      </div> */}

      <div data-testid={dataTestid} className={`swiper-custom-sandee ${className}`}>
        <SwiperComps settings={settings}>
          {data.map((project) => (
            <Slide key={project.id} className={` ${slideClassName}`}>
              <Component data={project} />
            </Slide>
          ))}
          {extra && <Slide>{extra}</Slide>}
        </SwiperComps>
      </div>
    </Fragment>
  );
};

export default CustomSwiper;
