"use client";
import { siteMetadata } from "@/data/siteMetadata";
import SocialIcon from "@/components/social-icons";
import Image from "next/image";
import { CustomRow } from "./Custom-Display";
import Link from "next/link";
import FooterSubscription from "./FooterSubscription";
import { Collapse } from "antd";
import { useState } from "react";
import { DownArrowIcon, UPArrowIcon } from "./social-icons/icons";
import LicenseOurDataForm from "./LicenseOurDataForm";

export default function Footer() {
  const [openGroups, setOpenGroups] = useState({
    "Explore": true,
    "Company": true,
    "Legal": true,
    "Community": true,
  });
  const [open, setOpen] = useState(false);
  const BottomFooter = () => {
    const currentYear = new Date().getFullYear();
    return (
      <div className=" flex w-full flex-col md:items-center items-start md:justify-center justify-start border-t border-white border-opacity-20 pt-4 md:text-base text-[12px] md:mb-0 mb-[5px]">
        <p className="text-start font-normal  text-white ">
          {/* Site data &copy; {currentYear} Sandee, LLC.&nbsp;&nbsp;All rights
          reserved. */}
          © {currentYear} by Sandee, LLC.&nbsp;&nbsp; All rights reserved.
        </p>
      </div>
    );
  };
  const LINKS = [
    // {
    //   title: "See Your Beach",
    //   items: [
    //     { link: "/", name: "Beach Drone Videos" },
    //     // { link: "/", name: "Beach Cams" },
    //     {
    //       link: "/",
    //       name: "Best Beach Photos",
    //     },
    //   ],
    // },
    {
      title: "Explore",
      items: [
        { link: "/countries", name: "Countries" },
        { link: "/nude-beaches", name: "Nude Beaches" },
        // { link: "/filter/Nude", name: "Nude Beaches" },
        {
          link: "/list/best-11-beach-destinations-for-2023",
          name: "Best Beaches",
        },
        { link: "/sitemap", name: "Sitemap" },
      ],
    },
    {
      title: "Company",
      items: [
        { link: "/about-us", name: "About" },
        { link: "/press", name: "Press" },
        { link: "/mr-beach", name: "Mr. Beach" },
        {
          href: {
            pathname: '',
            query: { form: 'license-data' }
          }, name: "License Our Data"
        },

      ],
    },
    {
      title: "Legal",
      items: [
        { link: "/privacy-policy", name: "Privacy Policy" },
        { link: "/cookie-policy", name: "Cookie Policy" },
        { link: "/terms", name: "Terms and Conditions" },
      ],
    },
    {
      title: "Community",
      items: [
        { link: "/news", name: "Beach News" },
        { link: "/contact-us", name: "Contact Us" },
        { link: "/save-our-ocean", name: "Save Our Oceans" },
      ],
    },
  ];
  const CollapseChild = (items) => {
    return (
      <>
        {items?.map((link, i) => (
          link?.href ? <span key={link?.query?.form + link?.name + i}>
            <div
              // hrefLang="en-us"
              key={link?.query?.form}
              // href={link?.href}
              className="no-underline"
              aria-label="View the link"
              onClick={() => setOpen(pr => !pr)}
              scroll={false}
            >
              <p className="py-0 m-0 mb-[10px]  text-xs font-normal transition-colors hover:underline text-white">
                {link?.name}
              </p>
            </div>
          </span> : <span key={link?.link + link?.name + i}>
            <Link
              hrefLang="en-us"
              key={link?.link}
              href={`${link?.link}`}
              className="no-underline"
              aria-label="View the link"
            >
              <p className="py-0 m-0 mb-[10px]  text-xs font-normal transition-colors hover:underline text-white">
                {link?.name}
              </p>
            </Link>
          </span>
        ))}
      </>
    );
  };
  const items = [
    {
      key: "1",
      label: "Explore",
      children: CollapseChild(LINKS[0]?.items),
    },
    {
      key: "2",
      label: "Company",
      children: CollapseChild(LINKS[1]?.items),
    },
    {
      key: "3",
      label: "Legal",
      children: CollapseChild(LINKS[2]?.items),
    },
    {
      key: "4",
      label: "Community",
      children: CollapseChild(LINKS[3]?.items),
    },
  ];
  const toggleGroup = (title) => {
    setOpenGroups((prev) => ({
      ...prev,
      [title]: !prev[title], // Toggle the specific group open/closed
    }));
  };
  return (
    <footer className=" bg-sandee-blue mt-10">
      {/* <div className="relative h-[390px] bg-sandee-blue">
        <div className="absolute left-0 top-0  h-[390px] w-full ">
          <Image
            priority
            src={"https://images.sandee.com/Sandee-Footer.avif"}
            // src="https://shorturl.at/aILST"
            // src={"/static/images/Sandee_Footer.jpeg"}
            // src={"/static/images/Sandee_Footer.png"}
            alt="HomePage Sandee Photo "
            className=" object-cover bg-blend-overlay"
            // className=" object-cover bg-blend-overlay scale-x-[-1]"
            fill
          />
        </div>
        <div className="absolute flex justify-center flex-col items-center  h-[390px] w-full ">
          <div className="flex mb-4  sm:text-sandee-48  text-sandee-48 p-0 m-0 leading-none text-white font-semibold flex-col text-center">
            <p role="heading" aria-level="2"> Beaches Too?</p>
            // <p> Discover the magic of </p> <p> our beach escape</p>
          </div>
          <div className="flex mb-4 text-sandee-sm flex-col text-white font-medium text-center">
            <p className="">Ready for the best beach newsletter on the web?</p>
            <p className="">Sign up for our monthly travel newsletter!</p>
          </div>
          <FooterSubscription />
        </div>
      </div> */}

      <div className=" text-black md:px-10 py-2  ">
        {/* <CustomContainer className=" text-black"> */}
        <div className="flex md:gap-10 gap-5 flex-col lg:flex-row px-4 md:px-10  md:justify-between mt-2 items-start ">
          <div className=" w-full lg:w-1/4">
            <div className="flex flex-col md:justify-center md:items-center sm:justify-start sm:items-start">
              <Link hrefLang="en-us" href="/">
                {/* <Image
                  src={"/static/images/Sandee_Logo.png"}
                  alt="me"
                  width="120"
                  height="120"
                /> */}
                <div className="relative md:items-center justify-start w-[150px] h-8">
                  <Image
                    // src={"/static/images/Sandee-White.svg"}
                    className="white-logo "
                    src={"/static/images/Sandee-Blue.webp"}
                    fill
                    sizes="1080"
                    alt="Sandee logo Blue"
                  />
                </div>
              </Link>
              {/* <p
                className=" font-normal transition-colors hover:text-blue-gray-900  text-white"
                style={{ display: "flex", justifyContent: "center" }}
              >
                Connect With Us
              </p> */}
            </div>
            <div className="flex md:gap-2 gap-5 md:mt-5 mt-10 md:mb-3 md:justify-center sm:justify-start">
              {/* <SocialIcon
                  kind="mail"
                  href={`mailto:${siteMetadata.email}`}
                  size={6}
                /> */}
              {/* <SocialIcon kind="github" href={siteMetadata.github} size={6} /> */}
              <SocialIcon
                kind="instagram"
                href={siteMetadata.instagram}
                size={7}
              />
              <SocialIcon
                kind="facebook"
                href={siteMetadata.facebook}
                size={9}
              />
              <SocialIcon kind="twitter" href={siteMetadata.twitter} size={5} />
              <SocialIcon kind="youtube" href={siteMetadata.youtube} size={9} />
              {/* <SocialIcon
                  kind="pintrest"
                  href={siteMetadata.linkedin}
                  size={6}
                /> */}

              {/* <SocialIcon
                  kind="threads"
                  href={siteMetadata.threads}
                  size={6}
                /> */}
            </div>
            <div className="flex pt-6 md:pt-2 md:justify-center md:items-center sm:justify-start sm:items-start text-white font-semibold text-sandee-base">
              Choose Your Beach!{" "}
              <sup className=" text-sandee-sm -top-2 lg:-top-3">TM</sup>
            </div>
            {/* <div className="flex pt-6 md:pt-2 md:justify-center md:items-center sm:justify-start sm:items-start text-white font-semibold text-sandee-base">
              <Link
                href={{
                  pathname: '',
                  query: { form: 'license-data' }
                }}
                scroll={false}  // This prevents scrolling
                // shallow={true}  // Prevents scrolling and full page reload 
                className=" !text-nowrap"> Sandee - License Our Data{" "}</Link>
            </div> */}
          </div>
          <div xs={24} className=" w-full lg:w-3/5">
            <div className="gap-10 md:flex-nowrap flex-wrap justify-end md:grid grid-cols-2 md:grid-cols-4 hidden ">
              {LINKS.map(({ title, items }) => (
                <div key={title} className="w-full">
                  <ul style={{ padding: 0 }}>
                    <li
                      style={{ textDecoration: "none" }}
                      className="flex gap-x-2 items-center mb-2 cursor-pointer"
                      onClick={() => toggleGroup(title)}
                    >
                      <p className="text-sandee-sm font-semibold text-white opacity-1">
                        {title}
                      </p>
                      {openGroups[title] ? (
                        <span>
                          <UPArrowIcon
                            className="w-[10px] h-[10px] opacity-60"
                            fill="#fff"
                          />
                        </span>
                      ) : (
                        <span>
                          <DownArrowIcon
                            className="w-[14px] h-[14px] opacity-60"
                            fill="#fff"
                          />
                        </span>
                      )}
                    </li>

                    {/* Use CSS to control show/hide with smooth height transition */}
                    <div
                      className={`transition-all overflow-hidden duration-300 ease-in-out ${openGroups[title] ? "max-h-96" : "max-h-0"
                        }`}
                    >
                      {items.map((link, i) => (
                        link?.href ? <li
                          style={{ textDecoration: "none" }}
                          key={link?.query?.form + link.name + i}
                        >
                          <div
                            // hrefLang="en-us"
                            key={link?.query?.form}
                            // href={link?.href}
                            className="cursor-pointer no-underline"
                            // style={{ textDecoration: "none" }}
                            aria-label="View the link"
                            onClick={() => setOpen(pr => !pr)}
                          // scroll={false}
                          >
                            <p className="py-0 m-0 mb-2 text-xs font-normal transition-colors hover:underline text-white">
                              {link.name}
                            </p>
                          </div>
                        </li> :
                          <li
                            style={{ textDecoration: "none" }}
                            key={link.link + link.name + i}
                          >
                            <Link
                              hrefLang="en-us"
                              key={link.link}
                              href={`${link.link}`}
                              style={{ textDecoration: "none" }}
                              aria-label="View the link"
                            >
                              <p className="py-0 m-0 mb-2 text-xs font-normal transition-colors hover:underline text-white">
                                {link.name}
                              </p>
                            </Link>
                          </li>
                      ))}
                    </div>
                  </ul>
                </div>
              ))}
            </div>
            <Collapse
              items={items}
              bordered={false}
              className="bg-transparent footer-accordion md:hidden"
              expandIconPosition="end"
            />
          </div>
        </div>

        <CustomRow className=" px-4 md:px-10 lg:mb-0 mb-14">
          <BottomFooter />
        </CustomRow>
        <LicenseOurDataForm open={open} setOpen={setOpen} />
      </div>
    </footer>
  );
}

{
  /* <div className="mt-16 flex flex-col items-center">
        <div className="mb-3 flex space-x-4 ">
          <SocialIcon
            kind="mail"
            href={`mailto:${siteMetadata.email}`}
            size={6}
          />
          <SocialIcon kind="github" href={siteMetadata.github} size={6} />
          <SocialIcon kind="facebook" href={siteMetadata.facebook} size={6} />
          <SocialIcon kind="youtube" href={siteMetadata.youtube} size={6} />
          <SocialIcon kind="linkedin" href={siteMetadata.linkedin} size={6} />
          <SocialIcon kind="twitter" href={siteMetadata.twitter} size={6} />
          <SocialIcon kind="instagram" href={siteMetadata.instagram} size={6} />
          <SocialIcon kind="threads" href={siteMetadata.threads} size={6} />
        </div>
        <div className="mb-2 flex space-x-2 text-sandee-sm text-gray-500 ">
          <div>{siteMetadata.author}</div>
          <div>{` • `}</div>
          <div>{`© ${new Date().getFullYear()}`}</div>
          <div>{` • `}</div>
          <Link href="/">{siteMetadata.title}</Link>
        </div>
        <div className="mb-8 text-sandee-sm text-gray-500  transition ease-in-out duration-300  hover:scale-125 hover:bg-slate-400 hover:p-3 hover:rounded-xl hover:text-white ">
          <Link href="https://github.com/timlrx/tailwind-nextjs-starter-blog">
            Tailwind Nextjs Theme
          </Link>
        </div>
      </div> */
}
