// "use client";
// import { FooterSubscribe } from "@/app/(HomeHeader)/action";
// import { notification } from "antd";
// import React, { useState } from "react";

// const FooterSubscription = () => {
//   const [isLoading, setIsloading] = useState(false);
//   return (
//     <form
//       name="subscription"
//       className="flex gap-3 bg-white rounded-full p-2"
//       onSubmit={async (e) => {
//         e.preventDefault();
//         const email = e.target?.["email-subscription"].value;
//         const res = await FooterSubscribe({ email });
//         notification.success({
//           message: "Email subscription Added successfully",
//           duration: 3,
//         });
//         setIsloading(true);
//       }}
//     >
//       <input
//         className="appearance-none block border-none active:border-none   bg-transparent border rounded-full py-2 px-3 leading-tight focus:outline-none  text-start text-sandee-sm   text-black placeholder:text-black w-[180px] sm:w-[260px] 425:w-[180px]"
//         id="email-subscription"
//         name="email-subscription"
//         pattern="[^@\s]+@[^@\s]+\.[^@\s]+"
//         title="Invalid email address"
//         type="email"
//         onChange={() => {
//           setIsloading(false);
//         }}
//         required
//         placeholder="Enter Your Email Address"
//       />
//       <button
//         type="submit"
//         className="active:bg-white rounded-full active:text-sandee-blue  py-2 px-5 transition ease-in-out duration-300  text-xs font-medium text-white bg-sandee-blue active:scale-[0.7] hover:scale-110"
//         disabled={isLoading}
//       >
//         {!isLoading ? "Subscribe" : "Subscribed"}
//       </button>
//     </form>
//   );
// };

// export default FooterSubscription;

"use client";
import { FooterSubscribe } from "@/app/(HomeHeader)/action";
import { notification } from "antd";
import React, { useState, useRef } from "react";

const FooterSubscription = () => {
  const [isLoading, setIsloading] = useState(false);
  const emailRef = useRef(null); // Create a ref for the input element

  const handleSubmit = async (e) => {
    e.preventDefault();
    const email = emailRef.current.value; // Access the input value via ref

    setIsloading(true);
    try {
      await FooterSubscribe({ email });
      notification.success({
        message: "Email subscription added successfully",
        duration: 3,
      });
      emailRef.current.value = ""; // Clear the input field
    } catch (error) {
      notification.error({
        message: "Subscription failed",
        description: error.message,
        duration: 3,
      });
    } finally {
      setIsloading(false);
    }
  };

  return (
    <form
      name="subscription"
      className="flex gap-3 bg-white rounded-full p-2"
      onSubmit={handleSubmit} // Use the handleSubmit function
    >
      <input
        className="appearance-none block border-none active:border-none bg-transparent border rounded-full py-2 px-3 leading-tight focus:outline-none text-start text-sandee-sm text-black placeholder:text-black w-[180px] sm:w-[260px] 425:w-[180px]"
        id="email-subscription"
        name="email-subscription"
        pattern="[^@\s]+@[^@\s]+\.[^@\s]+"
        title="Invalid email address"
        type="email"
        ref={emailRef} // Attach the ref to the input element
        onChange={() => setIsloading(false)}
        required
        placeholder="Enter Your Email Address"
      />
      <button
        type="submit"
        className="active:bg-white rounded-full active:text-sandee-blue py-2 px-5 transition ease-in-out duration-300 text-xs font-medium text-white bg-sandee-blue active:scale-[0.7] hover:scale-110"
        disabled={isLoading}
      >
        {!isLoading ? "Subscribe" : "Subscribed"}
      </button>
    </form>
  );
};

export default FooterSubscription;

