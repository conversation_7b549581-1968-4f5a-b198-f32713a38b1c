import Image from "next/image";

const HeaderHomeSearchUp = ({ name, imageSrc }) => {
  return (
    <>
      <header className=" items-center  justify-around  h-[450px]">
        <div className="relative h-[450px]">
          <div className="absolute left-0 top-0  h-[450px] w-full ">
            <Image
              priority
              src={imageSrc}
              alt="HomePage Sandee Photo "
              className=" object-cover bg-blend-overlay"
              fill
            />
          </div>
          <div className="absolute flex justify-center items-center  h-[450px] w-full ">
            <h1 className=" text-white font-bold rounded-full border border-gray-300 bg-[#EBEBEB66] [text-shadow:0px_4px_4px_rgba(0,_0,_0,_0.25)] px-8 text-[20px]  sm:text-sandee-32 md:text-[40px] xl:text-[45px] translate-y-[-50%]">
              {name}
            </h1>
          </div>
        </div>
      </header>
    </>
  );
};

export default HeaderHomeSearchUp;
