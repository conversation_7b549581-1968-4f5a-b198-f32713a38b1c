import { siteMetadata } from "@/data/siteMetadata";
import MobileNav from "./MobileNav";
// import ThemeSwitch from "./ThemeSwitch";
// import SearchButton from "./SearchButton";
import { headerNavLinks } from "@/data/headerNavLinks";
import Image from "next/image";
import { CustomContainer, CustomRow } from "./Custom-Display";
import Link from "next/link";
import ActiveLinks from "./ActiveLinks";
import SearchBar from "./SearchBar";
import LogInButton from "./LogInButton";

const HeaderHome = ({ imageSrc, name }) => {
  return (
    <>
      <header className=" !z-[10] ">
        <div className="relative header-image md:h-[65vh] max-h-[800px] h-[295px]">
          <div className="absolute header-image left-0 bottom-0  md:h-[65vh] max-h-[800px] h-[295px] w-full">
            <Image
              priority
              src={imageSrc}
              alt="HomePage Sandee Photo "
              className="object-cover bg-blend-overlay lg:rounded-b-none rounded-b-[40px]"
              fill
            />
          </div>
          <div className="absolute right-0 bottom-0  header-image md:h-[65vh] max-h-[800px] h-[295px] w-full bg-transparent z-1 ">
            <CustomRow className=" gap-8 flex justify-center  w-full max-w-[680px] sm:w-2/3  lg:w-7/12 xl:w-6/12 px-3 mb-6 md:mb-0 items-center relative md:top-[45%] top-[57%] left-[50%] translate-x-[-50%] translate-y-[-50%] ">
              {/* <p className=" text-white font-bold rounded-full border border-gray-300 bg-[#EBEBEB66] [text-shadow:0px_4px_4px_rgba(0,_0,_0,_0.25)]   text-[28px]  xs:text-[28px] px-3 xs:px-3  sm:text-sandee-32 sm:px-4 md:text-[40px] md:px-8 xl:text-[45px] ">
                {name}
              </p> */}
              <h1 className=" text-white font-bold [text-shadow:0px_4px_4px_rgba(0,_0,_0,_0.25)]   text-[28px]  xs:text-[28px] px-3 xs:px-3  sm:text-sandee-32 sm:px-4 md:text-[40px] md:px-8 xl:text-[45px] ">
                {name}
              </h1>
              <SearchBar />
              {/* TODO: use proper way Link  */}
              <Link href={`/podcast`} target="_blank" className=" !text-nowrap">
                <button className="justify-center items-center  py-2 px-10 flex relative  overflow-hidden font-semibold  text-sandee-blue bg-[#F9FE00] active:text-sandee-blue text-sandee-20 h-auto rounded-full group">
                  <span className="absolute bottom-0 left-0 flex w-full h-0 mb-0 transition-all duration-200 ease-out transform translate-y-0  text-sandee-blue opacity-100 group-hover:h-full"></span>
                  <span className="relative flex items-center group-hover:text-sandee-blue ">
                    The Beaches Podcast <sup className="mr-2 ms-[3px]">TM</sup>
                  </span>
                </button>
              </Link>
            </CustomRow>
          </div>
          <div className="absolute right-0 top-0   w-full bg-transparent z-1 ">
            <CustomContainer>
              <div className="flex justify-between  py-3 bg-transparent ">
                {/* <div className=" justify-around  py-3 bg-white"> */}
                <div className="flex justify-center items-center  min-w-[135px]">
                  <Link className="text-center gap-y-1.5 flex flex-col items-center justify-center" href="/" aria-label={siteMetadata.headerTitle}>
                    <div className="relative items-center justify-between w-[130px] h-7">
                      <Image
                        // src={"/static/images/Sandee-White.svg"}
                        className="white-logo "
                        src={"/static/images/Sandee-Blue.webp"}
                        fill
                        sizes="1080"
                        alt="Sandee logo Blue"
                      />
                    </div>
                    {/* <p className="text-start font-normal exs:text-[8px] xs:text-[8px] 425:text-[8px] xl:text-xs text-sandee-blue ">
                      Site data © Sandee, LLC.&nbsp;&nbsp; All rights reserved.
                    </p> */}
                  </Link>
                </div>
                <div
                  className="hidden md:flex justify-between items-center h-12"
                  role="navigation"
                >
                  <ul className="flex justify-between items-center list-none gap-6">
                    {headerNavLinks["newHome"].map((link) => (
                      <li
                        key={link.title}
                        className={`relative mx-2 mr-2 text-white text-sandee-sm font-semibold underline-offset-4 hover:underline group`} // Add 'group' here
                      >
                        <ActiveLinks
                          {...link}
                          className={`font-medium text-white text-sandee-18 sm:block w-auto h-auto`}
                          ActiveClassName="underline"
                          href={link.href}
                        >
                          {link.title}
                        </ActiveLinks>

                        {/* Submenu for items with sub_menus */}
                        {/* {link.sub_menus && (
                          <ul className="py-1.5 px-2 transform transition-all duration-300 ease-in-out border border-gray-200 absolute w-44 z-[500] rounded-md header-submenu  left-0 hidden group-hover:block bg-white shadow-lg mt-0 z-10">
                            {link.sub_menus.map((subLink) => (
                              <li key={subLink.title} className="text-black px-2 py-2  rounded-md hover:text-sandee-blue hover:bg-[#00aae330]">
                                <ActiveLinks
                                  {...subLink}
                                  className="font-medium text-black text-sandee-18  w-auto text-start flex items-center justify-start line-clamp-1"
                                // ActiveClassName="bg-[#87cefc54] sandee-blue"
                                >
                                  {subLink.title}
                                </ActiveLinks>
                              </li>
                            ))}
                          </ul>
                        )} */}
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="hidden md:flex justify-center items-center ">
                  {/* <button className="justify-center items-center flex bg-white py-2 text-sandee-blue transition duration-300 border-sandee-blue border-2  px-10 text-xs font-medium rounded-[15px] active:text-white active:bg-sandee-blue active:scale-[0.7] hover:scale-110">
                  Log In
                </button> */}
                  {/* <button className=" justify-center items-center  py-2 px-8 flex relative   overflow-hidden font-medium  text-white border-2 border-white active:text-white text-sandee-sm h-auto rounded-full group">
                  <span className="absolute bottom-0 left-0 flex w-full h-0 mb-0 transition-all duration-200 ease-out transform translate-y-0 bg-sandee-blue  opacity-90 group-hover:h-full"></span>
                  <span className="relative flex items-center group-hover:text-white ">
                    Log In
                  </span>
                </button> */}
                  <LogInButton />
                </div>
                <MobileNav headerNavLinks={headerNavLinks["Home"]} />
              </div>
            </CustomContainer>
          </div>
        </div>
      </header>
    </>
  );
};

export default HeaderHome;

{
  /* <div className="flex items-center space-x-4 leading-5 sm:space-x-6 w-full h-16 justify-end">
          {headerNavLinks
            .filter((link) => link.href !== "/")
            .map((link) => (
              <Link
                key={link.title}
                href={link.href}
                className=" font-medium text-gray-900 sm:block h-16 w-auto h-auto"
              >
                {link.title}
              </Link>
            ))}
          <SearchButton />
          <ThemeSwitch />
          <MobileNav />
        </div> */
}
