"use client"
import { siteMetadata } from "@/data/siteMetadata";
import MobileNav from "./MobileNav";
// import ThemeSwitch from "./ThemeSwitch";
// import SearchButton from "./SearchButton";
import { headerNavLinks } from "@/data/headerNavLinks";
import Image from "next/image";
import Link from "next/link";
import ActiveLinks from "./ActiveLinks";
import { CustomContainer } from "./Custom-Display";
import SearchButton from "./SearchButton";
import LogInButton from "./LogInButton";
import { useState } from "react";

const HeaderSlim = () => {

  const [isFocus, setIsFocus] = useState(false);
  // console.log(isFocus, "isFocus")
  return (
    <header className="w-full ">
      {/* <header className="w-full sticky top-0 bg-white z-50 "> */}
      <CustomContainer>
        <div className="flex justify-between items-center py-3 bg-white">
          <div className="flex  min-w-[135px] ">
            {/* <div className="hidden md:flex"> */}
            <Link href="/" className="text-center gap-y-1.5 flex flex-col items-center justify-center" aria-label={siteMetadata.headerTitle}>
              <div className="relative items-center justify-between w-[130px] h-7">
                <Image
                  src={"/static/images/Sandee-Blue.webp"}
                  fill
                  sizes="1080"
                  alt="Sandee logo Blue"
                  className="black-logo "
                />
              </div>
              {/* <p className="text-start font-normal exs:text-[8px] xs:text-[8px] 425:text-[8px] xl:text-xs text-sandee-blue ">
                Site data © Sandee, LLC.&nbsp;&nbsp; All rights reserved.
              </p> */}
            </Link>

          </div>
          <div className="hidden md:flex items-center justify-center">
            {/* <div className="w-3/4 md:w-1/2 lg:w-1/3 xl:w-1/3 flex justify-center items-center ">
            <SearchBar onTop />
          </div> */}

            <div className=" hidden md:flex justify-between items-center h-12 " role="navigation">
              <ul className={`flex justify-between list-none ${isFocus ? "gap-3" : "gap-7"}`}>
                {headerNavLinks["InnerPage"].map((link) => (
                  <li
                    key={link.title}
                    className="relative group text-black font-semibold underline-offset-4 hover:underline"
                  >
                    {/* Main Link */}
                    <ActiveLinks
                      {...link}
                      className="font-medium text-black text-sandee-18 h-16 w-auto text-center flex items-center justify-center line-clamp-1"
                      ActiveClassName="underline"
                    >
                      {link.title}
                    </ActiveLinks>

                    {/* Submenu for items with sub_menus */}
                    {/* {link.sub_menus && (
                      <ul className="py-1.5 px-3 transform transition-all duration-300 ease-in-out border border-gray-200 absolute w-44 z-[500] rounded-md header-submenu  left-0 hidden group-hover:block bg-white shadow-lg mt-0 z-10">
                        {link.sub_menus.map((subLink) => (
                          <li key={subLink.title} className="text-black px-2 py-2  rounded-md hover:text-sandee-blue hover:bg-[#00aae330]">
                            <ActiveLinks
                              {...subLink}
                              className="font-medium text-black text-sandee-18  w-auto text-start flex items-center justify-start line-clamp-1"
                            // ActiveClassName="bg-[#87cefc54] sandee-blue"
                            >
                              {subLink.title}
                            </ActiveLinks>
                          </li>
                        ))}
                      </ul>
                    )} */}
                  </li>
                ))}
              </ul>
            </div>
          </div>
          <div className={`md:hidden flex items-center justify-end gap-2 `}>
            <SearchButton isFocus={isFocus} setIsFocus={setIsFocus} />
            <MobileNav headerNavLinks={headerNavLinks["Home"]} />
          </div>
          <div className=" hidden md:flex items-center justify-end gap-2">
            <SearchButton isFocus={isFocus} setIsFocus={setIsFocus} />
            {/* <div className="min-w-[60px]"> */}
            <LogInButton />
            {/* </div> */}

          </div>
        </div>
      </CustomContainer>
    </header>
  );
};

export default HeaderSlim;
