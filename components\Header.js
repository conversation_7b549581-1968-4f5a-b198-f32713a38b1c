import HeaderHome from "./Header-Home";
import HeaderHomeGlass from "./Header-Home-Glass";
import HeaderHomeSearchUp from "./Header-Home-SearchUp";
import HeaderSlim from "./Header-Slim";

const Header = ({
  type = 1,
  name = (
    <>
      Choose Your Beach{" "}
      <sup className=" text-sandee-sm -top-4 lg:-top-7">TM</sup>
    </>
  ),
  // imageSrc = "https://images.sandee.com/Sandee-Head.avif",
  imageSrc = "https://images.sandee.com/compressed.jpg",

}) => {
  switch (type) {
    case 1:
      return <HeaderHome name={name} imageSrc={imageSrc} />;
    case 2:
      return <HeaderSlim name={name} imageSrc={imageSrc} />;
    case 3:
      return <HeaderHomeSearchUp name={name} imageSrc={imageSrc} />;
    case 4:
      return <HeaderHomeGlass name={name} imageSrc={imageSrc} />;

    default:
      return <h1> No Header Found</h1>;
  }
};

export default Header;
