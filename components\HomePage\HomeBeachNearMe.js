"use client";

import {
  API_BASE_URL,
  FinalImageGenerator,
  generateBreakpoints,
} from "@/helper/functions";
import axios from "axios";
import React, { useEffect, useState } from "react";
import NameTitle from "../Common/NameTitle";
import { CustomContainer, CustomGrid } from "../Custom-Display";
import BeachCard from "../Cards/BeachCard";
import CustomSwiper from "../Custom-Swiper";
export const getBeachNearMe = async (data) => {
  const APISingleBeachesMap = `${API_BASE_URL}/beachMain/getNearByBeach?limit=8&page=1&coordinates=${data}`;
  // logController(APICountryAllBeaches);
  const response = await axios.get(APISingleBeachesMap);
  return response?.data;
};
const HomeBeachNearMe = () => {
  const [location, setLocation] = useState(null);
  const [beachData, setBeachData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const getLocation = () => {
    if (typeof window !== "undefined" && window.navigator.geolocation) {
      window.navigator.geolocation.getCurrentPosition(
        (position) => {
          setLocation(
            `${position.coords.longitude},${position.coords.latitude}`
          );
          FetchBeachNearMe(
            `${position.coords.longitude},${position.coords.latitude}`
          );
        },
        (err) => {
          console.log(err, "err");
        }
      );
    } else {
    }
  };
  useEffect(() => {
    getLocation();
  }, []);
  const FetchBeachNearMe = async (location) => {
    try {
      setIsLoading(true);
      const Results = await getBeachNearMe(location);
      setBeachData(Results?.data);
      setIsLoading(false);
    } catch (err) {
      //  console.log(err)
    }
  };
  return (
    <>
      <CustomContainer>
        {isLoading ? (
          <div className="animate-pulse h-[265px] bg-slate-200 mt-5"></div>
        ) : location && beachData?.length ? (
          <>
            <NameTitle
              className="mt-5 "
              name={"Beaches Near Me"}
            // description={el?.description}
            // extraButton={
            //   <div className=" hidden md:flex justify-end items-start w-3/12">
            //     <Link href={`/list/${el?.nameSlug}`} className=" !text-nowrap">
            //       {ExtraButton ? (
            //         ExtraButton
            //       ) : (
            //         <CustomButton type={4}>
            //           View All
            //           <ExploreMoreArrow className=" ml-2 fill-sandee-blue group-hover:fill-white  h-4 w-4" />
            //         </CustomButton>
            //       )}
            //     </Link>
            //   </div>
            // }
            />
            <CustomSwiper
              data={beachData}
              Component={({ data: dataProps }) => {
                if (dataProps?.city?.state?.country?.slug) {
                  dataProps.link = `/${dataProps?.city?.state?.country?.slug}/${dataProps?.city?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.nameSlug}`; //
                  dataProps.location = `${dataProps?.city?.name}, ${dataProps?.city?.state?.country?.code}`; //
                } else {
                  dataProps.link = `/${dataProps?.country?.slug}/${dataProps?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.nameSlug}`; //
                  dataProps.location = `${dataProps?.city?.name}, ${dataProps?.country?.code}`; //
                }
                dataProps.imageSrc = FinalImageGenerator(
                  dataProps?.images?.[0]
                );
                return BeachCard({
                  data: { ...dataProps, ...dataProps },
                });
              }}
              settingsExtra={{
                breakpoints: {
                  ...generateBreakpoints(300, 768, 50, 180),
                  ...generateBreakpoints(768, 1300, 50, 280),
                  ...generateBreakpoints(1300, 2400, 50, 330),
                },
                spaceBetween: 22,
                // breakpoints: {
                //   300: { slidesPerView: 1.2 },
                //   800: { slidesPerView: 3 },
                //   1200: { slidesPerView: 5.5 },
                //   1400: { slidesPerView: 6.5 },
                // },
              }}
              cardHeight={260}
              slideClassName={`max-w-[180px] lg:max-w-[240px] xl:max-w-[230px]  2xl:max-w-[250px] 3xl:max-w-[300px] mr-8`}
              className="mb-2 mt-[9.2px]"
              id={"me_1"}
            // extra={
            //   <div className="relative h-[300px]  rounded-xl flex items-center justify-center ">
            //     <Link href={`/list/${listSlug}`}>
            //   <CustomButton type={4}>
            //     View More
            //   </CustomButton>
            //     </Link>
            //   </div>
            // }
            />
            {/* <CustomGrid
              className="!my-5 gap-4 sm:gap-8 md:hidden "
              Component={({ data: dataProps }) => {
                if (dataProps?.city?.state?.country?.slug) {
                  dataProps.link = `/${dataProps?.city?.state?.country?.slug}/${dataProps?.city?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.nameSlug}`;
                  dataProps.location = `${dataProps?.city?.name}, ${dataProps?.city?.state?.country?.name}`;
                } else {
                  dataProps.link = `/${dataProps?.country?.slug}/${dataProps?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.nameSlug}`;
                  dataProps.location = `${dataProps?.city?.name}, ${dataProps?.country?.name}`;
                }
                dataProps.imageSrc = FinalImageGenerator(
                  dataProps?.images?.[0]
                );
                return BeachCard({
                  data: { ...dataProps, ...dataProps },
                });
              }}
              data={beachData}
              xs={2}
              sm={3}
              md={3}
              lg={4}
              xl={5}
              xxl={5}
              xxxl={5}
            /> */}
            {/* <div className=" flex md:hidden justify-center items-center mb-5">
    <Link href={`/list/${el?.nameSlug}`} className=" ">
      {ExtraButton ? (
        ExtraButton
      ) : (
        <CustomButton type={4}>
          View All
          <ExploreMoreArrow className=" ml-2 fill-sandee-blue group-hover:fill-white  h-4 w-4" />
        </CustomButton>
      )}
    </Link>
  </div> */}{" "}
          </>
        ) : null}
      </CustomContainer>
    </>
  );
};

export default HomeBeachNearMe;
