import { HomePageData } from "@/data/HomePageData";
import React from "react";
import { CustomGrid } from "../Custom-Display";
import Link from "next/link";
import BlogCard from "../Cards/BlogCard";
import { ExploreMoreArrow, ReadBookIcon } from "../social-icons/icons";
import NameTitle from "../Common/NameTitle";

const Home_Blog_Section = ({ data }) => {
  return (
    <>
      <NameTitle
        className="mt-5"
        description={"EXPERT ADVICE AND TIPS"}
        name={HomePageData.BlogSection.title}
        extraButton={
          <div className=" hidden md:flex justify-end items-start w-3/12">
            <Link
              href={HomePageData.BlogSection.button_link}
              className=" custom-hover-slide-button group"
            >
              <span className="custom-hover-slide group-hover:h-full"></span>
              <span className="custom-hover-slide-text group-hover:text-white font-bold ">
                <ReadBookIcon className=" me-2 fill-transparent stroke-sandee-orange group-hover:stroke-white  h-5 w-5 -mt-0.5" />
                {HomePageData.BlogSection.button_text}
              </span>
            </Link>
          </div>
        }
        type={2}
      />

      <CustomGrid
        data={data}
        // className="gap-10 !my-5"
        className="gap-8 mb-5 mt-[6px]"
        Component={({ data: dataProps }) => {
          dataProps.link = `/blog/${dataProps?.slug}`; //
          // dataProps.imageSrc = FinalImageGenerator(dataProps?.listicleImage);
          dataProps.imageSrc = dataProps?.image;
          dataProps.name = dataProps?.title;
          return BlogCard({
            data: { ...dataProps },
            copyRightsData: dataProps?.image,
          });
        }}
        xs={1}
        sm={2}
        md={2}
        lg={3}
        xl={4}
      />
      <div className=" md:hidden my-5">
        <Link
          href={HomePageData.BlogSection.button_link}
          className=" custom-hover-slide-button group"
        >
          <span className="custom-hover-slide group-hover:h-full"></span>
          <span className="custom-hover-slide-text group-hover:text-white font-bold">
            <ReadBookIcon className=" me-2 fill-transparent stroke-sandee-orange group-hover:stroke-white h-5 w-5 -mt-0.5" />
            {HomePageData.BlogSection.button_text}
          </span>
        </Link>
      </div>
    </>
  );
};

export default Home_Blog_Section;
