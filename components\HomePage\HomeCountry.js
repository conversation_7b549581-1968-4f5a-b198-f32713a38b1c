import { HomePageData } from "@/data/HomePageData";
import React from "react";
import { CustomGrid } from "../Custom-Display";
import Link from "next/link";
import CountryCard from "../Cards/CountryCard";
import { CategoryIcon, ExploreMoreArrow } from "../social-icons/icons";
import NameTitle from "../Common/NameTitle";

const Home_Country_Section = ({ data }) => {
  return (
    <>
      <NameTitle
        className="mt-5"
        name={HomePageData.CountriesSection.title}
        description={
          <>
            <span className=" font-semibold">
              {HomePageData.CountriesSection.sub_title}
            </span>
            {HomePageData.CountriesSection.description}
          </>
        }
        extraButton={
          <div className=" hidden md:flex justify-end items-start ">
            <Link
              href={HomePageData.CountriesSection.button_link}
              className=" custom-hover-slide-button group !text-nowrap"
            >
              <span className="custom-hover-slide group-hover:h-full"></span>
              <span className="custom-hover-slide-text group-hover:text-white !text-nowrap font-bold">
                <CategoryIcon className="me-2 fill-sandee-orange group-hover:fill-white h-4 w-4" />
                {HomePageData.CountriesSection.button_text}
              </span>
            </Link>
          </div>
        }
      />
      <CustomGrid
        className=" gap-4 sm:gap-4 mb-5 mt-[11.2px]"
        Component={({ data: dataProps }) => {
          dataProps.images = [dataProps?.image];
          dataProps.image = dataProps?.image?.imageUrl;
          dataProps.link = `/${dataProps?.slug}`;
          return CountryCard({
            data: { ...dataProps },
            copyRightsData: dataProps?.images,
          });
        }}
        data={data}
        xs={2}
        sm={2}
        md={3}
        lg={4}
        xl={4}
        xxl={4}
        xxxl={4}
      />
      <div className="md:hidden my-5">
        <Link
          href={HomePageData.CountriesSection.button_link}
          className=" custom-hover-slide-button group"
        >
          <span className="custom-hover-slide group-hover:h-full"></span>
          <span className="custom-hover-slide-text group-hover:text-white font-bold">
            <CategoryIcon className=" me-2 fill-sandee-orange group-hover:fill-white  h-4 w-4" />
            {HomePageData.CountriesSection.button_text}
          </span>
        </Link>
      </div>
    </>
  );
};

export default Home_Country_Section;
