import React from "react";
import { CustomGrid } from "../Custom-Display";
import { HomePageData } from "@/data/HomePageData";
import FilterCard from "../Cards/FilterCard";
import Link from "next/link";
import { ExploreMoreArrow } from "../social-icons/icons";

const Home_Filter_Component = ({ data }) => {
  return (
    <div className=" mb-0">
      <div className="  flex  md:flex-row flex-col md:items-center items-start gap-10">
        <div className=" w-full  md:w-5/12 xl:w-5/12 flex justify-center items-center lg:mt-3 mt-[1.75rem]">
          <div className="flex flex-col text-start">
            <p className="3xl:text-sandee-48 xl:text-[44px] lg:text-[34px] text-sandee-24 font-semibold leading-none" role="heading" aria-level="1">
              {HomePageData?.FilterSection?.title}
            </p>
            <p className=" py-3 md:py-6 text-sandee-18 font-normal leading-7 text-sandee-grey  text-start md:block hidden">
              {HomePageData?.FilterSection?.description}
            </p>
            <div className="md:flex justify-start items-center hidden">
              <Link
                href={`/countries`}
                className=" custom-hover-slide-button group !bg-transparent"
              >
                <span className="custom-hover-slide group-hover:h-full"></span>
                <span className="custom-hover-slide-text group-hover:text-white ">
                  Explore Countries
                  <ExploreMoreArrow className=" ml-2 fill-sandee-orange group-hover:fill-white  h-4 w-4" />
                </span>
              </Link>
            </div>
          </div>
        </div>
        <div className=" w-full  md:w-7/12 xl:w-7/12">
          <CustomGrid
            data={HomePageData?.FilterSection?.cards?.map((el) => ({
              ...el,
              count: data?.[el?.id] ?? 0,
            }))}
            Component={FilterCard}
            className="gap-4 sm:gap-8"
            xs={2}
            sm={2}
            // md={2}
            lg={3}
          // xl={3}
          />
        </div>
      </div>
    </div>
  );
};

export default Home_Filter_Component;
