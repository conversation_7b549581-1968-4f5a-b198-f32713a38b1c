import { siteMetadata } from "@/data/siteMetadata";
import React from "react";

const HomeJSONLD = () => {
  const jsonLdOrganization = {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: siteMetadata?.title || "Sandee",
    description: siteMetadata?.description ||
      "Discover the Best Beautiful Beaches in the World - Your Ultimate Guide to Top Beach Destinations for Vacations with family. Explore the Paradise You've Been Dreaming Of!",
    url: "https://sandee.com/",
    logo: "https://sandee.com/_next/image?url=%2Fstatic%2Fimages%2FSandee-Blue.webp&w=1920&q=75",
    // contactPoint: {
    //   "@type": "ContactPoint",
    //   telephone: "******-472-7600",
    //   contactType: "customer service",
    //   areaServed: "US",
    //   availableLanguage: "en",
    // },
    // address: {
    //   "@type": "PostalAddress",
    //   streetAddress: "12011 San Vicente Blvd # 405",
    //   addressLocality: "Los Angeles",
    //   addressRegion: "CA",
    //   postalCode: "90049",
    //   addressCountry: "USA",
    // },
    sameAs: [
      "https://www.facebook.com/sandeebeachescompany/",
      "https://twitter.com/sandee?lang=en",
      "https://www.instagram.com/sandee/",
      "https://www.youtube.com/@sandee",
    ],
  };
  const jsonLocalSchema = {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    name: siteMetadata?.title || "Sandee",
    image:
      "https://sandee.com/_next/image?url=https%3A%2F%2Fimages.unsplash.com%2Fphoto-1582076197950-7a1dcdd1e07f%3Fcrop%3Dentropy%26cs%3Dsrgb%26fm%3Djpg%26ixid%3DM3wzMjM2NjF8MHwxfHNlYXJjaHwxMzh8fGJvbmRpJTIwYmVhY2h8ZW58MHwwfHx8MTcyNjA5NTAzMXww%26ixlib%3Drb-4.0.3%26q%3D85&w=1920&q=75",
    "@id": "",
    url: "https://www.google.com/search?q=sandee+%E2%80%93+your+ultimate+beach+guide&newwindow=1&sca_esv=c152e517206e90dd&rlz=1C1YTUH_enIN1104IN1104&sxsrf=ADLYWILMCMVBWwtBCTQwnVpcmrcz6R5uAg%3A1728298179680&ei=w7wDZ9WSKfuWnesP5dn0oQU&oq=sandee&gs_lp=Egxnd3Mtd2l6LXNlcnAiBnNhbmRlZSoCCAAyBxAjGLADGCcyBxAjGLADGCcyBxAjGLADGCcyChAAGLADGNYEGEcyChAAGLADGNYEGEcyChAAGLADGNYEGEcyChAAGLADGNYEGEcyChAAGLADGNYEGEcyChAAGLADGNYEGEcyChAAGLADGNYEGEcyDRAAGIAEGLADGEMYigUyDRAAGIAEGLADGEMYigUyDhAAGLADGOQCGNYE2AEBMg4QABiwAxjkAhjWBNgBATITEC4YgAQYsAMYQxjIAxiKBdgBATIZEC4YgAQYsAMY0QMYQxjHARjIAxiKBdgBATITEC4YgAQYsAMYQxjIAxiKBdgBATITEC4YgAQYsAMYQxjIAxiKBdgBAUjVC1AAWABwAngBkAEAmAEAoAEAqgEAuAEByAEAmAICoAI6mAMAiAYBkAYSugYGCAEQARgJkgcBMqAHAA&sclient=gws-wiz-serp",
    telephone: "(*************",
    address: {
      "@type": "PostalAddress",
      streetAddress: "San Vicente Blvd, Los Angeles, CA 90049",
      addressLocality: "LA",
      addressRegion: "CA",
      postalCode: "12011",
      addressCountry: "US",
    },
    geo: {
      "@type": "GeoCoordinates",
      latitude: 34.05254748803338,
      longitude: -118.47341433333834,
    },
    sameAs: [
      "https://www.facebook.com/sandeebeachescompany/",
      "https://twitter.com/sandee?lang=en",
      "https://www.instagram.com/sandee/",
      "https://www.youtube.com/@sandee",
    ],
  };

  // const jsonLdSearch = {
  //   "@context": "https://schema.org",
  //   "@type": "WebSite",
  //   url: "https://www.sandee.com/",
  //   potentialAction: [
  //     {
  //       "@type": "SearchAction",
  //       target: {
  //         "@type": "EntryPoint",
  //         urlTemplate: "https://sandee.com/search?q={search_term_string}",
  //       },
  //       "query-input": "required name=search_term_string",
  //     },
  //     {
  //       "@type": "SearchAction",
  //       target: {
  //         "@type": "EntryPoint",
  //         urlTemplate:
  //           "android-app://com.sandee/https/sandee.com/search/?q={search_term_string}",
  //       },
  //       "query-input": "required name=search_term_string",
  //     },
  //   ],
  // };
  const jsonLDFAQWebPage = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    url: "https://sandee.com/",
    name: siteMetadata?.title || "Sandee",
    description: siteMetadata?.description ||
      "Plan your perfect beach day with our beach map, guide, and information. Get the latest on beach parties and the world's most beautiful beaches.",
    publisher: { "@type": "Organization", name: "Sandee" },
    keywords:
      "best beaches in the world,beautiful beaches in the world,best beaches in the world for vacation,best beaches destinations in the world,best beaches in the world for families,world beach guide",
  };
  return (
    <>
      <script
        async
        type="application/ld+json"
        id="application/ld+jsonOrganization"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLdOrganization) }}
      ></script>
      <script
        async
        type="application/ld+json"
        id="application/ld+jsonLocalBusiness"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLocalSchema) }}
      ></script>
      <script
        type="application/ld+json"
        id="application/ld+jsonWebPage"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLDFAQWebPage) }}
      ></script>
      {/* <script
        type="application/ld+json"
        id="application/ld+jsonSearch"
        dangerouslySetInnerHTML={{__html: JSON.stringify(jsonLdSearch)}}
      ></script> */}
    </>
  );
};

export default HomeJSONLD;
