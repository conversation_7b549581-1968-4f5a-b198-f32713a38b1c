import { HomePageData } from "@/data/HomePageData";
import React from "react";
import { CustomGrid } from "../Custom-Display";
import { FinalImageGenerator } from "@/helper/functions";
import ListCard from "../Cards/ListCard";
import Link from "next/link";
import { ExploreMoreArrow, ReadBookIcon } from "../social-icons/icons";
import NameTitle from "../Common/NameTitle";

const Home_List_Section = ({ data }) => {
  return (
    <>
      <NameTitle
        className="mt-12 mb-[0.5rem]"
        description={" OUR EXPERTLY CRAFTED LISTS"}
        name={HomePageData.ListSection.title}
        extraButton={
          <div className=" hidden md:flex justify-end items-start w-3/12">
            <Link
              href={HomePageData.ListSection.button_link}
              className="custom-hover-slide-button group"
            >
              <span className="custom-hover-slide group-hover:h-full"></span>
              <span className="custom-hover-slide-text group-hover:text-white font-bold">
                <ReadBookIcon className=" me-2 fill-transparent stroke-sandee-orange group-hover:stroke-white  h-5 w-5 -mt-0.5" />
                {HomePageData.ListSection.button_text}
              </span>
            </Link>
          </div>
        }
        type={2}
      />

      <CustomGrid
        data={data}
        className="gap-4 sm:gap-8 mb-5 mt-[6px]"
        Component={({ data: dataProps }) => {
          dataProps.link = `/list/${dataProps?.nameSlug}`; //
          dataProps.imageSrc = FinalImageGenerator(dataProps?.listicleImage);
          return ListCard({ data: { ...dataProps } });
        }}
        xs={1}
        sm={2}
        // md={2}
        lg={3}
      // xl={3}
      />
      <div className=" md:hidden mb-6">
        <Link
          href={HomePageData.ListSection.button_link}
          className=" custom-hover-slide-button group"
        >
          <span className="custom-hover-slide group-hover:h-full"></span>
          <span className="custom-hover-slide-text group-hover:text-white font-bold">
            <ReadBookIcon className=" me-2 fill-transparent stroke-sandee-orange group-hover:stroke-white h-5 w-5 -mt-0.5" />
            {HomePageData.ListSection.button_text}
          </span>
        </Link>
      </div>
    </>
  );
};

export default Home_List_Section;
