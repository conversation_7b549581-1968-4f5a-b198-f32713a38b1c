import React from "react";
import SingleListicle from "../SingleListicle";
import Link from "next/link";
import { BeachIcon, CategoryIcon, ExploreMoreArrow } from "../social-icons/icons";
import { CustomContainer } from "../Custom-Display";
import CustomButton from "../Custom-Button";
import NameTitle from "../Common/NameTitle";

const Home_Listcle_Section = ({
  data,
  className = "",
  buttonType = { type: 4, color: "fill-sandee-orange" },
  ExtraButton,
  index = null,
  dataTestid = "home-listicle-section",
}) => {

  // const FilteredData = data?.filter(
  //   (el) => !!el?.listiclesBeaches?.filter((beach) => beach?.AllBeach)?.length
  // );
  // const FilteredData = data?.filter(
  //   (el) => !!el?.listiclesBeaches?.filter((beach) => beach?.AllBeach)?.length
  // );
  const homeData = data?.rows && data?.rows?.length ? data?.rows : data;
  const FilteredData = index === null
    ? homeData?.filter(el => el?.listiclesBeaches?.some(beach => beach?.AllBeach))
    : [homeData?.[index]];

  return (
    <section data-testid={dataTestid} className={`${className}`}>
      {FilteredData?.map((el, i) => {
        // const FilteredListicleData = el?.listiclesBeaches?.filter(
        //   (beach) => beach?.AllBeach
        // );
        return (
          <div
            key={el?.id}
            className={`mt-5`}
            data-testid={`home-listicle-item-${i}`}
          // className={`my-5 ${i % 2 === 0 ? "" : "bg-[#F6F6F6]"}`}
          >
            {/* className="mt-[30px]" */}
            <CustomContainer dataTestid={`home-listicle-container-${i}`}>
              <NameTitle
                className=""
                name={el?.name}
                dataTestid={`home-listicle-title-${i}`}
                // description={el?.description}
                extraButton={
                  <div className=" hidden md:flex justify-end items-start w-3/12"
                    data-testid={`home-listicle-extra-button-${i}`}>
                    <Link
                      href={`/list/${el?.nameSlug}`}
                      className=" !text-nowrap"
                      data-testid={`home-listicle-link-${i}`}
                    >
                      {ExtraButton ? (
                        ExtraButton
                      ) : (
                        <CustomButton type={buttonType?.type}>
                          <CategoryIcon
                            className={`me-2 ${buttonType?.color} group-hover:fill-white  h-4 w-4`}
                          />
                          View All
                        </CustomButton>
                      )}
                    </Link>
                  </div>
                }
              />
              <SingleListicle
                data={el?.listiclesBeaches}
                listSlug={el?.nameSlug}
                id={el?.id}
                dataTestid={`home-listicle-single-${i}`}
              />
              <div className=" md:hidden mb-9" data-testid={`home-listicle-mobile-button-${i}`}>
                {ExtraButton ? (
                  ExtraButton
                ) : (
                  <Link
                    href={`/list/${el?.nameSlug}`}
                    className="custom-hover-slide-button group font-bold "
                    data-testid={`home-listicle-mobile-link-${i}`}
                  >
                    <span className="custom-hover-slide group-hover:h-full"></span>
                    <span className="custom-hover-slide-text group-hover:text-white font-bold">
                      <CategoryIcon className=" me-2 fill-sandee-orange group-hover:fill-white h-4 w-4" />
                      View All
                    </span>
                  </Link>
                  // <CustomButton type={4}>
                  //   <BeachIcon className=" me-2 fill-sandee-blue group-hover:fill-white  h-4 w-4" />
                  //   View All
                  // </CustomButton>
                )}
              </div>
            </CustomContainer>
          </div>
        );
        // : (
        //   ""
        // );
      })}
    </section>
  );
};

export default Home_Listcle_Section;

{
  /**
   *   <Link
   *    href={`/list/${el?.nameSlug}`}
   *    className=" custom-hover-slide-button group"
   *    >
   *    <span className="custom-hover-slide group-hover:h-full"></span>
   *    <span className="custom-hover-slide-text group-hover:text-white ">
   *      View All
   *      <ExploreMoreArrow className=" ml-2 fill-sandee-blue group-hover:fill-white  h-4 w-4" />
   *    </span>
   *  </Link>
   */
}
