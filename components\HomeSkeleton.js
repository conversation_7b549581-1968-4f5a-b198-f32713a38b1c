import { CustomContainer, CustomGrid } from "@/components/Custom-Display";
import React from "react";
//change
const HomeSkeleton = () => {
  return (
    <div className="animate-pulse">
         <div className="animate-pulse h-20 bg-slate-200 my-4"></div>
         <div className="animate-pulse h-96 bg-slate-200 my-4"></div>
      <CustomContainer>
        {/**
         *
         * Heading
         *
         */}

        <div className="heading my-8">
          <div className="animate-pulse h-5 bg-slate-200 my-4"></div>
          <div className="animate-pulse h-5 bg-slate-200 my-4"></div>
        </div>

        <div className="heading my-2">
          <div className="animate-pulse h-5 bg-slate-200 my-4"></div>
          <div className="animate-pulse h-4 bg-slate-200 my-4"></div>
          <div className="animate-pulse h-3 bg-slate-200 my-4"></div>
        </div>
        {/**
         *
         * Country Section
         *
         */}
        <CustomGrid
          className="gap-4 sm:gap-8 my-6"
          Component={() => {
            return (
              <div className="relative h-[250px] group rounded-sandee bg-slate-200 animate-pulse"></div>
            );
          }}
          data={Array(8).fill(1)}
          xs={1}
          sm={2}
          md={3}
          lg={3}
          xl={4}
          xxl={4}
          xxxl={4}
        />
        <div className=" h-14 bg-slate-200 animate-pulse my-8 "></div>

        {/**
         *
         * Listicle Section
         *
         */}
        {Array(2)
          .fill(1)
          .map((el, i) => {
            return (
              <div className="listicle-skeleton my-8 " key={i}>
                <div className="heading">
                  <div className="animate-pulse h-5 bg-slate-200 my-4"></div>
                  <div className="animate-pulse h-5 bg-slate-200 my-4"></div>
                </div>
                <div className=" my-8 h-[220px] bg-slate-200 animate-pulse "></div>
              </div>
            );
          })}
      </CustomContainer>
      {/**
       *
       * Filter Section
       *
       */}
      <div className="bg-slate-400 mb-10">
        <CustomContainer className="">
          <div className="px-4 py-8 sm:px-10  flex  md:flex-row flex-col mb-10 gap-10">
            <div className=" w-full  md:w-7/12 xl:w-6/12">
              <CustomGrid
                data={Array(6).fill(1)}
                Component={() => {
                  return (
                    <div className="relative h-[150px] group rounded-sandee bg-slate-200 animate-pulse"></div>
                  );
                }}
                xs={2}
                sm={2}
                md={2}
                lg={3}
                // xl={3}
              />
            </div>
            <div className=" w-full  md:w-5/12 xl:w-6/12 flex justify-center items-center">
              <div className="flex justify-center items-center flex-col text-center">
                <div className=" h-28 font-medium"></div>
                <div className=" h-28 font-medium"></div>
              </div>
            </div>
          </div>
        </CustomContainer>
      </div>
      <CustomContainer>
        {/**
         *
         * Blog Section
         *
         */}
        <div className="heading my-2">
          <div className="animate-pulse h-5 bg-slate-200 my-4"></div>
          <div className="animate-pulse h-6 bg-slate-200 my-4"></div>
        </div>
        <CustomGrid
          className="gap-4 sm:gap-8 my-6 mb-12"
          data={Array(6).fill(1)}
          Component={() => {
            return (
              <div className="relative h-[105px] group rounded-sandee bg-slate-200 animate-pulse"></div>
            );
          }}
          xs={1}
          sm={2}
          // md={2}
          lg={3}
          // xl={3}
        />
        {/**
         *
         * Blog Section
         *
         */}
        <div className="heading my-2">
          <div className="animate-pulse h-5 bg-slate-200 my-4"></div>
          <div className="animate-pulse h-6 bg-slate-200 my-4"></div>
        </div>
        <CustomGrid
          className="gap-4 sm:gap-8 my-6"
          data={Array(6).fill(1)}
          Component={() => {
            return (
              <div className="relative h-[105px] group rounded-sandee bg-slate-200 animate-pulse"></div>
            );
          }}
          xs={1}
          sm={2}
          // md={2}
          lg={3}
          // xl={3}
        />
        <div className=" h-14 bg-slate-200 animate-pulse my-8"></div>
      </CustomContainer>
    </div>
  );
};

export default HomeSkeleton;
