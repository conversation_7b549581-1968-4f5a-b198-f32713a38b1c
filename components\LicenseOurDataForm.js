import { But<PERSON>, Drawer, Space, Checkbox, message } from 'antd';
import React, { useState } from 'react';
import { CloseIcon } from './social-icons/icons';
// import { useRouter, useSearchParams } from 'next/navigation';
// import Link from 'next/link';
import useLoggedIn from '@/helper/hook/useLoggedIn';
import { licenseData } from '@/app/(HomeHeader)/action';

const LicenseOurDataForm = ({
    open = false,
    setOpen
}) => {
    const [loading, setLoading] = useState(false);
    const [consent, setConsent] = useState(false);
    const { token } = useLoggedIn();

    const [formData, setFormData] = useState({
        name: null,
        company: '',
        email: '',
        useCase: ''
    });

    const [interests, setInterests] = useState({
        licensing: false,
        api: false,
        both: false
    });

    // const router = useRouter();
    // const searchParams = useSearchParams();
    // const searches = searchParams.get('form');

    // Handle input changes
    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    // Handle textarea change with character limit
    const handleTextareaChange = (e) => {
        const { name, value } = e.target;
        if (value.length <= 255) {
            setFormData(prev => ({
                ...prev,
                [name]: value
            }));
        }
    };

    // Handle interest checkbox changes
    const handleInterestChange = (key) => {
        setInterests(prev => {
            // If "both" is selected, toggle all
            if (key === 'both') {
                const newValue = !prev.both;
                return {
                    licensing: newValue,
                    api: newValue,
                    both: newValue
                };
            }

            // For individual checkboxes
            const newInterests = {
                ...prev,
                [key]: !prev[key]
            };

            // Update "both" checkbox based on the other two
            newInterests.both = newInterests.licensing && newInterests.api;

            return newInterests;
        });
    };
    const clearFormData = () => {
        setFormData({
            name: "",
            company: '',
            email: '',
            useCase: ''
        });
        setInterests({
            licensing: false,
            api: false,
            both: false
        });
        setConsent(false);
    }

    // Handle form submission
    const handleSubmit = async (e) => {
        e.preventDefault();
        // Validate form
        if (!formData.email && !formData.name && !formData.useCase) {
            message.error('Please fill all required fields');
            return;
        }
        if (!interests.licensing && !interests.api) {
            message.error('Please select at least one interest');
            return;
        }

        if (!consent) {
            message.error('Please agree to the Terms and Privacy Policy');
            return;
        }


        setLoading(true);
        // Replace empty strings with null
        const data = formData
        for (const key in data) {
            if (data[key] === "") {
                data[key] = null;
            }
        }
        try {
            const payload = {
                ...data,
                interestedIn: [
                    ...(interests?.licensing ? ['licensing'] : []),
                    ...(interests?.api ? ['api'] : [])
                ]
            };
            const response = await licenseData(payload, token);
            if (response?.status === "success") {
                message.success('Request API Access submitted successfully!');
                clearFormData();
                setOpen(pr => !pr);
                setLoading(false);
                // router.push('/', { scroll: false });
            } else {
                throw new Error(response?.message || 'Submission failed');
            }
        } catch (error) {
            setLoading(false);
            console.error('Submission error:', error);
            message.error(error.message || 'Failed to submit form');
        }
    };

    return (
        <Drawer
            title={
                <div className="flex items-center justify-between">
                    <p className="form-modal-head mb25 text-xl">{"License Our Data / Request API Access"}</p>
                    <Space>
                        <div
                            onClick={() => {
                                setOpen(pr => !pr);
                                clearFormData();
                            }}
                            // href="/"
                            scroll={false}
                            className="cursor-pointer rounded-lg border-[1px] border-[#D9D9D9] w-8 h-8 flex items-center justify-center"
                        >
                            <CloseIcon className="text-success-300" />
                        </div>
                    </Space>
                </div>
            }
            width={500}
            className="rounded-xl"
            closeIcon={false}
            open={open}
            // open={searches == "license-data"}
            maskClosable={false}
            styles={{
                body: { padding: "16px 24px" },
                footer: {
                    border: 0,
                    display: "flex",
                    flexDirection: "row-reverse",
                },
            }}
            style={{ zIndex: 99 }}
            footer={
                <Button
                    key="submit"
                    className={`bg-sandee-blue text-white w-full h-14 text-lg font-semibold mb-2 mt-2 login-button ${loading ? "" : ""
                        }`}
                    type='submit'
                    htmlType="submit"
                    form="licenseDataForm"
                    loading={loading}
                >
                    {"Submit"}
                </Button>
            }
        >
            <form
                id="licenseDataForm"
                className="space-y-6"
                onSubmit={handleSubmit}
            >
                <p className="text-base font-semibold text-black mb-6">
                    Interested in using Sandee&apos;s proprietary beach data?  Please fill out this quick form and we&apos;ll get back to you shortly.
                </p>

                {/* Name */}
                <div>
                    <label className="block text-base font-semibold text-gray-700 mb-1">Name<span className='text-[#fb3333]'>*</span></label>
                    <input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        className="input focus:border-sandee-blue focus:border-solid focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                        placeholder="Enter your name"
                    />
                </div>

                {/* Company */}
                <div>
                    <label className="block text-base font-semibold text-gray-700 mb-1">Company</label>
                    <input
                        type="text"
                        name="company"
                        value={formData.company}
                        onChange={handleInputChange}
                        className="input focus:border-sandee-blue focus:border-solid focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                        placeholder="Enter your company name"
                    />
                </div>

                {/* Email */}
                <div>
                    <label className="block text-base font-semibold text-gray-700 mb-1">
                        Email<span className='text-[#fb3333]'>*</span>
                    </label>
                    <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="input focus:border-sandee-blue focus:border-solid focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                        placeholder="Enter your email address"
                    />
                </div>

                {/* Interests */}
                <div>
                    <label className="block text-base font-semibold text-gray-700 mb-2">
                        I&apos;m interested in:<span className='text-[#fb3333]'>*</span>
                    </label>
                    <div className="space-y-2">
                        <div className="flex items-center">
                            <Checkbox
                                checked={interests.licensing}
                                onChange={() => handleInterestChange('licensing')}
                                className="mr-2"
                            />
                            <span>Licensing Beach Data</span>
                        </div>
                        <div className="flex items-center">
                            <Checkbox
                                checked={interests.api}
                                onChange={() => handleInterestChange('api')}
                                className="mr-2"
                            />
                            <span>API Access</span>
                        </div>
                        <div className="flex items-center">
                            <Checkbox
                                checked={interests.both}
                                onChange={() => handleInterestChange('both')}
                                className="mr-2"
                            />
                            <span>Both</span>
                        </div>
                    </div>
                </div>

                {/* Usage */}
                <div>
                    <label className="block text-base font-semibold text-gray-700 mb-1">
                        How do you propose to use our proprietary beach data?<span className='text-[#fb3333]'>*</span>
                    </label>
                    <textarea
                        name="useCase"
                        value={formData.useCase}
                        onChange={handleTextareaChange}
                        required
                        className="input focus:border-sandee-blue focus:border-solid focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                        rows={4}
                        placeholder="Describe how you plan to use the data/API (max 255 characters)"
                        maxLength={255}
                    />
                    <div className={`text-right text-sm ${formData.useCase.length < 255 ? "text-gray-500" : "text-[#fb3333]"} mt-1`}>
                        {formData.useCase.length}/255 characters
                    </div>
                </div>

                {/* Consent */}
                <div className="pt-2">
                    <div className="flex items-center">
                        <Checkbox
                            checked={consent}
                            onChange={(e) => setConsent(e.target.checked)}
                            className="mr-2"
                        />
                        <span className="text-gray-700">
                            I agree to {' '}
                            <a href="/terms" className="text-sandee-blue hover:underline">
                                Terms
                            </a>{' '}
                            and{' '}
                            <a href="/privacy-policy" className="text-sandee-blue hover:underline">
                                Privacy Policy
                            </a>.
                        </span>
                    </div>
                </div>
            </form>
        </Drawer>
    );
};

export default LicenseOurDataForm;