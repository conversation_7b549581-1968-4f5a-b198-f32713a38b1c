"use client";
import React, { useEffect, useRef, useState } from "react";
import { CustomContainer, CustomGrid } from "../Custom-Display";
import ListCard from "../Cards/ListCard";
import { FinalImageGenerator } from "@/helper/functions";
import { ListgetAllListicle } from "@/app/(HomeHeader)/action";
import ScrollBar from "../ScrollBar";
import List_Filter_Section from "./List_Filter_Section";

const AllListWithScroll = ({ initialData }) => {
  const [listicleData, setListicleData] = useState(initialData);
  const [selectedCategory, setSelectedCategory] = useState([]);
  const [selectedCountry, setSelectedCountry] = useState("");
  const [query, setQuery] = useState("");
  const [selection, setSelection] = useState("DESC");
  const [scrollLoader, setScrollLoader] = useState(false);
  const [loadoff, setLoadOff] = useState(false);
  const [pagination, setPagination] = useState({
    limit: 100,
    page: 1,
  });
  const [refresh, setRefresh] = useState(false);

  const prevSelection = useRef(selection);
  const prevSelectedCategory = useRef(selectedCategory);
  const prevSelectedCountry = useRef(selectedCountry);
  const prevQuery = useRef(query);
  const delayedSearch = async (
    queryValue,
    selectionValue,
    selectedCategoryValue,
    selectedCountry
  ) => {
    const query = { ...pagination };
    if (queryValue?.trim()?.length) {
      query.searchQuery = queryValue;
    }

    if (selectedCategoryValue?.length) {
      query.tag = selectedCategoryValue?.join(",");
    }
    if (selectedCountry) {
      // query.countryId = selectedCountry?.join(",");
      query.countryId = selectedCountry;
    }

    if (Object.keys(query)?.length > 0) {
      if (!!selectionValue) {
        query.sortBy = "createdAt";
        query.sortOrder = selectionValue;
      }
    }
    const { data } = await ListgetAllListicle({ ...query });
    // let filterdData=data?.filter((i)=> i?.countryName !== null)
    if (data?.length && pagination.page > 1) {
      setListicleData((prev) => [...prev, ...data]);
      setLoadOff(false);
    } else if (data?.length && pagination.page === 1) {
      setListicleData((prev) => [...data]);
      setLoadOff(false);
    } else {
      setLoadOff(true);
    }
    setScrollLoader(false);
  };

  useEffect(() => {
    // if (pagination?.page > 2) {
    setScrollLoader(true);
    delayedSearch(query, selection, selectedCategory, selectedCountry);
    // }
  }, [pagination.page, refresh]);

  useEffect(() => {
    if (
      prevSelection.current !== selection ||
      prevSelectedCategory.current !== selectedCategory ||
      prevQuery.current !== query || prevSelectedCountry.current !== selectedCountry
    ) {
      const getData = setTimeout(() => {
        setPagination((prev) => {
          if (prev.page === 1) {
            setRefresh((prevR) => !prevR);
            setListicleData([]);
          }
          return { ...prev, page: 1 };
        });
      }, 400);

      // Update previous values
      prevSelection.current = selection;
      prevSelectedCategory.current = selectedCategory;
      prevSelectedCountry.current = selectedCountry;
      prevQuery.current = query;
      return () => clearTimeout(getData);
    }
  }, [selection, selectedCategory, selectedCountry, query]);

  return (
    <>

      <CustomContainer>
        <h1 className="text-sandee-32 mt-2 mb-3 font-bold text-center md:text-start">
          All Lists
        </h1>
        <List_Filter_Section
          selectedCategory={selectedCategory}
          setSelectedCategory={setSelectedCategory}
          setSelection={setSelection}
          setQuery={setQuery}
          query={query}
          selectedCountry={selectedCountry}
          setSelectedCountry={setSelectedCountry}
        />
      </CustomContainer>
      <CustomContainer>
        {!listicleData?.length && !scrollLoader ? (
          <p className=" px-2 py-5 text-error-red-600  border-error-red-400 bg-error-red-50 border-2 rounded-sandee text-center">
            No Data Found for your applied filters
          </p>
        ) : (
          <CustomGrid
            data={listicleData}
            className="gap-4 sm:gap-8 my-6"
            Component={({ data: dataProps }) => {
              dataProps.link = `/list/${dataProps?.nameSlug}`; //
              dataProps.imageSrc = FinalImageGenerator(dataProps?.listicleImage);
              dataProps.copyRightsData = dataProps?.listicleImage;
              dataProps.title = dataProps?.name;

              return ListCard({ data: { ...dataProps } });
            }}
            xs={1}
            sm={2}
            // md={2}
            lg={3}
          // xl={3}
          />)}
      </CustomContainer>
      <ScrollBar
        threshold={60}
        loadMoreFunction={() => {
          setPagination((prev) => ({ ...prev, page: prev?.page + 1 }));
        }}
        isLoading={scrollLoader}
        loadoff={loadoff}
        timeout={10}
      />
    </>
  );
};

export default AllListWithScroll;
