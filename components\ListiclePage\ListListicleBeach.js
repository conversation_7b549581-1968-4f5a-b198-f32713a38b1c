import React from "react";
import { CustomGrid } from "../Custom-Display";
import {
  EditorContent,
  FinalImageGenerator,
  altText,
  blurDataURL,
  defaultImage,
} from "@/helper/functions";
import Link from "next/link";
import Image from "next/image";
import CopyRight from "../Common/CopyRight";
import CustomeImage from "../Common/CustomeImage";

const ListListicleBeach = ({ listicleData }) => {
  return (
    <div>
      <CustomGrid
        data={listicleData}
        className=" gap-4 mb-6 "
        Component={({ data: dataProps, index }) => {
          if (dataProps?.city?.state?.country?.slug) {
            dataProps.link = `/${dataProps?.city?.state?.country?.slug}/${dataProps?.city?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.nameSlug}`; //
            dataProps.location = `${dataProps?.city?.state?.country?.name}, ${dataProps?.city?.state?.name}, ${dataProps?.city?.name}`; //
          } else {
            dataProps.link = `/${dataProps?.country?.slug}/${dataProps?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.nameSlug}`; //
            dataProps.location = `${dataProps?.country?.name}, ${dataProps?.state?.name}, ${dataProps?.city?.name}`; //
          }
          dataProps.imageSrc = FinalImageGenerator(
            dataProps?.images?.[0],
            1800,
            2
          );
          return BeachCard({
            data: { ...dataProps, ...dataProps, index },
            copyRightsData: dataProps?.images,
          });
        }}
        xs={1}
        sm={1}
        md={1}
        lg={1}
        xl={1}
      />
    </div>
  );
};

export default ListListicleBeach;
export const BeachCard = ({ data, copyRightsData }) => {
  data.lat = data?.GeoLoc?.coordinates?.length
    ? data?.GeoLoc?.coordinates?.[1]
    : 0;
  data.lon = data?.GeoLoc?.coordinates?.length
    ? data?.GeoLoc?.coordinates?.[0]
    : 0;
  return (
    // <div className="relative aspect-video  rounded-sandee ">
    <>
      <div className="relative aspect-video rounded-sandee xl:w-[600px]">
        <Link
          href={`${data?.link}`}
          className="w-full h-full group rounded-sandee shadow-[0_3px_10px_rgb(0,0,0,0.2)] "
        >
          <div className="absolute left-0 top-0  aspect-video w-full  rounded-sandee">
            <div className="relative  overflow-hidden  w-full h-full  rounded-sandee ">
              {data?.imageSrc && data?.imageSrc !== defaultImage ? (
                <CustomeImage
                  // priority
                  className="transition-transform duration-1000 ease-in-out transform group-hover:scale-125 w-full object-fill"
                  src={data?.imageSrc}
                  // alt={`${data?.location
                  //   ? `Sandee ${data?.name} Photo`
                  //   : `Sandee ${data?.name}`
                  //   }`}
                  alt={altText(data)}
                  fill
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  blurDataURL={blurDataURL(300, 200)}
                  placeholder="blur"
                />
              ) : (
                <iframe
                  className=" bg-gray-200 w-full h-full rounded-sandee min-h-[280px] pointer-events-none"
                  loading="lazy"
                  src={`https://maps.google.com/?q=${data?.lat},${data?.lon}&ie=UTF8&iwloc=&output=embed&t=k&disableDefaultUI=true`}
                ></iframe>
              )}
            </div>
          </div>
          <div className="absolute left-0 top-0 flex group-hover:sm:translate-x-[0%] md:translate-x-[150%] sm:p-3 group-hover:w-full w-full md:w-1 rounded-2xl sm:rounded-sandee md:opacity-0 group-hover:opacity-100 transition-all duration-[10000] ease-in-out">
            <div className="py-2 px-2 mt-2 ml-1 sm:mt-0 sm:ml-0 sm:px-3 rounded-full bg-[#ffffff33]  text-white flex justify-between items-center backdrop-blur-sm">
              <p className=" flex justify-around items-center text-xs font-semibold">
                <span className=" mr-1 sm:mr-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="17"
                    height="16"
                    viewBox="0 0 17 16"
                    fill="none"
                    className=" stroke-white"
                  >
                    <path
                      d="M14.8714 7.9998C14.8714 11.6798 11.8848 14.6665 8.20475 14.6665C4.52475 14.6665 1.53809 11.6798 1.53809 7.9998C1.53809 4.3198 4.52475 1.33313 8.20475 1.33313C11.8848 1.33313 14.8714 4.3198 14.8714 7.9998Z"
                      strokeWidth="1.07684"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M10.678 10.1203L8.61134 8.88696C8.25134 8.67362 7.95801 8.16029 7.95801 7.74029V5.00696"
                      strokeWidth="1.07684"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </span>
                24 / 7
              </p>
              <div className="  border-r-2 border-white h-full  mx-2"></div>
              <p className=" flex justify-around items-center text-xs font-semibold">
                <span className=" mr-1 sm:mr-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="14"
                    height="14"
                    viewBox="0 0 14 14"
                    fill="none"
                    className=" fill-white"
                  >
                    <path d="M8.35835 1.33977L9.53168 3.68643C9.69169 4.0131 10.1184 4.32643 10.4784 4.38643L12.605 4.73976C13.965 4.96643 14.285 5.9531 13.305 6.92643L11.6517 8.57976C11.3717 8.85976 11.2184 9.39976 11.305 9.78643L11.7784 11.8331C12.1517 13.4531 11.2917 14.0798 9.85835 13.2331L7.86502 12.0531C7.50502 11.8398 6.91169 11.8398 6.54502 12.0531L4.55168 13.2331C3.12502 14.0798 2.25835 13.4464 2.63168 11.8331L3.10502 9.78643C3.19168 9.39976 3.03835 8.85976 2.75835 8.57976L1.10502 6.92643C0.131684 5.9531 0.445018 4.96643 1.80502 4.73976L3.93168 4.38643C4.28502 4.32643 4.71168 4.0131 4.87168 3.68643L6.04502 1.33977C6.68502 0.0664324 7.72502 0.0664324 8.35835 1.33977Z" />
                  </svg>
                </span>
                {data?.rating100 / 20}
              </p>
            </div>
          </div>
          <div
            className={`absolute content-gradiant-black left-0 bottom-0 px-3 pb-10 w-full rounded-2xl sm:rounded-sandee group-hover:cardgradianthovered-listicle  flex flex-col justify-end md:cardgradianthovered-listicle  cardgradianthovered-listicle  transition-all duration-1000 `}
          // className={`absolute content-gradiant-black left-0 bottom-0 pb-1 ${className} w-full  rounded-2xl sm:rounded-sandee  px-3 flex flex-col justify-end hoverfullcard transition-all duration-1000 `}
          >
            {/* <div className="absolute content-gradiant-black left-0 bottom-0 aspect-video w-full   rounded-sandee  px-3 pb-10 flex flex-col justify-end hoverfullcard transition-all duration-1000"> */}
            <div className=" text-white  flex gap-2">
              <div className=" p-2">
                <p className=" text-white text-sandee-32 pr-2 font-bold border-r-2 border-r-white">
                  {`${data?.index + 1}`}
                </p>
              </div>
              <div>
                <p className="  text-sandee-18 py-[3px] font-medium line-clamp-1">
                  {data?.name}
                </p>

                <div className="  text-[11px] py-[3px] text-white font-medium flex ">
                  <p className="line-clamp-1 group-hover:line-clamp-6 ">
                    {data?.location}
                  </p>
                </div>
              </div>
            </div>
          </div>
          {copyRightsData?.length || data?.images?.length ? (
            <CopyRight
              copyRightsData={
                copyRightsData?.length ? copyRightsData : data?.images
              }
              background={true}
              classNameExtra={"bottom-0 left-1"}
              customSize="xs:text-[6px] text-[10px]"
            // styleExtra={{ bottom: "5px", left: "3px" }}
            />
          ) : (
            ""
          )}
        </Link>
      </div>
      <div className="my-2 xl:w-[600px]">
        <EditorContent className="line-clamp-6" value={data?.beachDescription?.summary &&
          data?.beachDescription?.summary !== "<p><br></p>" &&
          data?.beachDescription?.summary !== "<p> </p>"
          ? data?.beachDescription?.summary
          : data?.beachDescription?.introduction &&
            data?.beachDescription?.introduction !== "<p><br></p>" &&
            data?.beachDescription?.introduction !== "<p> </p>"
            ? data?.beachDescription?.introduction
            : ""} />
        {/* <div
          className="line-clamp-6"
          dangerouslySetInnerHTML={{
            __html:
              data?.beachDescription?.summary &&
              data?.beachDescription?.summary !== "<p><br></p>" &&
              data?.beachDescription?.summary !== "<p> </p>"
                ? data?.beachDescription?.summary
                : data?.beachDescription?.introduction &&
                  data?.beachDescription?.introduction !== "<p><br></p>" &&
                  data?.beachDescription?.introduction !== "<p> </p>"
                ? data?.beachDescription?.introduction
                : "",
          }}
        /> */}

        {(data?.beachDescription?.summary &&
          data?.beachDescription?.summary !== "<p><br></p>" &&
          data?.beachDescription?.summary !== "<p> </p>") ||
          (data?.beachDescription?.introduction &&
            data?.beachDescription?.introduction !== "<p><br></p>" &&
            data?.beachDescription?.introduction !== "<p> </p>") ? (
          <Link
            href={`${data?.link}`}
            className="text-primary-600 hover:underline"
          >
            Read More...
          </Link>
        ) : null}
      </div>
    </>
  );
};
