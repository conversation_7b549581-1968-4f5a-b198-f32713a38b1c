"use client";
import React, { useEffect, useState } from "react";
import { API_BASE_URL } from "@/helper/functions";
import axios from "axios";
import SelectBox from "../Common/SelectBox";
import CustomBlogFilter from "../BlogPage/CustomBlogFilter";

const List_Filter_Section = ({
  selectedCategory,
  setSelectedCategory,
  setSelection,
  query,
  setQuery,
  selectedCountry,
  setSelectedCountry,
}) => {
  // const [selectedCategory, setSelectedCategory] = useState([]);
  const [categories, setCategories] = useState([]);
  const [countries, setCountries] = useState([]);

  const handelRefresher = () => {
    //   clearTimeout(debounce.current);
    //   debounce.current = setTimeout(() => {
    //     setPagination((prev) => ({ ...prev, page: 1 }));
    //   }, 500);
  };
  useEffect(() => {
    const getTagData = async () => {
      const CATEGORY = await getTestCategories();
      if (CATEGORY?.tags?.length) {
        setCategories([
          ...CATEGORY?.tags?.map((el) => ({
            label: el,
            value: el,
            id: el,
          })),
        ]);
      }
    };
    const getCountriesData = async () => {
      const COUNTRIES = await getCountries();
      if (COUNTRIES?.data?.length) {
        setCountries([
          {
            label: "All",
            value: "",
            id: 0,
          },
          ...COUNTRIES?.data?.map((el) => ({
            label: el?.country,
            value: el?.CountryId,
            id: el?.CountryId,
          })),
        ]);
      }
    };
    getTagData();
    getCountriesData();
  }, []);
  return (
    <div className=" grid lg:grid-flow-col gap-4">
      <div className="relative col-span-6">
        <div className="absolute inset-y-0 start-0 flex items-center ps-2 pe-2 ms-2 pointer-events-none rounded-full">
          <svg
            className="w-4 h-4 text-[#7D7D7D]  "
            aria-hidden="true"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 20 20"
          >
            <path
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"
            />
          </svg>
        </div>
        <input
          type="text"
          id="simple-search"
          className={`transition-all duration-500  focus:outline-none outline-none  focus:ring-0 ring-0 focus:border-sandee-blue  bg-white border border-sandee-blue block w-full ps-10 sm:text-[20px]  rounded-xl text-gray-600 !pl-[50px] lg:text-sandee-sm  !text-sandee-sm p-2 `}
          placeholder="Search for Beach Lists"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
        />
      </div>
      <div className="w-full h-full items-center flex gap-3 lg:justify-end flex-wrap col-span-6">
        <CustomBlogFilter
          categories={categories}
          setSelectedCategory={setSelectedCategory}
          selectCategory={selectedCategory}
          refresher={handelRefresher}
        />
        <SelectBox
          options={countries}
          deafultSelectSort={{
            value: "",
            label: "All",
          }}
          getSortingVal={(v) => {
            setSelectedCountry(v);
          }}
          placeholder={"Countries"}
          // className="w-[200px]"
          menuClassName="w-[200px]"
        />
        <SelectBox
          options={[
            {
              value: "DESC",
              label: "Latest",
            },
            {
              value: "ASC",
              label: "Oldest",
            },
          ]}
          deafultSelectSort={{
            value: "DESC",
            label: "Latest",
          }}
          getSortingVal={(v) => {
            setSelection(v);
          }}
          menuClassName="w-full"
        />
      </div>
    </div>
  );
};

export default List_Filter_Section;

export const getTestCategories = async () => {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/listiclesMain/getUniqueTags`
    );
    return response?.data;
  } catch (error) {
    // logController(error);
  }
};

export const getCountries = async (data) => {
  const response = await axios.get(`${API_BASE_URL}/sitemap/getAllCountry`);
  return response?.data;
};
