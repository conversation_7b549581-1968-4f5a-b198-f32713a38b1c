"use client";
import React, { useContext, useEffect, useRef, useState } from "react";
import mapboxgl from "!mapbox-gl";
import { BeachgetNearByBeachWithoutPage } from "@/app/(HeaderSlim)/(Single Country Page)/[countrySlug]/action";
import { useRouter } from "next/navigation";
import { valContext } from "@/helper/context/ValContext";
mapboxgl.accessToken = process.env.NEXT_PUBLIC_MAPBOX_API_KEY;
const ListicleMap = ({ countryName }) => {
  const mapContainer = useRef(null);
  const map = useRef(null);
  const {setListicleLocation} = useContext(valContext);
  // const {location, setlocation} = useContext(valContext);
  const [location,setLocation]=useState(null);
  const router=useRouter();
  useEffect(() => {
    const fetchCoordinates = async () => {
      try {
        const response = await fetch(
          `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(
            countryName
          )}.json?access_token=${mapboxgl.accessToken}`
        );
        const data = await response.json();
        if (data.features && data.features.length > 0) {
          const { center } = data.features[0];
          setLocation(center);
        } else {
          console.error("No results found for the specified country.");
        }
      } catch (error) {
        console.error("Error fetching coordinates:", error);
      }
    };

    fetchCoordinates();
  }, [countryName]);

  useEffect(() => {
    if (!location) return;

    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: "mapbox://styles/mapbox/streets-v12",
      center: countryName ? location : [-118.4117325, 34.020479],
      zoom: 4,
      minZoom: 3,
      maxBounds: [
        [-180, -85], // Southwest coordinates
        [180, 85], // Northeast coordinates
      ],
      projection: { name: "mercator" },
      interactive: false, // Disable all interactions
      scrollZoom: false, // Disable scroll zoom
      dragPan: false, // Disable drag panning
      touchZoomRotate: false, // Disable touch zoom and rotate
      doubleClickZoom: false, // Disable double click zoom
      keyboard: false, // Disable keyboard controls
    });

    // map.current.on("load", async () => {
    //     // const Results = await BeachgetNearByBeachWithoutPage({log:location[1],lat:location[0]});
    //     // console.log(Results,"result --=-=-=-=-=-=-=-==--=-=")
        
    //   //   setLoaded(true);
    //   // setIsloading(true);
    //   //   setRefresh((prev) => !prev);
    // });
    // if (latStr && longStr) {
    //   map?.current?.jumpTo({ center: [longStr, latStr], zoom: 12 });
    // }
    // map.current.on("moveend", handleMoveEnd);

    return () => {
      //   map.current.off("moveend", handleMoveEnd);
      map.current.remove();
    };
  }, [location]);
  //   useEffect(() => {
  //     getLocation();
  //   }, []);
  return (
    <>
      <div className="relative  h-[300px] w-full  p-1  listicle-map rounded-xl cursor-pointer" onClick={()=>{setListicleLocation(countryName ? location : []);router.push('/map');}}>
        <div
          ref={mapContainer}
          className="md:map-container md:h-[282px] w-full h-[275px] overflow-hidden border border-gray-400 rounded-xl"
        />
      </div>
    </>
  );
};

export default ListicleMap;
