import React from "react";

const ListiclePageJSONLD = () => {
  const webPageSchema = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    url: "https://sandee.com/list",
    name: `Lists | <PERSON>ee the Ultimate beach guide`,
    headline: `Lists | Sandee the Ultimate beach guide`,
    description: "Discover the best beaches with Sand<PERSON> - the ultimate beach guide! Dive into our lists for travel tips, hidden gems, and beachfront bliss. Uncover paradise with <PERSON><PERSON>'s expert insights. Your journey to sun, sea, and sand starts here",
    publisher: {
      "@type": "Organization",
      name: "<PERSON><PERSON>",
    },
  };
  const ListBreadCumber = {
    "@context": "https://schema.org/",
    "@type": "BreadcrumbList",
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        name: "Home",
        item: `https://sandee.com/`,
      },
      {
        "@type": "ListItem",
        position: 2,
        name: "All Lists",
        item: `https://sandee.com/list`,
      },
    ],
  };

  return (
    <>
      <script
        type="application/ld+json"
        id="application/ld+jsonBeach"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(webPageSchema) }}
      ></script>
      <script
        type="application/ld+json"
        id="application/ld+jsonBeachBreadCumber"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(ListBreadCumber) }}
      ></script>
    </>
  );
};

export default ListiclePageJSONLD;
