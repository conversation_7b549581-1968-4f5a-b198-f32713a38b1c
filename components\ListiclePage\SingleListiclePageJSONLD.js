import { siteMetadata } from "@/data/siteMetadata";
import { slugConverter } from "@/helper/functions";
import React from "react";

const SingleListiclePageJSONLD = ({ listData, params }) => {
  const ListBreadCumber = {
    "@context": "https://schema.org/",
    "@type": "BreadcrumbList",
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        name: "Home",
        item: `https://sandee.com/`,
      },
      {
        "@type": "ListItem",
        position: 2,
        name: "All Lists",
        item: `https://sandee.com/list`,
      },
      {
        "@type": "ListItem",
        position: 3,
        name: listData?.name,
        item: `https://sandee.com/list/${listData?.slug}`,
      },
    ],
  };

  const webPageSchema = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    url: "https://sandee.com/list",
    name: `${slugConverter(params?.listSlug, true)} - <PERSON><PERSON> `,
    description: "<PERSON><PERSON> is the most comprehensive list of beaches around the world! <PERSON><PERSON> includes over 100,000 beaches in every country of the world. Includes activities, photos, maps, and reviews.",
    publisher: {
      "@type": "Organization",
      name: siteMetadata?.title || "Sandee",
    },
  };

  return (
    <>
      <script
        type="application/ld+json"
        id="application/ld+jsonBeachBreadCumber"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(ListBreadCumber) }}
      ></script>
      <script
        type="application/ld+json"
        id="application/ld+jsonBeachBreadCumber"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(webPageSchema) }}
      ></script>
    </>
  );
};

export default SingleListiclePageJSONLD;
