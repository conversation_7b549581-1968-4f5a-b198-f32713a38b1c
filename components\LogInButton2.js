"use client";
import Image from 'next/image';
import React from 'react'
import CustomButton from './Custom-Button';
import { Popover } from 'antd';
import AuthWrapper from './Common/AuthWrapper';
import useLoggedIn from '@/helper/hook/useLoggedIn';
import { MenuIcon, UserIcon } from './social-icons/icons';

const LogInButtonWithMenu = () => {
    const AuthData = useLoggedIn();

    return (
        <div className="hidden md:flex justify-end items-center">
            <AuthWrapper
                WithoutLogIn={
                    <div
                        id="withoutlogin-display"
                        className="hidden md:flex justify-center items-center gap-x-2 group group-hover:text-sandee-blue"
                    >
                        {/* <button className=" justify-center items-center  py-2 px-8 flex relative   overflow-hidden font-medium  text-white border-2 border-sandee-blue bg-sandee-blue active:text-white text-sandee-sm h-auto rounded-full group">
                            <span className="absolute bottom-0 left-0 flex w-full h-0 mb-0 transition-all duration-200 ease-out transform translate-y-0 bg-white text-sandee-blue opacity-100 group-hover:h-full"></span>
                            <span className="relative flex items-center group-hover:text-sandee-blue ">
                                Log In
                            </span>
                        </button> */}
                        <button className=" justify-center items-center  py-2 px-2 flex relative transition-all duration-200 ease-out transform translate-y-0 overflow-hidden font-medium  text-white  bg-gray-100 group-hover:bg-sandee-blue active:text-white text-sandee-sm h-auto rounded-full group">
                            {/* <span className="absolute bottom-0 left-0 flex w-full h-0 mb-0 transition-all duration-200 ease-out transform translate-y-0  text-sandee-blue opacity-100 group-hover:h-full"></span> */}
                            <span className="relative flex items-center text-sandee-blue group-hover:text-white ">
                                <UserIcon />
                            </span>
                        </button>
                        <MenuIcon stroke="#ffffff" className="group-hover:text-sandee-blue" />
                    </div>
                }
                // WithLogIn={<ProfileCard />}
                WithLogIn={
                    <Popover
                        placement="top"
                        title={AuthData?.profileData?.name}
                        content={
                            <>
                                <div className="username font-normal text-xs text-sandee-grey">
                                    {AuthData?.profileData?.email}
                                </div>
                                <div className="flex justify-center items-center mt-3">
                                    <CustomButton
                                        type={3}
                                        onClick={() => {
                                            typeof window &&
                                                typeof window !== "undefined" &&
                                                window.localStorage.clear();
                                            typeof window &&
                                                typeof window !== "undefined" &&
                                                window.location.reload();
                                        }}
                                    >
                                        Log Out
                                    </CustomButton>
                                </div>
                            </>
                        }
                        className='flex items-center gap-x-1'
                    // arrow={mergedArrow}
                    >
                        <div className="relative image-profile !w-8 !h-8 rounded-full">
                            {!!AuthData?.profileData?.photo ? (

                                <Image
                                    fill
                                    src={AuthData?.profileData?.photo}
                                    sizes="100"
                                    alt="Profile Photo"
                                    className="rounded-full image-profile !w-8 !h-8"
                                />
                            ) : (
                                <div className='rounded-full bg-sandee-blue text-center !w-8 !h-8'>
                                    <span className="text-white font-bold text-xl image-profile">
                                        {AuthData?.profileData?.email?.charAt(0).toUpperCase()}
                                    </span>
                                </div>
                                //     <span className="text-white font-bold text-xl image-profile">
                                //       {AuthData?.profileData?.email?.charAt(0).toUpperCase()}
                                //     </span>
                                //   </div>
                                // <svg
                                //     viewBox="0 0 24 24"
                                //     fill="none"
                                //     xmlns="http://www.w3.org/2000/svg"
                                //     className="rounded-full image-profile bg-gray-200 p-2"
                                // >
                                //     <g id="SVGRepo_bgCarrier" strokeWidth={0} />
                                //     <g
                                //         id="SVGRepo_tracerCarrier"
                                //         strokeLinecap="round"
                                //         strokeLinejoin="round"
                                //     />
                                //     <g id="SVGRepo_iconCarrier">
                                //         <path
                                //             d="M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z"
                                //             stroke="#000000"
                                //             strokeWidth={2}
                                //             strokeLinecap="round"
                                //             strokeLinejoin="round"
                                //         />
                                //         <path
                                //             d="M12 14C8.13401 14 5 17.134 5 21H19C19 17.134 15.866 14 12 14Z"
                                //             stroke="#000000"
                                //             strokeWidth={2}
                                //             strokeLinecap="round"
                                //             strokeLinejoin="round"
                                //         />
                                //     </g>
                                // </svg>
                            )}
                        </div>
                        <MenuIcon stroke="#ffffff" />
                    </Popover>
                }
            />
        </div>
    );
}

export default LogInButtonWithMenu