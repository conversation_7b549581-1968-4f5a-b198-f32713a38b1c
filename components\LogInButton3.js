"use client";
import React from "react";
import AuthWrapper from "./Common/AuthWrapper";
import useLoggedIn from "@/helper/hook/useLoggedIn";
import Image from "next/image";
import CustomButton from "./Custom-Button";
import { Popover } from "antd";
import { LoginIcon, ThreeDotMenuIcon } from "./social-icons/icons";

const LogInButtonSideBar = ({ isCollapsed }) => {
  const AuthData = useLoggedIn();

  return (
    <div className=" md:flex items-center justify-center !min-w-full min-w-[108px]">
      <AuthWrapper
        WithoutLogIn={
          <div
            id="withoutlogin-display"
            className="w-full md:flex justify-center items-center "
          >
            {isCollapsed ? <button className=" justify-center items-center  py-2 px-2 flex relative   overflow-hidden font-medium  text-white border-2 border-sandee-blue bg-sandee-blue active:text-white text-sandee-sm h-auto rounded-full group">
              <span className="absolute bottom-0 left-0 flex w-full h-0 mb-0 transition-all duration-200 ease-out transform translate-y-0 bg-white text-sandee-blue opacity-100 group-hover:h-full"></span>
              <span className="relative flex items-center group-hover:text-sandee-blue ">
                <LoginIcon />
              </span>
            </button> : <button className=" justify-center items-center  py-2 px-8 flex relative   overflow-hidden font-medium  text-white border-2 border-sandee-blue bg-sandee-blue active:text-white text-sandee-sm h-auto rounded-full group">
              <span className="absolute bottom-0 left-0 flex w-full h-0 mb-0 transition-all duration-200 ease-out transform translate-y-0 bg-white text-sandee-blue opacity-100 group-hover:h-full"></span>
              <span className="relative flex items-center group-hover:text-sandee-blue ">
                Log In
              </span>
            </button>}
          </div>
        }
        // WithLogIn={<ProfileCard />}
        WithLogIn={
          <>
            <div className="w-full gap-2 md:gap-0 flex md:flex-row flex-col justify-center md:flex items-center md:justify-between">
              {!!AuthData?.profileData?.photo ? (
                <div className={`${isCollapsed ? "!hidden" : "relative"} relative w-[32px] h-[32px]`}>
                  <Image
                    fill
                    src={AuthData?.profileData?.photo}
                    sizes="24"
                    // width={24}
                    // height={24}
                    alt="Profile Photo"
                    className={`rounded-full ${isCollapsed ? "!hidden" : ""} image-profile hidden md:block`}
                  />
                </div>
              ) : (
                <>
                  <div className={`${isCollapsed ? "!hidden" : "md:block"} hidden  rounded-full bg-sandee-blue text-center h-full`}>
                    <span className="text-white font-bold text-2xl image-profile flex items-center justify-center h-full">
                      {AuthData?.profileData?.email?.charAt(0).toUpperCase()}
                      {/* {AuthData?.profileData?.email?.charAt(0).toUpperCase()} */}
                    </span>
                  </div>
                </>
              )}
              <p className={`${isCollapsed ? "!hidden" : "md:block"} hidden  text-base font-semibold`}>{AuthData?.profileData?.name}</p>
              <Popover
                placement="top"
                title={<p className="px-2 pt-2">{AuthData?.profileData?.name}</p>}
                content={
                  <>
                    <div className="px-2 username font-normal text-xs text-sandee-grey">
                      {AuthData?.profileData?.email}
                    </div>
                    <div className="flex justify-center items-center mt-3 px-2 pb-2">
                      <CustomButton
                        type={3}
                        onClick={() => {
                          typeof window &&
                            typeof window !== "undefined" &&
                            window.localStorage.clear();
                          typeof window &&
                            typeof window !== "undefined" &&
                            window.location.reload();
                        }}
                      >
                        Log Out
                      </CustomButton>
                    </div>
                  </>
                }
              // arrow={mergedArrow}
              >
                <div className={`hover:bg-gray-200 p-[1px] rounded-full ${isCollapsed ? "w-full flex justify-center items-center" : ''}`}>
                  <ThreeDotMenuIcon />
                </div>
              </Popover>
            </div>
          </>

        }
      />
    </div>
  );
};

export default LogInButtonSideBar;
