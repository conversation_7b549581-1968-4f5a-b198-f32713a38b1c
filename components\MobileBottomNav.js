"use client"
import React from "react";
import { BlogIcon, FlagIcon, HomeIcon, MapIcon, SharkIcon, SharkIconNew, SharkIconShadow } from "./social-icons/icons";
import Link from "next/link";
import { usePathname } from "next/navigation";

const MobileBottomNav = () => {
  const pathname = usePathname();
  let isInIframe = false;
  if (typeof window !== "undefined") {
    isInIframe = window.self === window.top;
    // if (isInIframe) {
    //   console.log("This page is loaded inside an iframe.", window.self, window.top);
    // } else {
    //   console.log("This page is NOT loaded inside an iframe.", window.self, window.top);
    // }
  }
  const list = [
    {
      id: 1,
      label: "Home",
      value: "home",
      icon: <HomeIcon width={18} height={18} stroke={pathname === "/" ? "#0194c5" : "black"} />,
      link: "/"
    },
    {
      id: 4,
      label: "Map",
      value: "map",
      icon: <MapIcon width={20} height={20} stroke={pathname === "/map" ? "#0194c5" : "black"} />,
      link: "/map"
    },
    {
      id: 2,
      label: "Sharks",
      value: "shark",
      icon: <SharkIconNew width={20} height={20} fill={pathname === "/shark" ? "#0194c5" : "black"} />,
      link: "/shark"
    },
    {
      id: 3,
      label: "Blogs",
      value: "blogs",
      icon: <BlogIcon width={20} height={20} fill={pathname === "/blog" ? "#0194c5" : "black"} />,
      link: "/blog"
    },
  ];
  return (
    <>
      {isInIframe && <nav className="bg-white fixed bottom-0 py-2 shadow-inner  w-screen lg:hidden z-50">
        <ul className="flex justify-evenly">
          {list?.map((i, index) => {
            return (
              <li className="text-sm cursor-pointer" key={i?.id}>
                <Link href={i?.link}>
                  <span className="flex justify-center">{i?.icon}</span>
                  <span className={pathname === i?.link ? "text-primary-600" : "text-black"}>{i?.label}</span>
                </Link>
              </li>
            );
          })}
        </ul>
      </nav>}
    </>
  );
};

export default MobileBottomNav;
