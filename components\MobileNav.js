"use client";

import { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import ActiveLinks from "./ActiveLinks";
import AuthWrapper from "./Common/AuthWrapper";
import { Popover } from "antd";
import CustomButton from "./Custom-Button";
import useLoggedIn from "@/helper/hook/useLoggedIn";

const MobileNav = ({ headerNavLinks = [] }) => {
  const [navShow, setNavShow] = useState(false);
  const AuthData = useLoggedIn();
  // const [openSubmenu, setOpenSubmenu] = useState(null);

  // Function to toggle submenu visibility
  // const toggleSubmenu = (title) => {
  //   setOpenSubmenu((prev) => (prev === title ? null : title));
  // };
  const onToggleNav = () => {
    setNavShow((status) => {
      if (status) {
        document.body.style.overflow = "auto";
      } else {
        // Prevent scrolling
        document.body.style.overflow = "hidden";
      }
      return !status;
    });
  };

  return (
    <>
      <button
        aria-label="Toggle Menu"
        onClick={onToggleNav}
        className="md:hidden"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 20 20"
          fill="currentColor"
          className="h-8 w-8 text-gray-900  "
        >
          <path
            fillRule="evenodd"
            d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
            clipRule="evenodd"
          />
        </svg>
      </button>
      <div
        className={`fixed left-0 top-0 z-[999] h-full w-full transform bg-white opacity-95 duration-300 ease-in-out   ${navShow ? "translate-x-0" : "translate-x-full"
          }`}
      >
        <div className="flex justify-between items-end">
          <Link href="/" aria-label={"Sandee Home"}>
            <div className="mx-12 relative items-end justify-between w-[100px] h-5">
              <Image
                src={"/static/images/Sandee-Blue.webp"}
                fill
                sizes="1080"
                alt="Sandee logo Blue"
              />
            </div>
          </Link>
          <button
            className="mx-8 mt-11 h-8 w-8 active:rotate-90 transition-all delay-75 ease-in duration-300"
            aria-label="Toggle Menu"
            onClick={onToggleNav}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
              className="text-gray-900 "
            >
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        </div>
        <nav className="fixed mt-8 h-full">
          <div className="px-12 pt-4 pb-1 flex justify-start items-center">
            <AuthWrapper
              WithoutLogIn={
                <div
                  id="withoutlogin-display"
                  className="flex justify-start items-center "
                >
                  <button className=" justify-center items-center  py-2 px-8 flex relative   overflow-hidden font-medium  text-white border-2 border-sandee-blue bg-sandee-blue active:text-white text-sandee-sm h-auto rounded-full group">
                    <span className="absolute bottom-0 left-0 flex w-full h-0 mb-0 transition-all duration-200 ease-out transform translate-y-0 bg-white text-sandee-blue opacity-100 group-hover:h-full"></span>
                    <span className="relative flex items-center group-hover:text-sandee-blue ">
                      Log In
                    </span>
                  </button>
                </div>
              }
              // WithLogIn={<ProfileCard />}
              WithLogIn={
                <Popover
                  placement="top"
                  title={AuthData?.profileData?.name}
                  content={
                    <>
                      <div className="username font-normal text-xs text-sandee-grey">
                        {AuthData?.profileData?.email}
                      </div>
                      <div className="flex justify-center items-center mt-3">
                        <CustomButton
                          type={3}
                          onClick={() => {
                            typeof window &&
                              typeof window !== "undefined" &&
                              window.localStorage.clear();
                            typeof window &&
                              typeof window !== "undefined" &&
                              window.location.reload();
                          }}
                        >
                          Log Out
                        </CustomButton>
                      </div>
                    </>
                  }
                // arrow={mergedArrow}
                >
                  <div className="relative image-profile rounded-full">
                    {!!AuthData?.profileData?.photo ? (
                      <Image
                        fill
                        src={AuthData?.profileData?.photo}
                        sizes="100"
                        alt="Profile Photo"
                        className="rounded-full image-profile"
                      />
                    ) : (
                      <svg
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        className="rounded-full image-profile bg-gray-200 p-2"
                      >
                        <g id="SVGRepo_bgCarrier" strokeWidth={0} />
                        <g
                          id="SVGRepo_tracerCarrier"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <g id="SVGRepo_iconCarrier">
                          <path
                            d="M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z"
                            stroke="#000000"
                            strokeWidth={2}
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                          <path
                            d="M12 14C8.13401 14 5 17.134 5 21H19C19 17.134 15.866 14 12 14Z"
                            stroke="#000000"
                            strokeWidth={2}
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </g>
                      </svg>
                    )}
                  </div>
                </Popover>
              }
            />
          </div>
          {/* {headerNavLinks.map((link) => (
            <div key={link.title} className="px-12 py-4">
              <ActiveLinks
                {...link}
                className="text-2xl font-bold tracking-widest text-gray-900 "
                ActiveClassName=" underline"
                onClick={onToggleNav}
                href={link.href}
              >
                {link.title}
              </ActiveLinks> */}
          {/* <Link
                href={link.href}
                className="text-2xl font-bold tracking-widest text-gray-900 "
                onClick={onToggleNav}
              >
                {link.title}
              </Link> */}
          {/* </div>
          ))} */}
          <div className="w-full h-[20rem] scrollbar-hide  overflow-y-auto">
            {headerNavLinks.map((link) => (
              <div
                key={link.title}
                className="px-12 py-4 border-b relative" // Added relative here
              >
                {/* Parent Option: Plus/Minus Button and Title */}
                <div className="flex items-center justify-between">
                  <ActiveLinks
                    {...link}
                    className="text-2xl font-bold tracking-widest text-gray-900 flex-1"
                    ActiveClassName="underline"
                    onClick={onToggleNav}
                    href={link.href}
                  >
                    {link.title}
                  </ActiveLinks>
                  {/* {link.sub_menus && (
                    <button
                      className="mr-4 text-xl font-bold"
                      onClick={() => toggleSubmenu(link.title)}
                      aria-label={`Toggle ${link.title} submenu`}
                    >
                      {openSubmenu === link.title ? "−" : "+"}
                    </button>
                  )} */}
                </div>

                {/* Submenu List */}
                {/* {link.sub_menus && openSubmenu === link.title && (
                  <div className="mt-2  pl-8"> 
                    {link.sub_menus.map((subLink) => (
                      <ActiveLinks
                        key={subLink.title}
                        {...subLink}
                        className="block text-lg font-medium tracking-wide text-gray-700 py-2 px-2 hover:bg-gray-200 rounded"
                        onClick={onToggleNav}
                        href={subLink.href}
                      >
                        {subLink.title}
                      </ActiveLinks>
                    ))}
                  </div>
                )} */}
              </div>
            ))}
          </div>
        </nav>
      </div>
    </>
  );
};

export default MobileNav;
