"use client";
import Link from 'next/link';
import React, { useState } from 'react'
import { NextArrowIcon, ShareIcon1 } from '../social-icons/icons';
import CustomToolTip from '../Common/CustomToolTip';
import defaultImage from "../../public/static/images/header/Countries.avif";
import ShareModel from "../Common/shareModal";
import Image from 'next/image';
import NewsdetailModal from './newsdetailModal';
import { altText, blurDataURL, isHTML } from '@/helper/functions';
import moment from 'moment';
import CustomeImage from '../Common/CustomeImage';

const NewsCard2 = ({ data }) => {
    const [showShareIcons, setShowShareIcons] = useState(false);
    const [newDetailOpen, setNewDetailOpen] = useState(false);

    const handleShare = () => {
        setShowShareIcons((prv) => !prv);
    };
    return (
        <div className="relative rounded-sandee ">
            <div className='gap-5 p-5 rounded-[30px] 320:rounded-[20px] text-inherit bg-white shadow-[5px_5px_24px_0px_rgba(0,0,0,0.10)]'>
                <div className="relative overflow-hidden aspect-video rounded-sandee">
                    <CustomeImage
                        // priority
                        // className="transition-transform duration-1000 ease-in-out transform group-hover:scale-125 w-full object-cover object-center"
                        src={data?.imageSrc ?? defaultImage}
                        defaultImages={defaultImage}
                        alt={altText(data, `Sandee - News /`)}
                        // alt={`Sandee - Blog / ${data?.name}`}
                        fill
                        // sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                        blurDataURL={blurDataURL(300, 200)}
                        placeholder="blur"
                    />
                </div>
                <div className="flex flex-col gap-y-[0.1rem] mt-[0.7rem] ms-[5px]">
                    <p
                        className=" truncate text-xs font-medium flex items-center gap-3 uppercase"
                        suppressHydrationWarning
                    >
                        {moment(data?.createdAt?.split("T")?.[0]).format('MMM DD, YYYY') ??
                            "Nov, 09 2023"}
                        <span className="h-1 w-1 bg-[#7D7D7D] rounded-full "></span> SANDEE
                    </p>
                    <Link
                        href={`/news/${data?.slug}`}
                        className="tw-no-underline tw-text-inherit "
                        hrefLang="en-us"
                        target="_blank"
                        rel="nofollow"
                        title={data?.title}
                        onClick={() => {
                            // handleViews(data?.id);
                        }}
                    >
                        <h3 className=" text-black text-[18px] h-12 text-start line-clamp-2 leading-6 font-semibold hover:underline">
                            {data?.name}
                        </h3>
                    </Link>
                    {
                        data?.shortDescription ? isHTML(data?.shortDescription) ? (
                            <div className=" text-[#7D7D7D] text-sm font-normal line-clamp-2 text-start" dangerouslySetInnerHTML={{ __html: data?.description }} />
                        ) : (
                            <p className=" text-[#7D7D7D] text-sm font-normal  line-clamp-2 text-start ">
                                {data?.shortDescription}
                            </p>
                        ) : <p className='h-10'></p>
                    }
                    <div className={`flex justify-between items-center `}>
                        {data?.source !== null && data?.sourceLink !== null ? (
                            <Link
                                href={data?.sourceLink}
                                className="hover:text-sandee-blue py-2 px-3 text-gray-800  bg-gray-100 rounded-full font-inter text-sm my-1 no-underline flex gap-x-1 w-fit"
                                hrefLang="en-us"
                                target="_blank"
                                rel="nofollow"
                                title={data?.source}
                                onClick={() => {
                                    // handleViews(data?.id);
                                }}
                            >
                                {data?.source}
                            </Link>
                        ) : (
                            <div className="my-[22px]"></div>
                        )}
                        {/* <span className="xl:text-base text-sm flex justify-end text-light-gray-900"> */}
                        <div className="">
                            <div
                                key={`news-card-icon-3`}
                                onClick={() => {
                                    handleShare(data?.newsId, "");
                                }}
                                className=" cursor-pointer"
                            >
                                <ShareIcon1 />
                            </div>
                        </div>
                        {/* </span> */}
                    </div>
                    {/* <div className="flex justify-between items-center ">
                    <div className='flex items-center justify-between'>
                        <Link
                            href={data?.sourceLink}
                            className="tw-no-underline tw-text-inherit "
                            hrefLang="en-us"
                            target="_blank"
                            rel="nofollow"
                            title={data?.source}
                            onClick={() => {
                                // handleViews(data?.id);
                            }}
                        >
                            Continue Reading
                        </Link>
                        <span className="mt-1">
                            <NextArrowIcon stroke="#FFFFFF" />
                        </span>
                    </div>
                    <div className="flex gap-x-2 items-center">
                        <div
                            key={`news-card-icon-3`}
                            onClick={() => {
                                handleShare(data?.newsId, "");
                            }}
                            className=" cursor-pointer"
                        >
                            <ShareIcon1 />
                        </div>
                    </div>
                </div> */}

                    {showShareIcons ? (
                        <ShareModel
                            modalOpen={showShareIcons}
                            setModalOpen={setShowShareIcons}
                            link={`/news/${data?.slug}`}
                        //   top={"lg:top-[30%] top-[28%]"}
                        //   iconHeight={"2xl:py-5 py-2"}
                        />
                    ) : null}
                    <NewsdetailModal
                        modalOpen={newDetailOpen}
                        setModalOpen={setNewDetailOpen}
                        data={data}
                    />
                    {/* <p className=" text-[#7D7D7D] text-sm font-normal  line-clamp-2 text-start "> */}
                    {/* <BecahIcon className=" h-5 w-5 fill-[#7D7D7D] mr-1 items-center" /> */}
                    {/* {data?.description}
                </p> */}
                </div>
            </div>
        </div>
    );
}

export default NewsCard2