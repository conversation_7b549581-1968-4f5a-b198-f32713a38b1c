"use client";
import React, { useEffect, useState } from "react";
import AuthWrapper from "../Common/AuthWrapper";
import {
  getBlogLikeCount,
  getComment,
  postBlogLike,
  postBlogWhishlist,
} from "@/app/(HomeHeader)/action";
import { usePathname } from "next/navigation";
import useLoggedIn from "@/helper/hook/useLoggedIn";
import { EnvTrueFalse } from "@/helper/functions";
import {
  BookMark,
  Comment,
  FillBookMark,
  FillComment,
  FillLike,
  Like,
} from "../social-icons/icons";
import ShareLinks from "../BlogPage/ShareLinks";
import BlogComment from "../BlogPage/BlogComment";

const NewsLikeShare = ({
  BlogData,
  className,
  Initialdata = {
    likes: 0,
    comments: 0,
    share: "",
    wishlists: 0,
    isLiked: false,
    iscommented: false,
    inWishList: false,
  },
  invisiblity = {
    likes: false,
    wishlists: false,
    comments: false,
    share: false,
  },
}) => {
  const { slug } = BlogData;
  const CONST_ENV_WEB_URL =
    process.env.NEXT_PUBLIC_ENV == "production"
      ? process.env.NEXT_PUBLIC_WEB_URL
      : process.env.NEXT_PUBLIC_DEV_WEB_URL;
  const { isLoggedIn, token } = useLoggedIn();
  const router = usePathname();

  const [BlogCountData, setBlogCountData] = useState({ ...Initialdata });
  const [open, setOpen] = useState(false);

  const [refresh, setRefresh] = useState(true);
  const [commentrefresh, setCommentRefresh] = useState(true);
  const [allComments, setAllComments] = useState([]);
  const GetDataForCounts = async () => {
    const response = await getBlogLikeCount({ token, slug });

    setBlogCountData({
      ...response?.data,
      share: `${CONST_ENV_WEB_URL}/${router}`,
    });
  };
  const showDrawer = () => {
    setOpen(true);
  };
  const onClose = () => {
    setOpen(false);
  };
  useEffect(() => {
    GetDataForCounts();
  }, [refresh]);
  const getAllComments = async () => {
    const { data: BlogCommentData } = await getComment(BlogData?.id);

    setAllComments(BlogCommentData?.rows);
  };
  useEffect(() => {
    if (BlogData?.id) {
      getAllComments();
    }
  }, [commentrefresh]);
  return (
    <div className={`${className} flex gap-3 `}>
      {!EnvTrueFalse[invisiblity?.likes] && (
        <AuthWrapper
          WithLogIn={
            <div
              className=" flex gap-1 flex-col cursor-pointer items-center justify-center "
              onClick={async () => {
                const response = await postBlogLike(
                  { blogId: BlogData?.id },
                  token
                );
                setRefresh((prev) => !prev);
              }}
            >
              {!EnvTrueFalse[BlogCountData?.isLiked] ? (
                <Like className="   cursor-pointer  w-6 h-6 stroke-red-500 hover:scale-105 hover:fill-red-500" />
              ) : (
                <FillLike
                  //   onClick={onLike}
                  className="cursor-pointer  fill-red-500 w-6 h-6 stroke-red-500 hover:scale-105 "
                />
              )}
              <span>{BlogCountData?.likes ?? 1}</span>
            </div>
          }
          WithoutLogIn={
            <div className=" flex gap-1 flex-col cursor-pointer items-center justify-center opacity-60">
              {!EnvTrueFalse[BlogCountData?.isLiked] ? (
                <Like className="  w-6 h-6 stroke-red-500 hover:scale-105 hover:fill-red-500" />
              ) : (
                <FillLike
                  //   onClick={onLike}
                  className=" fill-black w-6 h-6 stroke-black hover:scale-105 "
                />
              )}
              <span>{BlogCountData?.likes ?? 0}</span>
            </div>
          }
        />
      )}
      {!EnvTrueFalse[invisiblity?.comments] && (
        <AuthWrapper
          WithLogIn={
            <>
              <div
                className=" flex gap-1 flex-col cursor-pointer items-center justify-center "
                onClick={showDrawer}
              >
                {!EnvTrueFalse[BlogCountData?.iscommented] ? (
                  <Comment className="cursor-pointer  fill-red-500 w-7 h-7 stroke-black hover:scale-105 " />
                ) : (
                  <FillComment className="cursor-pointer  fill-black w-7 h-7 stroke-black hover:scale-105 " />
                )}
                <span>{BlogCountData?.comments ?? 0}</span>
              </div>
              <BlogComment
                open={open}
                setOpen={setOpen}
                onClose={onClose}
                showDrawer={showDrawer}
                totalComments={BlogCountData?.comments ?? 0}
                token={token}
                BlogData={BlogData}
                refresher={() => {
                  setCommentRefresh((prev) => !prev);
                  setRefresh((prev) => !prev);
                }}
                allComments={allComments}
              />
            </>
          }
          WithoutLogIn={
            <div className=" flex gap-1 flex-col cursor-pointer items-center justify-center  opacity-60">
              {!EnvTrueFalse[BlogCountData?.iscommented] ? (
                <Comment className="cursor-pointer  fill-red-500 w-7 h-7 stroke-black hover:scale-105 " />
              ) : (
                <FillComment
                  //   onClick={onLike}
                  className="cursor-pointer  fill-black w-6 h-6 stroke-black hover:scale-105 "
                />
              )}
              <span>{BlogCountData?.comments ?? 0}</span>
            </div>
          }
        />
      )}
      {!EnvTrueFalse[invisiblity?.wishlists] && (
        <AuthWrapper
          WithLogIn={
            <div
              className=" flex gap-1 flex-col cursor-pointer items-center justify-center "
              onClick={async () => {
                const response = await postBlogWhishlist(
                  { blogId: BlogData?.id },
                  token
                );

                setRefresh((prev) => !prev);
              }}
            >
              {!EnvTrueFalse[BlogCountData?.inWishList] ? (
                <BookMark className="cursor-pointer  fill-black w-6 h-6 stroke-black hover:scale-105 " />
              ) : (
                <FillBookMark
                  //   onClick={onLike}
                  className="cursor-pointer  fill-black w-6 h-6 stroke-black hover:scale-105 "
                />
              )}
              <span>{BlogCountData?.wishlists ?? 0}</span>
            </div>
          }
          WithoutLogIn={
            <div className=" flex gap-1 flex-col cursor-pointer items-center justify-center  opacity-60">
              {!EnvTrueFalse[BlogCountData?.inWishList] ? (
                <BookMark className="cursor-pointer  fill-black w-6 h-6 stroke-black hover:scale-105 " />
              ) : (
                <FillBookMark
                  //   onClick={onLike}
                  className="cursor-pointer  fill-black w-6 h-6 stroke-black hover:scale-105 "
                />
              )}
              <span>{BlogCountData?.wishlists ?? 0}</span>
            </div>
          }
        />
      )}
      {!invisiblity?.share && !!BlogCountData?.share && (
        <div className=" flex gap-1 flex-col cursor-pointer items-center justify-center ">
          {/* <IoShareSocialOutline className=" text-3xl hover:scale-105 " /> */}
          <ShareLinks
            link={BlogCountData?.share ?? "https://sandee.com"}
            type={["facebook", "instagram", "twitter", "linkedin"]}
          />
        </div>
      )}
    </div>
  );
};

export default NewsLikeShare;
