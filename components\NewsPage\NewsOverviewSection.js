"use client";
import React, { useState } from "react";
import Image from "next/image";
import moment from "moment";
import ShareModel from "../Common/shareModal";
import CopyRight from "../Common/CopyRight";
import { blurDataURL, FinalImageGenerator } from "@/helper/functions";
import Link from "next/link";
import { ShareIcon1 } from "../social-icons/icons";
const NewsOverviewSection = ({ BlogData }) => {
  const [showShareIcons, setShowShareIcons] = useState(false);
  const handleShare = () => {
    setShowShareIcons((prv) => !prv);
  };
  return (
    <div className=" mb-5 ">
      <div style={{ display: "block", position: "sticky", top: 0 }}>
        {/* <div className="hidden md:flex gap-2 flex-col lg:flex-row my-5">
          <div className=" w-full  ">
            <div className="relative h-[520px]">
              <div className="absolute left-0 bottom-0  h-[520px] w-full ">
                <Image
                  priority
                  src={FinalImageGenerator(BlogData?.image, 1600, 4)}
                  alt={`${BlogData?.name ?? "Shark Attack Photo "
                    }`}
                  style={{
                    objectPosition: `center ${BlogData?.image?.position?.objectPositionY ? BlogData?.image?.position?.objectPositionY
                      : "50"}%`,
                  }}
                  className="object-cover bg-blend-overlay rounded-sandee"
                  blurDataURL={blurDataURL(300, 200)}
                  placeholder="blur"
                  fill
                />
                <CopyRight
                  copyRightsData={[BlogData?.image]}
                  classNameExtra={"bottom-0"}
                />
              </div>
            </div>
          </div>
        </div> */}
        <div className="flex  flex-col sm:flex-row ">
          {/* <div className="col block sm:sticky top-0 w-full lg:w-4/12 xl:w-3/12 px-5 ">
            <div className=" sticky top-0 bg-white z-[500] pt-8">
              <NewsTableOfContent h2Ids={h2Ids} headings={headings} />
            </div>
          </div> */}
          <div className="col bg-white block w-full ">
            <div className=" bg-white z-[500] pb-3  pt-2">
              {/* <div className=" sticky top-0 bg-white z-[500] pb-3 px-3 pt-8"> */}
              <div className=" bg-white">
                <h1 className=" text-sandee-24 font-semibold ">
                  {BlogData?.title}
                </h1>

                <div className="block sm:flex justify-between text-sandee-sm ">
                  <p className="text-sandee-20 font-semibold">
                    {moment(BlogData?.createdAt?.split("T")?.[0])?.format(
                      "MMM DD, YYYY"
                    ) ?? "Nov, 09 2023"}
                  </p>
                  <div className="flex gap-x-2 items-center">
                    {/* // <CustomToolTip title={icon?.name} key={index}> */}

                    <div
                      onClick={() => {
                        handleShare();
                      }}
                      className=" cursor-pointer"
                    >
                      <ShareIcon1 />
                    </div>

                    {/* </CustomToolTip> */}
                  </div>
                </div>
              </div>
              {/* <div className="flex gap-4 px-3 justify-center sm:justify-start flex-wrap mt-4">
                {BlogData?.blogCategories
                  ?.map((tag) => tag?.category?.name)
                  ?.map((el) => (
                    <p
                      key={el}
                      className=" text-sandee-blue"
                    // className=" text-sandee-blue active:text-white active:bg-sandee-blue border-2 border-sandee-blue  py-2 px-5 transition ease-in-out duration-300  px-10 text-xs font-medium  rounded-full active:scale-[0.7] hover:scale-110"
                    >
                      {`#${el}`}
                    </p>
                  ))}
              </div> */}
            </div>
            {/* <div className=" bg-white px-3 w-full flex md:hidden gap-3  !cursor-pointer ">
              <BlogLikeShare slug={BlogData?.slug} BlogData={BlogData} />
            </div> */}
            <div className=" gap-4 ">
              {/* <div className="col h-[430px] w-full lg:w-9/12">
                <div className=" relative w-full h-full">
                  {BlogData?.image && (
                    <Image
                      src={BlogData?.image}
                      fill
                      alt={BlogData?.title}
                      //   blurDataURL={blurDataURL(1000, 600)}
                      //   placeholder="blur"
                      className="w-full h-full header-static object-cover rounded-sandee"
                      priority
                    />
                  )}

             
                </div>
              </div> */}
              {/* <p className="mb-4">{BlogData?.description}</p> */}
              <div
                className="mb-4"
                dangerouslySetInnerHTML={{
                  __html: BlogData?.description ?? "",
                }}
              />

              {/* </div> */}
              {/* <p><span className="font-semibold">Source:</span> {BlogData?.sourceLink ? <Link className="text-sandee-blue" href={BlogData?.sourceLink} target="_blank">{BlogData?.source}</Link> : BlogData?.source}</p> */}

              {BlogData?.source !== null && BlogData?.sourceLink !== null ? (
                <Link
                  href={BlogData?.sourceLink}
                  className="hover:text-sandee-blue py-2 px-3 text-gray-800  bg-gray-100 rounded-full font-inter text-sm my-1 no-underline flex gap-x-1 w-fit"
                  hrefLang="en-us"
                  target="_blank"
                  rel="nofollow"
                  title={BlogData?.source}
                >
                  {/* <div className="  py-2 px-3 text-gray-800  bg-gray-100 rounded-full font-inter text-sm my-1 no-underline flex gap-x-1 w-fit">
                    <span className=" "> */}
                  {BlogData?.source}
                  {/* </span>
                  </div> */}
                </Link>
              ) : (
                <div className="my-[22px]"></div>
              )}
              {/* <EditorContent id="content"
                className=" min-w-full px-2 ql-container ql-editor blog-editor"
                value={processContent(modifiedHtmlString)} /> */}
              {/* <div
                id="content"
                className=" min-w-full px-2 ql-container ql-editor blog-editor"
                dangerouslySetInnerHTML={{
                  __html: modifiedHtmlString,
                }}
              /> */}
            </div>
          </div>
          {/* <div className="col bg-white w-full hidden md:block lg:w-2/12 xl:w-1/12">
            <BlogLikeShare
              slug={BlogData?.slug}
              BlogData={BlogData}
              className="flex flex-col mt-8 !cursor-pointer"
            />
          </div> */}
        </div>
      </div>

      {showShareIcons ? (
        <ShareModel
          modalOpen={showShareIcons}
          setModalOpen={setShowShareIcons}
          link={`/news/${BlogData?.slug}`}
          //   top={"lg:top-[30%] top-[28%]"}
          //   iconHeight={"2xl:py-5 py-2"}
        />
      ) : null}
    </div>
  );
};

export default NewsOverviewSection;
