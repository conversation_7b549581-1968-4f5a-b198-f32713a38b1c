import { SiteDataPageWise } from "@/data/siteMetadata";
import React from "react";

const NewsPageJSONLD = () => {
  const webPageSchema = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    url: "https://sandee.com/news",
    name: SiteDataPageWise.news.title || "Stay Updated with the Latest Beach News | Sandee",
    description: SiteDataPageWise.news.description || "Explore the latest beach news, updates, and trends from around the globe at Sandee. Discover insights, travel tips, and must-visit beach destinations all in one place.",
    publisher: {
      "@type": "Organization",
      name: "<PERSON><PERSON>",
    },
  };

  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        name: "Home",
        item: "https://sandee.com/",
      },
      {
        "@type": "ListItem",
        position: 2,
        name: "All News",
        item: "https://sandee.com/news",
      },
    ],
  };

  return (
    <>
      <script
        type="application/ld+json"
        id="webPageSchema"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(webPageSchema) }}
      ></script>
      <script
        type="application/ld+json"
        id="breadcrumbSchema"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbSchema) }}
      ></script>
    </>
  );
};

export default NewsPageJSONLD;
