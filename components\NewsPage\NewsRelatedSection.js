
"use client";
import { CustomGrid } from '../Custom-Display';
import NewsCard2 from './NewsCard2';


const NewsRelatedSection = ({ data }) => {

    return (
        <div>
            {/* <NameTitle
      // className="mt-8"
      description={"EXPERT ADVICE AND TIPS"}
      name={"Beach Blogs"}
      extraButton={
        <div className=" hidden md:flex justify-end items-start w-3/12">
          <Link
            href={HomePageData.BlogSection.button_link}
            className=" custom-hover-slide-button group"
          >
            <span className="custom-hover-slide group-hover:h-full"></span>
            <span className="custom-hover-slide-text group-hover:text-white font-semibold ">
              <ReadBookIcon className=" me-2 fill-transparent stroke-sandee-orange group-hover:stroke-white  h-5 w-5 -mt-0.5" />
              {HomePageData.BlogSection.button_text}
            </span>
          </Link>
        </div>
      }
      type={2}
    /> */}
            <CustomGrid
                data={data}
                className="gap-8 mt-[6px] mb-6 "
                Component={({ data: dataProps }) => {
                    dataProps.link = `/news/${dataProps?.slug}`; //
                    dataProps.imageSrc = dataProps?.image;
                    dataProps.name = dataProps?.title;
                    return NewsCard2({
                        data: { ...dataProps },
                        copyRightsData: dataProps?.image,
                    });
                }}
                xs={1}
                sm={2}
                md={2}
                lg={3}
                xl={3}
            />
            {/* <div className=" md:hidden my-5">
      <Link
        href={HomePageData.BlogSection.button_link}
        className=" custom-hover-slide-button group"
      >
        <span className="custom-hover-slide group-hover:h-full"></span>
        <span className="custom-hover-slide-text group-hover:text-white font-medium">
          <ReadBookIcon className=" me-2 fill-transparent stroke-sandee-orange group-hover:stroke-white h-5 w-5 -mt-0.5" />
          {HomePageData.BlogSection.button_text}
        </span>
      </Link>
    </div> */}
        </div>
    )
}

export default NewsRelatedSection


