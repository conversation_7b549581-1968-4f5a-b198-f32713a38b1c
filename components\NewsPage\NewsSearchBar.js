"use client";
import React, { useEffect, useState } from "react";
import { API_BASE_URL } from "@/helper/functions";
import axios from "axios";
import SelectBox from "../Common/SelectBox";
import CustomNewsFilter from "./CustomNewsFilter";
import { DatePicker } from "antd";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

dayjs.extend(utc);
dayjs.extend(timezone);

const NewsSearchBar = ({
    selectedCategory,
    setSelectedCategory,
    setSelection,
    query,
    setQuery,
    date,
    setDate,
    setNewsData,
    setPagination
}) => {
    // const [selectedCategory, setSelectedCategory] = useState([]);
    const [categories, setCategories] = useState([]);


    // console.log()

    const handelRefresher = () => {
        //   clearTimeout(debounce.current);
        //   debounce.current = setTimeout(() => {
        //     setPagination((prev) => ({ ...prev, page: 1 }));
        //   }, 500);
    };
    useEffect(() => {
        const getTagData = async () => {
            const CATEGORY = await getTestCategories();
            if (CATEGORY?.data?.length) {
                setCategories([
                    ...CATEGORY?.data?.map((el) => ({
                        label: el?.name,
                        value: el?.id,
                        id: el?.id,
                    })),
                ]);
            }
        };
        getTagData();
    }, []);
    return (
        <div className="  grid lg:grid-flow-col gap-4">
            <div className="relative col-span-6 ">
                <div className="absolute inset-y-0 start-0 flex items-center ps-2 pe-2 ms-2 pointer-events-none rounded-full">
                    <svg
                        className="w-4 h-4 text-[#7D7D7D]  "
                        aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 20 20"
                    >
                        <path
                            stroke="currentColor"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"
                        />
                    </svg>
                </div>
                <input
                    type="text"
                    id="simple-search"
                    className={`transition-all duration-500  focus:outline-none outline-none  focus:ring-0 ring-0 focus:border-sandee-blue  bg-white border border-sandee-blue block w-full ps-10 sm:text-[20px]  rounded-xl text-gray-600 !pl-[50px] lg:text-sandee-sm  !text-sandee-sm p-2 `}
                    placeholder="Search for Beach News"
                    value={query?.searchQuery}
                    onChange={(e) => setQuery(prev => ({
                        ...prev,
                        searchQuery: e.target.value
                    }))}
                />
            </div>
            <div>
                <DatePicker
                    className='h-10 w-full rounded-xl border-sandee-blue focus-within:shadow focus-within:border-sandee-blue hover:border-sandee-blue'
                    picker="month"
                    format={'MMMM'}
                    value={dayjs(date)}
                    disabledDate={(current) => {
                        return current && current > dayjs();
                    }}
                    allowClear={false}
                    onChange={(value) => {
                        value ? setDate(dayjs(value).utc().startOf('month')) : setDate(dayjs().utc().startOf('month'));
                        setNewsData([]);
                        setPagination({
                            page: 1,
                            limit: 10
                        })
                    }}
                />
            </div>
            {/* <div className="w-full h-full items-center flex gap-3 lg:justify-end flex-wrap col-span-6">
                <CustomNewsFilter
                    categories={categories}
                    setSelectedCategory={setSelectedCategory}
                    selectCategory={selectedCategory}
                    refresher={handelRefresher}
                />
                <SelectBox
                    options={[
                        {
                            value: "DESC",
                            label: "Latest",
                        },
                        {
                            value: "ASC",
                            label: "Oldest",
                        },
                    ]}
                    deafultSelectSort={{
                        value: "DESC",
                        label: "Latest",
                    }}
                    getSortingVal={(v) => {
                        setSelection(v);
                    }}
                    menuClassName="w-full"
                />
              
            </div> */}
        </div>
    );
};

export default NewsSearchBar;

export const getTestCategories = async () => {
    try {
        const response = await axios.get(`${API_BASE_URL}/category`);
        return response?.data;
    } catch (error) {
        // logController(error);
    }
};
