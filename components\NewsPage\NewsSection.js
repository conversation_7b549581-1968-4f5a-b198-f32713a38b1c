"use client";
import React, { useEffect, useRef, useState } from "react";
import { CustomContainer, CustomGrid } from "../Custom-Display";
import ScrollBar from "../ScrollBar";
import NewsSearchBar from "./NewsSearchBar";
import NewsCard from "../Cards/NewsCard";
import { getTestNews } from "@/app/(HeaderSlim)/(Single Country Page)/[countrySlug]/action";
import defaultImage from "../../public/static/images/header/Countries.avif";
import NewsNewCard from "../Cards/NewsNewCard";
import Image from "next/image";
import moment from "moment";
import { blurDataURL } from "@/helper/functions";
import defaultImageLogo from "../../public/static/images/Sandee_Logo.png";
import Link from "next/link";
import NewsCard1 from "../Cards/NewCard1";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";




const NewsSection = ({ initialData }) => {
  dayjs.extend(utc);
  dayjs.extend(timezone);

  const [newsData, setNewsData] = useState(initialData);
  const [date, setDate] = useState(dayjs().utc().startOf('month'))
  const [selection, setSelection] = useState("DESC");
  const [query, setQuery] = useState("");
  const [scrollLoader, setScrollLoader] = useState(false);
  const [loadoff, setLoadOff] = useState(false);
  const [pagination, setPagination] = useState({
    limit: 10,
    page: 1,
  });
  const [refresh, setRefresh] = useState(false);
  const prevSelection = useRef(selection);
  // const prevSelectedCategory = useRef(selectedCategory);
  const prevQuery = useRef(query);


  // Async function for delayed search
  const delayedSearch = async (
    queryValue,
    selectionValue,
    date
    // selectedCategoryValue
  ) => {
    const query = { ...pagination };
    if (queryValue?.searchQuery?.trim()?.length) {
      query.searchQuery = queryValue?.searchQuery;
    }
    if (date) {
      query.date = date?.toISOString()
    }
    // if (selectedCategoryValue?.length) {
    //   query.categoryIds = selectedCategoryValue?.join(",");
    // }
    if (Object.keys(query)?.length > 0) {
      // if (!!selectionValue) {
      query.sortBy = "date";
      query.sortOrder = "DESC";
      // }
    }

    try {
      const { data } = await getTestNews({ ...query });
      if (data?.rows?.length && pagination.page > 1) {
        setNewsData((prev) => [...prev, ...data?.rows]);
        setLoadOff(false);
      } else if (data?.rows?.length && pagination.page === 1) {
        setNewsData((prev) => [...data?.rows]);
        setLoadOff(false);
      } else {

        setLoadOff(true);
      }
    } catch (error) {
      console.error("Error fetching blog data:", error);
    } finally {
      setScrollLoader(false);
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      setScrollLoader(true);
      await delayedSearch(query, selection, date);
    };

    fetchData();
  }, [pagination.page, refresh, date]);

  useEffect(() => {
    if (
      prevSelection.current !== selection ||
      // prevSelectedCategory.current !== selectedCategory ||
      prevQuery.current !== query
    ) {
      const getData = setTimeout(() => {
        setPagination((prev) => {
          if (prev.page === 1) {
            setRefresh((prevR) => !prevR);
            setNewsData([]);
          }
          return { ...prev, page: 1 };
        });
      }, 400);

      // Update previous values
      prevSelection.current = selection;
      // prevSelectedCategory.current = selectedCategory;
      prevQuery.current = query;
      return () => clearTimeout(getData);
    }
  }, [selection, query]);

  return (
    <CustomContainer className="flex gap-x-4 w-full">
      <CustomContainer>
        <div className="w-full">
          <div className="w-full">
            <h1 className="text-sandee-32 mb-3 mt-2 font-bold text-center md:text-start">
              Beach News
            </h1>
            <NewsSearchBar
              // selectedCategory={selectedCategory}
              // setSelectedCategory={setSelectedCategory}
              date={date}
              setDate={setDate}
              // setSelection={setSelection}
              setQuery={setQuery}
              query={query}
              setNewsData={setNewsData}
              setPagination={setPagination}
            />
            <div className="min-h-screen">
              {!newsData?.length && !scrollLoader ? (
                <p className="px-2 py-5 mt-5  text-error-red-600 border-error-red-400 bg-error-red-50 border-2 rounded-sandee text-center">
                  No Data Found for your applied filters.
                </p>
              ) : (
                <CustomGrid
                  data={newsData}
                  className="gap-4 sm:gap-8 my-6 mb-12"
                  Component={({ data: dataProps }) => {
                    dataProps.link = `/news/${dataProps?.id}`; //
                    dataProps.imageSrc = dataProps?.image ?? defaultImage;
                    dataProps.name = dataProps?.title;
                    return NewsCard1({
                      data: { ...dataProps },
                      copyRightsData: dataProps?.image,
                    });
                  }}
                  xs={1}
                  sm={1}
                  md={1}
                  lg={1}
                  xl={1}
                />
              )}
              <ScrollBar
                threshold={60}
                loadMoreFunction={() => {
                  setPagination((prev) => ({ ...prev, page: prev?.page + 1 }));
                }}
                isLoading={scrollLoader}
                loadoff={loadoff}
                timeout={10}
              />
            </div>


          </div>
        </div>
      </CustomContainer>
    </CustomContainer>
  );
};

export default NewsSection;

//recommanded section
{/* <div className="w-4/12">
        <div className="my-5">
          <div className="bg-gray-100 px-3 py-3">
            Recommended News
          </div>
        </div>
        <div>
          <div className="bg-white py-5 rounded-[2px] shadow-[rgba(0,0,0,0.16)_0px_1px_4px]">
            {
              newsData?.map((data, index) => {
                return <div key={`news-${index}`}>
                  <div className=" px-5 flex flex-col gap-x-5 lg:gap-y-3 gap-y-2  relative  border-gray-200">
                    <div className="w-full flex gap-2 items-start justify-start">
                      <div className="w-16">
                        <div className="relative w-14 h-14 hover:lg:scale-[1.03]">
                          <Image
                            // src={defaultImageLogo}
                            src={data?.image ?? defaultImage}
                            fill
                            alt={`Sandee - News / ${data?.title}`}
                            className="rounded-full border-gray-color overflow-hidden object-cover relative"
                            title={`Sandee - News / ${data?.title}`}
                            sizes="auto"
                            blurDataURL={blurDataURL(400, 250)}
                            placeholder="blur"
                          />
                        </div>
                      </div>
                      <div className="">
                        <Link
                          className="cursor-pointer"
                          hrefLang="en-us"
                          href={`/news/${data?.slug}`}
                          title={data?.title}
                          // target="_blank"
                          rel="noopener noreferrer"
                        >
                          <h3 className=" text-black text-xl hover:underline text-start line-clamp-2 leading-7 font-semibold">
                            {data?.title}
                          </h3>
                        </Link>
                       
                      </div >
                    </div >
  <p>{data?.shortDescription ?? ""}</p>
                  </div >
  { newsData?.length != index + 1 && <div className="border-b-[1px] w-full mt-4 mb-4 px-2"></div>}
                </div >
              })
            }
          </div >
        </div >

      </div > */}