"use client"
import React, { useEffect, useRef, useState } from 'react'
import CustomToolTip from '../Common/CustomToolTip'
import Link from 'next/link'

const NewsDesc = ({
    data = {}
}) => {
    const [isShow, setIsShow] = useState(false);
    const [isOverflow, setIsOverflow] = useState(false);
    const contentRef = useRef(null);

    // Check if the content exceeds 5 lines
    useEffect(() => {
        const contentEl = contentRef.current;
        if (contentEl) {
            const lineHeight = parseFloat(
                window.getComputedStyle(contentEl).lineHeight
            );
            const maxHeight = lineHeight * 3; // Height for 5 lines
            if (contentEl.scrollHeight > maxHeight) {
                setIsOverflow(true); // Content is more than 5 lines
            } else {
                setIsOverflow(false); // Content fits within 5 lines
            }
        }
    }, [data?.description]);

    return (
        <div className="border-[1px] border-gray-300 p-3 relative">
            <div className="flex items-start justify-start flex-col relative">
                {/* <CustomToolTip title={data?.name}> */}
                <Link
                    className="cursor-pointer "
                    hrefLang="en-us"
                    href={`/news/${data?.slug}`}
                    title={data?.title}
                    // target="_blank"
                    rel="noopener noreferrer"
                >
                    <h2 className=" text-black text-2xl hover:underline text-start line-clamp-3 leading-8 font-semibold ">
                        {data?.name}
                    </h2>
                </Link>
                {/* </CustomToolTip> */}
                <p
                    className={`${!isShow ? "clamp-text" : ""} font-inter text-[#7D7D7D] mt-1`}
                    ref={contentRef}
                >
                    {/* {data?.shortDescription} */}
                    {data?.description}
                </p>
                {!isShow && <button
                    className='bg-sandee-blue absolute bottom-3 z-20 left-[43%] text-white py-2 px-3 rounded-sm mt-3'
                    onClick={() => setIsShow(!isShow)}
                >
                    Show More
                </button>}
            </div>
            {!isShow && <div class="absolute inset-x-0 bottom-0 h-10 bg-white/40 backdrop-blur-[1px]  "></div>}
        </div>
    )
}

export default NewsDesc