"use client"
import { Modal } from 'antd'
import React from 'react'
import { CancelIcon } from '../social-icons/icons'
import Link from 'next/link'
import moment from 'moment'
import { isMobileView } from '@/helper/functions'

const NewsdetailModal = ({
    modalOpen, setModalOpen, data,
}) => {
    const isMobileViews = isMobileView()

    // Toggle modal visibility
    const toggle = () => {
        setModalOpen((prev) => !prev);
    };
    return (
        <Modal
            open={modalOpen}
            onCancel={toggle}
            footer={null}
            closable={false}
            centered
            width={460}
            className="authpage"
            bodyStyle={{ padding: 0 }}
        >
            <div className="bg-white rounded-xl h-full pb-6 px-4 pt-4">
                <div className="flex justify-between items-center pb-1">
                    <p className="text-[1.1rem] leading-[110%] font-semibold">News Detail</p>
                    <div className="flex justify-end me-0  cursor-pointer" onClick={toggle}>
                        <CancelIcon className="w-7 h-7 opacity-50" />
                    </div>
                </div>

                <div className="flex flex-col 2xl:gap-y-1 xl:gap-y-2 lg:gap-y-2.5 gap-y-2">
                    <Link
                        href={`/news/${data?.slug}`}
                        className="tw-no-underline tw-text-inherit "
                        hrefLang="en-us"
                        target="_blank"
                        rel="nofollow"
                        title={data?.title}
                        onClick={() => {
                            // handleViews(data?.id);
                        }}
                    >
                        <h3 className="3xl:text-2xl xl:text-xl text-black text-lg font-semibold line-clamp-2 hover:underline hover:text-black">
                            {data?.title}
                        </h3>
                    </Link>

                    <div className="flex justify-between items-center ">
                        {data?.source !== null && data?.sourceLink !== null ? (
                            <Link
                                href={data?.sourceLink}
                                className="tw-no-underline tw-text-inherit "
                                hrefLang="en-us"
                                target="_blank"
                                rel="nofollow"
                                title={data?.source}
                                onClick={() => {
                                    // handleViews(data?.id);
                                }}
                            >
                                <div className="  py-2 px-3 text-gray-800  bg-gray-100 rounded-full font-inter text-sm my-1 no-underline flex gap-x-1 w-fit">
                                    <span className=" ">
                                        {data?.source}
                                    </span>
                                </div>
                            </Link>
                        ) : (
                            <div className="my-[22px]"></div>
                        )}
                        <span className="xl:text-base text-sm flex justify-end text-light-gray-900">
                            {moment(data?.createdAt?.split("T")?.[0]).format('MMM DD, YYYY') ??
                                "Nov, 09 2023"}
                        </span>
                    </div>
                    <div dangerouslySetInnerHTML={{ __html: data?.description }} />
                    {/* <p className="line-clamp-2 xl:text-base text-sm ">
                            {data?.shortDescription}
                        </p> */}

                </div>



            </div>
        </Modal>
    )
}

export default NewsdetailModal