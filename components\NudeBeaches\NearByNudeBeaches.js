"use client";
import React, { useEffect, useState } from 'react'
import { CustomContainer, CustomGrid } from '../Custom-Display';
import CustomSwiper from '../Custom-Swiper';
import NameTitle from '../Common/NameTitle';
import { altText, API_BASE_URL, blurDataURL, FinalImageGenerator, generateBreakpoints } from '@/helper/functions';
import CustomToolTip from '../Common/CustomToolTip';
import CopyRight from '../Common/CopyRight';
import Image from 'next/image';
import Link from 'next/link';
import axios from 'axios';
import CustomeImage from '../Common/CustomeImage';
import defaultSharkBeachImage from "../../public/default_shark_beach.jpg";

export const getBeachNearMe = async (data) => {
    const APISingleBeachesMap = `${API_BASE_URL}/beachMain/getNearByBeach?limit=8&page=1&nude=1&coordinates=${data}`;
    // logController(APICountryAllBeaches);
    const response = await axios.get(APISingleBeachesMap);
    return response?.data;
};
const NearByNudeBeaches = ({
    title = "Nude Beach Near You", description = "Find the nearest nude beach", data
}) => {
    const [location, setLocation] = useState(null);
    const [beachData, setBeachData] = useState([]);
    const [isLoading, setIsLoading] = useState(false);

    const getLocation = () => {
        if (typeof window !== "undefined" && window.navigator.geolocation) {
            window.navigator.geolocation.getCurrentPosition(
                (position) => {
                    // console.log(position)
                    setLocation(
                        `${position.coords.longitude},${position.coords.latitude}`
                    );
                    FetchBeachNearMe(
                        `${position.coords.longitude},${position.coords.latitude}`
                    );
                },
                (err) => {
                    console.log(err, "err");
                }
            );
        } else {
        }
    };
    useEffect(() => {
        getLocation();
    }, []);
    const FetchBeachNearMe = async (location) => {
        try {
            setIsLoading(true);
            const Results = await getBeachNearMe(location);

            setBeachData(Results?.data);
            setIsLoading(false);
        } catch (err) {
            //  console.log(err)
        }
    };

    return (
        <>
            {beachData?.length ? <CustomContainer>
                <NameTitle
                    className="mt-6"
                    name={title}
                    description={description}
                    type={2}
                />
                <CustomSwiper
                    data={beachData}
                    Component={({ data: dataProps }) => {
                        if (dataProps?.city?.state?.country?.slug) {
                            dataProps.link = `/${dataProps?.city?.state?.country?.slug}/${dataProps?.city?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.nameSlug}`; //
                            dataProps.location = dataProps?.city?.name && dataProps?.city?.state?.country?.code ? `${dataProps?.city?.name}, ${dataProps?.city?.state?.country?.code}` : ""; //
                        } else {
                            dataProps.link = `/${dataProps?.country?.slug}/${dataProps?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.nameSlug}`; //
                            dataProps.location = dataProps?.city?.name && dataProps?.country?.code ? `${dataProps?.city?.name}, ${dataProps?.country?.code}` : ""; //

                        }
                        dataProps.imageSrc = FinalImageGenerator(dataProps?.images?.[0]);
                        return BeachCard({
                            data: { ...dataProps, ...dataProps },
                        });
                    }}
                    settingsExtra={{
                        breakpoints: {
                            ...generateBreakpoints(300, 768, 50, 180),
                            ...generateBreakpoints(768, 1300, 50, 280),
                            ...generateBreakpoints(1300, 2400, 50, 330),
                        },
                        spaceBetween: 22,
                        // breakpoints: {
                        //   300: { slidesPerView: 1.2 },
                        //   800: { slidesPerView: 3 },
                        //   1200: { slidesPerView: 5.5 },
                        //   1400: { slidesPerView: 6.5 },
                        // },
                    }}
                    cardHeight={260}
                    slideClassName={`max-w-[180px] lg:max-w-[240px] xl:max-w-[230px]  2xl:max-w-[250px] 3xl:max-w-[300px] mr-8`}
                    className="mb-5 mt-[11.2px] hidden md:block"
                    id={"me_1"}
                // extra={
                //   <div className="relative h-[300px]  rounded-xl flex items-center justify-center ">
                //     <Link href={`/list/${listSlug}`}>
                //   <CustomButton type={4}>
                //     View More
                //   </CustomButton>
                //     </Link>
                //   </div>
                // }
                />
                <CustomGrid
                    className="!my-5 gap-4 sm:gap-8 md:hidden "
                    Component={({ data: dataProps }) => {
                        let newLocation = "";
                        if (dataProps?.city?.state?.country?.slug) {
                            dataProps.link = `/${dataProps?.city?.state?.country?.slug}/${dataProps?.city?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.nameSlug}`;
                            dataProps.location = `${dataProps?.city?.name}, ${dataProps?.city?.state?.country?.name}`;
                            newLocation = dataProps?.city?.name && dataProps?.city?.state?.country?.code
                                ? `${dataProps?.city?.name}, ${dataProps?.city?.state?.country?.code}`
                                : "";
                        } else {
                            dataProps.link = `/${dataProps?.country?.slug}/${dataProps?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.nameSlug}`;
                            dataProps.location = `${dataProps?.city?.name}, ${dataProps?.country?.name}`;
                            newLocation = dataProps?.city?.name && dataProps?.country?.code
                                ? `${dataProps?.city?.name}, ${dataProps?.country?.code}`
                                : "";
                        }
                        dataProps.imageSrc = FinalImageGenerator(dataProps?.images?.[0]);
                        return BeachCard({
                            data: { ...dataProps, ...dataProps, newLocation: newLocation },
                        });
                    }}
                    data={beachData}
                    xs={2}
                    sm={3}
                    md={3}
                    lg={4}
                    xl={5}
                    xxl={5}
                    xxxl={5}
                />
                {/* <div className=" flex md:hidden justify-center items-center mb-5">
        <Link href={`/list/${el?.nameSlug}`} className=" ">
          {ExtraButton ? (
            ExtraButton
          ) : (
            <CustomButton type={4}>
              View All
              <ExploreMoreArrow className=" ml-2 fill-sandee-blue group-hover:fill-white  h-4 w-4" />
            </CustomButton>
          )}
        </Link>
      </div> */}
            </CustomContainer> : <></>}
        </>
    );
}

export default NearByNudeBeaches;

const BeachCard = ({
    data,
    className = "md:aspect-[180/140] aspect-square",
}) => {
    data.lat = data?.GeoLoc?.coordinates?.length
        ? data?.GeoLoc?.coordinates?.[1]
        : 0;
    data.lon = data?.GeoLoc?.coordinates?.length
        ? data?.GeoLoc?.coordinates?.[0]
        : 0;
    const newLocation = data?.city?.state?.country?.slug &&
        (data?.city?.name && data?.city?.state?.country?.code)
        ? `${data?.city?.name}, ${data?.city?.state?.country?.code}`
        : data?.city?.name && data?.country?.code ? `${data?.city?.name}, ${data?.country?.code}`
            : "";
    return (
        <Link
            href={`${data?.link}`}
            className=""
        >
            <div className="flex flex-col gap-y-[10px]">
                <div
                    className={`relative overflow-hidden aspect-square rounded-[25px] sm:rounded-sandee ${className}`}
                >
                    <div
                        className={`relative overflow-hidden aspect-square rounded-[25px] sm:rounded-sandee ${className}`}
                    >
                        <CustomeImage
                            className="w-full object-cover"
                            src={data?.imageSrc ?? defaultSharkBeachImage}
                            defaultImages={defaultSharkBeachImage}
                            // alt={`${data?.location
                            //     ? `Sandee ${data?.name ?? data?.otherLocation ?? "Unknown"} Photo`
                            //     : `${data?.name ?? data?.otherLocation ?? "Unknown"}`
                            //     }`}
                            alt={altText(data)}
                            fill
                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                            blurDataURL={blurDataURL(300, 200)}
                            placeholder="blur"
                        />
                        {data?.attacks && <span className="absolute top-2 right-3 bg-[#FF1616]  py-1 px-3 rounded-full text-sm text-white">
                            {data?.attacks} Attacks
                        </span>}
                        <CopyRight
                            copyRightsData={data?.images}
                            // background={true}
                            // styleExtra={{ bottom: "1px", left: "3px" }}
                            classNameExtra={"bottom-0"}
                        />
                    </div>
                </div>
                <div className="flex flex-col gap-y-1 ms-[5px]">
                    <CustomToolTip title={data?.name ?? data?.otherLocation ?? "Unknown"}>
                        <h3 className="text-lg font-bold line-clamp-1 mb-0 leading-[24px]">
                            {data?.name ?? data?.otherLocation ?? "Unknown"}
                        </h3>
                    </CustomToolTip>
                    <span className="uppercase opacity-[0.8] text-[#656565] line-clamp-2 md:text-base text-sm font-semibold -mt-[5px]">
                        {newLocation || ""}
                    </span>
                    {data?.distance && (
                        <span className=" text-sandee-12 ms-1">{`(${data?.distance.toFixed(
                            2
                        )} Miles)`}</span>
                    )}
                </div>
            </div>
        </Link>
    );
};