import React from 'react'
import SingleListicle from '../SingleListicle'
import { CategoryIcon } from '../social-icons/icons'
import Link from 'next/link'
import CustomButton from '../Custom-Button'
import NameTitle from '../Common/NameTitle'
import { CustomContainer } from '../Custom-Display'

const NudeBeachListicle = ({
    data,
    className = "",
    buttonType = { type: 4, color: "fill-sandee-orange" },
    ExtraButton,
    title,
    description
}) => {
    return (
        <>
            <div

                className={`mt-5`}
            // className={`my-5 ${i % 2 === 0 ? "" : "bg-[#F6F6F6]"}`}
            >
                {/* className="mt-[30px]" */}
                <CustomContainer>
                    <NameTitle
                        className=""
                        name={title ?? data?.name ?? ""}
                        type={2}
                        description={description ?? data?.description ?? ""}
                        extraButton={
                            <div className=" hidden md:flex justify-end items-start w-3/12">
                                <Link
                                    href={`/list/${data?.nameSlug}`}
                                    className=" !text-nowrap"
                                >
                                    {ExtraButton ? (
                                        ExtraButton
                                    ) : (
                                        <CustomButton type={buttonType?.type}>
                                            <CategoryIcon
                                                className={`me-2 ${buttonType?.color} group-hover:fill-white  h-4 w-4`}
                                            />
                                            View All
                                        </CustomButton>
                                    )}
                                </Link>
                            </div>
                        }
                    />
                    <SingleListicle
                        data={data?.listiclesBeaches}
                        listSlug={data?.nameSlug}
                        id={data?.id}
                    />
                    <div className=" md:hidden mb-9">
                        {ExtraButton ? (
                            ExtraButton
                        ) : (
                            <Link
                                href={`/list/${data?.nameSlug}`}
                                className="custom-hover-slide-button group font-bold "
                            >
                                <span className="custom-hover-slide group-hover:h-full"></span>
                                <span className="custom-hover-slide-text group-hover:text-white font-bold">
                                    <CategoryIcon className=" me-2 fill-sandee-orange group-hover:fill-white h-4 w-4" />
                                    View All
                                </span>
                            </Link>
                            // <CustomButton type={4}>
                            //   <BeachIcon className=" me-2 fill-sandee-blue group-hover:fill-white  h-4 w-4" />
                            //   View All
                            // </CustomButton>
                        )}
                    </div>
                </CustomContainer>
            </div>
        </>
    )
}

export default NudeBeachListicle