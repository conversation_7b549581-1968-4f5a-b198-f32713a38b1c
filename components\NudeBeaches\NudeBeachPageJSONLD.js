import { SiteDataPageWise } from "@/data/siteMetadata";
import React from "react";

const NudeBeachPageJSONLD = () => {
  const webPageSchema = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    url: "https://sandee.com/nude-beaches",
    name: SiteDataPageWise.nudeBeach.title || "Best Nude Beaches Worldwide | Sandee",
    headline: SiteDataPageWise?.nudeBeach?.title,
    description: SiteDataPageWise?.nudeBeach?.description || "Explore the top nude beaches worldwide with <PERSON><PERSON>. Find the perfect spot to enjoy sun, sea, and freedom on stunning clothing-optional beaches.",
    publisher: {
      "@type": "Organization",
      name: "<PERSON><PERSON>",
    },
  };

  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        name: "Home",
        item: "https://sandee.com/",
      },
      {
        "@type": "ListItem",
        position: 2,
        name: "Nude Beaches",
        item: "https://sandee.com/nude-beaches",
      },
    ],
  };

  return (
    <>
      <script
        type="application/ld+json"
        id="webPageSchema"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(webPageSchema) }}
      ></script>
      <script
        type="application/ld+json"
        id="breadcrumbSchema"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbSchema) }}
      ></script>
    </>
  );
};

export default NudeBeachPageJSONLD;
