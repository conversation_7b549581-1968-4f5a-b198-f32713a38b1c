"use client";
import React, { useEffect, useState } from 'react'
import { CustomGrid } from '../Custom-Display';
import Link from 'next/link';
import Pagination from '../Common/Pagination';
import { API_BASE_URL, buildQueryString } from '@/helper/functions';
import axios from 'axios';
export const getWithoutNudeBeachStateList = async (query) => {
    const APISharkSpecies = `${API_BASE_URL}/states/getNoNudeBeachStates${buildQueryString(
        query
    )}`;
    const response = await axios.get(APISharkSpecies);
    return response?.data;
};
const WithoutNudeBeachesStateList = ({ data, totalRecords = 100, isPagination = false }) => {
    const [loading, setLoading] = useState(true);
    const [total, setTotal] = useState(totalRecords);
    const [query, setQuery] = useState("");
    const [CurrentBeach, setCurrentBeach] = useState(data);
    const [currentPage, setCurrentPage] = useState(1);
    const [refresh, setRefresh] = useState(true);


    const FetchOrSetBeach = async () => {
        setLoading(true);
        if (!!query) {
            const AllBeachesFilterResponse = await getWithoutNudeBeachStateList({
                page: currentPage,
                // searchQuery: query,
                limit: 40,
                // withOutAttacks: 1,
                // countryId: countryId,

            });
            setTotal(AllBeachesFilterResponse?.data?.count);
            setCurrentBeach(AllBeachesFilterResponse?.data?.rows);
            return setLoading(false);
        }

        const AllBeachesResponse = await getWithoutNudeBeachStateList({
            page: currentPage,
            limit: 40,
            // withOutAttacks: 1,
            // countryId: countryId,
        });

        setTotal(AllBeachesResponse?.data?.count);
        setCurrentBeach(AllBeachesResponse?.data?.rows);
        return setLoading(false);
    };
    useEffect(() => {
        if (!isPagination) return
        FetchOrSetBeach();
    }, [refresh]);


    return (
        <>

            <CustomGrid
                data={CurrentBeach}
                className="gap-2 sm:gap-2 my-1 mb-10"
                Component={({ data: dataProps }) => {
                    return <Link href={`/${dataProps?.country?.slug}/${dataProps?.slug}`} className='text-start text-sandee-16'>{dataProps?.name}</Link>

                }}
                xs={2}
                sm={2}
                md={3}
                lg={4}
                xl={4}
            />
            {isPagination && <Pagination
                className="pagination-bar"
                currentPage={currentPage}
                totalCount={total}
                pageSize={40}
                onPageChange={(page) => {
                    setCurrentPage(page);
                    setRefresh((prev) => !prev);
                }}
            />}
        </>
    )
}

export default WithoutNudeBeachesStateList