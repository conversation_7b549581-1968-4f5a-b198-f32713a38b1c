import { SiteDataPageWise } from "@/data/siteMetadata";
import React from "react";

const PodcastPageJSONLD = () => {
    const webPageSchema = {
        "@context": "https://schema.org",
        "@type": "WebPage",
        url: "https://sandee.com/podcast",
        name: SiteDataPageWise.podcast.title || "Listen to the Best Podcasts – Inspiring Stories & Expert Talks | Sandee",
        headline: SiteDataPageWise.podcast.title || "Listen to the Best Podcasts – Inspiring Stories & Expert Talks | Sandee",
        description: SiteDataPageWise.podcast.description || "Explore a collection of engaging podcasts covering diverse topics, from travel and adventure to self-improvement and expert interviews. Listen now and get inspired!",
        publisher: {
            "@type": "Organization",
            name: "<PERSON><PERSON>",
        },
    };

    const breadcrumbSchema = {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        itemListElement: [
            {
                "@type": "ListItem",
                position: 1,
                name: "Home",
                item: "https://sandee.com/",
            },
            {
                "@type": "ListItem",
                position: 2,
                name: "The Beaches Podcast",
                item: "https://sandee.com/podcast",
            },
        ],
    };

    return (
        <>
            <script
                type="application/ld+json"
                id="webPageSchema"
                dangerouslySetInnerHTML={{ __html: JSON.stringify(webPageSchema) }}
            ></script>
            <script
                type="application/ld+json"
                id="breadcrumbSchema"
                dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbSchema) }}
            ></script>
        </>
    );
};

export default PodcastPageJSONLD;
