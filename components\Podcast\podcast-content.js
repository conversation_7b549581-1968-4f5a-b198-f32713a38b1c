"use client";
import React from "react";
import { CustomContainer, HeadingTags } from "../Custom-Display";
import Script from "next/script";

const PodcastContent = () => {
  return (
    <CustomContainer>
      <div className="text-center">
        <HeadingTags
          htag={1}
          className="mt-4 flex justify-center text-sandee-20 sm:text-sandee-24 font-semibold"
        >
          <span className="absolute bottom-0 left-0 flex w-full h-0 mb-0 transition-all duration-200 ease-out transform translate-y-0  text-sandee-blue opacity-100 group-hover:h-full"></span>
          <span className="relative flex items-center group-hover:text-sandee-blue ">
            The Beaches Podcast <sup className="mr-2 text-sandee-12">TM</sup>
          </span>
        </HeadingTags>

        <p className=" text-sandee-base my-5">
          At The Beaches Podcast, we&apos;re not just here to give you facts about
          beaches. We&apos;re here to spark your imagination. To take you beyond
          the ordinary. We bring you closer to the heart of every coastline,
          every wave, every grain of sand. Because beaches aren&apos;t just
          destinations—they&apos;re experiences, they’re moments of wonder. And
          with The Beaches Podcast, we aim to help you rediscover that magic.
        </p>

        {/* <CustomGrid
          data={listicleData}
          className="gap-4 sm:gap-8 my-6"
          Component={({ data: dataProps }) => {
           

            return <Link
            href={``}
            className="relative flex h-[110px] group  rounded-sandee shadow-[0_3px_10px_rgb(0,0,0,0.2)]"
          >
            <div className="    h-[110px] w-[110px] sm:w-[130px] rounded-s-sandee"></div>
            <div className=" px-4 rounded-sandee flex gap-2 justify-center items-start flex-col">
              <h3 className=" text-sandee-base  line-clamp-2">{"Hello"}</h3>
            </div>
          </Link>
          }}
          xs={1}
          sm={2}
          // md={2}
          lg={3}
          // xl={3}
        /> */}
        <div id="buzzsprout-large-player"></div>
        <Script
          type="text/javascript"
          charset="utf-8"
          src="https://www.buzzsprout.com/2411256.js?container_id=buzzsprout-large-player&player=large"
        ></Script>
      </div>
    </CustomContainer>
  );
};

export default PodcastContent;
