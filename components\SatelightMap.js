"use client";
import React, { useEffect, useRef } from "react";
import mapboxgl from "!mapbox-gl";
mapboxgl.accessToken = process.env.NEXT_PUBLIC_MAPBOX_API_KEY;
const SatelightMap = ({ location }) => {
  const mapContainer = useRef(null);
  const map = useRef(null);
  useEffect(() => {
    if (!location) return;
    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: 'mapbox://styles/mapbox/satellite-v9', // style URL
      center:location,
      zoom: 5,
    //   minZoom: 3,
      maxBounds: [
        [-180, -85], // Southwest coordinates
        [180, 85], // Northeast coordinates
      ],
      projection: { name: "globe" },
      interactive: false, // Disable all interactions
      scrollZoom: false, // Disable scroll zoom
      dragPan: false, // Disable drag panning
      touchZoomRotate: false, // Disable touch zoom and rotate
      doubleClickZoom: false, // Disable double click zoom
      keyboard: false, // Disable keyboard controls
    });

    return () => {
      map.current.remove();
    };
  }, [location]);

  return (
    <>
      <div className="relative  h-[300px] w-full  p-1  listicle-map rounded-xl cursor-pointer">
        <div
          ref={mapContainer}
          className="md:map-container md:h-[282px] w-full h-[275px] overflow-hidden border border-gray-400 rounded-xl"
        />
      </div>
    </>
  );
};

export default SatelightMap;
