import React from "react";
import BreadCumber from "@/components/Common/BreadCumber";
import Image from "next/image";
import { CustomContainer } from "../Custom-Display";
import CustomeImage from "../Common/CustomeImage";

const Save_Our_Ocean_Hero_Section = ({
  data = {
    name: "Save Our Ocean",
    summary:
      "As the leading beach information company, we believe in protecting the beautiful oceans and beaches we visit. That's why we proudly support nonprofits dedicated to marine conservation and sustainability. We are fortunate to work with the best ocean conservancy groups on the planet. Read on to learn about some of the amazing groups working to protect beaches and marine life worldwide.",
    image: "https://images.sandee.com/images/header/ContactUsHeader.avif",
  },
}) => {
  return (
    <>
      <div className=" bg-gray-100 w-full p-4 flex items-center">
        <CustomContainer>
          <BreadCumber
            data={[
              {
                title: `${data?.name ?? "Save Our Ocean"}`,
              },
            ]}
          />
        </CustomContainer>
      </div>
      <CustomContainer>
        <div className=" flex gap-2 flex-col lg:flex-row my-5">
          <div className=" w-full  ">
            <div className="relative h-[420px]">
              <div className="absolute left-0 bottom-0  h-[420px] w-full ">
                <CustomeImage
                  priority
                  // src={FinalImageGenerator(data?.image, 1600, 3)}
                  src={data?.image}
                  alt={`${data?.alterText ? data?.alterText : data?.name ? `${data?.name} Photo - Sandee` : "HomePage Sandee Photo "
                    }`}
                  className=" object-cover bg-blend-overlay rounded-sandee"
                  fill
                />
              </div>
              <div className="absolute right-0 bottom-0  h-[420px] w-full bg-transparent z-1 flex justify-center  items-center ">
                <h1 className=" text-white font-bold rounded-full border border-gray-300 bg-[#EBEBEB66] [text-shadow:0px_4px_4px_rgba(0,_0,_0,_0.25)]   text-[28px]  xs:text-[28px] px-3 xs:px-3  sm:text-sandee-32 sm:px-4 md:text-[40px] md:px-8 xl:text-[45px] top-[50%] left-[50%]  ">
                  {data?.name}
                </h1>
              </div>
            </div>
          </div>
        </div>
        <p className=" text-start align-middle text-sandee-18 mb-5 px-5">
          {`${data?.summary ?? data?.overview ?? data?.description ?? ""}`}
        </p>
      </CustomContainer>
    </>
  );
};

export default Save_Our_Ocean_Hero_Section;
