import React from "react";
import { CustomGrid } from "../Custom-Display";
import Image from "next/image";
import Link from "next/link";
import { altText, blurDataURL } from "@/helper/functions";
import NameTitle from "../Common/NameTitle";
import CustomeImage from "../Common/CustomeImage";

export const alphanumericSort = (a, b, value) => {
  if (isNaN(parseInt(a[value]))) {
    return a[value]?.localeCompare(b[value]);
  }
  return a[value] - b[value];
};
const SaveOcean_Charities_Section = ({ data }) => {
  // logController(data?.charities?.sort((a, b) => alphanumericSort(a, b, "title")));
  return (
    <>
      <NameTitle name={data?.title ?? ""} />
      <CustomGrid
        className="gap-4 sm:gap-8 my-6 mb-12"
        Component={ListCard}
        data={data?.charities?.sort((a, b) => alphanumericSort(a, b, "title"))}
        xs={1}
        sm={2}
        md={2}
        lg={2}
        xl={2}
        xxl={2}
        xxxl={2}
      />
    </>
  );
};

export default SaveOcean_Charities_Section;

export const ListCard = ({ data }) => {
  return (
    <Link
      href={`${data?.link}`}
      target="_blank"
      className="relative flex h-[150px] group  rounded-sandee shadow-[0_3px_10px_rgb(0,0,0,0.2)]"
    >
      <div className="    h-[150px] w-[150px] rounded-s-sandee">
        <div className=" h-[150px] w-[150px] relative  overflow-hidden  rounded-s-sandee ">
          <CustomeImage
            // priority
            className=" h-[150px] w-[150px] transition-transform duration-1000 ease-in-out transform group-hover:scale-110 object-contain bg-blend-color-burn px-3 py-3"
            src={data?.image}
            alt={altText(data)}
            fill
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            blurDataURL={blurDataURL(300, 200)}
            placeholder="blur"
          />
        </div>
      </div>
      <div className=" px-5 rounded-sandee flex gap-2 justify-center items-strat flex-col">
        <h3 className=" text-[20px]  line-clamp-2">{data?.title}</h3>
        <p className=" text-xs line-clamp-4 text-sandee-grey">
          {data?.description}
        </p>
      </div>
    </Link>
  );
};
