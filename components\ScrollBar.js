"use client";
import React, { useEffect, useRef, useState } from "react";

const ScrollBar = (props) => {
  const {
    loadMoreFunction = () => {},
    threshold = 90,
    isLoading,
    loadoff,
    timeout = 500,
  } = props;
  const debounce = useRef(null);
  const [scroll, setScroll] = useState(1);
  const handleScroll = () => {
    const scrollHeight = document.documentElement.scrollHeight;
    const scrollTop = document.documentElement.scrollTop;
    const windowHeight = window.innerHeight;
    const scrollPercentage = ((scrollTop + windowHeight) / scrollHeight) * 100;

    if (scrollPercentage >= threshold) {
      setScroll(scrollPercentage);
    }
  };

  useEffect(() => {
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);
  useEffect(() => {
    if (scroll < threshold) {
      return;
    }
    clearTimeout(debounce.current);
    debounce.current = setTimeout(() => {
      if (loadoff || isLoading) {
        return;
      } else {
        loadMoreFunction();
      }
    }, timeout);
  }, [scroll]);
  return (
    <div
      style={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        marginTop: "20px",
        marginBottom: "20px",
      }}
    >
      {isLoading && !loadoff ? (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          xmlnsXlink="http://www.w3.org/1999/xlink"
          style={{ margin: "auto", background: "#fff", display: "block" }}
          width="50px"
          height="50px"
          viewBox="0 0 100 100"
          preserveAspectRatio="xMidYMid"
        >
          <circle
            cx={50}
            cy={50}
            fill="none"
            stroke="#00aae3"
            strokeWidth={10}
            r={35}
            strokeDasharray="164.93361431346415 56.97787143782138"
          >
            <animateTransform
              attributeName="transform"
              type="rotate"
              repeatCount="indefinite"
              dur="1s"
              values="0 50 50;360 50 50"
              keyTimes="0;1"
            />
          </circle>
        </svg>
      ) : (
        ""
      )}
    </div>
  );
};

export default ScrollBar;
