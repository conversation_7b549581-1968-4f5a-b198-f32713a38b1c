"use client";
import React, { useEffect, useRef, useState } from "react";

function ScrollLoader({ loadMoreData, hasMore, oneTimeScroll }) {
  const loaderRef = useRef(null);
  const [canLoad, setCanLoad] = useState(true);

  useEffect(() => {
    const options = {
      root: null,
      rootMargin: "0px",
      threshold: 0.5, // Trigger when 50% of the element is visible
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting && hasMore && canLoad) {
          loadMoreData();
          if (oneTimeScroll) {
            setCanLoad(false); // Disable further loading
          }
        }
      });
    }, options);

    if (loaderRef.current) {
      observer.observe(loaderRef.current);
    }

    return () => {
      if (loaderRef.current) {
        observer.unobserve(loaderRef.current);
      }
    };
  }, [loadMoreData, hasMore, oneTimeScroll, canLoad]);

  return <div ref={loaderRef}></div>;
}

export default ScrollLoader;
