"use client";
import React, { useState } from "react";
import SearchBar from "./SearchBar";
import { SearchIcon } from "./social-icons/icons";
import { Modal } from "antd";

const SearchButton = ({ isFocus, setIsFocus }) => {
  const [open, setOpen] = useState(false);
  return (
    <div className="">
      <div className=" relative md:hidden ">
        <div className=" flex justify-center items-center cursor-pointer border border-sandee-blue p-2 rounded-full">
          <SearchIcon
            className="w-6 h-6 text-[#7D7D7D] bg-white fill-white "
            onClick={() => {
              setOpen(true);
            }}
          />
        </div>

        <Modal
          role="dialog"
          aria-modal="true"
          footer={null}
          closable={false}
          onCancel={() => {
            setOpen(false);
          }}
          okButtonProps={{
            style: {
              display: "none",
            },
          }}
          style={{
            padding: 0,
            // background: "white",
            // display: "flex",
            // justifyContent: "center",
            // alignItems: "center",
          }}
          open={open}
          wrapClassName=" !bg-white !bg-opacity-85"
        >
          <SearchBar
            extraFunction={() => {
              setOpen(false);
            }}
          />
        </Modal>
      </div>
      <div className=" hidden md:block">
        <SearchBar onTop setIsFocus={setIsFocus} />
      </div>
    </div>
  );
};

export default SearchButton;

// "use client";
// import React from "react";
// import SearchBar from "./SearchBar";

// const SearchButton = () => {
//   return <SearchBar onTop />;
// };

// export default SearchButton;
