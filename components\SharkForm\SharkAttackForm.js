"use client"
import { <PERSON><PERSON>, Drawer, message, Space } from 'antd';
import React, { useState } from 'react';
import { CloseIcon } from '../social-icons/icons';
import { redirect, usePathname, useRouter, useSearchParams } from 'next/navigation';
import { API_BASE_URL } from '@/helper/functions';
import axios from 'axios';

const SharkAttackForm = ({ open = false, formType = null }) => {
    const [loading, setLoading] = useState(false);
    const pathname = usePathname();
    const searchParams = useSearchParams();
    const router = useRouter();

    const searches = searchParams.get('form')
    // console.log(pathname, searchParams, search)
    // Handle form submission
    const handleSubmit = async (event) => {

        event?.preventDefault();
        setLoading(true);
        try {
            // Get form data
            const formData = new FormData(event?.target);
            const formDataObject = {};

            // Convert FormData to object
            formData.forEach((value, key) => {
                formDataObject[key] = value || null; // Convert empty strings to null
            });
            // Validate required fields
            if (!formDataObject.name?.trim()) {
                throw new Error('Name is required');
            }
            // Create payload with required structure
            const payload = {
                type: "report-shark-attack",
                formData: formDataObject
            };

            // Make API call
            const response = await axios.post(`${API_BASE_URL}/sharkReports`, payload);

            if (response?.status === 200 || response?.status === 201) {
                message?.success('Shark attack report submitted successfully!');
                event?.target?.reset(); // Reset form
                router?.push('/shark', undefined, { shallow: true }); // Close drawer
            } else {
                throw new Error('Failed to submit report');
            } 0
        } catch (error) {
            console.error('Submission error:', error);
            message.error(error.response?.data?.message || 'Failed to submit shark attack report');
        } finally {
            setLoading(false);
        }
    };

    return (
        <>
            <Drawer
                title={
                    <div className="flex items-center justify-between">
                        <p className="form-modal-head mb25 text-xl">{"Shark Attack Report Questionnaire"}</p>
                        <Space>
                            {/* Close Button */}
                            <Space
                                onClick={() => {
                                    router.push(`shark`, undefined, { shallow: true });
                                }}
                                className="cursor-pointer rounded-lg border-[1px] border-[#D9D9D9] w-8 h-8 flex items-center justify-center"
                            >
                                <CloseIcon className="text-success-300" />
                            </Space>
                        </Space>
                    </div >}
                width={500}
                className="rounded-xl"
                closeIcon={false}
                open={searches == "report-shark-attack"}
                maskClosable={false}
                styles={{
                    body: {
                        padding: "16px 24px",
                    },
                    footer: {
                        border: 0,
                        display: "flex",
                        flexDirection: "row-reverse",
                    },
                }}
                style={{ zIndex: 99 }}
                footer={
                    [
                        (
                            // <>
                            //     {onSubmitConfirm ? (
                            //         <Popconfirm title="Are you sure?" onConfirm={handleFormSubmit}>
                            //             <Button
                            //                 key="submit"
                            //                 className="text-base"
                            //                 disabled={CONSTANTS.USER_ROLE && disableSubmit}
                            //                 style={{
                            //                     borderRadius: "5px",
                            //                     height: "50px",
                            //                     width: "180px",
                            //                 }}
                            //                 type="primary"
                            //                 loading={API.isLoading}
                            //             >
                            //                 {SubmitName}
                            //             </Button>
                            //         </Popconfirm>
                            //     ) : (
                            <>
                                {/* <button
                                type="submit"
                                key="shark-submit"
                                className={`!w-[40%] login-button ${loading ? " animate-pulse !bg-sandee-grey" : ""
                                    }`}

                            // className="bg-blue-500 text-white px-6 py-2 rounded-md hover:bg-blue-600"
                            >
                                Submit
                            </button> */}
                                <Button
                                    key="submit"
                                    //   disabled={CONSTANTS.USER_ROLE && disableSubmit}
                                    // className="w-full h-14 text-lg font-semibold mb-2 mt-2"
                                    className={`bg-sandee-blue text-white w-full h-14 text-lg font-semibold mb-2 mt-2 login-button ${loading ? " animate-pulse !bg-sandee-grey" : ""
                                        }`}
                                    type="submit"
                                    //   onClick={handleFormSubmit}
                                    loading={loading}
                                    form="sharkAttackForm"
                                    htmlType="submit"
                                >
                                    {"Submit"}
                                </Button>
                            </>
                            //     )}
                            // </>
                        ),
                    ]}
            >
                {/* <div className="custom-auth min-h-screen py-8 px-4 sm:px-6 lg:px-8">
                    <div className="containerB max-w-4xl mx-auto bg-gray-100 p-8 rounded-lg shadow-lg"> */}
                {/* <h2 className="text-3xl heading font-bold text-center mb-8">Shark Attack Report Questionnaire</h2> */}
                <form method="POST" className="space-y-6 form" id="sharkAttackForm" onSubmit={handleSubmit}>
                    {/* Victim Information */}
                    <div>
                        <h2 className="text-xl font-semibold mb-4">1. Victim Information</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Name<span className="text-[#fb3333]">*</span></label>
                                <input
                                    type="text"
                                    name="name"
                                    required
                                    className="input focus:border-sandee-blue focus:border-solid  focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter victim's name"
                                />
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Phone Number</label>
                                <input
                                    type="text"
                                    name="phone"
                                    className="input focus:border-sandee-blue focus:border-solid  focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter phone number"
                                />
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Age</label>
                                <input
                                    type="text"
                                    name="age"
                                    className="input focus:border-sandee-blue focus:border-solid  focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter victim's age"
                                />
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Email</label>
                                <input
                                    type="email"
                                    name="email"
                                    className="input focus:border-sandee-blue focus:border-solid  focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter email address"
                                />
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Height</label>
                                <input
                                    type="text"
                                    name="height"
                                    className="input focus:border-sandee-blue focus:border-solid  focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter height (e.g., 5'10)"
                                />
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Weight</label>
                                <input
                                    type="text"
                                    name="weight"
                                    className="input focus:border-sandee-blue focus:border-solid  focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter weight (e.g., 160 lbs)"
                                />
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Sex</label>
                                <input
                                    type="text"
                                    name="sex"
                                    className="input focus:border-sandee-blue focus:border-solid  focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter sex (e.g., Male, Female)"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Attack Details */}
                    <div>
                        <h2 className="text-xl font-semibold mb-4">2. Attack Details</h2>
                        <div className="space-y-4">
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Date and time of the attack</label>
                                <input
                                    type="datetime-local"
                                    name="attackDateTime"
                                    className="input focus:border-sandee-blue focus:border-solid  focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                />
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Clothing and accessories worn by the victim (including colors)</label>
                                <textarea
                                    name="clothing"
                                    className="input focus:border-sandee-blue focus:border-solid  focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    rows="3"
                                    placeholder="Describe clothing and accessories"
                                ></textarea>
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Activity before the attack (e.g., swimming, surfing, diving)</label>
                                <input
                                    type="text"
                                    name="activity"
                                    className="input focus:border-sandee-blue focus:border-solid  focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter activity"
                                />
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Duration spent in the water before the attack</label>
                                <input
                                    type="text"
                                    name="durationInWater"
                                    className="input focus:border-sandee-blue focus:border-solid  focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter duration (e.g., 30 minutes)"
                                />
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Direction faced and observed shark approach direction</label>
                                <input
                                    type="text"
                                    name="direction"
                                    className="input focus:border-sandee-blue focus:border-solid  focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter direction details"
                                />
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Description of the attack sequence (initial strike, number of strikes, shark behavior)</label>
                                <textarea
                                    name="attackDescription"
                                    className="input focus:border-sandee-blue focus:border-solid  focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    rows="5"
                                    placeholder="Describe the attack sequence"
                                ></textarea>
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Outcome of the attack (no injury, minor injury, major injury, fatal)</label>
                                <input
                                    type="text"
                                    name="outcome"
                                    className="input focus:border-sandee-blue focus:border-solid  focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter outcome"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Environmental Conditions */}
                    <div>
                        <h2 className="text-xl font-semibold mb-4">3. Environmental Conditions</h2>
                        <div className="space-y-4">
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Location of the attack (beach/reef name, GPS coordinates)</label>
                                <input
                                    type="text"
                                    name="location"
                                    className="input focus:border-sandee-blue focus:border-solid  focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter location details"
                                />
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Depth of water at the attack site</label>
                                <input
                                    type="text"
                                    name="waterDepth"
                                    className="input focus:border-sandee-blue focus:border-solid  focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter water depth"
                                />
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Weather conditions (e.g., sunny, overcast)</label>
                                <input
                                    type="text"
                                    name="weather"
                                    className="input focus:border-sandee-blue focus:border-solid  focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter weather conditions"
                                />
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Sea conditions (e.g., water visibility, sea temperature, current direction)</label>
                                <input
                                    type="text"
                                    name="seaConditions"
                                    className="input focus:border-sandee-blue focus:border-solid  focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter sea conditions"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Shark Information */}
                    <div>
                        <h2 className="text-xl font-semibold mb-4">4. Shark Information</h2>
                        <div className="space-y-4">
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Was the shark seen before or during the attack?</label>
                                <input
                                    type="text"
                                    name="sharkSeen"
                                    className="input focus:border-sandee-blue focus:border-solid  focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter details"
                                />
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Estimated size and species of the shark</label>
                                <input
                                    type="text"
                                    name="sharkSizeSpecies"
                                    className="input focus:border-sandee-blue focus:border-solid  focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter size and species"
                                />
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Number of sharks involved</label>
                                <input
                                    type="text"
                                    name="numberOfSharks"
                                    className="input focus:border-sandee-blue focus:border-solid  focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter number of sharks"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Witness Information */}
                    <div>
                        <h2 className="text-xl font-semibold mb-4">5. Witness Information</h2>
                        <div className="space-y-4">
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Name</label>
                                <input
                                    type="text"
                                    name="witnessName"
                                    className="input focus:border-sandee-blue focus:border-solid  focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter witness name"
                                />
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Phone</label>
                                <input
                                    type="text"
                                    name="witnessPhone"
                                    className="input focus:border-sandee-blue focus:border-solid  focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter witness phone number"
                                />
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Email</label>
                                <input
                                    type="email"
                                    name="witnessEmail"
                                    className="input focus:border-sandee-blue focus:border-solid  focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter witness email"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Medical Details */}
                    <div>
                        <h2 className="text-xl font-semibold mb-4">6. Medical Details</h2>
                        <div className="space-y-4">
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Injuries sustained by the victim</label>
                                <textarea
                                    name="injuries"
                                    className="input focus:border-sandee-blue focus:border-solid  focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    rows="3"
                                    placeholder="Describe injuries"
                                ></textarea>
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">First aid administered and by whom</label>
                                <textarea
                                    name="firstAid"
                                    className="input focus:border-sandee-blue focus:border-solid  focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    rows="3"
                                    placeholder="Describe first aid"
                                ></textarea>
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Time between attack and hospital arrival</label>
                                <input
                                    type="text"
                                    name="timeToHospital"
                                    className="input focus:border-sandee-blue focus:border-solid  focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter time (e.g., 1 hour)"
                                />
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Name and address of the hospital and treating physician</label>
                                <textarea
                                    name="hospitalDetails"
                                    className="input focus:border-sandee-blue focus:border-solid  focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    rows="3"
                                    placeholder="Enter hospital and physician details"
                                ></textarea>
                            </div>
                        </div>
                    </div>

                    {/* Additional Observations */}
                    <div>
                        <h2 className="text-xl font-semibold mb-4">7. Additional Observations</h2>
                        <div className="space-y-4">
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Any fishing or unusual marine activity near the site</label>
                                <textarea
                                    name="marineActivity"
                                    className="input focus:border-sandee-blue focus:border-solid  focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    rows="3"
                                    placeholder="Describe marine activity"
                                ></textarea>
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Other animals observed (e.g., dolphins, seals)</label>
                                <textarea
                                    name="otherAnimals"
                                    className="input focus:border-sandee-blue focus:border-solid  focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    rows="3"
                                    placeholder="Describe other animals"
                                ></textarea>
                            </div>
                        </div>
                    </div>

                    {/* Cause of Attack */}
                    <div>
                        <h2 className="text-xl font-semibold mb-4">8. Cause of Attack</h2>
                        <div className="space-y-4">
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Victim&apos;s theory about why the shark attacked</label>
                                <textarea
                                    name="causeOfAttack"
                                    className="input focus:border-sandee-blue focus:border-solid  focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    rows="5"
                                    placeholder="Enter victim's theory"
                                ></textarea>
                            </div>
                        </div>
                    </div>

                    {/* Submit Button */}
                    {/* <div className="flex justify-end">
                                <button
                                    type="submit"
                                    className={`!w-[40%] login-button ${loading ? " animate-pulse !bg-sandee-grey" : ""
                                        }`}
                                // className="bg-blue-500 text-white px-6 py-2 rounded-md hover:bg-blue-600"
                                >
                                    Submit
                                </button>
                            </div> */}
                </form>
                {/* </div>
                </div> */}
            </Drawer >
        </>
    );
};

export default SharkAttackForm;