"use client"
import { <PERSON><PERSON>, Drawer, message, Space } from 'antd';
import React, { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { CloseIcon } from '../social-icons/icons';
import axios from 'axios';
import { API_BASE_URL } from '@/helper/functions';

const SharkSightingForm = () => {
    const [loading, setLoading] = useState(false);
    const router = useRouter();
    const searchParams = useSearchParams();

    const searches = searchParams.get('form');

    // Handle form submission
    const handleSubmit = async (event) => {

        event.preventDefault();
        setLoading(true);
        try {
            // Get form data
            const formData = new FormData(event.target);
            const formDataObject = {};

            // Convert FormData to object
            formData.forEach((value, key) => {
                formDataObject[key] = value || null; // Convert empty strings to null
            });

            // Validate required fields
            if (!formDataObject.observerName?.trim()) {
                throw new Error('Oberver Name is required');
            }
            // Create payload with required structure
            const payload = {
                type: "report-shark-sighting",
                formData: formDataObject
            };

            // Make API call
            const response = await axios.post(`${API_BASE_URL}/sharkReports`, payload);

            if (response?.status === 200 || response?.status === 201) {
                message?.success('Shark sighting report submitted successfully!');
                event?.target?.reset(); // Reset form
                router?.push('/shark', undefined, { shallow: true }); // Close drawer
            } else {
                throw new Error('Failed to submit report');
            }
        } catch (error) {
            console.error('Submission error:', error);
            message.error(error.response?.data?.message || 'Failed to submit shark sighting report');
        } finally {
            setLoading(false);
        }
    };

    return (
        <>
            <Drawer
                title={
                    <div className="flex items-center justify-between">
                        <p className="form-modal-head mb25 text-xl">{"Shark Sighting Report Questionnaire"}</p>
                        <Space>
                            {/* Close Button */}
                            <Space
                                onClick={() => {
                                    router.push(`shark`, undefined, { shallow: true });
                                }}
                                className="cursor-pointer rounded-lg border-[1px] border-[#D9D9D9] w-8 h-8 flex items-center justify-center"
                            >
                                <CloseIcon className="text-success-300" />
                            </Space>
                        </Space>
                    </div>
                }
                width={500}
                className="rounded-xl"
                closeIcon={false}
                open={searches == "report-shark-sighting"}
                maskClosable={false}
                styles={{
                    body: {
                        padding: "16px 24px",
                    },
                    footer: {
                        border: 0,
                        display: "flex",
                        flexDirection: "row-reverse",
                    },
                }}
                style={{ zIndex: 99 }}
                footer={
                    [
                        (
                            <Button
                                key="submit"
                                htmlType="submit"  // Add this
                                className={`bg-sandee-blue text-white w-full h-14 text-lg font-semibold mb-2 mt-2 login-button ${loading ? " animate-pulse !bg-sandee-grey" : ""
                                    }`}
                                type="submit"
                                loading={loading}
                                form="sharkSightingForm"  // Add this to connect button to form
                            // onSubmit={(e) => handleSubmit(e)}
                            >
                                {"Submit"}
                            </Button>
                        )
                    ]
                }
            >
                <form
                    method="POST"  // Add this
                    className="space-y-8" id="sharkSightingForm" onSubmit={handleSubmit}>
                    {/* 1. Observer Information */}
                    <div>
                        <h2 className="text-xl font-semibold mb-4">1. Observer Information</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Name<span className="text-[#fb3333]">*</span></label>
                                <input
                                    type="text"
                                    name="observerName"
                                    required
                                    className="input focus:border-sandee-blue focus:border-solid focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter your name"
                                />
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Phone Number</label>
                                <input
                                    type="text"
                                    name="observerPhone"
                                    className="input focus:border-sandee-blue focus:border-solid focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter your phone number"
                                />
                            </div>
                        </div>
                    </div>

                    {/* 2. Sighting Details */}
                    <div>
                        <h2 className="text-xl font-semibold mb-4">2. Sighting Details</h2>
                        <div className="space-y-4">
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Location of the shark (e.g., beach/reef name, GPS coordinates, or landmark)</label>
                                <input
                                    type="text"
                                    name="location"
                                    className="input focus:border-sandee-blue focus:border-solid focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter location details"
                                />
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Number of sharks observed</label>
                                <input
                                    type="text"
                                    name="numberOfSharks"
                                    className="input focus:border-sandee-blue focus:border-solid focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter number of sharks"
                                />
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Estimated size of the shark(s)</label>
                                <input
                                    type="text"
                                    name="sharkSize"
                                    className="input focus:border-sandee-blue focus:border-solid focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter estimated size"
                                />
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Species of the shark (if identifiable)</label>
                                <input
                                    type="text"
                                    name="sharkSpecies"
                                    className="input focus:border-sandee-blue focus:border-solid focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter species"
                                />
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Distance of the shark(s) from the shore</label>
                                <input
                                    type="text"
                                    name="distanceFromShore"
                                    className="input focus:border-sandee-blue focus:border-solid focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter distance"
                                />
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Depth of water where the shark(s) was observed</label>
                                <input
                                    type="text"
                                    name="waterDepth"
                                    className="input focus:border-sandee-blue focus:border-solid focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter depth"
                                />
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Duration of the sighting</label>
                                <input
                                    type="text"
                                    name="sightingDuration"
                                    className="input focus:border-sandee-blue focus:border-solid focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter duration"
                                />
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Direction the shark(s) was moving</label>
                                <input
                                    type="text"
                                    name="sharkDirection"
                                    className="input focus:border-sandee-blue focus:border-solid focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter direction"
                                />
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Weather conditions at the time (e.g., sunny, overcast, raining)</label>
                                <input
                                    type="text"
                                    name="weatherConditions"
                                    className="input focus:border-sandee-blue focus:border-solid focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter weather conditions"
                                />
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Sea conditions (e.g., calm, choppy, rough, water visibility)</label>
                                <input
                                    type="text"
                                    name="seaConditions"
                                    className="input focus:border-sandee-blue focus:border-solid focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter sea conditions"
                                />
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Estimated water temperature</label>
                                <input
                                    type="text"
                                    name="waterTemperature"
                                    className="input focus:border-sandee-blue focus:border-solid focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter water temperature"
                                />
                            </div>
                        </div>
                    </div>

                    {/* 3. Behavior of the Shark(s) */}
                    <div>
                        <h2 className="text-xl font-semibold mb-4">3. Behavior of the Shark(s)</h2>
                        <div className="space-y-4">
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Shark activity (e.g., swimming calmly, hunting, circling, breaching)</label>
                                <input
                                    type="text"
                                    name="sharkActivity"
                                    className="input focus:border-sandee-blue focus:border-solid focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Describe shark activity"
                                />
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Was the shark interacting with other animals? (e.g., fish, seals, dolphins)</label>
                                <input
                                    type="text"
                                    name="sharkInteraction"
                                    className="input focus:border-sandee-blue focus:border-solid focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Describe interaction"
                                />
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Did the shark approach people or boats?</label>
                                <input
                                    type="text"
                                    name="sharkApproach"
                                    className="input focus:border-sandee-blue focus:border-solid focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Describe approach"
                                />
                            </div>
                        </div>
                    </div>

                    {/* 4. Surrounding Activities */}
                    <div>
                        <h2 className="text-xl font-semibold mb-4">4. Surrounding Activities</h2>
                        <div className="space-y-4">
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Number of people in the water near the sighting</label>
                                <input
                                    type="text"
                                    name="peopleInWater"
                                    className="input focus:border-sandee-blue focus:border-solid focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter number of people"
                                />
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Were there any fishing activities nearby?</label>
                                <input
                                    type="text"
                                    name="fishingActivities"
                                    className="input focus:border-sandee-blue focus:border-solid focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Describe fishing activities"
                                />
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Were there other marine animals in the area?</label>
                                <input
                                    type="text"
                                    name="marineAnimals"
                                    className="input focus:border-sandee-blue focus:border-solid focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Describe marine animals"
                                />
                            </div>
                        </div>
                    </div>

                    {/* 5. Additional Observations */}
                    <div>
                        <h2 className="text-xl font-semibold mb-4">5. Additional Observations</h2>
                        <div className="space-y-4">
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Did you observe any unusual bird or marine life activity before or after the sighting?</label>
                                <textarea
                                    name="unusualActivity"
                                    className="input focus:border-sandee-blue focus:border-solid focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    rows="3"
                                    placeholder="Describe unusual activity"
                                ></textarea>
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Did you notice any attractants in the area (e.g., bait, fish schools, fishing lines)?</label>
                                <textarea
                                    name="attractants"
                                    className="input focus:border-sandee-blue focus:border-solid focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    rows="3"
                                    placeholder="Describe attractants"
                                ></textarea>
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Was this area known for frequent shark sightings?</label>
                                <input
                                    type="text"
                                    name="frequentSightings"
                                    className="input focus:border-sandee-blue focus:border-solid focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    placeholder="Enter details"
                                />
                            </div>
                            <div>
                                <label className="block text-base font-semibold text-gray-700">Any additional notes or theories about the sighting?</label>
                                <textarea
                                    name="additionalNotes"
                                    className="input focus:border-sandee-blue focus:border-solid focus:border-2 focus:outline-none mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                                    rows="5"
                                    placeholder="Enter additional notes"
                                ></textarea>
                            </div>
                        </div>
                    </div>

                    {/* Submit Button */}
                    {/* <div className="flex justify-end">
                        <Button
                            type="submit"
                            className={`bg-sandee-blue text-white w-full h-14 text-lg font-semibold mb-2 mt-2 login-button ${loading ? " animate-pulse !bg-sandee-grey" : ""
                                }`}
                            loading={loading}
                        >
                            Submit
                        </Button>
                    </div> */}
                </form>
            </Drawer>
        </>
    );
};

export default SharkSightingForm;