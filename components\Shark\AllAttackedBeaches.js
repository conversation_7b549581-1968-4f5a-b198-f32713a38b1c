"use client";

import React, { useEffect, useRef, useState } from 'react'
import AttackBeacheSwiper from './AttackBeacheSwiper'
import ScrollBar from '../ScrollBar';
import { CustomGrid } from '../Custom-Display';
import { getStatesWithSharkAttacks } from '@/app/(HeaderSlim)/(Shark Section)/shark/[countrySlug]/action';
import CustomButton from '../Custom-Button';
import { LoaderIcon } from '../social-icons/icons';

const AllAttackedBeaches = ({ initialData, countryId }) => {
    const [blogData, setBlogData] = useState(initialData ?? []);

    // const [selectedCategory, setSelectedCategory] = useState([]);
    // const [selection, setSelection] = useState("DESC");
    // const [query, setQuery] = useState("");
    const [scrollLoader, setScrollLoader] = useState(false);
    const [loadoff, setLoadOff] = useState(false);
    const [pagination, setPagination] = useState({
        limit: 3,
        page: 1,
    });
    const [refresh, setRefresh] = useState(false);

    // const prevSelection = useRef(selection);
    // const prevSelectedCategory = useRef(selectedCategory);
    // const prevQuery = useRef(query);
    // console.log(blogData, "blogData", countryId, pagination)
    // Async function for delayed search
    const delayedSearch = async (
        // selectionValue,
        selectedCategoryValue
    ) => {
        const query = { ...pagination };
        // if (queryValue?.trim()?.length) {
        //     query.searchQuery = queryValue;
        // }
        if (countryId) {
            query.countryId = countryId;
        }

        // if (selectedCategoryValue?.length) {
        //     query.categoryIds = selectedCategoryValue?.join(",");
        // }
        // if (Object.keys(query)?.length > 0) {
        //     if (!!selectionValue) {
        //         query.sortBy = "createdAt";
        //         query.sortOrder = selectionValue;
        //     }
        // }
        try {
            const { data, count } = await getStatesWithSharkAttacks({ ...query });
            if (data?.length && pagination.page > 1) {
                setBlogData((prev) => [...prev, ...data]);
                setLoadOff(false);
            } else if (data?.length && pagination.page === 1) {
                setBlogData((prev) => [...data]);
                setLoadOff(false);
            } else {
                setLoadOff(true);
            }
        } catch (error) {
            console.error("Error fetching blog data:", error);
        } finally {
            setScrollLoader(false);
        }
    };
    useEffect(() => {

        const fetchData = async () => {
            if (pagination.page === 1) return
            setScrollLoader(true);
            // console.log("first")
            await delayedSearch();
        };

        fetchData();
    }, [pagination.page, refresh]);



    // useEffect(() => {
    //     if (
    //         prevSelection.current !== selection ||
    //         prevSelectedCategory.current !== selectedCategory ||
    //         prevQuery.current !== query
    //     ) {
    //         const getData = setTimeout(() => {
    //             setPagination((prev) => {
    //                 if (prev.page === 1) {
    //                     setRefresh((prevR) => !prevR);
    //                     setBlogData([]);
    //                 }
    //                 return { ...prev, page: 1 };
    //             });
    //         }, 400);

    //         // Update previous values
    //         prevSelection.current = selection;
    //         prevSelectedCategory.current = selectedCategory;
    //         prevQuery.current = query;
    //         return () => clearTimeout(getData);
    //     }
    // }, [selection, selectedCategory, query]);
    return (
        <>
            {!blogData?.length && !scrollLoader ? (
                <p className="px-2 py-5 text-error-red-600 border-error-red-400 bg-error-red-50 border-2 rounded-sandee text-center">
                    No Data Found !
                </p>
            ) : (
                <CustomGrid
                    data={blogData}
                    className="gap-2 sm:gap-[11.2px] mt-[11.2px] mb-[-20px]"
                    Component={({ data: dataProps }) => {
                        return <AttackBeacheSwiper
                            title={`${dataProps?.name} Shark Attack Beaches`}
                            dangerAttack={dataProps?.beaches} />
                    }}
                    xs={1}
                />
            )}
            <div className='flex items-center justify-center mt-8'>
                {
                    (scrollLoader) && blogData?.length ? (
                        <LoaderIcon />
                    ) : (
                        !loadoff && <div onClick={() => {

                            setPagination((prev) => ({ ...prev, page: prev?.page + 1 }));
                        }}>
                            <CustomButton type={4}>
                                See more
                            </CustomButton>
                        </div>
                    )
                }
                {/* <LoaderIcon /> */}
            </div>
            {/* <ScrollBar
                threshold={60}
                loadMoreFunction={() => {
                    setPagination((prev) => ({ ...prev, page: prev?.page + 1 }));
                }}
                isLoading={scrollLoader}
                loadoff={loadoff}
                timeout={10}
            /> */}

        </>
    )
}

export default AllAttackedBeaches


