import { SiteDataPageWise, siteMetadata } from "@/data/siteMetadata";
import React from "react";

const AllSharkAttacksPageJSONLD = ({ countryData }) => {
  const webPageSchema = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    url: "https://sandee.com/shark",
    name: SiteDataPageWise.allShark.title || `🦈 Shark Attacks – Full Reports & Statistics | Sandee`,
    description: SiteDataPageWise.allShark.description || `Explore a complete list of shark attacks, including dates, locations, species involved, and victim details. Stay updated with real-time reports and safety insights.`,
    publisher: {
      "@type": "Organization",
      name: siteMetadata?.title || "Sandee",
    },
  };

  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        name: "Home",
        item: "https://sandee.com/",
      },
      {
        "@type": "ListItem",
        position: 2,
        name: "All Shark Attacks",
        item: "https://sandee.com/allsharkattacks",
      },
      // {
      //   "@type": "ListItem",
      //   position: 3,
      //   name: `${countryData?.name}`,
      //   item: `https://sandee.com/shark/${countryData?.slug}/allsharkattacks`,
      // },
    ],
  };

  return (
    <>
      <script
        type="application/ld+json"
        id="webPageSchema"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(webPageSchema) }}
      ></script>
      <script
        type="application/ld+json"
        id="breadcrumbSchema"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbSchema) }}
      ></script>
    </>
  );
};

export default AllSharkAttacksPageJSONLD;
