import { siteMetadata } from "@/data/siteMetadata";
import { slugConverter } from "@/helper/functions";
import React from "react";

const AllSharkCountryPageJSONLD = ({ countryData }) => {
  const webPageSchema = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    url: "https://sandee.com/shark",
    // name: `🦈 Shark Attacks in the ${countryData?.name} – Full Reports & Statistics | Sandee`,
    // description: `Explore a complete list of shark attacks in the ${countryData?.name}, including dates, locations, species involved, and victim details. Stay updated with real-time reports and safety insights.`,
    name: `Shark Attacks in ${countryData?.name}`,
    description: `Explore ${countryData?.name} all shark attacks data with Sandee. Learn about hotspots, safety tips, and facts surrounding international shark encounters worldwide.`,
    publisher: {
      "@type": "Organization",
      name: siteMetadata?.title || "Sandee",
    },
  };

  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        name: "Home",
        item: "https://sandee.com/",
      },
      {
        "@type": "ListItem",
        position: 2,
        name: "All Shark Attacks",
        item: "https://sandee.com/shark",
      },
      {
        "@type": "ListItem",
        position: 3,
        name: `${countryData?.name}`,
        item: `https://sandee.com/shark/${countryData?.slug}/allsharkattacks`,
      },
    ],
  };

  return (
    <>
      <script
        type="application/ld+json"
        id="webPageSchema"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(webPageSchema) }}
      ></script>
      <script
        type="application/ld+json"
        id="breadcrumbSchema"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbSchema) }}
      ></script>
    </>
  );
};

export default AllSharkCountryPageJSONLD;
