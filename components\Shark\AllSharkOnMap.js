"use client";
import React, { useEffect, useRef, useState } from "react";
import mapboxgl from "!mapbox-gl";
import { GetSharkAttackeByID, GetSharkAttackeByLatLong, PostSharkForMap } from "@/app/(HomeHeader)/action";
import useLoggedIn from "@/helper/hook/useLoggedIn";
import SharkAttacksModal from "./SharkAttacksModal";
import SharkAttacksModalNew from "./sharkAttackModalNew";

mapboxgl.accessToken = process.env.NEXT_PUBLIC_MAPBOX_API_KEY;

const AllSharkOnMap = ({ latStr, longStr, zoom = 2 }) => {
  const mapContainer = useRef(null);
  const map = useRef(null);
  const [beachesCount, setBeachesCount] = useState([]);
  const [isLoadingCount, setIsLoadingCount] = useState(true);
  const [location, setLocation] = useState(null);
  const [loaded, setLoaded] = useState(false);
  const [open, setOpen] = useState({});
  const { token } = useLoggedIn();
  // Function to fetch beaches count
  const FetchAndSetDelayedBeachesCount = async () => {
    if (!map.current) return;

    const bounds = map.current.getBounds();
    const payload = {
      point1: bounds.getSouthWest().toArray().reverse(),
      point2: bounds.getNorthEast().toArray().reverse(),
      point3: bounds.getNorthWest().toArray().reverse(),
      point4: bounds.getSouthEast().toArray().reverse(),
    };

    try {
      const Results = await PostSharkForMap(payload, token);
      const beachLists = Results?.data?.data?.rows;
      // Refine data for rendering beaches
      const AllBeachWithRefinedDataCount = beachLists?.map((el) => ({
        ...el,
        name: el?.AllBeach?.name,
        lat: el?.AllBeach?.GeoLoc?.coordinates?.[1] ?? 0,
        long: el?.AllBeach?.GeoLoc?.coordinates?.[0] ?? 0,
        locationAddress: {
          link: `/map/${el?.AllBeach?.nameSlug}/@${el?.AllBeach?.GeoLoc?.coordinates?.[1] ?? 0},${el?.AllBeach?.GeoLoc?.coordinates?.[0] ?? 0
            }`,
        },
      }));
      setBeachesCount(AllBeachWithRefinedDataCount);
      setIsLoadingCount(false);
    } catch (error) {
      console.error("Error fetching beach data:", error);
    }
  };
  // Function to add beaches and clusters to the map
  const SetBeaches = () => {
    if (!mapContainer?.current || !beachesCount?.length || !loaded) return;

    if (map.current.getSource("beaches")) {
      map.current.getSource("beaches").setData({
        type: "FeatureCollection",
        features: beachesCount.map((beach) => ({
          type: "Feature",
          geometry: {
            type: "Point",
            coordinates: [beach.long, beach.lat],
          },
          properties: beach,
        })),
      });
    } else {
      map.current.addSource("beaches", {
        type: "geojson",
        data: {
          type: "FeatureCollection",
          features: beachesCount.map((beach) => ({
            type: "Feature",
            geometry: {
              type: "Point",
              coordinates: [beach.long, beach.lat],
            },
            properties: beach,
          })),
        },
        cluster: true,
        clusterMaxZoom: 14,
        clusterRadius: 50,
      });
      // Add cluster layer
      map.current.addLayer({
        id: "clusters",
        type: "circle",
        source: "beaches",
        filter: ["has", "point_count"],
        paint: {
          "circle-color": [
            "step",
            ["get", "point_count"],
            "#fa1b1b",
            100,
            "#fa1b1b",
            750,
            "#fa1b1b",
          ],
          "circle-radius": [
            "step",
            ["get", "point_count"],
            25,
            100,
            25,
            750,
            30,
          ],
        },
      });

      // Add cluster count layer
      map.current.addLayer({
        id: "cluster-count",
        type: "symbol",
        source: "beaches",
        filter: ["has", "point_count"],
        layout: {
          "text-field": ["get", "point_count_abbreviated"],
          "text-font": ["DIN Offc Pro Medium", "Arial Unicode MS Bold"],
          "text-size": 16,
        },
        paint: {
          "text-color": "#fff",
        }
      });

      // Load and add beach icons
      map.current.loadImage("/shark1.png", (error, image) => {
        if (error) throw error;
        map.current.addImage("beach-icon", image);

        // Add unclustered points layer  --- shark attack
        map.current.addLayer({
          id: "unclustered-point",
          type: "symbol",
          source: "beaches",
          filter: ["!", ["has", "point_count"]],
          layout: {
            "icon-image": "beach-icon",
            "icon-size": 0.4,
            "icon-allow-overlap": true,
            "text-field": ["get", "name"],
            "text-size": 18,
            "text-offset": [1, 0],
            "text-anchor": "left",
            "text-font": ["Open Sans Bold", "Arial Unicode MS Bold"]
          },
          paint: {
            "text-color": "red", // Set text color FF6B00
            "text-halo-color": "#fff", // Optional: Add a halo around the text for better readability
            "text-halo-width": 1, // Optional: Set halo width
          },
        });
      });

      if (latStr && longStr) {
        new mapboxgl.Marker({ color: "red" })
          .setLngLat([longStr, latStr])
          .addTo(map.current);
        // map.current.flyTo({ center: [longStr, latStr], zoom: 10 });
      }

      // Handle cluster clicks
      map.current.on("click", "clusters", (e) => {
        const features = map.current.queryRenderedFeatures(e.point, {
          layers: ["clusters"],
        });
        const clusterId = features[0].properties.cluster_id;
        map.current
          .getSource("beaches")
          .getClusterExpansionZoom(clusterId, (err, zoom) => {
            if (err) return;
            map.current.easeTo({
              center: features[0].geometry.coordinates,
              zoom: zoom,
            });
          });
      });

      // Handle unclustered-point clicks
      map.current.on("click", "unclustered-point", async (e) => {
        const feature = e.features[0];
        const address = JSON.parse(feature.properties.locationAddress);

        try {
          // const Results = await GetSharkAttackeByID(feature?.properties?.id, token);
          const el = e.features[0].properties;
          const coordinatesData = JSON.parse(el?.GeoLoc);
          const AllBeach = JSON.parse(el?.AllBeach);
          const cityName = JSON.parse(el?.city);
          const state = JSON.parse(el?.state);
          const country = JSON.parse(el?.country);
          const Results = await GetSharkAttackeByLatLong({
            page: 1,
            limit: 5,
            // coordinates: `${coordinatesData?.coordinates?.[1] ?? 0},${coordinatesData?.coordinates?.[0] ?? 0}`
          }, `${coordinatesData?.coordinates?.[1] ?? 0},${coordinatesData?.coordinates?.[0] ?? 0}`);
          let newLink = "";
          const res = Results?.data
          if (Results?.status === "success") {
            if (res?.city?.state?.country?.slug) {
              newLink = `/${res?.city?.state?.country?.slug}/${res?.city?.state?.slug}/${res?.city?.slug}/${res?.AllBeach?.nameSlug}`; //
              res.link = `/${res?.city?.state?.country?.slug}/${res?.city?.state?.slug}/${res?.city?.slug}/${res?.AllBeach?.nameSlug}`; //
              res.location = `${res?.city?.name}, ${res?.city?.state?.country?.code}`; //
            } else {
              newLink = `/${res?.country?.slug}/${res?.state?.slug}/${res?.city?.slug}/${res?.AllBeach?.nameSlug}`; //
              res.link = `/${res?.country?.slug}/${res?.state?.slug}/${res?.city?.slug}/${res?.AllBeach?.nameSlug}`; //
              res.location = `${res?.city?.name}, ${res?.country?.code}`; //
            }
            setOpen({
              ...res, ...res?.AllBeach, coordinatesData: coordinatesData,
              newLink: country?.slug && state?.slug && cityName?.slug && AllBeach?.nameSlug ? `/${country?.slug}/${state?.slug}/${cityName?.slug}/${AllBeach?.nameSlug}` : null,
              beachName: AllBeach?.name,
              cityName: cityName?.name,
              stateName: state?.name,
              countryName: country?.name
            });
          }

          // setOpen({
          //   name: "Kukio Beach Park",
          //   date: "2023-01-01",
          //   fatal: true,
          //   newLink: "#",
          //   sharkSpecy: {
          //     name: "Tiger Shark",
          //   },
          //   sharkSize: "5.5 feet (1.7 meters)",
          //   victimCount: 6,
          //   summary: "Ethan Davis, 23, sustained lacerations to his legs while spearfishing at Spanish Cay.",
          // })
        } catch (error) {
          console.log(error)
        }

        // if (address?.link) {
        //   window.open(address.link, "_blank");
        // }
      });

      // Cursor handling for clusters and unclustered points
      map.current.on("mouseenter", ["clusters", "unclustered-point"], () => {
        map.current.getCanvas().style.cursor = "pointer";
      });
      map.current.on("mouseleave", ["clusters", "unclustered-point"], () => {
        map.current.getCanvas().style.cursor = "";
      });

      map.current.addControl(new mapboxgl.ScaleControl());
    }
  };

  // Fetch beaches count when map bounds change
  const handleMoveEnd = () => {
    setIsLoadingCount(true);
    FetchAndSetDelayedBeachesCount();
  };

  // Set the initial location
  const getLocation = () => {
    if (latStr && longStr) {
      setLocation([longStr, latStr]);
    } else {
      setLocation([-50.4117325, 34.020479]); // los angeles coordinates
    }
  };

  // Initialize map when location is set
  useEffect(() => {
    if (!location) return;
    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: "mapbox://styles/mapbox/streets-v12",
      center: location,
      zoom: zoom,
      minZoom: 2,
      maxBounds: [
        [-180, -85],
        [180, 85],
      ],
      projection: { name: "mercator" },
    });
    map.current?.addControl(new mapboxgl.NavigationControl(), "bottom-right");
    // map.current.dragRotate.disable();
    // map.current.touchZoomRotate.disableRotation();
    map.current.on("load", () => {
      setLoaded(true);
      handleMoveEnd();
    });

    map.current.on("moveend", handleMoveEnd);

    return () => {
      map.current.off("moveend", handleMoveEnd);
      map.current.remove();
    };
  }, [location]);

  // Load beaches after data is fetched
  useEffect(() => {
    SetBeaches();
  }, [beachesCount, loaded]);

  // Fetch beach data after the map is refreshed
  useEffect(() => {
    getLocation();
  }, []);

  return (
    <div className="relative rounded-[22px] overflow-hidden shark-map-container my-2">
      <div
        ref={mapContainer}
        className="md:map-container w-full lg:h-[550px] h-[470px] p-0 rounded-[22px]"
      />
      {isLoadingCount ? (
        <div className="absolute md:top-5 right-4 bg-sandee-blue py-1 rounded-lg md:px-4 px-2 text-white md:text-base text-[12px] top-4">
          <span className="font-semibold">Loading Beaches...</span>
        </div>
      ) : null}
      <SharkAttacksModalNew open={open} setOpen={setOpen} />
    </div>
  );
};

export default AllSharkOnMap;
