"use client";
import React from 'react'
import NameTitle from '../Common/NameTitle'
import CustomSwiper from '../Custom-Swiper';
import CustomToolTip from '../Common/CustomToolTip';
import CopyRight from '../Common/CopyRight';
import Link from 'next/link';
import Image from 'next/image';
import {
    altText,
    blurDataURL,
    defaultImage,
    FinalImageGenerator,
    generateBreakpoints,
} from "@/helper/functions";
import defaultSharkBeachImage from "../../public/default_shark_beach.jpg";
import CustomeImage from '../Common/CustomeImage';


const AttackBeacheSwiper = ({ dangerAttack, title }) => {
    return (
        <>
            <NameTitle
                className="mt-0"
                name={title ?? "Most Dangerous Shark Attack Beaches"}
                // description={"There have been 109 shark attacks in Florida since 1927.  There have been 36 fatal shark attacks and 36 nonfatal shark attacks."}
                type={5}
            />
            <CustomSwiper
                data={dangerAttack}
                Component={({ data: dataProps }) => {
                    // /united-states/florida/new-smyrna-beach
                    if (dataProps?.city?.state?.country?.slug) {
                        dataProps.link = `/${dataProps?.city?.state?.country?.slug}/${dataProps?.city?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.nameSlug}`; //
                        // dataProps.link = `/${dataProps?.city?.state?.country?.slug}/${dataProps?.city?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.slug}`; //
                        dataProps.location = `${dataProps?.city?.name}, ${dataProps?.city?.state?.country?.code}`; //
                    } else {
                        dataProps.link = `/${dataProps?.country?.slug}/${dataProps?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.nameSlug}`; //
                        // dataProps.link = `/${dataProps?.country?.slug}/${dataProps?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.slug}`; //
                        dataProps.location = `${dataProps?.city?.name}, ${dataProps?.country?.code}`; //
                    }
                    // dataProps.imageSrc = FinalImageGenerator(dataProps?.images?.[0]);
                    dataProps.imageSrc = FinalImageGenerator(dataProps?.images?.[0], 1600, 1, defaultSharkBeachImage);

                    return BeachCard({
                        data: { ...dataProps, ...dataProps },
                    });
                }}
                settingsExtra={{
                    breakpoints: {
                        ...generateBreakpoints(300, 768, 50, 180),
                        ...generateBreakpoints(768, 1300, 50, 280),
                        ...generateBreakpoints(1300, 2400, 50, 330),
                    },
                    spaceBetween: 22,
                    // breakpoints: {
                    //   300: { slidesPerView: 1.2 },
                    //   800: { slidesPerView: 3 },
                    //   1200: { slidesPerView: 5.5 },
                    //   1400: { slidesPerView: 6.5 },
                    // },
                }}
                cardHeight={260}
                slideClassName={`max-w-[180px] lg:max-w-[240px] xl:max-w-[230px] 2xl:max-w-[250px] 3xl:max-w-[300px] mr-8`}
                className="mb-0 mt-[0px] block md:block"
                id={"me_1"}
            // extra={
            //   <div className="relative h-[300px]  rounded-xl flex items-center justify-center ">
            //     <Link href={`/list/${listSlug}`}>
            //   <CustomButton type={4}>
            //     View More
            //   </CustomButton>
            //     </Link>
            //   </div>
            // }
            />
        </>
    )
}

export default AttackBeacheSwiper

const BeachCard = ({
    data,
    className = "md:aspect-[180/140] aspect-square",
}) => {
    data.lat = data?.GeoLoc?.coordinates?.length
        ? data?.GeoLoc?.coordinates?.[1]
        : 0;
    data.lon = data?.GeoLoc?.coordinates?.length
        ? data?.GeoLoc?.coordinates?.[0]
        : 0;
    // Check if restrooms, showers, and lifeguard are all true
    // const hasAllCategories =
    //   data?.beachCategory?.restRooms &&
    //   data?.beachCategory?.showers &&
    //   data?.beachCategory?.lifeguard;

    // const amenitiesTag = BeachCategoryTags?.find((tag) => {
    //   if (tag?.value === "family" && hasAllCategories) {
    //     // Prioritize the Family tag only when all categories are true
    //     return true;
    //   }
    //   // For other tags, check if they exist in beachCategory and are true
    //   return data?.beachCategory?.[tag?.value];
    // });

    // const selectedTag = amenitiesTag || {
    //   id: 0,
    //   label: "Family",
    //   value: "family",
    //   icon: <FamilyIcon className="w-[18px] h-[18px]" />,
    // };
    return (
        <Link
            href={`${data?.link}`}
            className=""
        // target={`${data?.target}` ?? '_self'}
        >
            <div className="flex flex-col gap-y-[10px]">
                <div
                    className={`relative overflow-hidden aspect-square rounded-[25px] sm:rounded-sandee ${className}`}
                >
                    {/* {data?.imageSrc && data?.imageSrc !== defaultImage ? ( */}
                    <div
                        className={`relative overflow-hidden aspect-square rounded-[25px] sm:rounded-sandee ${className}`}
                    >
                        <CustomeImage
                            className="w-full object-cover"
                            src={data?.imageSrc}
                            // alt={`${data?.location
                            //     ? `Sandee ${data?.name} Photo`
                            //     : `${data?.name}`
                            //     }`}
                            alt={altText(data)}
                            fill
                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                            blurDataURL={blurDataURL(300, 200)}
                            placeholder="blur"
                        />
                        {/* <span className="absolute top-2 right-3 bg-[#FF1616]  py-1 px-3 rounded-full text-sm text-white">
                  Fatal Attack
                </span> */}
                        {data?.attacks && <span className="absolute top-2 right-3 bg-[#FF1616]  py-1 px-3 rounded-full text-sm text-white">
                            {data?.attacks} Attacks
                        </span>}
                        <CopyRight
                            copyRightsData={data?.images}
                            // background={true}
                            // styleExtra={{ bottom: "1px", left: "3px" }}
                            classNameExtra={"bottom-0"}
                        />
                    </div>
                    {/* ) : (
                        <iframe
                            className=" bg-gray-200 w-full h-full rounded-sandee min-h-[280px] pointer-events-none"
                            loading="lazy"
                            src={`https://maps.google.com/?q=${data?.lat},${data?.lon}&ie=UTF8&iwloc=&output=embed&t=k&disableDefaultUI=true`}
                        ></iframe>
                    )} */}
                </div>
                <div className="flex flex-col gap-y-1 ms-[5px]">
                    {/* <div className="flex gap-x-2 items-center text-sm">
            {selectedTag?.icon}
            <span className="font-medium">{selectedTag?.label}</span>
              <span>|</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width={18}
                height={18}
                viewBox="0 0 16 16"
                fill="none"
              >
                <path
                  d="M7.99956 11.5135L10.7662 13.1868C11.2729 13.4935 11.8929 13.0402 11.7596 12.4668L11.0262 9.32017L13.4729 7.20017C13.9196 6.8135 13.6796 6.08017 13.0929 6.0335L9.87289 5.76017L8.61289 2.78684C8.38623 2.24684 7.61289 2.24684 7.38623 2.78684L6.12623 5.7535L2.90623 6.02684C2.31956 6.0735 2.07956 6.80684 2.52623 7.1935L4.97289 9.3135L4.23956 12.4602C4.10623 13.0335 4.72623 13.4868 5.23289 13.1802L7.99956 11.5135Z"
                  fill="#1F1F1F"
                />
              </svg>
              <span className="font-medium">
                {((data?.rating100 ?? 80) / 20)?.toFixed(2)}
              </span>
            </div> */}
                    <CustomToolTip title={data?.name}>
                        <h3 className="text-lg font-bold line-clamp-1 mb-0 leading-[24px]">
                            {data?.name}
                        </h3>
                    </CustomToolTip>
                    <span className="uppercase opacity-[0.8] text-[#656565] line-clamp-2 md:text-base text-sm font-semibold -mt-[5px]">
                        {data?.location}
                    </span>
                </div>
            </div>
        </Link>
    );
};