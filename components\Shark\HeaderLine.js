"use client"
import Link from 'next/link'
import React, { useState } from 'react'
import CustomButton from '../Custom-Button'
import { FullViewIcon, ShareIcon, SharkIconNew } from '../social-icons/icons'
import ShareModel from "@/components/Common/shareModal";


const HeaderLine = ({ title = "", fullViewLink = "/maps/shark-attacks", link, isMobile = false, isSharkForm = false }) => {
    const [isOpen, setIsOpen] = useState(false);
    return (
        <>
            <div className={`${isMobile ? "hidden" : ""} md:flex justify-between items-center`}>
                <h1 className="text-sandee-32 font-bold text-center md:text-start">
                    {title}
                </h1>
                <div className="flex gap-x-2 justify-between md:justify-normal items-center">
                    {isSharkForm && <Link href={`?form=report-shark-sighting`} className=" !text-nowrap">
                        <CustomButton type={4}>
                            <SharkIconNew className=" me-2 fill-sandee-orange group-hover:fill-white  h-4 w-4" />
                            Sighting Report
                        </CustomButton>
                    </Link>}
                    <Link href={fullViewLink} className=" !text-nowrap">
                        <CustomButton type={4}>
                            <FullViewIcon className=" me-2 fill-sandee-orange group-hover:fill-white  h-4 w-4" />
                            Full View
                        </CustomButton>
                    </Link>
                    <div
                        onClick={() => setIsOpen(pr => !pr)}
                        className=" !text-nowrap">
                        <CustomButton type={4}>
                            <ShareIcon className=" me-2 stroke-sandee-orange group-hover:stroke-white  h-[18px] w-[18px] " />
                            Share
                        </CustomButton>
                    </div>
                </div>
            </div>
            <ShareModel
                modalOpen={isOpen}
                setModalOpen={setIsOpen}
                link={link}
            />
        </>
    )
}

export default HeaderLine