"use client";
import React, { memo, useState } from "react";
import { CustomContainer, CustomGrid } from "../Custom-Display";
import CustomSwiper from "../Custom-Swiper";
import NameTitle from "../Common/NameTitle";
import {
  altText,
  blurDataURL,
  defaultImage,
  FinalImageGenerator,
  generateBreakpoints,
} from "@/helper/functions";
import Image from "next/image";
import CopyRight from "../Common/CopyRight";
import CustomToolTip from "../Common/CustomToolTip";
import Link from "next/link";
import moment from "moment";
import SharkAttacksModal from "./SharkAttacksModal";
import RecentSharkCard from "./recentSharkCard";
import CustomButton from "../Custom-Button";
import { CategoryIcon, ExploreMoreArrow } from "../social-icons/icons";
import defaultSharkBeachImage from "../../public/default_shark_beach.jpg";
import { GetSharkAttackeByLatLong } from "@/app/(HomeHeader)/action";
import RecentNewCard from "./recentNewCard";
import CustomeImage from "../Common/CustomeImage";
// import SharkAttacksModalNew from "./sharkAttackModalNew";

const RecentSharkAttacks = ({ recentAttack,
  description = "Recent Shark Attack Beaches",
  buttonType = { type: 4, color: "fill-sandee-orange" },
  ExtraButton,
  title = "Most Recent Shark Attacks" }) => {
  const [open, setOpen] = useState({});
  const onOpen = (data) => setOpen({ ...data });
  return (
    <>
      <CustomContainer>
        {/* <div className="flex items-center justify-between"> */}
        <NameTitle
          className="mt-7"
          name={title}
          description={description}
          type={2}
          extraButton={<div className=" hidden md:flex justify-end items-start w-3/12">
            {ExtraButton}
          </div>
          }
        />
        {/* <div className=" flex  justify-center items-center mb-5">
            <Link href={`/list/`} className=" ">

              <CustomButton type={4}>
                View All
                <ExploreMoreArrow className=" ml-2 fill-sandee-blue group-hover:fill-white  h-4 w-4" />
              </CustomButton>

            </Link>
          </div>
        </div> */}
        <CustomSwiper
          data={recentAttack}
          Component={async ({ data: dataProps }) => {
            // console.log(dataProps, "kdhfjdhfjdfjgd", dataProps?.AllBeach?.nameSlug)
            let newLink = "";
            if (dataProps?.city?.state?.country?.slug) {
              newLink = dataProps?.city?.state?.slug && dataProps?.city?.slug && dataProps?.AllBeach?.nameSlug ? `/${dataProps?.city?.state?.country?.slug}/${dataProps?.city?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.AllBeach?.nameSlug}` : null; //
              dataProps.link = `/${dataProps?.city?.state?.country?.slug}/${dataProps?.city?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.AllBeach?.nameSlug}`; //
              dataProps.location = `${dataProps?.city?.name}, ${dataProps?.city?.state?.country?.code}`; //
            } else {
              newLink = dataProps?.country?.slug && dataProps?.state?.slug && dataProps?.city?.slug && dataProps?.AllBeach?.nameSlug ? `/${dataProps?.country?.slug}/${dataProps?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.AllBeach?.nameSlug}` : null; //
              dataProps.link = `/${dataProps?.country?.slug}/${dataProps?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.AllBeach?.nameSlug}`; //
              dataProps.location = `${dataProps?.city?.name}, ${dataProps?.country?.code}`; //
            }
            dataProps.imageSrc = FinalImageGenerator(
              dataProps?.AllBeach?.images?.[0], 1600, 1, defaultSharkBeachImage
            );
            // return <RecentSharkCard data={{ ...dataProps, ...dataProps?.AllBeach }} />
            return <RecentNewCard data={
              {
                ...dataProps, ...dataProps?.AllBeach,
                coordinatesData: dataProps?.GeoLoc,
                beachName: dataProps?.AllBeach?.name,
                cityName: dataProps?.city?.name,
                stateName: dataProps?.state?.name,
                countryName: dataProps?.country?.name
              }
            } />
            // BeachCard({
            //   data: { ...dataProps, ...dataProps?.AllBeach },
            // onOpen: onOpen
            // onOpen: (data = {}) => onOpen({
            //   // ...Results?.data,
            //   ...dataProps?.AllBeach,
            //   newLink: newLink,
            //   ...data,
            //   coordinatesData: dataProps?.GeoLoc,
            //   beachName: dataProps?.AllBeach?.name,
            //   cityName: dataProps?.city?.name,
            //   stateName: dataProps?.state?.name,
            //   countryName: dataProps?.country?.name
            // }),
            // });
          }}
          settingsExtra={{
            breakpoints: {
              ...generateBreakpoints(300, 768, 50, 180),
              ...generateBreakpoints(768, 1300, 50, 280),
              ...generateBreakpoints(1300, 2400, 50, 330),
            },
            spaceBetween: 22,
            // breakpoints: {
            //   300: { slidesPerView: 1.2 },
            //   800: { slidesPerView: 3 },
            //   1200: { slidesPerView: 5.5 },
            //   1400: { slidesPerView: 6.5 },
            // },
          }}
          cardHeight={260}
          slideClassName={`max-w-[180px] lg:max-w-[240px] xl:max-w-[230px]  2xl:max-w-[250px] 3xl:max-w-[300px] mr-8`}
          className=" mt-[11.2px] hidden md:block"
          id={"me_1"}
        // extra={
        //   <div className="relative h-[300px]  rounded-xl flex items-center justify-center ">
        //     <Link href={`/list/${listSlug}`}>
        //   <CustomButton type={4}>
        //     View More
        //   </CustomButton>
        //     </Link>
        //   </div>
        // }
        />
        < CustomGrid
          className="!my-5 gap-4 sm:gap-8 md:hidden "
          Component={({ data: dataProps }) => {
            let newLink = "";
            let newLocation = ""; // Correct variable name (capital "N").
            if (dataProps?.city?.state?.country?.slug) {
              newLink = dataProps?.city?.state?.slug && dataProps?.city?.slug && dataProps?.AllBeach?.nameSlug ? `/${dataProps?.city?.state?.country?.slug}/${dataProps?.city?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.AllBeach?.nameSlug}` : null; //
              dataProps.link = `/${dataProps?.city?.state?.country?.slug}/${dataProps?.city?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.AllBeach?.nameSlug}`; //
              dataProps.location = dataProps?.city?.name && dataProps?.city?.state?.country?.code ? `${dataProps?.city?.name}, ${dataProps?.city?.state?.country?.code}` : ""; //
              newLocation = (dataProps?.city?.name && dataProps?.city?.state?.country?.code)
                ? `${dataProps?.city?.name}, ${dataProps?.city?.state?.country?.code}`
                : "";
              dataProps["newLocation"] = (dataProps?.city?.name && dataProps?.city?.state?.country?.code)
                ? `${dataProps?.city?.name}, ${dataProps?.city?.state?.country?.code}`
                : "";
            } else {
              newLink = dataProps?.country?.slug && dataProps?.state?.slug && dataProps?.city?.slug && dataProps?.AllBeach?.nameSlug ? `/${dataProps?.country?.slug}/${dataProps?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.AllBeach?.nameSlug}` : null; //
              dataProps.link = `/${dataProps?.country?.slug}/${dataProps?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.AllBeach?.nameSlug}`; //
              newLocation = (dataProps?.city?.name && dataProps?.country?.code)
                ? `${dataProps?.city?.name}, ${dataProps?.country?.code}`
                : "";
              // dataProps["newLink"] = (dataProps?.city?.name && dataProps?.country?.code)
              //   ? `${dataProps?.city?.name}, ${dataProps?.country?.code}`
              //   : "";
            }
            dataProps.imageSrc = FinalImageGenerator(
              dataProps?.AllBeach?.images?.[0], 1600, 1, defaultSharkBeachImage
            );
            return <RecentNewCard data={
              {
                ...dataProps, ...dataProps?.AllBeach,
                coordinatesData: dataProps?.GeoLoc,
                beachName: dataProps?.AllBeach?.name,
                cityName: dataProps?.city?.name,
                stateName: dataProps?.state?.name,
                countryName: dataProps?.country?.name,
                newLink: newLink,
              }
            } />

            // return BeachCard({
            //   data: { ...dataProps, ...dataProps?.AllBeach, newLocation: newLocation },
            //   onOpen: (data = {}) => onOpen({ ...dataProps, ...dataProps?.AllBeach, ...data, newLink: newLink, newLocation: newLocation }),
            //   // onOpen: onOpen
            // });
          }}
          data={recentAttack}
          xs={2}
          sm={3}
          md={3}
          lg={4}
          xl={5}
          xxl={5}
          xxxl={5}
        />
        {ExtraButton && <div className=" md:hidden mb-9">
          {ExtraButton ? (
            ExtraButton
          ) : (
            <Link
              href={`/list/`}
              className="custom-hover-slide-button group font-bold "
            >
              <span className="custom-hover-slide group-hover:h-full"></span>
              <span className="custom-hover-slide-text group-hover:text-white font-bold">
                <CategoryIcon className=" me-2 fill-sandee-orange group-hover:fill-white h-4 w-4" />
                View All
              </span>
            </Link>
            // <CustomButton type={4}>
            //   <BeachIcon className=" me-2 fill-sandee-blue group-hover:fill-white  h-4 w-4" />
            //   View All
            // </CustomButton>
          )}
        </div>}
        {/* <div className=" flex md:hidden justify-center w-full items-center mb-5">
          <Link href={`/list/`} className="custom-hover-slide-button group font-bold  ">
            {ExtraButton ? (
              ExtraButton
            ) : (
              <CustomButton type={4}>
                View All
                <ExploreMoreArrow className=" ml-2 fill-sandee-blue group-hover:fill-white  h-4 w-4" />
              </CustomButton>
            )}
          </Link>
        </div> */}
        {/* <SharkAttacksModal open={open} setOpen={setOpen} /> */}
        {/* <SharkAttacksModalNew open={open} setOpen={setOpen} /> */}
      </CustomContainer>
    </>
  );
};

export default memo(RecentSharkAttacks);

const BeachCard = ({
  data,
  onOpen,
  className = "md:aspect-[180/140] aspect-square",
}) => {
  // data.lat = data?.GeoLoc?.coordinates?.length
  //   ? data?.GeoLoc?.coordinates?.[1]
  //   : 0;
  // data.lon = data?.GeoLoc?.coordinates?.length
  //   ? data?.GeoLoc?.coordinates?.[0]
  //   : 0;
  const newLocation = data?.city?.state?.country?.slug &&
    (data?.city?.name && data?.city?.state?.country?.code)
    ? `${data?.city?.name}, ${data?.city?.state?.country?.code}`
    : data?.city?.name && data?.country?.code ? `${data?.city?.name}, ${data?.country?.code}`
      : "";

  return (
    <div
      // href={`${data?.link}`}
      className="cursor-pointer"
      onClick={() => {
        // const Results = await GetSharkAttackeByLatLong({
        //   page: 1,
        //   limit: 5,
        //   // coordinates: `${coordinatesData?.coordinates?.[1] ?? 0},${coordinatesData?.coordinates?.[0] ?? 0}`
        // }, `${data?.GeoLoc?.coordinates?.[1] ?? 0},${data?.GeoLoc?.coordinates?.[0] ?? 0}`);
        // onOpen(data);
        // console.log("first")
      }}
    // target={`${data?.target}` ?? '_self'}
    >
      <div className="flex flex-col gap-y-[10px]">
        <div
          className={`relative overflow-hidden aspect-square rounded-[25px] sm:rounded-sandee ${className}`}
        >
          {/* {data?.imageSrc && data?.imageSrc !== defaultImage ? ( */}
          <div
            className={`relative overflow-hidden aspect-square rounded-[25px] sm:rounded-sandee ${className}`}
          >
            {/* {data?.fatal && <div
                className="top-2 flex absolute z-50 right-3 rounded-xl bg-[#FF1616] p-1 px-2 text-white text-[14px] md:text-xs"
              >Fatal Attack</div>} */}
            <CustomeImage
              className="w-full object-cover"
              src={data?.imageSrc ?? defaultSharkBeachImage}
              defaultImagesrc={defaultSharkBeachImage}
              // alt={`${data?.location
              //   ? `Sandee ${data?.name ?? data?.otherLocation ?? "Unknown"} Photo`
              //   : `${data?.name ?? data?.otherLocation ?? "Unknown"}`
              //   }`}
              alt={altText(data)}
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              blurDataURL={blurDataURL(300, 200)}
              placeholder="blur"
            />
            {data?.fatal && <span className="absolute top-2 right-3 bg-[#FF1616]  py-1 px-3 rounded-full text-sm text-white">
              Fatal Attack
            </span>}
            <CopyRight
              copyRightsData={data?.images}
              // background={true}
              // styleExtra={{ bottom: "1px", left: "3px" }}
              classNameExtra={"bottom-0"}
            />
          </div>
          {/* )  */}
          {/*}   : (
            <iframe
              className=" bg-gray-200 w-full h-full rounded-sandee min-h-[280px] pointer-events-none"
              loading="lazy"
              src={`https://maps.google.com/?q=${data?.lat},${data?.lon}&ie=UTF8&iwloc=&output=embed&t=k&disableDefaultUI=true`}
            ></iframe>
          )} */}
        </div>
        <div className="flex flex-col gap-y-1 ms-[5px]">
          <div className="flex gap-x-1 items-center text-sm">
            {data?.date && <><span className="font-medium">
              {moment(data?.date?.split("T")?.[0]).format("M/D/YY") ??
                ""}
            </span>
              <span>|</span></>}
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width={18}
              height={18}
              viewBox="0 0 16 16"
              fill="none"
            >
              <path
                d="M7.99956 11.5135L10.7662 13.1868C11.2729 13.4935 11.8929 13.0402 11.7596 12.4668L11.0262 9.32017L13.4729 7.20017C13.9196 6.8135 13.6796 6.08017 13.0929 6.0335L9.87289 5.76017L8.61289 2.78684C8.38623 2.24684 7.61289 2.24684 7.38623 2.78684L6.12623 5.7535L2.90623 6.02684C2.31956 6.0735 2.07956 6.80684 2.52623 7.1935L4.97289 9.3135L4.23956 12.4602C4.10623 13.0335 4.72623 13.4868 5.23289 13.1802L7.99956 11.5135Z"
                fill="#1F1F1F"
              />
            </svg>
            <span className="font-medium">
              {((data?.rating100 ?? 80) / 20)?.toFixed(2)}
            </span>
          </div>
          <CustomToolTip title={data?.name ?? data?.otherLocation ?? "Unknown"}>
            <h3 className="text-lg font-bold line-clamp-1 mb-0 leading-[24px]">
              {data?.name ?? data?.otherLocation ?? "Unknown"}
            </h3>
          </CustomToolTip>
          <span className="opacity-[0.8] uppercase text-[#656565] line-clamp-2 md:text-base text-sm font-semibold -mt-[5px]">
            {newLocation ?? ''}
          </span>
        </div>
      </div>
    </div>
  );
};
