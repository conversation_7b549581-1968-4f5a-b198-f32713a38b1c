"use client"
import { Modal } from 'antd';
import React, { useEffect, useState } from 'react';
import CustomButton from '../Custom-Button';
import { CancelIcon } from '../social-icons/icons';
import moment from 'moment';
import Link from 'next/link';
import { EditorContent, isHTML, isMobileView } from '@/helper/functions';
import "../BlogPage/blog.css"


const SharkAttacksModal = ({ open, setOpen }) => {
    const [isClient, setIsClient] = useState(false);
    const isMobileViews = isMobileView()

    useEffect(() => {
        setIsClient(true);
    }, []);

    const toggle = () => {
        setOpen({});
    };

    if (!isClient) return null; // Don't render on the server

    return (
        <>
            <Modal
                role="dialog"
                aria-modal="true"
                open={open && Object?.keys(open)?.length > 0}
                onCancel={toggle}
                footer={null}
                closable={false}
                okButtonProps={{ style: { display: "none" } }}
                style={{ padding: 0 }}
                width={350}
                centered
                wrapClassName=" !p-0"
                styles={{
                    body: {
                        padding: "0px !important",
                    },
                }}
                cancelButtonProps={{ style: { display: "none" } }}
            >
                <div className="bg-white rounded-xl h-full pb-6 pt-2">
                    <div className="flex justify-end me-2  cursor-pointer" onClick={toggle}>
                        <CancelIcon className="w-7 h-7 opacity-50" />
                    </div>
                    <div className='px-6'>
                        <div className='text-center pb-4'>
                            <p className="text-sandee-24 ">{open?.name ?? "Unknown"}</p>
                            <p className='text-sandee-base my-1 mb-2'>
                                {open?.date && <>  <span className="font-bold text-sandee-orange">{moment(open?.date?.split("T")?.[0])?.format("MM/DD/YY") ??
                                    "Nov/09/2023"}
                                    {/* {moment(open?.date)?.format("DD/MM/YY") ??
                                    " 09 Nov, 2023"} */}
                                </span> |</>}
                                <span className="font-bold text-sandee-orange">{open?.fatal ? "Fatal" : "Non-Fatal"}</span>
                            </p>
                            {open?.newLink && <Link
                                href={`${open?.newLink}`}
                                className=" text-sandee-blue active:text-white active:bg-sandee-blue border border-sandee-blue py-[6px] px-2 transition ease-in-out duration-300 text-sandee-sm rounded-[35px] active:scale-[0.7] hover:scale-110 text-xs"
                                type={7}>
                                View Beach
                            </Link>}
                        </div>
                        <p className='font-bold my-1 text-sandee-base'>Shark Species: <span className="font-medium">{open?.sharkSpecy ? open?.sharkSpecy?.name : "Unknown"}</span></p>
                        <p className='font-bold my-1 text-sandee-base'>Shark Size: <span className="font-medium">{(open?.sharkSize && open?.sharkSize !== "Unknown") ? open?.sharkSize : "Unknown"}</span></p>

                        {/* <p className='font-bold text-sandee-base'>Shark Size: <span className="font-medium">-</span></p> */}

                        <p className='font-bold my-1 text-sandee-base'>Number of Victims: <span className="font-medium">{open?.victimCount ?? "0"}</span></p>
                        <div className='bg-[#F7F5F0] rounded-md p-3 mt-2 mb-1'>
                            {open?.summary && isHTML(open?.summary) ? <div className='font-bold'>Description:
                                <EditorContent className="ql-container font-medium ql-editor blog-editor" value={open?.summary} />

                                {/* <div className='ql-container font-medium ql-editor blog-editor' dangerouslySetInnerHTML={{ __html: open?.summary }} /> */}
                            </div>
                                :
                                <p className='font-bold'>Description: <span className="font-medium">{open?.summary ?? "-"}</span></p>
                            }

                        </div>
                    </div>
                </div>
            </Modal>
        </>
    );
};

export default SharkAttacksModal;
