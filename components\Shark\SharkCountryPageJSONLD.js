import { siteMetadata } from "@/data/siteMetadata";
import { slugConverter } from "@/helper/functions";
import React from "react";

const SharkCountryPageJSONLD = ({ countryData, params }) => {
  const webPageSchema = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    url: "https://sandee.com/shark",
    // name: `🦈 Shark Attacks in the ${countryData?.name} – Latest Reports & Safety Tips | Sandee`,
    // description: `Stay updated with real-time shark attack reports, global statistics, and expert safety tips in ${countryData?.name}. Explore insights on shark behavior, attack hotspots, and how to stay safe in the ocean. Get the latest news, verified incident data, and risk analysis to make informed decisions before heading into the water.`,
    name: `Shark Protection Laws in ${slugConverter(params?.countrySlug, true)} 2025 | Sandee`,
    description: `Learn about shark protection laws and regulations in ${slugConverter(params?.countrySlug, true)} for 2025. Stay informed with <PERSON><PERSON> on safe practices and conservation efforts to protect marine life in your region.`,
    publisher: {
      "@type": "Organization",
      name: siteMetadata?.title || "<PERSON>ee",
    },
  };

  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        name: "Home",
        item: "https://sandee.com/",
      },
      {
        "@type": "ListItem",
        position: 2,
        name: "Sharks Attacks",
        item: "https://sandee.com/shark",
      },
      {
        "@type": "ListItem",
        position: 3,
        name: `Shark Attacks in ${countryData?.name}`,
        item: `https://sandee.com/shark/${countryData?.slug}`,
      },
    ],
  };

  return (
    <>
      <script
        type="application/ld+json"
        id="webPageSchema"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(webPageSchema) }}
      ></script>
      <script
        type="application/ld+json"
        id="breadcrumbSchema"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbSchema) }}
      ></script>
    </>
  );
};

export default SharkCountryPageJSONLD;
