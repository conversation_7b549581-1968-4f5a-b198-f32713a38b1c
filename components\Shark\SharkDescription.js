"use client";
import { EditorContent, processContent } from "@/helper/functions";
import React, { useEffect, useRef, useState } from "react";

const SharkDescription = ({ data, className = "" }) => {
  const [isShow, setIsShow] = useState(false);
  const [isOverflow, setIsOverflow] = useState(false);

  // const fullContent =
  //   // data?.summary?.replaceAll(
  //   //   "<p><br></p>",
  //   //   '<div class="spacing_overview"></div>'
  //   // ) ??
  //   // data?.overview?.replaceAll(
  //   //   "<p><br></p>",
  //   //   '<div class="spacing_overview"></div>'
  //   // ) ??
  //   data?.description?.replaceAll(
  //     "<p><br></p>",
  //     '<div class="spacing_overview"></div>'
  //   ) ??
  //   "";

  // Process content fields in order of priority
  const fullContent =
    // processContent(data?.summary) ||
    // processContent(data?.overview) ||
    processContent(data?.description) ||
    "";
  const contentRef = useRef(null);
  const sanitizeContent =
    fullContent?.trim()?.length !== 0 &&
    fullContent?.trim() !== '<div class="spacing_overview"></div>';
  // Check if the content exceeds 5 lines
  useEffect(() => {
    const contentEl = contentRef.current;
    if (contentEl) {
      const lineHeight = parseFloat(
        window.getComputedStyle(contentEl).lineHeight
      );
      const maxHeight = lineHeight * 5; // Height for 5 lines
      if (contentEl.scrollHeight > maxHeight) {
        setIsOverflow(true); // Content is more than 5 lines
      } else {
        setIsOverflow(false); // Content fits within 5 lines
      }
    }
  }, [fullContent]);
  return (
    <>

      {(data?.summary || data?.overview || data?.description) &&
        sanitizeContent && (
          <div className={`text-black font-normal text-sandee-18 ${className}`}>
            {/* <EditorContent
              ref={contentRef}
              className={`${!isShow
                ? "clamp-text" // Limit to 5 lines
                : ""
                }`}
              value={fullContent} /> */}

            <div
              ref={contentRef}
              className={`${!isShow
                ? "clamp-text" // Limit to 5 lines
                : ""
                } ck-content`}
              dangerouslySetInnerHTML={{
                __html: fullContent,
              }}
            ></div>

            {sanitizeContent && isOverflow && (
              <button
                onClick={() => setIsShow(!isShow)}
                className="font-bold hover:underline text-sandee-orange"
              // className="font-bold hover:underline text-gray-500"
              >
                {isShow ? "View Less" : "View More"}
              </button>
            )}
          </div>
        )}
    </>
  );
};

export default SharkDescription;
