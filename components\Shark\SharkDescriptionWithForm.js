"use client";
import { processContent } from "@/helper/functions";
import { useRouter } from "next/navigation";
import React, { useEffect, useRef, useState } from "react";

const SharkDescriptionWithForm = ({ data, className = "" }) => {
    const [isShow, setIsShow] = useState(false);
    const [isOverflow, setIsOverflow] = useState(false);
    const router = useRouter();
    // console.log(router)
    useEffect(() => {
        // Remove search parameters on page load
        if (router?.query?.form) {
            router.replace(router?.pathname);
        }
    }, [router]);
    // Process content fields in order of priority
    const fullContent =
        processContent(`
    <p>Shark attacks, while often sensationalized, are incredibly rare events.&nbsp; Globally, there have been over 7,000 recorded shark attacks, occurring in a variety of aquatic environments, including beaches, coral reefs, the open ocean, rivers, and even lakes.&nbsp; Despite their diverse habitats, the likelihood of encountering a shark remains extraordinarily low.</p>
    
    <p>Each year, there are an average of 72 shark attacks worldwide, with just 6 proving fatal.&nbsp; To put this into perspective, with an estimated 13.1 billion beach visits globally every year, the odds of being bitten by a shark are a staggering 1 in 180 million. Even at <a href="https://sandee.com/united-states/florida/new-smyrna-beach/new-smyrna-beach" target="_blank" rel="noopener noreferrer"><strong>New Smyrna Beach</strong></a> 
    in Florida—the world’s “shark bite capital” with over 210 recorded incidents in 53 years—the chances of an attack are still remarkably low, at 1 in 1.2 million.</p>

    <p>In order, the most common shark attack countries are:</p>
    <ul>
      <li><a href="https://sandee.com/united-states" target="_blank" rel="noopener noreferrer"><strong>The United States</strong></a> (2,542 recorded attacks; 201 fatal attacks)</li>
      <li><a href="https://sandee.com/australia" target="_blank" rel="noopener noreferrer"><strong>Australia</strong></a> (1,469 recorded attacks, 306 fatal attacks)</li>
      <li><a href="https://sandee.com/south-africa" target="_blank" rel="noopener noreferrer"><strong>South Africa</strong></a> (592 recorded attacks, 112 fatal attacks)</li>
      <li><a href="https://sandee.com/papua-new-guinea" target="_blank" rel="noopener noreferrer"><strong>Papua New Guinea</strong></a> (147 recorded attacks, 67 fatal attacks)</li>
      <li><a href="https://sandee.com/new-zealand" target="_blank" rel="noopener noreferrer"><strong>New Zealand</strong></a> (141 recorded attacks, 26 fatal attacks)</li>
      <li><a href="https://sandee.com/bahamas" target="_blank" rel="noopener noreferrer"><strong>The Bahamas</strong></a> (135 recorded attacks, 13 fatal attacks).</li>
    </ul>

    <p>If you would like to report a shark sighting or shark attack, please submit a 
    <p href="#" id="sighting-link"><strong>sighting report</strong></p> or 
    <a href="#" id="attack-link"><strong>attack report</strong></a>.
    You can also email us at <a href="mailto:<EMAIL>"><strong><EMAIL></strong></a>.</p>

    <p>Shark attack information was generously provided by the <a href="https://www.sharkattackfile.net/index.htm" target="_blank" rel="noopener noreferrer"><strong>SharkAttackFile</strong></a>, scientists and researchers around the world, and the Sandee community.</p>
  `);
    const contentRef = useRef(null);
    const sanitizeContent =
        fullContent?.trim()?.length !== 0 &&
        fullContent?.trim() !== '<div class="spacing_overview"></div>';
    // Check if the content exceeds 5 lines
    useEffect(() => {
        const contentEl = contentRef.current;
        if (contentEl) {
            const lineHeight = parseFloat(
                window.getComputedStyle(contentEl).lineHeight
            );
            const maxHeight = lineHeight * 5; // Height for 5 lines
            if (contentEl.scrollHeight > maxHeight) {
                setIsOverflow(true); // Content is more than 5 lines
            } else {
                setIsOverflow(false); // Content fits within 5 lines
            }
        }
    }, [fullContent]);

    // Handle local shark sighting/attack report links to remove search params before navigation
    const handleReportClick = (event, type) => {
        // event.preventDefault();
        router.push(`/shark?form=${type}`, undefined, { shallow: true });
    };

    useEffect(() => {
        const sightingLink = document.getElementById("sighting-link");
        const attackLink = document.getElementById("attack-link");
        // console.log(sightingLink)
        if (sightingLink) {
            // console.log("first")
            sightingLink.addEventListener("click", (e) => handleReportClick(e, "report-shark-sighting"));
        }
        if (attackLink) {
            attackLink.addEventListener("click", (e) => handleReportClick(e, "report-shark-attack"));
        }

        return () => {
            if (sightingLink) {
                sightingLink.removeEventListener("click", handleReportClick);
            }
            if (attackLink) {
                attackLink.removeEventListener("click", handleReportClick);
            }
        };
    }, []);
    return (
        <>

            {(data?.summary || data?.overview || data?.description) &&
                sanitizeContent && (
                    <div className={`text-black font-normal text-sandee-18 ${className}`}>
                        <div
                            ref={contentRef}
                            className={`${!isShow
                                ? "clamp-text" // Limit to 5 lines
                                : ""
                                } ck-content`}
                        // dangerouslySetInnerHTML={{
                        //     __html: fullContent,
                        // }}
                        >
                            {/* <div class=" ck-content"> */}
                            <p>Shark attacks, while often sensationalized, are incredibly rare events.&nbsp; Globally, there have been over 7,000 recorded shark attacks, occurring in a variety of aquatic environments, including beaches, coral reefs, the open ocean, rivers, and even lakes.&nbsp; Despite their diverse habitats, the likelihood of encountering a shark remains extraordinarily low.</p>

                            <p>Each year, there are an average of 72 shark attacks worldwide, with just 6 proving fatal.&nbsp; To put this into perspective, with an estimated 13.1 billion beach visits globally every year, the odds of being bitten by a shark are a staggering 1 in 180 million. Even at <a href="https://sandee.com/united-states/florida/new-smyrna-beach/new-smyrna-beach" target="_blank" rel="noopener noreferrer"><strong>New Smyrna Beach</strong></a>
                                in Florida—the world’s “shark bite capital” with over 210 recorded incidents in 53 years—the chances of an attack are still remarkably low, at 1 in 1.2 million.</p>

                            <p>In order, the most common shark attack countries are:</p>
                            <ul>
                                <li><a href="https://sandee.com/united-states" target="_blank" rel="noopener noreferrer"><strong>The United States</strong></a> (2,542 recorded attacks; 201 fatal attacks)</li>
                                <li><a href="https://sandee.com/australia" target="_blank" rel="noopener noreferrer"><strong>Australia</strong></a> (1,469 recorded attacks, 306 fatal attacks)</li>
                                <li><a href="https://sandee.com/south-africa" target="_blank" rel="noopener noreferrer"><strong>South Africa</strong></a> (592 recorded attacks, 112 fatal attacks)</li>
                                <li><a href="https://sandee.com/papua-new-guinea" target="_blank" rel="noopener noreferrer"><strong>Papua New Guinea</strong></a> (147 recorded attacks, 67 fatal attacks)</li>
                                <li><a href="https://sandee.com/new-zealand" target="_blank" rel="noopener noreferrer"><strong>New Zealand</strong></a> (141 recorded attacks, 26 fatal attacks)</li>
                                <li><a href="https://sandee.com/bahamas" target="_blank" rel="noopener noreferrer"><strong>The Bahamas</strong></a> (135 recorded attacks, 13 fatal attacks).</li>
                            </ul>

                            <p>If you would like to report a shark sighting or shark attack, please submit a
                            </p><span className="text-sandee-blue cursor-pointer" onClick={() => {
                                router.push(`?form=report-shark-sighting`, undefined, { shallow: true });
                            }} id="sighting-link"><strong>sighting report</strong></span> or&nbsp;
                            <span className="text-sandee-blue cursor-pointer" onClick={() => {
                                router.push(`?form=report-shark-attack`, undefined, { shallow: true });
                            }} id="attack-link"><strong>attack report</strong></span>.
                            You can also email us at <a href="mailto:<EMAIL>"><strong><EMAIL></strong></a>.<p></p>

                            <p>Shark attack information was generously provided by the <a href="https://www.sharkattackfile.net/index.htm" target="_blank" rel="noopener noreferrer"><strong>SharkAttackFile</strong></a>, scientists and researchers around the world, and the Sandee community.</p>
                            {/* </div> */}
                        </div>

                        {sanitizeContent && isOverflow && (
                            <button
                                onClick={() => setIsShow(!isShow)}
                                className="font-bold hover:underline text-sandee-orange"
                            // className="font-bold hover:underline text-gray-500"
                            >
                                {isShow ? "View Less" : "View More"}
                            </button>
                        )}
                    </div>
                )}
        </>
    );
};

export default SharkDescriptionWithForm;
