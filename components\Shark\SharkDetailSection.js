"use client";
import { blurD<PERSON>UR<PERSON>, EditorContent, FinalImageGenerator, processContent, sanitizeHtml } from '@/helper/functions';
import * as Cheer<PERSON> from "cheerio";
import Image from 'next/image';
import React, { useRef } from 'react';
import CopyRight, { CustomLink } from '../Common/CopyRight';
import { CustomContainer } from '../Custom-Display';
import BreadCumber from '../Common/BreadCumber';
import "../../components/BlogPage/blog.css"
import CustomeImage from '../Common/CustomeImage';

const SharkDetailSection = ({ data }) => {

    // const fullContent =
    //     data?.sharkSpecies?.description?.replaceAll(
    //         "<p><br></p>",
    //         '<div class="spacing_overview"></div>'
    //     ) ??
    //     "";

    // Process content fields in order of priority
    const fullContent =
        // processContent(data?.summary) ||
        // processContent(data?.overview) ||
        processContent(data?.sharkSpecies?.description) ||
        "";
    const contentRef = useRef(null);
    const sanitizeContent =
        fullContent?.trim()?.length !== 0 &&
        fullContent?.trim() !== '<div class="spacing_overview"></div>';

    const $ = Cheerio?.load(sanitizeHtml(data?.sharkSpecies?.description), null, false);
    const h2TextArray = [];
    const h2Ids = [];
    $("h2")?.each((index, element) => {
        const uniqueId = `${index + 1}`;
        $(element).attr("id", uniqueId);
        const text = $(element).text();
        h2TextArray.push(text);
        const idAttribute = $(element).attr("id");
        if (idAttribute && text !== "") {
            h2Ids.push(idAttribute);
        }
    });

    // $("img").attr({
    //     title: blogs?.data?.title,
    //     alt: blogs?.data?.title,
    // });

    $("a")?.each((index, element) => {
        const aElement = $(element);
        const textContent = aElement.text();
        aElement.attr("title", textContent);
    });

    $("span")?.css({
        height: null,
        width: null,
    });
    const modifiedHtmlString = $?.html();
    return (
        <>
            <div className="hidden md:flex gap-2 flex-col lg:flex-row my-5">
                <div className=" w-full  ">
                    <div className="relative h-[520px]">
                        <div className="absolute left-0 bottom-0  h-[520px] w-full ">
                            <CustomeImage
                                priority
                                src={FinalImageGenerator(data?.sharkSpecies?.image, 1600, 4)}
                                // src={(data?.sharkSpecies?.image && data?.sharkSpecies?.image?.imageUrl) ?? "https://images.sandee.com/images/header/Default-Header.avif"}
                                alt={`${data?.sharkSpecies?.alterText ? data?.sharkSpecies?.alterText : data?.alterTextdata?.sharkSpecies?.name ?? "Shark Attack Photo "
                                    }`}
                                style={{
                                    objectPosition: `center ${data?.sharkSpecies?.image?.position?.objectPositionY ? data?.sharkSpecies?.image?.position?.objectPositionY
                                        : "50"}%`,
                                }}
                                className="object-cover bg-blend-overlay rounded-sandee"
                                blurDataURL={blurDataURL(300, 200)}
                                placeholder="blur"
                                fill
                            />
                            <CopyRight
                                copyRightsData={[data?.sharkSpecies?.image]}
                                classNameExtra={"bottom-0"}
                            // tooltipContentHTML={<div style={{
                            //     fontSize: "10px",
                            //     color: "white",
                            // }}>
                            //     {data?.sharkSpecies?.photographerName === "Randall Kaplan" ? (
                            //         <CustomLink href={"https://www.randallkaplan.com/biography"}>
                            //             {`Copyright Randall Kaplan. All Rights Reserved.`}
                            //         </CustomLink>
                            //     ) : data?.sharkSpecies?.photographerLink && data?.sharkSpecies?.photographerName ? (
                            //         <CustomLink href={data?.sharkSpecies?.photographerLink}>
                            //             {`Copyright ${data?.sharkSpecies?.photographerName || ""
                            //                 }. All Rights Reserved.`}</CustomLink>
                            //     ) : data?.sharkSpecies?.photographerName ? `Copyright ${data?.sharkSpecies?.photographerName || ""
                            //         }. All Rights Reserved.` : (
                            //         "Copyright. All Rights Reserved."
                            //     )}
                            // </div>}
                            />
                        </div>
                        {/* <div className="absolute right-0 bottom-0  h-[420px] w-full bg-transparent z-1 flex justify-center  items-center ">
                <h1 className=" text-white font-bold rounded-full border border-gray-300 bg-[#EBEBEB66] [text-shadow:0px_4px_4px_rgba(0,_0,_0,_0.25)]   text-[28px]  xs:text-[28px] px-3 xs:px-3  sm:text-sandee-32 sm:px-4 md:text-[40px] md:px-8 xl:text-[45px] top-[50%] left-[50%]  ">
                  {data?.name}
                </h1>
              </div> */}
                    </div>
                </div>
            </div>
            <div className="relative md:hidden  md:h-[520]  h-[260px]">
                <div className="absolute left-0 top-0  md:h-[520] h-[260px] w-full ">
                    <CustomeImage
                        priority
                        // src={FinalImageGenerator(data?.image, 1600, 3)}
                        src={FinalImageGenerator(data?.sharkSpecies?.image, 1600, 5)}
                        // src={(data?.sharkSpecies?.image && data?.sharkSpecies?.image?.imageUrl) ?? "https://images.sandee.com/images/header/Default-Header.avif"}
                        alt={`${data?.sharkSpecies?.alterText ? data?.sharkSpecies?.alterText : data?.sharkSpecies?.name ?? "Shark Attack Photo "
                            }`}
                        className="object-cover bg-blend-overlay lg:rounded-b-none rounded-b-[40px] "
                        fill
                        blurDataURL={blurDataURL(300, 200)}
                        placeholder="blur"

                    />
                </div>

                <div className="absolute flex justify-center items-center  md:h-[500px] h-[260px] w-full lg:rounded-b-none rounded-b-[40px]">
                    <h1 className=" text-white font-bold text-sandee-3xl w-full lg:w-1/2  sm:text-sandee-3xl md:text-sandee-4xl xl:text-sandee-6xl 3xl:text-sandee-7xl text-center">
                        {`${data?.sharkSpecies?.name}`}
                    </h1>
                    <CopyRight
                        copyRightsData={[data?.sharkSpecies?.image]}
                        background={true}
                        classNameExtra={"bottom-2 lg:left-0 left-6"}
                    // tooltipContentHTML={<div style={{
                    //     fontSize: "10px",
                    //     color: "white",
                    // }}>
                    //     {data?.sharkSpecies?.photographerName === "Randall Kaplan" ? (
                    //         <CustomLink href={"https://www.randallkaplan.com/biography"}>
                    //             {`Copyright Randall Kaplan. All Rights Reserved.`}
                    //         </CustomLink>
                    //     ) : data?.sharkSpecies?.photographerLink && data?.sharkSpecies?.photographerName ? (
                    //         <CustomLink href={data?.sharkSpecies?.photographerLink}>
                    //             {`Copyright ${data?.sharkSpecies?.photographerName || ""
                    //                 }. All Rights Reserved.`}</CustomLink>
                    //     ) : data?.sharkSpecies?.photographerName ? `Copyright ${data?.sharkSpecies?.photographerName || ""
                    //         }. All Rights Reserved.` : (
                    //         "Copyright. All Rights Reserved."
                    //     )}
                    // </div>}
                    // styleExtra={{ left: "25px" }}
                    />
                </div>
            </div>
            <div className="md:hidden  w-full pt-5 flex items-center">
                {/* <CustomContainer className='md:hidden'> */}
                <BreadCumber
                    data={[
                        {
                            title: "Sharks Attacks",
                            to: "/shark",
                        },
                        // {
                        //     title: "Shark Species",
                        // },
                        {
                            title: data?.sharkSpecies?.name,
                        },
                    ]}
                />
                <p className="text-sandee-24 leading-[35px] font-bold mt-1">
                    {data?.name}
                </p>
                {/* </CustomContainer> */}
            </div>
            <h2 className="text-sandee-32 font-bold pb-2">Introduction</h2>
            <EditorContent id="content"
                className=" min-w-full px-2 ql-container ql-editor blog-editor"
                value={modifiedHtmlString} />

            {/* <div
                id="content"
                className=" min-w-full px-2 ql-container ql-editor blog-editor"
                dangerouslySetInnerHTML={{
                    __html: modifiedHtmlString,
                }}
            /> */}
            {/* {<div className="text-black font-normal  text-sandee-18 pt-2">
                <div
                    ref={contentRef}
                    className='ql-container ql-editor'
                    // className={`${!isShow
                    //     ? "clamp-text" // Limit to 5 lines
                    //     : ""
                    //     }`}
                    dangerouslySetInnerHTML={{
                        __html: data?.sharkSpecies?.description,
                    }}
                /> */}

            {/* {sanitizeContent && isOverflow && (
                <button
                  onClick={() => setIsShow(!isShow)}
                  className="font-bold hover:underline"
                >
                  {isShow ? "View Less" : "View More"}
                </button>
              )} */}
            {/* </div>} */}
        </>
    )
}

export default SharkDetailSection