"use client";

import React, { useEffect, useRef, useState } from 'react'
import AttackBeacheSwiper from './AttackBeacheSwiper'
import ScrollBar from '../ScrollBar';
import { CustomGrid } from '../Custom-Display';
import { getRecentSharkAttacks } from '@/app/(HeaderSlim)/(Shark Section)/shark/[countrySlug]/action';
import CustomButton from '../Custom-Button';
import { LoaderIcon } from '../social-icons/icons';
import SharkAttacksModal from './SharkAttacksModal';
import CustomToolTip from '../Common/CustomToolTip';
import CopyRight from '../Common/CopyRight';
import Image from 'next/image';
import { altText, blurDataURL, defaultImage, FinalImageGenerator } from '@/helper/functions';
import moment from 'moment';
import defaultSharkBeachImage from "../../public/default_shark_beach.jpg";
import SharkAttacksModalNew from './sharkAttackModalNew';
import CustomeImage from '../Common/CustomeImage';

// import { getRecentSharkAttacks } from '@/app/(HeaderSlim)/(Shark Section)/shark/page';

const SharkListDetail = ({ initialData = [], countryId = null }) => {
    const [sharkData, setSharkData] = useState(initialData ?? []);
    // const [selectedCategory, setSelectedCategory] = useState([]);
    // const [selection, setSelection] = useState("DESC");
    // const [query, setQuery] = useState("");
    const [scrollLoader, setScrollLoader] = useState(false);
    const [loadoff, setLoadOff] = useState(false);
    const [pagination, setPagination] = useState({
        limit: 25,
        page: 1,
    });
    const [refresh, setRefresh] = useState(false);
    const [open, setOpen] = useState({});
    const onOpen = (data) => setOpen({ ...data });

    // const prevSelection = useRef(selection);
    // const prevSelectedCategory = useRef(selectedCategory);
    // const prevQuery = useRef(query);
    // console.log(sharkData, "sharkData", countryId, pagination)
    // Async function for delayed search
    const delayedSearch = async (
        // selectionValue,
        selectedCategoryValue
    ) => {
        const query = { ...pagination };
        // if (queryValue?.trim()?.length) {
        //     query.searchQuery = queryValue;
        // }
        // =====================
        if (countryId) {
            query.countryId = countryId;
        }
        // ===========================

        // if (selectedCategoryValue?.length) {
        //     query.categoryIds = selectedCategoryValue?.join(",");
        // }
        // if (Object.keys(query)?.length > 0) {
        //     if (!!selectionValue) {
        //         query.sortBy = "createdAt";
        //         query.sortOrder = selectionValue;
        //     }
        // }
        try {
            const { data, count } = await getRecentSharkAttacks({ ...query });
            if (data?.length && pagination.page > 1) {
                setSharkData((prev) => [...prev, ...data]);
                setLoadOff(false);
            } else if (data?.length && pagination.page === 1) {
                setSharkData((prev) => [...data]);
                setLoadOff(false);
            } else {
                setLoadOff(true);
            }
        } catch (error) {
            console.error("Error fetching blog data:", error);
        } finally {
            setScrollLoader(false);
        }
    };
    useEffect(() => {

        const fetchData = async () => {
            if (pagination.page === 1) return
            setScrollLoader(true);
            // console.log("first")
            await delayedSearch();
        };

        fetchData();
    }, [pagination.page, refresh]);



    // useEffect(() => {
    //     if (
    //         prevSelection.current !== selection ||
    //         prevSelectedCategory.current !== selectedCategory ||
    //         prevQuery.current !== query
    //     ) {
    //         const getData = setTimeout(() => {
    //             setPagination((prev) => {
    //                 if (prev.page === 1) {
    //                     setRefresh((prevR) => !prevR);
    //                     setSharkData([]);
    //                 }
    //                 return { ...prev, page: 1 };
    //             });
    //         }, 400);

    //         // Update previous values
    //         prevSelection.current = selection;
    //         prevSelectedCategory.current = selectedCategory;
    //         prevQuery.current = query;
    //         return () => clearTimeout(getData);
    //     }
    // }, [selection, selectedCategory, query]);
    return (
        <div className={`${!sharkData?.length && !scrollLoader ? "h-screen" : ""}`}>
            {!sharkData?.length && !scrollLoader ? (
                <p className="px-2 py-5 text-error-red-600 border-error-red-400 bg-error-red-50 border-2 rounded-sandee text-center">
                    No Data Found !
                </p>
            ) : (
                <CustomGrid
                    data={sharkData}
                    className="gap-2 sm:gap-[11.2px] mt-[11.2px] mb-[-20px]"
                    Component={({ data: dataProps }) => {
                        // console.log(dataProps, "dataProps")
                        let newLink = "";
                        if (dataProps?.city?.state?.country?.slug) {
                            newLink = dataProps?.city?.state?.slug && dataProps?.city?.slug && dataProps?.AllBeach?.nameSlug ? `/${dataProps?.city?.state?.country?.slug}/${dataProps?.city?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.AllBeach?.nameSlug}` : null; //
                            dataProps.link = `/${dataProps?.city?.state?.country?.slug}/${dataProps?.city?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.AllBeach?.nameSlug}`; //
                            dataProps.location = `${dataProps?.city?.name}, ${dataProps?.city?.state?.country?.code}`; //
                        } else {
                            newLink = dataProps?.country?.slug && dataProps?.state?.slug && dataProps?.city?.slug && dataProps?.AllBeach?.nameSlug ? `/${dataProps?.country?.slug}/${dataProps?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.AllBeach?.nameSlug}` : null; //
                            dataProps.link = dataProps?.country?.slug && dataProps?.state?.slug && dataProps?.city?.slug && dataProps?.AllBeach?.nameSlug ? `/${dataProps?.country?.slug}/${dataProps?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.AllBeach?.nameSlug}` : null; //
                            dataProps.location = `${dataProps?.city?.name}, ${dataProps?.country?.code}`; //
                        }
                        dataProps.imageSrc = FinalImageGenerator(
                            dataProps?.AllBeach?.images?.[0]
                            , 1600, 1, defaultSharkBeachImage);
                        return BeachCard({
                            data: { ...dataProps, ...dataProps?.AllBeach },
                            onOpen: () => onOpen({
                                ...dataProps, ...dataProps?.AllBeach,
                                coordinatesData: dataProps?.GeoLoc,
                                beachName: dataProps?.AllBeach?.name,
                                cityName: dataProps?.city?.name,
                                stateName: dataProps?.state?.name,
                                countryName: dataProps?.country?.name,
                                newLink: newLink
                            }),
                        });
                        // <AttackBeacheSwiper
                        //     title={`${dataProps?.name} Shark Attack Beaches`}
                        //     dangerAttack={dataProps?.beaches} />
                    }}
                    xs={1}
                    sm={3}
                    md={4}
                    lg={5}
                    xl={5}
                />
            )}
            {sharkData?.length > 0 && <div className='flex items-center justify-center mt-8'>
                {
                    (scrollLoader) && sharkData?.length ? (
                        <LoaderIcon />
                    ) : (
                        !loadoff && <div onClick={() => {

                            setPagination((prev) => ({ ...prev, page: prev?.page + 1 }));
                        }}>
                            <CustomButton type={4}>
                                See more
                            </CustomButton>
                        </div>
                    )
                }

            </div>}
            {/* <ScrollBar
                threshold={60}
                loadMoreFunction={() => {
                    setPagination((prev) => ({ ...prev, page: prev?.page + 1 }));
                }}
                isLoading={scrollLoader}
                loadoff={loadoff}
                timeout={10}
            /> */}
            <SharkAttacksModalNew open={open} setOpen={setOpen} />


        </div>
    )
}

export default SharkListDetail

const BeachCard = ({
    data,
    onOpen,
    className = "md:aspect-[180/140] aspect-square",
}) => {
    data.lat = data?.GeoLoc?.coordinates?.length
        ? data?.GeoLoc?.coordinates?.[1]
        : 0;
    data.lon = data?.GeoLoc?.coordinates?.length
        ? data?.GeoLoc?.coordinates?.[0]
        : 0;
    // console.log(data, "skbfkshk")
    const newLocation = data?.city?.state?.country?.slug &&
        (data?.city?.name && data?.city?.state?.country?.code)
        ? `${data?.city?.name}, ${data?.city?.state?.country?.code}`
        : data?.city?.name && data?.country?.code ? `${data?.city?.name}, ${data?.country?.code}`
            : "";
    return (
        <div
            // href={`${data?.link}`}
            className="cursor-pointer"
            onClick={() => {
                onOpen();
            }}
        // target={`${data?.target}` ?? '_self'}
        >
            <div className="flex flex-col gap-y-[10px]">
                <div
                    className={`relative overflow-hidden aspect-square rounded-[25px] sm:rounded-sandee ${className}`}
                >
                    {/* {data?.imageSrc && data?.imageSrc !== defaultImage ? ( */}
                    <div
                        className={`relative overflow-hidden aspect-square rounded-[25px] sm:rounded-sandee ${className}`}
                    >
                        {/* {data?.fatal && <div
                  className="top-2 flex absolute z-50 right-3 rounded-xl bg-[#FF1616] p-1 px-2 text-white text-[14px] md:text-xs"
                >Fatal Attack</div>} */}
                        <CustomeImage
                            className="w-full object-cover"
                            src={data?.imageSrc ?? defaultSharkBeachImage}
                            defaultImagesrc={defaultSharkBeachImage}
                            // alt={`${data?.location
                            //     ? `Sandee ${data?.name ?? data?.otherLocation ?? "Unknown"} Photo`
                            //     : `${data?.name ?? data?.otherLocation ?? "Unknown"}`
                            //     }`}
                            alt={altText(data)}
                            fill
                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                            blurDataURL={blurDataURL(300, 200)}
                            placeholder="blur"
                        />
                        {data?.fatal && <span className="absolute top-2 right-3 bg-[#FF1616]  py-1 px-3 rounded-full text-sm text-white">
                            Fatal Attack
                        </span>}
                        <CopyRight
                            copyRightsData={data?.images}
                            // background={true}
                            // styleExtra={{ bottom: "1px", left: "3px" }}
                            classNameExtra={"bottom-0"}
                        />
                    </div>
                    {/* ) : (
                        <iframe
                            className=" bg-gray-200 w-full h-full rounded-sandee min-h-[280px] pointer-events-none"
                            loading="lazy"
                            src={`https://maps.google.com/?q=${data?.lat},${data?.lon}&ie=UTF8&iwloc=&output=embed&t=k&disableDefaultUI=true`}
                        ></iframe>
                    )} */}
                </div>
                <div className="flex flex-col gap-y-1 ms-[5px]">
                    <div className="flex gap-x-1 items-center text-sm">
                        <span className="font-medium">
                            {/* {moment(data?.date?.split("T")?.[0]).format("M/D/YY") ??
                                "-"} */}
                            {
                                moment(data?.date)?.local()?.format("M/D/YY") ?? "-"
                            }
                        </span>
                        <span>|</span>
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width={18}
                            height={18}
                            viewBox="0 0 16 16"
                            fill="none"
                        >
                            <path
                                d="M7.99956 11.5135L10.7662 13.1868C11.2729 13.4935 11.8929 13.0402 11.7596 12.4668L11.0262 9.32017L13.4729 7.20017C13.9196 6.8135 13.6796 6.08017 13.0929 6.0335L9.87289 5.76017L8.61289 2.78684C8.38623 2.24684 7.61289 2.24684 7.38623 2.78684L6.12623 5.7535L2.90623 6.02684C2.31956 6.0735 2.07956 6.80684 2.52623 7.1935L4.97289 9.3135L4.23956 12.4602C4.10623 13.0335 4.72623 13.4868 5.23289 13.1802L7.99956 11.5135Z"
                                fill="#1F1F1F"
                            />
                        </svg>
                        <span className="font-medium">
                            {((data?.rating100 ?? 80) / 20)?.toFixed(2)}
                        </span>
                    </div>
                    <CustomToolTip title={data?.name ?? data?.otherLocation ?? "Unknown"}>
                        <h3 className="text-lg font-bold line-clamp-1 mb-0 leading-[24px]">
                            {data?.name ?? data?.otherLocation ?? "Unknown"}
                        </h3>
                    </CustomToolTip>
                    <span className="opacity-[0.8] uppercase text-[#656565] line-clamp-2 md:text-base text-sm font-semibold -mt-[5px]">
                        {newLocation || ""}
                    </span>
                </div>
            </div>
        </div>
    );
};
