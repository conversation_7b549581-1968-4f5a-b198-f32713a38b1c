import { SiteDataPageWise } from "@/data/siteMetadata";
import React from "react";

const SharkPageJSONLD = () => {
  const webPageSchema = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    url: "https://sandee.com/shark",
    name: SiteDataPageWise.shark.title || "International Shark Attacks Worldwide | Insights by <PERSON><PERSON>",
    headline: SiteDataPageWise.shark.title || "International Shark Attacks Worldwide | Insights by <PERSON><PERSON>",
    description: SiteDataPageWise.shark.description || "Explore global shark attack data with <PERSON>ee. Learn about hotspots, safety tips, and facts surrounding international shark encounters worldwide.",
    publisher: {
      "@type": "Organization",
      name: "<PERSON><PERSON>",
    },
  };

  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        name: "Home",
        item: "https://sandee.com/",
      },
      {
        "@type": "ListItem",
        position: 2,
        name: "Sharks Attacks",
        item: "https://sandee.com/shark",
      },
    ],
  };

  return (
    <>
      <script
        type="application/ld+json"
        id="webPageSchema"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(webPageSchema) }}
      ></script>
      <script
        type="application/ld+json"
        id="breadcrumbSchema"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbSchema) }}
      ></script>
    </>
  );
};

export default SharkPageJSONLD;
