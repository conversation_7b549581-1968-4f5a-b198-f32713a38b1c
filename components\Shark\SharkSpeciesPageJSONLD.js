import { siteMetadata } from "@/data/siteMetadata";
import { slugConverter } from "@/helper/functions";
import React from "react";

const SharkSpeciesPageJSONLD = ({ data }) => {
  const webPageSchema = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    url: "https://sandee.com/shark",
    // name: `🦈 ${data?.sharkSpecies?.name} – Facts, Attacks & Habitat | Sandee`,
    // description: `Discover everything about the ${data?.sharkSpecies?.name}, including its habitat, behavior, attack history, and fascinating facts. Learn why it’s the ocean’s top predator.`,
    name: `Explore "${slugConverter(data?.sharkSpecies?.name, true)}" with <PERSON><PERSON> – The Ultimate Guide to Understanding and Conserving Sharks`,
    description: `Dive into the world of "${slugConverter(data?.sharkSpecies?.name, true)}" with <PERSON><PERSON>. Learn about their habitat, behavior, and conservation efforts, making you a shark expert in no time!`,
    publisher: {
      "@type": "Organization",
      name: siteMetadata?.title || "Sandee",
    },
  };

  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        name: "Home",
        item: "https://sandee.com/",
      },
      {
        "@type": "ListItem",
        position: 2,
        name: "Sharks Attacks",
        item: "https://sandee.com/shark",
      },
      {
        "@type": "ListItem",
        position: 3,
        name: `${data?.sharkSpecies?.name}`,
        item: `https://sandee.com/shark/shark-species/${data?.sharkSpecies?.slug}`,
      },
    ],
  };

  return (
    <>
      <script
        type="application/ld+json"
        id="webPageSchema"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(webPageSchema) }}
      ></script>
      <script
        type="application/ld+json"
        id="breadcrumbSchema"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbSchema) }}
      ></script>
    </>
  );
};

export default SharkSpeciesPageJSONLD;
