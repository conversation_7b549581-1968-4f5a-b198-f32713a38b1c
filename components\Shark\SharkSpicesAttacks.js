"use client";
import React, { useEffect, useState } from "react";
import { CustomGrid } from "../Custom-Display";
import {
  API_BASE_URL,
  FinalImageGenerator,
  altText,
  apiGenerator,
  blurDataURL,
  buildQueryString,
  defaultImage,
} from "@/helper/functions";
import NameTitle from "../Common/NameTitle";
import Pagination from "../Common/Pagination";
import axios from "axios";
import { BeachCardSkeleton } from "../Cards/BeachCard";
import Link from "next/link";
import Image from "next/image";
import CopyRight, { CustomLink } from "../Common/CopyRight";
import SharkDescription from "./SharkDescription";
import { sahrkspeciesDes } from "@/data/sharkPageData";
import CustomeImage from "../Common/CustomeImage";
// const getShakiestCountries = async (query) => {
//   const APIShakiestCountries = `${API_BASE_URL}/countries/shakiestCountries${buildQueryString(
//     query
//   )}`;
//   const response = await axios.get(APIShakiestCountries);
//   return response?.data?.data;
// };
const sharkDesc =
  "<p>Have you ever wondered why sharks get such a bad rap from humans? It surely can’t be because of their razor-sharp teeth, craving for blood, or uncanny hunting skills, right?  In actuality, it is!  Sharks, in general, epitomize human fear.  There have been over 6,000 shark attacks ever recorded, however, the United States, South Africa, and Australia take the cake for most shark attacks in the world. Specifically, the United States clocks in at over 1,600 shark attacks with over 60.</p>";

const SharkSpicesAttacks = ({ speciesData, totalRecords }) => {
  //   const [loading, setLoading] = useState(true);
  //   const [total, setTotal] = useState(totalRecords);
  //   const [query, setQuery] = useState("");
  //   const [CurrentBeach, setCurrentBeach] = useState(countryData);
  //   const [currentPage, setCurrentPage] = useState(1);
  //   const [refresh, setRefresh] = useState(true);
  //   const FetchOrSetBeach = async () => {
  //     setLoading(true);
  //     if (!!query) {
  //       const AllBeachesFilterResponse = await getShakiestCountries({
  //         page: currentPage,
  //         searchQuery: query,
  //         limit: 12,
  //       });
  //       setTotal(AllBeachesFilterResponse?.data?.count);
  //       setCurrentBeach(AllBeachesFilterResponse?.data?.rows);
  //       return setLoading(false);
  //     }

  //     const AllBeachesResponse = await getShakiestCountries({
  //       page: currentPage,
  //       limit: 12,
  //     });
  //     console.log(AllBeachesResponse,'AllBeachesResponse')
  //     setTotal(AllBeachesResponse?.data?.count);
  //     setCurrentBeach(AllBeachesResponse?.data?.rows);
  //     return setLoading(false);
  //   };
  //   useEffect(() => {
  //     FetchOrSetBeach();
  //   }, [refresh]);

  //   useEffect(() => {
  //     const getData = setTimeout(() => {
  //       // if (!!!query) {
  //       //   return;
  //       // }
  //       setRefresh((prev) => !prev);
  //     }, 400);

  //     return () => clearTimeout(getData);
  //   }, [query]);

  return (
    <section className="">
      <div className="flex justify-between flex-col md:flex-row mt-5 mb-[0px] gap-5">
        <NameTitle
          className=""
          description={"All Dangerous Shark Species"}
          name={`Shark Species by Attack Count`}
          type={2}
        />
        {/* <div className=" relative  flex justify-start md:justify-end items-start gap-4">
          {totalRecords > 8 ? (
            <div className="relative flex justify-center items-center max-w-[450px] w-full sm:w-auto md:min-w-[300px]">
              <div className="absolute inset-y-0 start-0 flex items-center ps-2 pe-2 ms-2 pointer-events-none rounded-full">
                <svg
                  className="w-4 h-4 text-[#7D7D7D]  "
                  aria-hidden="true"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 20 20"
                >
                  <path
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"
                  />
                </svg>
              </div>
              <input
                type="text"
                id="simple-search"
                className={`transition-all duration-500  focus:outline-none outline-none  focus:ring-0 ring-0 focus:border-sandee-blue  bg-white border-2 border-[#DEDEDE] block w-full ps-10 sm:text-[20px]  rounded-xl text-gray-600 !pl-[50px] lg:text-sandee-sm  !text-sandee-sm p-2 `}
                placeholder="Search"
                value={query}
                onChange={(e) => {
                  setQuery(e.target.value);
                  setCurrentPage(1);
                }}
              />
            </div>
          ) : null}
        </div> */}
      </div>
      <SharkDescription data={{ description: sahrkspeciesDes }} />
      {/* {loading ? (
        <CustomGrid
          className="!my-5 gap-4 sm:gap-8"
          data={Array(12).fill(1)}
          Component={BeachCardSkeleton}
          xs={2}
          sm={2}
          md={3}
          lg={4}
          xl={4}
          xxl={4}
          xxxl={4}
        />
      ) : total ? (
        <> */}
      <CustomGrid
        data={speciesData}
        className=" gap-4 sm:gap-8 my-4"
        Component={({ data: dataB }) => {
          const dataProps = { ...dataB };
          dataProps.link = `/shark/shark-species/${dataProps?.slug}`;
          dataProps.imageSrc = FinalImageGenerator(dataProps?.image?.imageUrl ?? dataProps?.license?.photoLink ?? dataProps?.photoLink);
          dataProps.images = [dataProps?.image];
          dataProps.image = FinalImageGenerator(dataProps?.image, 1600, 5);
          return CountryCard({
            data: { ...dataProps },
          });
        }}
        xs={2}
        sm={2}
        md={3}
        lg={4}
        xl={4}
        xxl={4}
        xxxl={4}
      />
      {/* </>
      ) : (
        <p className=" px-2 py-5 text-error-red-600  border-error-red-400 bg-error-red-50 border-2 rounded-sandee text-center mt-6 mb-10">
          No Data Found for your applied filters
        </p>
      )} */}
      {/* <Pagination
        className="pagination-bar"
        currentPage={currentPage}
        totalCount={total}
        pageSize={12}
        onPageChange={(page) => {
          setCurrentPage(page);
          setRefresh((prev) => !prev);
        }}
      /> */}
    </section>
  );
};

export default SharkSpicesAttacks;
const CountryCard = ({
  data,
  className = "md:aspect-[180/140] aspect-square",
}) => {
  return (
    <Link href={`${data?.link}`} className={`${className}`}>
      <div className={`${className}  w-full rounded-[18px]`}>
        <div className="relative  overflow-hidden  w-full h-full rounded-[18px] ">
          <CustomeImage
            src={data?.image ?? defaultImage}
            alt={altText(data)}
            className="object-cover object-center"
            fill
            blurDataURL={blurDataURL(300, 200)}
            placeholder="blur"
          />
          <CopyRight
            copyRightsData={data?.images}
            classNameExtra={"bottom-0"}
          // tooltipContentHTML={<div style={{
          //   fontSize: "10px",
          //   color: "white",
          // }}>
          //   {data?.photographerName === "Randall Kaplan" ? (
          //     <CustomLink href={"https://www.randallkaplan.com/biography"}>
          //       {`Copyright Randall Kaplan. All Rights Reserved.`}
          //     </CustomLink>
          //   ) : data?.photographerLink && data?.photographerName ? (
          //     <CustomLink href={data?.photographerLink}>
          //       {`Copyright ${data?.photographerName || ""
          //         }. All Rights Reserved.`}</CustomLink>
          //   ) : data?.photographerName ? `Copyright ${data?.photographerName || ""
          //     }. All Rights Reserved.` : (
          //     "Copyright. All Rights Reserved."
          //   )}
          // </div>}
          />
        </div>
        <h3 className="text-sandee-20 font-bold mt-2 ms-[5px]">{data?.name}</h3>
        <p className="uppercase text-sm -mt-[3px] ms-[5px]">
          {Number(data?.attacks)?.toLocaleString()}+ Attacks
        </p>
      </div>
    </Link>
  );
};
