"use client"
import React, { useEffect, useState } from 'react'
import { CustomGrid } from '../Custom-Display'
import Pagination from '../Common/Pagination'
import { withErrorHandling } from '@/app/(HeaderSlim)/(Single Country Page)/[countrySlug]/action';
import { API_BASE_URL, buildQueryString } from '@/helper/functions';
import axios from 'axios';
import Link from 'next/link';
// withOutAttacks: 1
const getWithoutSharkAttackStateList = async (query) => {
    const APISharkSpecies = `${API_BASE_URL}/states/shakiestStates${buildQueryString(
        query
    )}`;
    const response = await axios.get(APISharkSpecies);
    return response?.data?.data;
};

const WithoutSharkAttackStateList = ({ data, totalRecords, isPagination = true, countryId, countrySlug }) => {
    // console.log(data)
    const [loading, setLoading] = useState(true);
    const [total, setTotal] = useState(totalRecords);
    // const [query, setQuery] = useState("");
    const [CurrentBeach, setCurrentBeach] = useState(data);
    const [currentPage, setCurrentPage] = useState(1);
    const [refresh, setRefresh] = useState(true);
    // console.log(currentPage, total)


    const FetchOrSetBeach = async () => {
        setLoading(true);
        // if (!!query) {
        //     const AllBeachesFilterResponse = await getWithoutSharkAttackStateList({
        //         page: currentPage,
        //         // searchQuery: query,
        //         limit: 40,
        //         withOutAttacks: 1,
        //         countryId: countryId,

        //     });
        //     setTotal(AllBeachesFilterResponse?.data?.count);
        //     setCurrentBeach(AllBeachesFilterResponse?.data?.rows);
        //     return setLoading(false);
        // }

        const AllBeachesResponse = await getWithoutSharkAttackStateList({
            page: currentPage,
            limit: 40,
            withOutAttacks: 1,
            countryId: countryId,
        });

        setTotal(AllBeachesResponse?.data?.count);
        setCurrentBeach(AllBeachesResponse?.data?.rows);
        return setLoading(false);
    };
    useEffect(() => {
        if (!isPagination) return
        FetchOrSetBeach();
    }, [refresh]);

    // useEffect(() => {
    //     const getData = setTimeout(() => {
    //         // if (!!!query) {
    //         //   return;
    //         // }
    //         setRefresh((prev) => !prev);
    //     }, 400);

    //     return () => clearTimeout(getData);
    // }, [query]);

    return (
        <>

            <CustomGrid
                data={CurrentBeach}
                className="gap-2 sm:gap-2 my-1 mb-10"
                Component={({ data: dataProps }) => {
                    // dataProps.link = `/blog/${dataProps?.slug}`; //
                    // dataProps.imageSrc = dataProps?.imageSrc;
                    // dataProps.titl = dataProps?.title;
                    return <Link href={`/${countrySlug}/${dataProps?.slug}`} className='text-start text-sandee-16'>{dataProps?.name}</Link>
                    //   return BlogCard({
                    //     data: { ...dataProps },
                    //     copyRightsData: dataProps?.image,
                    //   });
                }}
                xs={2}
                sm={2}
                md={3}
                lg={4}
                xl={4}
            />
            {isPagination && <Pagination
                className="pagination-bar"
                currentPage={currentPage}
                totalCount={total}
                pageSize={40}
                onPageChange={(page) => {
                    setCurrentPage(page);
                    setRefresh((prev) => !prev);
                }}
            />}
        </>
    )
}

export default WithoutSharkAttackStateList