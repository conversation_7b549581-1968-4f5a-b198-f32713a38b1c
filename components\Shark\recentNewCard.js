"use client";
import React, { memo, useState } from 'react'
import CustomToolTip from '../Common/CustomToolTip';
import CopyRight from '../Common/CopyRight';
import moment from 'moment';
import Image from 'next/image';
import { altText, blurDataURL } from '@/helper/functions';
import SharkAttacksModal from './SharkAttacksModal';
import { GetSharkAttackeByLatLong } from '@/app/(HomeHeader)/action';
import SharkAttacksModalNew from './sharkAttackModalNew';
import CustomeImage from '../Common/CustomeImage';
import defaultSharkBeachImage from "../../public/default_shark_beach.jpg";

const RecentNewCard = ({
    data = {},
    onOpen,
    className = "md:aspect-[180/140] aspect-square",
}) => {
    const [open, setOpen] = useState({});

    const newLocation = data?.city?.state?.country?.slug &&
        (data?.city?.name && data?.city?.state?.country?.code)
        ? `${data?.city?.name}, ${data?.city?.state?.country?.code}`
        : data?.city?.name && data?.country?.code ? `${data?.city?.name}, ${data?.country?.code}`
            : "";

    return (
        <>
            <div
                // href={`${data?.link}`}
                className="cursor-pointer"
                onClick={() => {
                    // const Results = await GetSharkAttackeByLatLong({
                    //     page: 1,
                    //     limit: 5,
                    //     // coordinates: `${coordinatesData?.coordinates?.[1] ?? 0},${coordinatesData?.coordinates?.[0] ?? 0}`
                    // }, `${data?.coordinatesData?.coordinates?.[1] ?? 0},${data?.coordinatesData?.coordinates?.[0] ?? 0}`);
                    setOpen(data);
                }}
            // target={`${data?.target}` ?? '_self'}
            >
                <div className="flex flex-col gap-y-[10px]">
                    <div
                        className={`relative overflow-hidden aspect-square rounded-[25px] sm:rounded-sandee ${className}`}
                    >
                        {/* {data?.imageSrc && data?.imageSrc !== defaultImage ? ( */}
                        <div
                            className={`relative overflow-hidden aspect-square rounded-[25px] sm:rounded-sandee ${className}`}
                        >
                            {/* {data?.fatal && <div
                className="top-2 flex absolute z-50 right-3 rounded-xl bg-[#FF1616] p-1 px-2 text-white text-[14px] md:text-xs"
              >Fatal Attack</div>} */}
                            <CustomeImage
                                className="w-full object-cover"
                                src={data?.imageSrc ?? defaultSharkBeachImage}
                                defaultImages={defaultSharkBeachImage}
                                // alt={`${data?.location
                                //     ? `Sandee ${data?.name ?? data?.otherLocation ?? "Unknown"} Photo`
                                //     : `${data?.name ?? data?.otherLocation ?? "Unknown"}`
                                //     }`}
                                alt={altText(data)}
                                fill
                                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                                blurDataURL={blurDataURL(300, 200)}
                                placeholder="blur"
                            />
                            {data?.fatal && <span className="absolute top-2 right-3 bg-[#FF1616]  py-1 px-3 rounded-full text-sm text-white">
                                Fatal Attack
                            </span>}
                            <CopyRight
                                copyRightsData={data?.images}
                                // background={true}
                                // styleExtra={{ bottom: "1px", left: "3px" }}
                                classNameExtra={"bottom-0"}
                            />
                        </div>
                        {/* )  */}
                        {/*}   : (
            <iframe
              className=" bg-gray-200 w-full h-full rounded-sandee min-h-[280px] pointer-events-none"
              loading="lazy"
              src={`https://maps.google.com/?q=${data?.lat},${data?.lon}&ie=UTF8&iwloc=&output=embed&t=k&disableDefaultUI=true`}
            ></iframe>
          )} */}
                    </div>
                    <div className="flex flex-col gap-y-1 ms-[5px]">
                        <div className="flex gap-x-1 items-center text-sm">
                            {data?.date && <><span className="font-medium">
                                {/* {moment(data?.date?.split("T")?.[0]).format("M/D/YY") ??
                                    ""} */}
                                {moment(data?.date)?.local()?.format("M/D/YY") ?? "-"}
                            </span>
                                <span>|</span></>}
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width={18}
                                height={18}
                                viewBox="0 0 16 16"
                                fill="none"
                            >
                                <path
                                    d="M7.99956 11.5135L10.7662 13.1868C11.2729 13.4935 11.8929 13.0402 11.7596 12.4668L11.0262 9.32017L13.4729 7.20017C13.9196 6.8135 13.6796 6.08017 13.0929 6.0335L9.87289 5.76017L8.61289 2.78684C8.38623 2.24684 7.61289 2.24684 7.38623 2.78684L6.12623 5.7535L2.90623 6.02684C2.31956 6.0735 2.07956 6.80684 2.52623 7.1935L4.97289 9.3135L4.23956 12.4602C4.10623 13.0335 4.72623 13.4868 5.23289 13.1802L7.99956 11.5135Z"
                                    fill="#1F1F1F"
                                />
                            </svg>
                            <span className="font-medium">
                                {((data?.rating100 ?? 80) / 20)?.toFixed(2)}
                            </span>
                        </div>
                        <CustomToolTip title={data?.name ?? data?.otherLocation ?? "Unknown"}>
                            <h3 className="text-lg font-bold line-clamp-1 mb-0 leading-[24px]">
                                {data?.name ?? data?.otherLocation ?? "Unknown"}
                            </h3>
                        </CustomToolTip>
                        <span className="opacity-[0.8] uppercase text-[#656565] line-clamp-2 md:text-base text-sm font-semibold -mt-[5px]">
                            {newLocation ?? ''}
                        </span>
                    </div>
                </div>
            </div>
            {Object?.keys(open)?.length > 0 && <SharkAttacksModalNew open={open} setOpen={setOpen} />}
        </>
    );
}

export default memo(RecentNewCard)