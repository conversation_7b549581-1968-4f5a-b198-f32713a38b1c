import React, { useState } from 'react'
import CustomToolTip from '../Common/CustomToolTip';
import CopyRight from '../Common/CopyRight';
import Image from 'next/image';
import SharkAttacksModal from './SharkAttacksModal';
import { altText, blurDataURL } from '@/helper/functions';
import moment from 'moment';
import CustomeImage from '../Common/CustomeImage';

const RecentSharkCard = ({ data }) => {
    const [open, setOpen] = useState(false);

    data.lat = data?.GeoLoc?.coordinates?.length
        ? data?.GeoLoc?.coordinates?.[1]
        : 0;
    data.lon = data?.GeoLoc?.coordinates?.length
        ? data?.GeoLoc?.coordinates?.[0]
        : 0;
    const onOpen = () => setOpen(pr => !pr);


    return (
        <>
            <div
                // href={`${data?.link}`}
                className="cursor-pointer"
                onClick={() => {
                    onOpen()
                }}
            // target={`${data?.target}` ?? '_self'}
            >
                <div className="flex flex-col gap-y-[10px]">
                    <div
                        className={`relative overflow-hidden aspect-square rounded-[25px] sm:rounded-sandee ${className}`}
                    >
                        {data?.imageSrc && data?.imageSrc !== defaultImage ? (
                            <div
                                className={`relative overflow-hidden aspect-square rounded-[25px] sm:rounded-sandee ${className}`}
                            >
                                <CustomeImage
                                    className="w-full object-cover"
                                    src={data?.imageSrc}
                                    // alt={`${data?.location
                                    //     ? `Sandee ${data?.name} Photo`
                                    //     : `${data?.name}`
                                    //     }`}
                                    alt={altText(data)}
                                    fill
                                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                                    blurDataURL={blurDataURL(300, 200)}
                                    placeholder="blur"
                                />
                                {/* <span className="absolute top-2 right-3 bg-[#FF1616]  py-1 px-3 rounded-full text-sm text-white">
                Fatal Attack
              </span> */}
                                <CopyRight
                                    copyRightsData={data?.images}
                                    // background={true}
                                    // styleExtra={{ bottom: "1px", left: "3px" }}
                                    classNameExtra={"bottom-0"}
                                />
                            </div>
                        ) : (
                            <iframe
                                className=" bg-gray-200 w-full h-full rounded-sandee min-h-[280px] pointer-events-none"
                                loading="lazy"
                                src={`https://maps.google.com/?q=${data?.lat},${data?.lon}&ie=UTF8&iwloc=&output=embed&t=k&disableDefaultUI=true`}
                            ></iframe>
                        )}
                    </div>
                    <div className="flex flex-col gap-y-1 ms-[5px]">
                        <div className="flex gap-x-2 items-center text-sm">
                            <span className="font-medium">
                                {moment(data?.date?.split("T")?.[0]).format("DD/MM/YY") ??
                                    " 09 Nov, 2023"}
                            </span>
                            <span>|</span>
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width={18}
                                height={18}
                                viewBox="0 0 16 16"
                                fill="none"
                            >
                                <path
                                    d="M7.99956 11.5135L10.7662 13.1868C11.2729 13.4935 11.8929 13.0402 11.7596 12.4668L11.0262 9.32017L13.4729 7.20017C13.9196 6.8135 13.6796 6.08017 13.0929 6.0335L9.87289 5.76017L8.61289 2.78684C8.38623 2.24684 7.61289 2.24684 7.38623 2.78684L6.12623 5.7535L2.90623 6.02684C2.31956 6.0735 2.07956 6.80684 2.52623 7.1935L4.97289 9.3135L4.23956 12.4602C4.10623 13.0335 4.72623 13.4868 5.23289 13.1802L7.99956 11.5135Z"
                                    fill="#1F1F1F"
                                />
                            </svg>
                            <span className="font-medium">
                                {((data?.rating100 ?? 80) / 20)?.toFixed(2)}
                            </span>
                        </div>
                        <CustomToolTip title={data?.name}>
                            <h3 className="text-lg font-bold line-clamp-1 mb-0 leading-[24px]">
                                {data?.name}
                            </h3>
                        </CustomToolTip>
                        <span className="uppercase opacity-[0.8] text-[#656565] line-clamp-2 md:text-base text-sm font-semibold -mt-[5px]">
                            {data?.location}
                        </span>
                    </div>
                </div>
            </div>
            <SharkAttacksModal open={open} setOpen={setOpen} />
        </>
    );
}

export default RecentSharkCard