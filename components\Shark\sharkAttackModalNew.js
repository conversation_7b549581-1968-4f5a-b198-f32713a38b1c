"use client"
import { <PERSON>, Collapse, Divider, Modal, Select, Space, Spin, Tooltip } from 'antd';
import React, { memo, useEffect, useState } from 'react';
import CustomButton from '../Custom-Button';
import { CancelIcon, SharkIconShadow } from '../social-icons/icons';
import moment from 'moment';
import Link from 'next/link';
import { EditorContent, isHTML, isMobileView } from '@/helper/functions';
import "../BlogPage/blog.css"
import Image from 'next/image';
import SelectBox from '../Common/SelectBox';
import Pagination from '../Common/Pagination';
import { GetSharkAttackeByLatLong, getSharkSpeciesList } from '@/app/(HomeHeader)/action';
import CustomToolTip from '../Common/CustomToolTip';
const text = `
  A dog is a type of domesticated animal.
  Known for its loyalty and faithfulness,
  it can be found as a welcome guest in many households across the world.
`;

const SharkAttacksModalNew = ({ open, setOpen = () => { } }) => {
    const [isClient, setIsClient] = useState(false);
    const [sortOrder, setsortOrder] = useState("ASC");
    const [query, setQuery] = useState({});
    const [currentPage, setCurrentPage] = useState(1);
    const [sharkData, setSharkData] = useState({});
    const [sharkSpeciesData, setSharkSpeciesData] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const isMobileViews = isMobileView()

    // console.log(open, "posopdisjdikh")
    // const limit = FullCount?.limit ?? 20;
    // Fetch shark attack data
    const fetchSharkData = async () => {
        setIsLoading(true);
        const coordinates = open?.coordinatesData?.coordinates;
        const latitude = coordinates?.[1] ?? 0;
        const longitude = coordinates?.[0] ?? 0;

        const results = await GetSharkAttackeByLatLong(
            { page: currentPage, limit: 5, ...query },
            `${latitude},${longitude}`
        );

        if (results?.status === "success") {
            setSharkData({
                ...results.data,
                coordinatesData: open?.coordinatesData,
                beachName: open?.AllBeach?.name,
                cityName: open?.city?.name,
                countryName: open?.country?.name,
            });
            // console.log(results, "results?.data");
            setIsLoading(false);
        }
    };

    // Fetch shark species list
    const fetchSharkSpecies = async () => {
        const result = await getSharkSpeciesList();
        setSharkSpeciesData(result?.data ?? []);
    };

    // useEffect(() => {
    //     console.log("first", open)
    //     if (Object?.keys(open)?.length) fetchSharkData();
    // }, [open]);

    useEffect(() => {
        // console.log("first")
        if (open) fetchSharkData();
    }, [open, currentPage, query]);

    useEffect(() => {
        fetchSharkSpecies();
        setIsClient(true);
    }, []);

    const closeModal = () => {
        setOpen({});
        setCurrentPage(1);
        setSharkData({});
        setQuery({});
    };

    if (!isClient) return null; // Prevent server-side rendering
    // console.log(sharkData,)
    return (
        <>
            <Modal
                role="dialog"
                aria-modal="true"
                open={open && Object?.keys(open)?.length > 0}
                onCancel={closeModal}
                footer={null}
                closable={false}
                okButtonProps={{ style: { display: "none" } }}
                // style={{ padding: 0 }}
                width={700}
                centered
                className='shark-modal'
                wrapClassName=" !p-0"
                // styles={{
                //     body: {
                //         padding: "20px !important",
                //     },
                // }}
                cancelButtonProps={{ style: { display: "none" } }}
            >
                <div className="bg-white rounded-xl h-full  ">
                    <Space direction="vertical" className='px-6 pb-3 w-full '>
                        <div className="flex justify-between  pt-6  " >
                            <div>
                                <p className='font-extrabold text-2xl'>{open?.beachName ?? ""}</p>
                                {/* San Francisco, CA, United States state remain*/}
                                <p className='font-medium text-base text-[#1A1A1A]'>
                                    {`${open?.cityName ? `${open?.cityName},` : ""}`} {`${open?.stateName ? `${open?.stateName},` : ""}`} {open?.countryName ?? ""}
                                </p>
                            </div>
                            <CancelIcon onClick={closeModal} className="w-7 h-7 opacity-50 cursor-pointer" />
                        </div>
                        {sharkData?.count != 0 ? <div className='my-3 flex justify-between items-center'>
                            <p className="text-sandee-orange font-semibold text-sandee-18">
                                Total {sharkData?.count ?? 0} {sharkData?.count === 1 ? "Attack" : "Attacks"}
                            </p>
                            <div>
                                {sharkData?.rows && sharkData?.rows?.length && (open?.newLink || open?.link) && <Link
                                    href={`${open?.newLink || open?.link}`}
                                    className=" text-sandee-blue active:text-white active:bg-sandee-blue border border-sandee-blue py-[6px] px-2 transition ease-in-out duration-300 text-sandee-sm rounded-[35px] active:scale-[0.7] hover:scale-110 text-xs"
                                    type={7}>
                                    View Beach
                                </Link>}
                                {/* <Select
                                    placeholder="Select Shark"
                                    value={query?.sharkSpeciesId}
                                    allowClear
                                    onChange={(e) => {
                                        setQuery((prev) => {

                                            return {
                                                sharkSpeciesId: e
                                            }
                                        })
                                    }}
                                    options={
                                        sharkSpeciesData?.rows?.map((sData) => {
                                            return {
                                                value: sData?.id,
                                                label: sData?.name,
                                            }
                                        })
                                    }
                                /> */}
                            </div>
                        </div> : <></>}
                        <div className='h-[330px]  overflow-y-scroll overflow-hidden'>

                            {isLoading ?
                                <Spin className='shark-modal-spin flex items-center h-full justify-center text-sandee-blue ' size='large' /> :
                                sharkData?.rows && sharkData?.rows?.length ? <div className='mr-2 space-y-3'>
                                    {sharkData?.rows?.map((sData, index) => {
                                        // console.log(sData, "sData", open?.summary)

                                        return <Collapse
                                            key={`shark-attack-${index}`}
                                            ghost
                                            styles={{
                                                color: "red"
                                            }}
                                            className='border-[1px] rounded-xl border-solid border-[#FF161633]'
                                            collapsible="header"
                                            defaultActiveKey={index === 0 ? ['1'] : [""]}
                                            expandIconPosition='end'
                                            items={[
                                                {
                                                    key: '1',
                                                    label: (<div className='flex items-center space-x-2'>
                                                        <SharkIconShadow />
                                                        <p className='text-sandee-14 font-medium text-[#FF1616]'>
                                                            {/* {`${sData?.victims && sData?.victims?.length && sData?.victims?.[0]?.victimName ? `${sData?.victims?.[0]?.victimName
                                                                ?.split(" ")
                                                                .filter((word) => word.length > 0)?.[0]} -` : "Unknown -"} ${sData?.sharkSpecy?.name ?? "Unknown"}`} */}
                                                            {sData?.date
                                                                ? moment(sData?.date).local().format("MMMM D, YYYY") ?? "-"
                                                                : "-"}
                                                        </p>
                                                    </div>),
                                                    children: <div className='space-y-3'>
                                                        <Card
                                                            styles={{
                                                                header: {
                                                                    minHeight: "40px",
                                                                    padding: "0 20px",
                                                                },
                                                                body: {
                                                                    padding: "20px",
                                                                },
                                                            }}
                                                            title="Attack Details"
                                                        >
                                                            <div className="flex flex-wrap items-start justify-between gap-4">
                                                                {/* Victim Name */}
                                                                <div className="flex-1 min-w-[80px]">
                                                                    <p className="font-normal text-[#1A1A1A] text-sandee-14">Victim name</p>
                                                                    <Tooltip title={sData?.victims?.[0]?.victimName ?? "Unknown"}>
                                                                        <p className="font-semibold text-sandee-16 truncate overflow-hidden whitespace-nowrap">
                                                                            {sData?.victims?.[0]?.victimName ?? "Unknown"}
                                                                        </p>
                                                                    </Tooltip>
                                                                    {/* <div className="custom-tooltip group relative w-full inline-block">
                                                                        <p className="font-semibold text-sandee-16 truncate overflow-hidden whitespace-nowrap">
                                                                            {sData?.victims?.[0]?.victimName ?? "Unknown"}
                                                                        </p>
                                                                        <span className="custom-tooltiptext pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-300 ease-in-out transform translate-y-2 group-hover:translate-y-0 absolute z-10 text-sm rounded-md text-center text-white bg-sandee-blue max-w-[300px] w-max py-[3px] px-[10px] left-1/2 -translate-x-1/2 -top-[calc(100%+10px)] before:content-[''] before:absolute before:left-1/2 before:-translate-x-1/2 before:top-full before:border-8 before:border-transparent before:border-t-sandee-blue">
                                                                            {sData?.victims?.[0]?.victimName ?? "Unknown"}
                                                                        </span>
                                                                    </div> */}
                                                                </div>


                                                                {/* Divider */}
                                                                <Divider type="vertical" className="hidden md:block h-[40px] w-1" />
                                                                {/* Date */}
                                                                <div className="flex-1 min-w-[80px]">
                                                                    <p className="font-normal text-[#1A1A1A] text-sandee-14">Date</p>
                                                                    <Tooltip title={sData?.date
                                                                        ? moment(sData?.date).local().format("MMMM D, YYYY") ?? "-"
                                                                        : "-"}>
                                                                        <p className="font-semibold text-sandee-16 truncate overflow-hidden whitespace-nowrap">
                                                                            {sData?.date
                                                                                ? moment(sData?.date).local().format("MMMM D, YYYY") ?? "-"
                                                                                : "-"}
                                                                        </p>
                                                                    </Tooltip>
                                                                    {/* <div className="custom-tooltip group relative inline-block w-full">
                                                                        <p className="font-semibold text-sandee-16 truncate overflow-hidden whitespace-nowrap">
                                                                            {sData?.date
                                                                                ? moment(sData?.date?.split("T")?.[0])?.format("MMMM D, YYYY") ?? "-"
                                                                                : "-"}
                                                                        </p>
                                                                        <span className="custom-tooltiptext pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-300 ease-in-out transform translate-y-2 group-hover:translate-y-0 absolute z-10 text-sm rounded-md text-center text-white bg-sandee-blue max-w-[300px] w-max py-[3px] px-[10px] left-1/2 -translate-x-1/2 -top-[calc(100%+10px)] before:content-[''] before:absolute before:left-1/2 before:-translate-x-1/2 before:top-full before:border-8 before:border-transparent before:border-t-sandee-blue">
                                                                            {sData?.date
                                                                                ? moment(sData?.date?.split("T")?.[0])?.format("MMMM D, YYYY") ?? "-"
                                                                                : "-"}
                                                                        </span>
                                                                    </div> */}
                                                                </div>

                                                                {/* Divider */}
                                                                <Divider type="vertical" className="hidden md:block h-[40px] w-1" />

                                                                {/* Shark Species */}
                                                                <div className="flex-1 min-w-[80px]">
                                                                    <p className="font-normal text-[#1A1A1A] text-sandee-14">Shark Species</p>
                                                                    <Tooltip title={sData?.sharkSpecy?.name ?? "Unknown"}>
                                                                        <p className="font-semibold text-sandee-16 truncate overflow-hidden whitespace-nowrap">
                                                                            {sData?.sharkSpecy?.name ?? "Unknown"}
                                                                        </p>
                                                                    </Tooltip>
                                                                    {/* <div className="custom-tooltip group relative inline-block w-full">
                                                                        <p className="font-semibold text-sandee-16 truncate overflow-hidden whitespace-nowrap">
                                                                            {sData?.sharkSpecy?.name ?? "Unknown"}
                                                                        </p>
                                                                        <span className="custom-tooltiptext pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-300 ease-in-out transform translate-y-2 group-hover:translate-y-0 absolute z-10 text-sm rounded-md text-center text-white bg-sandee-blue max-w-[180px] w-max py-[3px] px-[10px] left-1/2 -translate-x-1/2 -top-[calc(100%+30px)] before:content-[''] before:absolute before:left-1/2 before:-translate-x-1/2 before:top-full before:border-8 before:border-transparent before:border-t-sandee-blue">
                                                                            {sData?.sharkSpecy?.name ?? "Unknown"}
                                                                        </span>
                                                                    </div> */}
                                                                </div>

                                                                {/* Divider */}
                                                                <Divider type="vertical" className="hidden md:block h-[40px] w-1" />

                                                                {/* Shark Size */}
                                                                <div className="flex-1 min-w-[80px]">
                                                                    <p className="font-normal text-[#1A1A1A] text-sandee-14">Shark Size</p>
                                                                    {/* <div className="custom-tooltip group relative inline-block w-full">
                                                                        <p className="font-semibold text-sandee-16 truncate overflow-hidden whitespace-nowrap">
                                                                            {sData?.sharkSize ?? "Unknown"}
                                                                        </p>
                                                                        <span className="custom-tooltiptext pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-300 ease-in-out transform translate-y-2 group-hover:translate-y-0 absolute z-10 text-sm rounded-md text-center text-white bg-sandee-blue max-w-[180px] w-max py-[3px] px-[10px] left-1/2 -translate-x-1/2 -top-[calc(100%+30px)] before:content-[''] before:absolute before:left-1/2 before:-translate-x-1/2 before:top-full before:border-8 before:border-transparent before:border-t-sandee-blue">
                                                                            {sData?.sharkSize ?? "Unknown"}
                                                                        </span>
                                                                    </div> */}
                                                                    <Tooltip title={sData?.sharkSize ?? "Unknown"}>
                                                                        <p className="font-semibold text-sandee-16 truncate overflow-hidden whitespace-nowrap">
                                                                            {sData?.sharkSize ?? "Unknown"}
                                                                        </p>
                                                                    </Tooltip>
                                                                    {/* <CustomButton title={sData?.sharkSize ?? "Unknown"}>
                                                                    <p className="font-semibold text-sandee-16 truncate overflow-hidden whitespace-nowrap">
                                                                            {sData?.sharkSize ?? "Unknown"}
                                                                        </p>
                                                                    </CustomButton> */}
                                                                </div>





                                                            </div>
                                                        </Card>
                                                        <div className='bg-[#FF16160D] p-4 rounded-xl'>
                                                            {/* <p className='text-[#FF1616] font-medium'>{open?.summary ?? sData?.summary ?? "-"}</p> */}
                                                            {(sData?.summary) && isHTML(sData?.summary) ? <div className='font-bold'>
                                                                <EditorContent className="ql-container font-medium ql-editor blog-editor" value={sData?.summary} />

                                                                {/* <div className='ql-container font-medium ql-editor blog-editor' dangerouslySetInnerHTML={{ __html: open?.summary }} /> */}
                                                            </div>
                                                                :
                                                                <p className='text-[#FF1616] font-medium'>{sData?.summary ?? "-"}</p>
                                                            }
                                                        </div>
                                                    </div>,
                                                },
                                            ]}
                                        />
                                    })}
                                </div> : <p className='flex items-center h-full justify-center text-xl'>Shark Attack data not found</p>
                            }
                        </div>
                    </Space>
                    <div className='flex justify-center items-center'>
                        <Pagination
                            className="pagination-bar"
                            currentPage={currentPage ?? 1}
                            totalCount={sharkData?.count ?? 0}
                            pageSize={5}
                            onPageChange={(page) => {
                                setCurrentPage(page);
                                // getData();
                                // setRefresh((prev) => !prev);
                            }}
                        />
                    </div>
                </div >
            </Modal >
        </>
    );
};

export default memo(SharkAttacksModalNew);
