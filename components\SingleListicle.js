"use client";
import React from "react";
import CustomSwiper from "./Custom-Swiper";
import { FinalImageGenerator, generateBreakpoints } from "@/helper/functions";
import BeachCard from "./Cards/BeachCard";
import Link from "next/link";
import { CustomGrid } from "./Custom-Display";

const SingleListicle = ({ data, listSlug, id, dataTestid = "single-listicle" }) => {
  return (
    <>
      <CustomSwiper
        data={data}
        dataTestid={dataTestid}
        Component={({ data: dataProps }) => {
          const countryCode = dataProps?.AllBeach?.city?.state?.country?.code;
          if (dataProps?.AllBeach?.city?.state?.country?.slug) {
            dataProps.link = `/${dataProps?.AllBeach?.city?.state?.country?.slug}/${dataProps?.AllBeach?.city?.state?.slug}/${dataProps?.AllBeach?.city?.slug}/${dataProps?.AllBeach?.nameSlug}`; //
            dataProps.location = `${dataProps?.AllBeach?.city?.name} ${!!countryCode ? `, ${countryCode}` : ""}`;

          } else {
            dataProps.link = `/${dataProps?.AllBeach?.country?.slug}/${dataProps?.AllBeach?.state?.slug}/${dataProps?.AllBeach?.city?.slug}/${dataProps?.AllBeach?.nameSlug}`; //
            dataProps.location = `${dataProps?.AllBeach?.city?.name} ${!!dataProps?.AllBeach?.country?.code ? `, ${dataProps?.AllBeach?.country?.code}` : ""}`;

          }
          dataProps.imageSrc = FinalImageGenerator(
            dataProps?.AllBeach?.images?.[0]
          );
          return BeachCard({ data: { ...dataProps?.AllBeach, ...dataProps } });
        }}
        settingsExtra={{
          breakpoints: {
            ...generateBreakpoints(250, 1300, 50, 280),
            ...generateBreakpoints(1300, 2400, 50, 330),
          },
          spaceBetween: 22,
          // breakpoints: {
          //   300: { slidesPerView: 1.2 },
          //   800: { slidesPerView: 3 },
          //   1200: { slidesPerView: 5.5 },
          //   1400: { slidesPerView: 6.5 },
          // },
        }}
        cardHeight={260}
        slideClassName={`max-w-[240px] lg:max-w-[240px] xl:max-w-[230px]  2xl:max-w-[250px] 3xl:max-w-[300px] mr-8`}
        className="mb-5 mt-[10px] hidden md:block "
        id={id}
      // extra={
      //   <div className="relative h-[300px]  rounded-xl flex items-center justify-center ">
      //     <Link href={`/list/${listSlug}`}>
      //   <CustomButton type={4}>
      //     View More
      //   </CustomButton>
      //     </Link>
      //   </div>
      // }
      />
      <CustomGrid
        className="!my-5 gap-4 sm:gap-8 md:hidden "
        dataTestid={dataTestid}
        Component={({ data: dataProps }) => {
          const countryCode = dataProps?.AllBeach?.city?.state?.country?.code;
          if (dataProps?.AllBeach?.city?.state?.country?.slug) {
            dataProps.link = `/${dataProps?.AllBeach?.city?.state?.country?.slug}/${dataProps?.AllBeach?.city?.state?.slug}/${dataProps?.AllBeach?.city?.slug}/${dataProps?.AllBeach?.nameSlug}`; //
            dataProps.location = `${dataProps?.AllBeach?.city?.name} ${!!countryCode ? `, ${countryCode}` : ""}`;
          } else {
            dataProps.link = `/${dataProps?.AllBeach?.country?.slug}/${dataProps?.AllBeach?.state?.slug}/${dataProps?.AllBeach?.city?.slug}/${dataProps?.AllBeach?.nameSlug}`; //
            dataProps.location = `${dataProps?.AllBeach?.city?.name} ${!!dataProps?.AllBeach?.country?.code ? `, ${dataProps?.AllBeach?.country?.code}` : ""}`;
          }
          dataProps.imageSrc = FinalImageGenerator(
            dataProps?.AllBeach?.images?.[0]
          );
          return BeachCard({ data: { ...dataProps?.AllBeach, ...dataProps } });
        }}
        data={data}
        xs={2}
        sm={3}
        md={3}
        lg={4}
        xl={5}
        xxl={5}
        xxxl={5}
      />
    </>
  );
};
export default SingleListicle;
