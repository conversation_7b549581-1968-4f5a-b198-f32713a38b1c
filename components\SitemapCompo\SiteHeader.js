import React from "react";
import { CustomContainer } from "../Custom-Display";
import ActiveLinks from "../ActiveLinks";

const SiteHeader = () => {
  const AllHTMLSitemaps = [
    { label: "All Sitemaps", path: "/sitemap" },
    { label: "All Countries", path: "/sitemap/country" },
    { label: "All States", path: "/sitemap/state" },
    { label: "All Cities", path: "/sitemap/city" },
    { label: "All Beaches", path: "/sitemap/beach" },
  ];
  return (
    <CustomContainer>
      <div className=" flex flex-wrap gap-4">
        {AllHTMLSitemaps?.map((data) => {
          return (
            <ActiveLinks
              key={`${data?.path}`}
              href={`${data?.path}`}
              className="flex justify-center hover:scale-105 py-2 px-5 hover:bg-gray-300 rounded-lg"
              ActiveClassName="  underline"
            >
              <span className=" text-sandee-base ">{data?.label}</span>
            </ActiveLinks>
          );
        })}
      </div>
    </CustomContainer>
  );
};

export default SiteHeader;
