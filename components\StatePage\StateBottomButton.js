"use client";
import { scrollToTop } from "@/helper/functions";
import Link from "next/link";
import React from "react";
import CustomButton from "../Custom-Button";
import { CustomButtonGrid } from "../Custom-Display";

const State_Bottom_Button = ({ stateHeaderImageResponse }) => {
  return (
    <CustomButtonGrid>
      <Link href={`/countries`}>
        <CustomButton type={4}>All Countries</CustomButton>
      </Link>
      <Link href={`/${stateHeaderImageResponse?.country?.slug}`}>
        <CustomButton type={4}>
          {stateHeaderImageResponse?.country?.name}
        </CustomButton>
      </Link>
      <CustomButton type={4} onClick={scrollToTop}>
        Back to Top
      </CustomButton>
    </CustomButtonGrid>
  );
};

export default State_Bottom_Button;
