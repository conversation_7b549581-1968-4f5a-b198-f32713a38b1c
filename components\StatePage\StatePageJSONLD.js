import { siteMetadata } from "@/data/siteMetadata";
import { slugConverter } from "@/helper/functions";
import React from "react";

const StatePageJSONLD = ({ StateData, params }) => {
  const StateBreadCumber = {
    "@context": "https://schema.org/",
    "@type": "BreadcrumbList",
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        name: "Home",
        item: `https://sandee.com/`,
      },
      {
        "@type": "ListItem",
        position: 2,
        name: StateData?.country?.name,
        item: `https://sandee.com/${StateData?.country?.slug}`,
      },
      {
        "@type": "ListItem",
        position: 3,
        name: StateData?.name,
        item: `https://sandee.com/${StateData?.country?.slug}/${StateData?.slug}`,
      },
    ],
  };
  const StatePageSchema = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `https://sandee.com/${StateData?.country?.slug}/${StateData?.slug}`
    },
    "headline": !!StateData?.title ? StateData?.title : `Complete List of Beaches in  ${slugConverter(
      params?.stateSlug,
      true
    )} - Sandee `,
    "description": !!StateData?.metaDescription ? StateData?.metaDescription : `Discover the complete list of beaches in the ${slugConverter(
      params?.stateSlug,
      true
    )},  ${slugConverter(params?.countrySlug, true)}. Plan your ${slugConverter(
      params?.stateSlug,
      true
    )} beach vacation with 100+ activities, photos, attractions, restaurants, and hotels.`,
    "author": {
      "@type": "Person",
      "name": "Mr. Beach",
      "url": `https://sandee.com/mr-beach`
    },
    "publisher": {
      "@type": "Organization",
      "name": siteMetadata?.title || "Sandee",
      "logo": {
        "@type": "ImageObject",
        "url": `https://sandee.com/_next/image?url=%2Fstatic%2Fimages%2FSandee-Blue.webp&w=1920&q=75`
      }
    },
    "datePublished": StateData?.createdAt,
    "dateModified": StateData?.updatedAt
  };

  return (
    <>
      <script
        type="application/ld+json"
        id="application/ld+jsonBeachBreadCumber"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(StateBreadCumber) }}
      ></script>
      <script
        type="application/ld+json"
        id="application/ld+jsonCountryPage"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(StatePageSchema) }}
      ></script>
    </>
  );
};

export default StatePageJSONLD;
