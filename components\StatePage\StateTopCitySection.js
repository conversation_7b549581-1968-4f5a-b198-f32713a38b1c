"use client";
import React, { useEffect, useState } from "react";
import { CustomGrid } from "../Custom-Display";
import {
  API_BASE_URL,
  FinalImageGenerator,
  apiGenerator,
  buildQueryString,
} from "@/helper/functions";
import NameTitle from "../Common/NameTitle";
import CountryCard from "../Cards/CountryCard";
import axios from "axios";
import { BeachCardSkeleton } from "../Cards/BeachCard";
import Pagination from "../Common/Pagination";
// import SelectBox from "../Common/SelectBox";

const CountrygetTopCities = async (data = {}, query) => {
  const APITopCity = apiGenerator(
    `${API_BASE_URL}/beachMain/getTopCityForUser/:countrySlug/:stateSlug${buildQueryString(
      query
    )}`,
    data
  );
  // console.log(APITopCity,'APITopCity')
  const response = await axios.get(APITopCity);
  return response?.data;
};
const State_City_Section = ({ data, params, topCityData, totalRecords }) => {
  const [loading, setLoading] = useState(true);
  const [query, setQuery] = useState("");
  const [total, setTotal] = useState(totalRecords);
  // const [AllBeachPage, setAllBeachPage] = useState(topCityData);
  const [CurrentBeach, setCurrentBeach] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [refresh, setRefresh] = useState(true);
  // const [sortOrder, setsortOrder] = useState("ASC");
  const FetchOrSetBeach = async () => {
    setLoading(true);
    if (!!query) {
      const AllBeachesFilterResponse = await CountrygetTopCities(params, {
        page: currentPage,
        searchQuery: query,
        limit: 8,
      });
      setTotal(AllBeachesFilterResponse?.totalRecords);
      setCurrentBeach(AllBeachesFilterResponse?.data);
      return setLoading(false);
    }
    setTotal(totalRecords);
    // if (currentPage == 1 && sortOrder === "ASC") {
    //   setCurrentBeach(topCityData);
    //   return setLoading(false);
    // }

    // if (AllBeachPage?.[currentPage - 1]?.length && sortOrder === "ASC") {
    //   setCurrentBeach(AllBeachPage?.[currentPage - 1]);
    //   return setLoading(false);
    // }
    const AllBeachesResponse = await CountrygetTopCities(params, {
      limit: 8,
      page: currentPage,
      // sortBy: "name",
      // sortOrder,
    });

    // if (sortOrder === "ASC") {
    //   setAllBeachPage((prev) => {
    //     prev[currentPage - 1] = AllBeachesResponse?.data;
    //     return prev;
    //   });
    // }
    setCurrentBeach(AllBeachesResponse?.data);
    return setLoading(false);
  };

  useEffect(() => {
    FetchOrSetBeach();
  }, [refresh]);
  useEffect(() => {
    const getData = setTimeout(() => {
      // if (!!!query) {
      //   return;
      // }
      setRefresh((prev) => !prev);
    }, 400);

    return () => clearTimeout(getData);
  }, [query]);

  return (
    <section className="pb-5">
      <div className="flex justify-between flex-col md:flex-row mt-5 gap-5">
        <NameTitle
          description={" EXPLORE TOP DESTINATIONS"}
          name={`Most Popular Beach Cities in ${data?.name}`}
          type={2}
        />
        <div className=" relative  flex justify-start md:justify-end items-start gap-4">
          {totalRecords > 8 ? (
            <div className="relative flex justify-center items-center max-w-[450px] w-full sm:w-auto md:min-w-[300px]">
              <div className="absolute inset-y-0 start-0 flex items-center ps-2 pe-2 ms-2 pointer-events-none rounded-full">
                <svg
                  className="w-4 h-4 text-[#7D7D7D]  "
                  aria-hidden="true"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 20 20"
                >
                  <path
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"
                  />
                </svg>
              </div>
              <input
                type="text"
                id="simple-search"
                className={`transition-all duration-500  focus:outline-none outline-none  focus:ring-0 ring-0 focus:border-sandee-blue  bg-white border-2 border-[#DEDEDE] block w-full ps-10 sm:text-[20px]  rounded-xl text-gray-600 !pl-[50px] lg:text-sandee-sm  !text-sandee-sm p-2 `}
                placeholder="Search"
                value={query}
                onChange={(e) => {
                  setQuery(e.target.value);
                  setCurrentPage(1);
                }}
              />
            </div>
          ) : null}
        </div>
      </div>
      {loading ? (
        <CustomGrid
          className="!mb-5 mt-[13px] gap-4 sm:gap-8"
          data={Array(8).fill(1)}
          Component={BeachCardSkeleton}
          xs={2}
          sm={2}
          md={3}
          lg={4}
          xl={5}
          xxl={5}
          xxxl={5}
        />
      ) : total ? (
        <>
          <CustomGrid
            data={CurrentBeach}
            className="!mb-5 mt-[13px] gap-4 sm:gap-8"
            Component={({ data: dataB }) => {
              const dataProps = { ...dataB };
              dataProps.link = `/${params?.countrySlug}/${params?.stateSlug}/${dataProps?.slug}`;
              dataProps.imageSrc = FinalImageGenerator(dataProps?.image);
              dataProps.alterText = dataProps?.images?.[0]?.alterText;
              dataProps.images = [dataProps?.image];
              dataProps.image = FinalImageGenerator(dataProps?.image);
              return CountryCard({
                data: { ...dataProps },
                copyRightsData: dataProps?.images,
              });
            }}
            xs={2}
            sm={2}
            md={3}
            lg={4}
            xl={4}
            xxl={4}
            xxxl={4}
          />
        </>
      ) : (
        <p className=" px-2 py-5 text-error-red-600  border-error-red-400 bg-error-red-50 border-2 rounded-sandee text-center mt-6 mb-10">
          No Data Found for your applied filters
        </p>
      )}
      <Pagination
        className="pagination-bar"
        currentPage={currentPage}
        totalCount={total}
        pageSize={8}
        onPageChange={(page) => {
          setCurrentPage(page);
          setRefresh((prev) => !prev);
        }}
      />
    </section>
  );
};

export default State_City_Section;
