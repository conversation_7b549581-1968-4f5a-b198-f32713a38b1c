
import { Swiper, SwiperSlide } from "swiper/react";

import Swiper<PERSON><PERSON>, {
  Autoplay,
  Navigation,
  Pagination,
  Thumbs,
  EffectFade,
  Grid,
  Mousewheel,
} from "swiper";
// import "swiper/css";
// import "swiper/css/pagination";
// import "swiper/css/navigation";
// import "swiper/css/grid";

// install Swiper modules
SwiperCore.use([Autoplay, Navigation, Pagination, Thumbs, EffectFade, Grid, Mousewheel]);

function SwiperComps({ children, sliderCName, settings }) {
  return (
    <Swiper
      className={`${sliderCName}  swiper-pointer-events swiper-backface-hidden`}
      {...settings}
    >
      {children}
    </Swiper>
  );
}
export { SwiperSlide as Slide };
export default SwiperComps;
