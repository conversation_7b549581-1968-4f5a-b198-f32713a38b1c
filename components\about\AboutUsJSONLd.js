import React from "react";
import BreadCumberJSONLD from "../BreadCumberJSONLD";
import { SiteDataPageWise } from "@/data/siteMetadata";

const AboutUsJSONLd = () => {
  const jsonLdOrganization = {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: SiteDataPageWise.aboutUS.title || "Sandee - List of all best beautiful destination beaches in the world for vacations.",
    description:
      SiteDataPageWise.aboutUS.description || "Discover the Best Beautiful Beaches in the World - Your Ultimate Guide to Top Beach Destinations for Vacations with family. Explore the Paradise You've Been Dreaming Of!",
    url: "https://sandee.com/",
    logo: "https://www.sandee.com/images/logo.png",
    contactPoint: {
      "@type": "ContactPoint",
      telephone: "******-472-7600",
      contactType: "customer service",
      areaServed: "US",
      availableLanguage: "en",
    },
    address: {
      "@type": "PostalAddress",
      streetAddress: "12011 San Vicente Blvd # 405",
      addressLocality: "Los Angeles",
      addressRegion: "CA",
      postalCode: "90049",
      addressCountry: "USA",
    },
    sameAs: [
      "https://www.facebook.com/sandeebeachescompany",
      "https://www.twitter.com/sandee",
      "https://www.instagram.com/sandee",
      "https://www.youtube.com/@sandee",
      "https://www.linkedin.com/company/sandee/about/",
      "https://www.pinterest.com/chooseyourbeach",
    ],
  };

  return (
    <>
      <BreadCumberJSONLD name="About US" slug={"about-us"} />
      <script
        async
        type="application/ld+json"
        id="application/ld+jsonOrganization"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLdOrganization) }}
      ></script>
    </>
  );
};

export default AboutUsJSONLd;
