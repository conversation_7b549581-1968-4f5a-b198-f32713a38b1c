'use client';

import { useEffect, useRef, useState } from 'react';
import { AudioIcon, BsRobot, ChatHistoryIcon, CopyIcon, FaPaperPlane, FaPlus, GreaterThenIcon, InfoIcon, InfoIcon2, LessThenIcon, MapBeachIcon, MenuIcon, MultiStar, PlaneIcon, PlusIcon, StarIcon, TextMesaageIcon, UserIcon } from '../social-icons/icons';
import Image from 'next/image';
import mapboxgl from "!mapbox-gl"; // eslint-disable-line import/no-webpack-loader-syntax
import MapWithBeachesNew from '../map/MapWithBeachesNew';
import MapChatBot from './MapChatBot';
import axios from 'axios';
import { API_BASE_URL, convertMarkdownToHTML, isHTML, sanitizeHtml, storeBeachesData } from '@/helper/functions';
import { Popover } from 'antd';
import "./chatbot.css"
import SideBar from './sideBar';
import parse from "html-react-parser";
import CustomeImage from '../Common/CustomeImage';
mapboxgl.accessToken = process.env.NEXT_PUBLIC_MAPBOX_API_KEY;

export const GetChatResponse = async (data = {}) => {

    try {
        const response = await axios.post(
            `${API_BASE_URL}/general/chatWithBoat`, data
        );
        return response?.data;
    } catch (error) {
        return { status: "error", data: error };
    }
};
const beaches = [
    {
        name: "Calangute Beach",
        link: "https://sandee.com/india/goa/calangute/calangute-beach",
        image: "https://sandee.com/_next/image?url=https%3A%2F%2Fcdn.sandee.com%2F45291_1650_1100.avif&w=1080&q=75",
        description: "A lively beach in Goa, perfect for water sports and nightlife.",
    },
    {
        name: "Mahabalipuram Beach",
        link: "https://sandee.com/india/tamil-nadu/mahabalipuram/mahabalipuram-beach",
        image: "https://sandee.com/_next/image?url=https%3A%2F%2Fcdn.sandee.com%2F45291_1650_1100.avif&w=1080&q=75",
        description: "Known for its historic sculptures and rock-cut temples.",
    },
    {
        name: "Kovalam Beach",
        link: "https://sandee.com/india/kerala/kovalam/kovalam-beach",
        image: "https://sandee.com/_next/image?url=https%3A%2F%2Fcdn.sandee.com%2F45291_1650_1100.avif&w=1080&q=75",
        description: "Famous for ayurvedic treatments and serene atmosphere.",
    },
    {
        name: "Radhanagar Beach",
        link: "https://sandee.com/india/andaman-and-nicobar-islands/havelock-island/radhanagar-beach",
        image: "https://sandee.com/_next/image?url=https%3A%2F%2Fcdn.sandee.com%2F45291_1650_1100.avif&w=1080&q=75",
        description: "Often cited as one of Asia's best beaches for its beauty.",
    },
    {
        name: "Puri Beach",
        link: "https://sandee.com/india/odisha/puri/puri-beach",
        image: "https://sandee.com/_next/image?url=https%3A%2F%2Fcdn.sandee.com%2F45291_1650_1100.avif&w=1080&q=75",
        description: "A spiritual and tourist hotspot, known for its festival.",
    },
];
// Function to generate pure HTML string
// const generateBeachHTML = () => {
//     return `
//       <p>India's coastline is adorned with many beautiful beaches, each offering a unique experience. Here are some notable beaches across the country:</p>
//       <ul>
//         ${beaches
//             .map(
//                 (beach) => `
//           <li>
//             <a href="${beach.link}" target="_blank" class="text-blue-600 font-semibold hover:underline">
//               ${beach.name}
//             </a>
//             <div class="hidden popover-content">
//               <img src="${beach.image}" class="w-full h-32 object-cover rounded-md" />
//               <h4 class="text-lg font-semibold mt-2">${beach.name}</h4>
//               <p class="text-sm text-gray-600">${beach.description}</p>
//             </div>
//           </li>`
//             )
//             .join("")}
//       </ul>
//       <p>These beaches offer a mixture of natural beauty, spiritual significance, and vibrant local culture, making them must-visit destinations for beach lovers visiting India.</p>
//     `;
// };

const updateMapDirection = (mapData) => {
    if (!mapData?.map) return;
    if (mapData?.lat && mapData?.lng) {
        // console.log(mapData, "gyuhjdbjfdb")
        // Set up marker on the map
        new mapboxgl.Marker({ color: "blue" }).setLngLat([
            mapData.lat,
            mapData.lng,
        ])
            .addTo(mapData?.map?.current)
            .getElement();

        mapData?.map?.current?.flyTo({
            center: [
                mapData.lat,
                mapData.lng,
            ],
            zoom: 17,
            easing(t) {
                return t;
            },
            speed: 1.5, // Adjust speed for smoothness
            curve: 1, // Smoothness of the flight path
            essential: true, // this animation is considered essential with respect to prefers-reduced-motion
        });
    }
}
const transformBeachHTML = (htmlString = "", map, beaches) => {
    // console.log(htmlString, "htmlString", beaches)
    return parse(htmlString, {
        replace: (domNode) => {
            // console.log(domNode, "domNode");
            if (domNode.name === "a") {
                // const linkNode = domNode.children.find((child) => child.name === "a");
                // if (linkNode) {
                const name = domNode?.children?.[0]?.data;
                const url = domNode?.attribs?.href;
                // const localAllData = localStorage.getItem("beachesData") || {};

                const localAllData = beaches || {};
                const matchedKey = Object.keys(localAllData).find(key => {
                    const [nameKey, citySlug, stateSlug, countrySlug] = key.split("||").map(v => v.trim());
                    // console.log(stateSlug, countrySlug, "stateSlug", "countrySlug")
                    // console.log(stateSlug, countrySlug, nameKey, "stateSlug", "countrySlug")
                    return url?.includes(citySlug) && url?.includes(stateSlug) && url?.includes(countrySlug);
                });

                const beachData = matchedKey ? localAllData[matchedKey] : {};
                // console.log(beachData, "beachDatasdsd", matchedKey)
                // console.log(beachData, "beachData", matchedKey, localAllData, url, name)

                //this is for localstorage start======================================
                // const localAllData = beaches || {};
                // console.log(beaches, "beaches", localAllData?.[name])
                // const beachData = Object?.keys(localAllData)?.length && localAllData?.[name] || {};
                // console.log(name, url, domNode, beachData)

                const lat = beachData?.GeoLoc?.coordinates?.[0] ?? 0;
                const long = beachData?.GeoLoc?.coordinates?.[1] ?? 0;
                const handleMouseEnter = () => {
                    // console.log("Enter......", lat, long)
                    if (lat && long) {
                        updateMapDirection({ lat: lat, lng: long, map: map });
                    }
                };
                return (
                    // <li>
                    <div className='flex items-center gap-1'>
                        {url?.includes("sandee.com") && <MapBeachIcon />}
                        {Object?.keys(beachData)?.length ? <Popover
                            openClassName='chatbot'
                            className='chatbot'
                            content={
                                <div className="w-64">
                                    {beachData?.images?.[0]?.imageUrl &&
                                        <div className='relative w-full h-32 object-cover rounded-md'>
                                            <CustomeImage
                                                src={beachData?.images?.[0]?.imageUrl}
                                                alt={beachData?.name || "Sandee Beach"}
                                                fill
                                                className="rounded-md object-cover" /></div>}
                                    <div className='p-3 '>
                                        {beachData?.name && <h4 className="text-lg font-semibold">{beachData?.name}</h4>}
                                        <p className='text-gray-400 text-[13px] mb-[5px] '>{`${beachData?.city?.name ? `${beachData?.city?.name},` : ""} ${beachData?.country?.name || ""}`}</p>
                                        {beachData?.beachDescription?.introduction && isHTML(beachData?.beachDescription?.introduction) ? <div className="line-clamp-3 text-sm text-black" dangerouslySetInnerHTML={{ __html: beachData?.beachDescription?.introduction }}></div> : <p className="line-clamp-3 text-sm text-black">{beachData?.beachDescription?.introduction}</p>}
                                    </div>
                                </div>
                            }
                            trigger="hover"
                            placement="rightTop"
                        >
                            <a
                                href={url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-sandee-blue font-semibold"
                                onMouseEnter={handleMouseEnter}
                            >
                                {name}
                            </a>
                        </Popover> : <a
                            href={url}
                            target="_blank"
                            rel="noopener noreferrer"
                            onMouseEnter={handleMouseEnter}
                            className="text-sandee-blue font-semibold"
                        >
                            {name}
                        </a>}
                    </div>
                    // </li>
                );
                // }
            }
        },
    });
};
export default function ChatbotPage() {
    // const [isSidebarOpen, setIsSidebarOpen] = useState(true);
    const [messages, setMessages] = useState([
        { sender: 'bot', text: 'Hey there! Where would you like to go today?' }
        //         {
        //             sender: "bot", text: <>{transformBeachHTML(`<p>Surat offers some delightful beaches that are perfect for relaxing by the Arabian Sea. Here are the top beaches you should consider visiting:</p>
        // <ul>
        //   <li><a href="https://sandee.com/india/gujarat/surat/pantai-jodoh">Pantai Jodoh</a>: A serene spot known for its beautiful sunsets and tranquil ambiance.</li>
        //   <li><a href="https://sandee.com/india/gujarat/surat/pantai-tete">Pantai Tete</a>: Ideal for those looking to escape the hustle and bustle and enjoy some quiet time by the sea.</li>
        //   <li><a href="https://sandee.com/india/gujarat/surat/tanjung-karang">Tanjung Karang</a>: Known for its clear waters and smooth sands, perfect for a day out with family or friends.</li>
        // </ul>
        // <p>Each beach offers a unique experience, promising plenty of beautiful views and relaxing moments.</p>`)}</>
        //             // <><p>
        //             //     <p>India's coastline is adorned with many beautiful beaches, each offering a unique experience. Here are some notable beaches across the country:</p>
        //             //     <ul className="mt-2 space-y-2">
        //             //         {beaches.map((beach) => (
        //             //             <li key={beach.link} className="relative chatbot">
        //             //                 <div className='flex items-center gap-1'>
        //             //                     <MapBeachIcon fill="#00aae3" />
        //             //                     <Popover
        //             //                         openClassName='chatbot'
        //             //                         className='chatbot'
        //             //                         content={
        //             //                             <div className="w-64">
        //             //                                 <img src={beach.image} alt={beach.name} className="w-full h-32 object-cover rounded-md" />
        //             //                                 <div className='p-3 '>
        //             //                                     <h4 className="text-lg font-semibold mt-2">{beach.name}</h4>
        //             //                                     <p className="text-sm text-gray-600">{beach.description}</p>
        //             //                                 </div>
        //             //                             </div>
        //             //                         }
        //             //                         trigger="hover"
        //             //                         placement="rightTop"
        //             //                     >

        //             //                         <a
        //             //                             href={beach.link}
        //             //                             target="_blank"
        //             //                             rel="noopener noreferrer"
        //             //                             className="text-blue-600 font-semibold hover:underline"
        //             //                         >
        //             //                             {beach.name}
        //             //                         </a>

        //             //                     </Popover>
        //             //                 </div>
        //             //             </li>
        //             //         ))}
        //             //     </ul>
        //             //     <p>These beaches offer a mixture of natural beauty, spiritual significance, and vibrant local culture, making them must-visit destinations for beach lovers visiting India.</p></p></>
        //         }
    ]);
    const map = useRef(null);
    const textareaRef = useRef(null);
    const [showMessages, setShowMessages] = useState(false);
    const [input, setInput] = useState('');
    const [isCollapsed, setIsCollapsed] = useState(false);
    const [isMapCollapsed, setIsMapCollapsed] = useState(false);
    const [isMapShow, setIsMapShow] = useState(false);
    const [isTyping, setIsTyping] = useState(false);
    const chatEndRef = useRef(null); // Auto scroll reference
    useEffect(() => {
        // Trigger the animation when messages change
        if (messages?.findIndex((msg) => msg.sender === 'user') !== -1) {
            setShowMessages(true);
        } else {
            setShowMessages(false);
        }
    }, [messages]);

    const toggleSidebar = () => {
        setIsCollapsed(pr => !pr);
    }
    const toggleMapSidebar = () => {
        setIsMapCollapsed(pr => !pr);
    }
    const toggleMapShow = () => {
        setIsMapShow(pr => !pr);
    }
    // console.log(convertMarkdownToHTML(""), "jijijiijjijiji")
    // Function to simulate the typing effect
    const typeMessage = (message, beaches) => {
        let index = 0;

        // Check if the message is HTML or plain text
        const isHtmlMessage = isHTML(message);
        // const plainText = isHtmlMessage ? message : `${message}`; // Use plain text for typing
        // // const convertedReactElements = isHtmlMessage ? transformBeachHTML(message) : transformBeachHTML((message)); // Convert to React elements
        // const convertedReactElements = transformBeachHTML(message, map, beaches); // Convert to React elements
        // console.log(convertedReactElements, "convertedReactElements");
        // If message is full HTML document, extract only body content
        let processedMessage = message;
        if (isHtmlMessage && message?.includes('<!DOCTYPE html>')) {
            const bodyMatch = message?.match(/<body[^>]*>([\s\S]*)<\/body>/i);
            if (bodyMatch && bodyMatch?.[1]) {
                processedMessage = bodyMatch?.[1];
            }
        }

        const plainText = isHtmlMessage ? processedMessage : `${message}`; // Use plain text for typing
        const convertedReactElements = transformBeachHTML(processedMessage, map, beaches); // Convert to React elements
        setMessages((prev) => {
            let lastMessage = prev[prev.length - 1];

            // If the last message is from the bot and ends with "...", reset it
            if (lastMessage.sender === "bot" && lastMessage.text.endsWith("...")) {
                lastMessage.text = "";
            }

            // Get the partially typed plain text
            const typedText = plainText.subsentring(0, index + 1);

            // Update the last message with the partially typed plain text
            return [
                ...prev.slice(0, -1), // Keep all messages except the last one
                { sender: "bot", text: convertedReactElements }, // Update the last message with the partially typed text
            ];
        });
        setIsTyping(false); // Stop the typing indicator
        // const interval = setInterval(() => {
        //     setMessages((prev) => {
        //         let lastMessage = prev[prev.length - 1];

        //         // If the last message is from the bot and ends with "...", reset it
        //         if (lastMessage.sender === "bot" && lastMessage.text.endsWith("...")) {
        //             lastMessage.text = "";
        //         }

        //         // Get the partially typed plain text
        //         const typedText = plainText.substring(0, index + 1);

        //         // Update the last message with the partially typed plain text
        //         return [
        //             ...prev.slice(0, -1), // Keep all messages except the last one
        //             { sender: "bot", text: typedText }, // Update the last message with the partially typed text
        //         ];
        //     });

        //     index++;

        //     // If the entire plain text has been typed, stop the interval
        //     if (index >= plainText.length) {
        //         clearInterval(interval);

        //         // Replace the plain text with the fully converted React elements
        //         setMessages((prev) => {
        //             let lastMessage = prev[prev.length - 1];
        //             return [
        //                 ...prev.slice(0, -1), // Keep all messages except the last one
        //                 { sender: "bot", text: convertedReactElements }, // Update the last message with the converted React elements
        //             ];
        //         });

        //         setIsTyping(false); // Stop the typing indicator
        //     }
        // }, 0); // Adjust typing speed (in milliseconds)
    };

    // Function to send a message
    const sendMessage = () => {
        if (!input.trim()) return;
        // console.log(input, "input")
        const inputText = transformBeachHTML(input?.trim(), map, {});
        // const inputText = convertMarkdownToHTML(input?.trim());
        const userMessage = { sender: "user", text: inputText };
        setMessages((prev) => [...prev, userMessage]);
        setInput("");
        setIsTyping(true);
        resetTextarea(); // Reset height
        // Simulate bot thinking
        setTimeout(async () => {
            setMessages((prev) => [...prev, { sender: "bot", text: "typing..." }]);

            const { data, status } = await GetChatResponse({ message: input });
            // console.log(data, "data")
            if (status == "success") {
                let beachesObject = {}
                if (data?.boatReply?.beaches?.length > 0) {

                    beachesObject = data?.boatReply?.beaches?.reduce((acc, beach) => {
                        acc[`${beach.name} || ${beach.citySlug} || ${beach.stateSlug} || ${beach.countrySlug}`] = beach;
                        return acc;
                    }, {});
                    // storeBeachesData(beachesObject)
                }
                // console.log(beachesObject, "beachesObject")
                // setTimeout(() => {
                if (data?.boatReply == "No beaches found in our database for your request.") {
                    typeMessage(data?.boatReply);
                } else {

                    typeMessage(data?.boatReply?.botReply, beachesObject);
                }
                // typeMessage("India offers a stunning variety of beaches across its vast coastline. Here are a few exquisite choices: - Explore the tranquil environment of [Pulicat Lagoon](https://sandee.com/india/tamil-nadu/karimanal/pulicat-lagoon), perfect for bird watching and enjoying a quiet day by the water in Tamil Nadu. - Experience the beautiful sands of [Pasir Iii Holtekamp Beach](https://sandee.com/india/kerala/thrikkunnapuzha/pasir-iii-holtekamp-beach) in Kerala, a hidden gem that offers serene views and a relaxing atmosphere. - Visit [Pantai Wanga](https://sandee.com/india/karnataka/shasihithlu/pantai-wanga) in Karnataka for its stunning coastline and inviting waters, ideal for a day of fun and sun. - Discover the unspoiled beauty of [Pulau Lihaga](https://sandee.com/india/kerala/kozhikode/pulau-lihaga) in Kerala, perfect for snorkeling and exploring marine life. - And don't miss [Pulau Menjangan Beach](https://sandee.com/india/kerala/vakkad/pulau-menjangan-beach), known for its crystal-clear waters and vibrant coral reefs in Kerala. Each of these beaches offers a unique slice of India's coastal charm!");
                // }, 500);
            } else if (status == "error") {
                typeMessage(data?.message);
            }

        }, 100);
    };
    // Auto-scroll to bottom when messages update
    useEffect(() => {
        chatEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }, [messages]);
    const autoResize = () => {
        if (textareaRef.current) {
            textareaRef.current.style.height = "auto"; // Reset height to auto
            textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`; // Set new height
        }
    };
    const handleInputChange = (e) => {
        setInput(e.target.value);
        autoResize();
    };

    const resetTextarea = () => {
        if (textareaRef.current) {
            textareaRef.current.style.height = "auto"; // Reset to original size
        }
    };

    return (
        <div className="flex h-screen w-full border-t-[1px] border-gray-200"
        >
            {/* Sidebar */}
            <SideBar isCollapsed={isCollapsed} toggleSidebar={toggleSidebar} setMessages={setMessages} />

            {/* Main Chat Area */}
            <main className={`flex flex-col bg-white transition-all duration-300 ${isMapCollapsed && isCollapsed ? "w-0" : isMapCollapsed ? 'w-0' : isCollapsed ? "min-w-[40%]" : 'w-[50%]'}`}>
                <div className="flex-1 p-4 overflow-y-auto space-y-4">
                    <div className='text-end'
                        onClick={() => {
                            toggleMapShow()
                        }}
                    >Map</div>
                    {showMessages ? (
                        <>
                            {messages?.map((msg, index) => (
                                <><div key={index} className={`group/msg relative z-0 flex gap-[--avatar-gap] leading-[--msg-leading] !m-0 p-3 ${msg.sender !== 'user' ? "hover:bg-[#f9f9f9] hover:rounded-3xl" : ""} `}>
                                    <div className="absolute inset-[calc(var(--hover-offset)*-1)] z-0 rounded-3xl transition-colors group-hover/msg:bg-subtle"></div>
                                    <div aria-hidden="true" className="relative z-1 mt-[calc((var(--msg-leading)-var(--msg-avatar-size))/2)] @lg:mt-0">
                                        <span data-slot="avatar" className="relative flex shrink-0 rounded-full font-medium size-[--avatar-size-sm] text-xs">
                                            <span className="flex size-full select-none items-center justify-center rounded-full uppercase bg-foreground text-background">

                                                {msg.sender === 'user' ? <UserIcon width="35px" height="35px" className={`inline p-2 rounded-full ${msg.sender === 'user' ? "bg-[#e7fdff]" : "bg-[#e7fdff]"}  mr-2 text-gray-600`} /> :
                                                    <MultiStar width="35px" height="35px" className={`inline p-2 rounded-full ${msg.sender === 'user' ? "bg-black" : "bg-sandee-blue"}  mr-2 text-white`} />}
                                            </span>
                                        </span>
                                    </div>
                                    <div className="flex min-w-0 flex-1 flex-col pt-[calc((var(--msg-avatar-size)-var(--msg-leading))/2)]">
                                        <div className="flex flex-col gap-4">
                                            {msg?.text == "typing..." ?
                                                <div className="flex h-10 cursor-wait items-center justify-start px-4">
                                                    <span className="flex items-center gap-[1em] text-[8px]">
                                                        <span className="size-[1em] dot-animation rounded-full bg-sandee-blue"></span>
                                                        <span className="size-[1em] dot-animation delay-200 rounded-full bg-sandee-blue"></span>
                                                        <span className="size-[1em] dot-animation delay-400 rounded-full bg-sandee-blue"></span>
                                                    </span>
                                                </div> :
                                                <div className="boat-answer ck-content rounded-md p-3 prose-chat prose relative break-words e2e:message-bot">
                                                    {msg?.text}
                                                </div>
                                                // <div
                                                //     className="boat-answer ck-content rounded-md p-3 prose-chat prose relative break-words e2e:message-bot"
                                                //     dangerouslySetInnerHTML={{ __html: msg?.text }}
                                                // />
                                            }
                                            {/* <p>{msg?.text}</p>
                                            </div> */}
                                        </div>
                                    </div>
                                </div>
                                    {/* <div
                                        data-orientation="horizontal"
                                        role="none"
                                        data-slot="separator"
                                        className="shrink-0 h-px w-full  bg-gray-300 !my-0"
                                    ></div> */}
                                </>
                            ))}

                            <div className='!m-0' ref={chatEndRef} /> {/* Auto scroll reference */}
                        </>
                    ) : (
                        <div className='pt-20 slide-in'>
                            <h2 className="mb-5 text-pretty text-2xl font-semibold tracking-tight sm:text-3xl xl:text-4xl">Where to today?</h2>
                            <div className="flex justify-start items-start gap-4">
                                <span
                                    className="relative flex shrink-0 rounded-full font-medium text-xs">
                                    <span className="flex select-none items-center justify-center  uppercase ">
                                        <MultiStar className="m-2 bg-sandee-blue text-white p-2 rounded-full" />
                                    </span>
                                </span>
                                <p className="text-pretty text-md font-medium sm:text-balance sm:text-lg">
                                    Hey there, where would you like to go? I&apos;m here to assist you in planning your experience. Ask me anything travel related.
                                </p>
                            </div>
                        </div>

                    )}
                </div>
                <div
                    className="p-4 bg-white shadow flex items-center gap-2 w-full"
                    style={{ "--chat-form-width": "46.5rem" }}
                >
                    {/* <div className="absolute inset-0 overflow-hidden rounded-t-2xl bg-background/80 backdrop-blur-md"></div> */}
                    <div className="rounded-t-2xl sm:px-5 w-full">
                        <div className="relative mx-auto max-w-[--chat-form-width]">
                            <div className="relative flex cursor-text gap-2 transition-colors hover:border-gray-7 rounded-t-2xl border-t px-container py-3 sm:rounded-3xl sm:border-2 sm:border-gray-5 sm:px-4 xs:gap-x-1 sm:!border-current">
                                <div className="mr-1 w-full flex items-center">
                                    <textarea
                                        ref={textareaRef}
                                        rows="1"
                                        className="scrollbar-hidden resize-none bg-transparent outline-none placeholder:text-muted focus:placeholder:text-gray-8 flex-1 disabled:text-gray-8 text-md xs:self-center sm:basis-full"
                                        name="message"
                                        placeholder="Ask anything..."
                                        autoComplete="off"
                                        maxLength="600"
                                        value={input}
                                        onChange={handleInputChange}
                                        onKeyDown={(e) => {
                                            if (isTyping) return;
                                            if (e.key === "Enter" && !e.shiftKey) {
                                                e.preventDefault(); // Prevents adding a new line in the textarea
                                                sendMessage();
                                            }
                                        }}
                                        style={{ height: "auto", overflow: "hidden" }} // Prevents scrollbar flickering
                                    />
                                </div>
                                <div className="flex items-start">
                                    <button
                                        className="group group/button relative z-0 border border-transparent inline-flex justify-center items-center rounded-full font-medium gap-[.3em] disabled:pointer-events-none transition-colors text-center py-[.25em] text-balance bg-primary text-primary-foreground hover:bg-primary-hover data-[state=open]:bg-primary-hover disabled:opacity-30 text-2xs leading-[1.125] px-0 shrink-0"
                                        onClick={sendMessage}
                                        disabled={isTyping}
                                    >
                                        <PlaneIcon className={`${input?.length > 0 ? "bg-sandee-blue text-white" : "text-black"} transition-all duration-300 rounded-full p-1`} />
                                    </button>
                                </div>
                            </div>
                            {/* <div className="absolute bottom-full right-container mb-4 rounded-full sm:right-[1.125rem] split:right-1/2 split:translate-x-1/2 pointer-events-none">
                                <div className="-mr-0.5 rounded-full border border-gray-5 bg-background shadow-[0_0_10px_rgba(0,0,0,.1)] transition-[transform,opacity] translate-y-2 opacity-0">
                                    <button
                                        data-variant="ghost"
                                        data-loading="false"
                                        className="group group/button relative z-0 border border-transparent inline-flex justify-center items-center rounded-full font-medium gap-[.3em] disabled:pointer-events-none disabled:opacity-50 transition-colors text-center py-[.25em] text-balance bg-transparent hover:bg-foreground/5 data-[state=open]:bg-foreground/5 data-[state=active]:border-current text-xs min-h-[--button-sm-size] leading-[1.125] shrink-0 size-[--button-sm-size] px-2.5"
                                        type="button"
                                    >
                                        <span aria-hidden="true" className="absolute left-1/2 top-1/2 size-9 -translate-x-1/2 -translate-y-1/2"></span>
                                        <span className="contents">
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                viewBox="0 0 24 24"
                                                fill="none"
                                                stroke="currentColor"
                                                strokeWidth="1.75"
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                className="shrink-0 transform-cpu size-[1em] text-[1.625em]"
                                            >
                                                <path d="M12 4.052v16.714M7.5 16.266l4.5 4.5 4.5-4.5"></path>
                                            </svg>
                                            <span className="sr-only">See latest messages</span>
                                        </span>
                                    </button>
                                </div>
                            </div> */}
                        </div>
                        <div className="relative text-center text-3xs leading-tight empty:hidden sm:text-2xs bg-gray-2 py-1.5 sm:bg-transparent">
                            <p className="text-muted text-sm flex items-center justify-center gap-1">
                                <InfoIcon2 width={17} height={17} />
                                Sandee can make mistakes. Check important info.
                            </p>
                        </div>
                    </div>
                </div>
            </main>

            {/* Right Map Section */}
            {/* <aside className={`${isCollapsed && isMapCollapsed ? "w-full" : isCollapsed ? "w-[50%]" : isMapCollapsed ? "w-full" : "w-2/5"} transition-all duration-300 hidden lg:block bg-gray-200 relative`}> */}
            <aside
                // className="hidden lg:block bg-gray-200 relative overflow-hidden"
                // style={{ width: `${100 - dividerPosition}%`, minWidth: '30%' }}
                className={`${isCollapsed && isMapCollapsed ? "w-full" : isCollapsed ? "w-[55%]" : isMapCollapsed ? "w-full" : "w-2/5"} transition-all duration-300 hidden lg:block bg-gray-200 relative`}
            >
                {/* <iframe
                    className="w-full h-full"
                    src="https://www.google.com/maps/embed/v1/place?key=YOUR_GOOGLE_MAPS_API_KEY&q=Ahmedabad,India"
                    allowFullScreen
                ></iframe> */}
                <MapChatBot
                    toggleMapSidebar={toggleMapSidebar}
                    isMapCollapsed={isMapCollapsed}
                    isCollapsed={isCollapsed}
                    latStr={null}
                    longStr={null}
                    map={map}
                    popupBeachData={
                        {}
                    }
                    isFilter={false}

                // isSharkToggle={true}
                // isNudeToggle={true}
                />
            </aside>
        </div>
    );
}
