'use client';

import { useEffect, useRef, useState } from 'react';
import { AudioIcon, BsRobot, ChatHistoryIcon, CopyIcon, EarthSideBarIcon, FaPaperPlane, FaPlus, GreaterThenIcon, InfoIcon, InfoIcon2, LessThenIcon, MapBeachIcon, MenuIcon, MultiStar, PlaneIcon, PlusIcon, StarIcon, TextMesaageIcon, UserIcon } from '../social-icons/icons';
import Image from 'next/image';
import mapboxgl from "!mapbox-gl"; // eslint-disable-line import/no-webpack-loader-syntax
import MapWithBeachesNew from '../map/MapWithBeachesNew';
import MapChatBot from './MapChatBot';
import axios from 'axios';
import { API_BASE_URL, convertMarkdownToHTML, isHTML, sanitizeHtml, storeBeachesData } from '@/helper/functions';
import { Popover } from 'antd';
import "./chatbot.css"
import SideBar from './sideBar';
import parse from "html-react-parser";
import HeaderTab from './HeaderTab';
import { io } from 'socket.io-client';
import DynamicTooltipRenderer from './DynamicTooltipRenderer';
import CustomeImage from '../Common/CustomeImage';
mapboxgl.accessToken = process.env.NEXT_PUBLIC_MAPBOX_API_KEY;

export const GetChatResponse = async (data = {}) => {

    try {
        const response = await axios.post(
            `${API_BASE_URL}/general/chatWithBoat`, data
        );
        return response?.data;
    } catch (error) {
        return { status: "error", data: error };
    }
};
const beaches = [
    {
        name: "Calangute Beach",
        link: "https://sandee.com/india/goa/calangute/calangute-beach",
        image: "https://sandee.com/_next/image?url=https%3A%2F%2Fcdn.sandee.com%2F45291_1650_1100.avif&w=1080&q=75",
        description: "A lively beach in Goa, perfect for water sports and nightlife.",
    },
    {
        name: "Mahabalipuram Beach",
        link: "https://sandee.com/india/tamil-nadu/mahabalipuram/mahabalipuram-beach",
        image: "https://sandee.com/_next/image?url=https%3A%2F%2Fcdn.sandee.com%2F45291_1650_1100.avif&w=1080&q=75",
        description: "Known for its historic sculptures and rock-cut temples.",
    },
    {
        name: "Kovalam Beach",
        link: "https://sandee.com/india/kerala/kovalam/kovalam-beach",
        image: "https://sandee.com/_next/image?url=https%3A%2F%2Fcdn.sandee.com%2F45291_1650_1100.avif&w=1080&q=75",
        description: "Famous for ayurvedic treatments and serene atmosphere.",
    },
    {
        name: "Radhanagar Beach",
        link: "https://sandee.com/india/andaman-and-nicobar-islands/havelock-island/radhanagar-beach",
        image: "https://sandee.com/_next/image?url=https%3A%2F%2Fcdn.sandee.com%2F45291_1650_1100.avif&w=1080&q=75",
        description: "Often cited as one of Asia's best beaches for its beauty.",
    },
    {
        name: "Puri Beach",
        link: "https://sandee.com/india/odisha/puri/puri-beach",
        image: "https://sandee.com/_next/image?url=https%3A%2F%2Fcdn.sandee.com%2F45291_1650_1100.avif&w=1080&q=75",
        description: "A spiritual and tourist hotspot, known for its festival.",
    },
];
// Function to generate pure HTML string
// const generateBeachHTML = () => {
//     return `
//       <p>India's coastline is adorned with many beautiful beaches, each offering a unique experience. Here are some notable beaches across the country:</p>
//       <ul>
//         ${beaches
//             .map(
//                 (beach) => `
//           <li>
//             <a href="${beach.link}" target="_blank" class="text-blue-600 font-semibold hover:underline">
//               ${beach.name}
//             </a>
//             <div class="hidden popover-content">
//               <img src="${beach.image}" class="w-full h-32 object-cover rounded-md" />
//               <h4 class="text-lg font-semibold mt-2">${beach.name}</h4>
//               <p class="text-sm text-gray-600">${beach.description}</p>
//             </div>
//           </li>`
//             )
//             .join("")}
//       </ul>
//       <p>These beaches offer a mixture of natural beauty, spiritual significance, and vibrant local culture, making them must-visit destinations for beach lovers visiting India.</p>
//     `;
// };

const updateMapDirection = (mapData) => {
    if (!mapData?.map) return;
    if (mapData?.lat && mapData?.lng) {
        // console.log(mapData, "gyuhjdbjfdb")
        // Set up marker on the map
        new mapboxgl.Marker({ color: "blue" }).setLngLat([
            mapData.lat,
            mapData.lng,
        ])
            .addTo(mapData?.map?.current)
            .getElement();

        mapData?.map?.current?.flyTo({
            center: [
                mapData.lat,
                mapData.lng,
            ],
            zoom: 17,
            easing(t) {
                return t;
            },
            speed: 1.5, // Adjust speed for smoothness
            curve: 1, // Smoothness of the flight path
            essential: true, // this animation is considered essential with respect to prefers-reduced-motion
        });
    }
}
const transformBeachHTML = (htmlString = "", map, beaches) => {
    // console.log(htmlString, "htmlString", beaches)
    return parse(htmlString, {
        replace: (domNode) => {
            // console.log(domNode, "domNode");
            if (domNode.name === "a") {
                // const linkNode = domNode.children.find((child) => child.name === "a");
                // if (linkNode) {
                const name = domNode?.children?.[0]?.data;
                const url = domNode?.attribs?.href;
                // const localAllData = localStorage.getItem("beachesData") || {};

                const localAllData = beaches || {};
                const matchedKey = Object.keys(localAllData).find(key => {
                    const [nameKey, citySlug, stateSlug, countrySlug] = key.split("||").map(v => v.trim());
                    // console.log(stateSlug, countrySlug, "stateSlug", "countrySlug")
                    // console.log(stateSlug, countrySlug, nameKey, "stateSlug", "countrySlug")
                    return url?.includes(citySlug) && url?.includes(stateSlug) && url?.includes(countrySlug);
                });

                const beachData = matchedKey ? localAllData[matchedKey] : {};
                // console.log(beachData, "beachDatasdsd", matchedKey)
                // console.log(beachData, "beachData", matchedKey, localAllData, url, name)

                //this is for localstorage start======================================
                // const localAllData = beaches || {};
                // console.log(beaches, "beaches", localAllData?.[name])
                // const beachData = Object?.keys(localAllData)?.length && localAllData?.[name] || {};
                // console.log(name, url, domNode, beachData)

                const lat = beachData?.GeoLoc?.coordinates?.[0] ?? 0;
                const long = beachData?.GeoLoc?.coordinates?.[1] ?? 0;
                const handleMouseEnter = () => {
                    // console.log("Enter......", lat, long)
                    if (lat && long) {
                        updateMapDirection({ lat: lat, lng: long, map: map });
                    }
                };
                return (
                    // <li>
                    <div className='flex items-center gap-1'>
                        {url?.includes("sandee.com") && <MapBeachIcon />}
                        {Object?.keys(beachData)?.length ? <Popover
                            openClassName='chatbot'
                            className='chatbot'
                            content={
                                <div className="w-64">
                                    {beachData?.images?.[0]?.imageUrl &&
                                        <div className='relative w-full h-32 object-cover rounded-md'>
                                            <CustomeImage
                                                src={beachData?.images?.[0]?.imageUrl}
                                                alt={beachData?.name || "Sandee Beach"}
                                                fill
                                                className="rounded-md object-cover" /></div>}
                                    <div className='p-3 '>
                                        {beachData?.name && <h4 className="text-lg font-semibold">{beachData?.name}</h4>}
                                        <p className='text-gray-400 text-[13px] mb-[5px] '>{`${beachData?.city?.name ? `${beachData?.city?.name},` : ""} ${beachData?.country?.name || ""}`}</p>
                                        {beachData?.beachDescription?.introduction && isHTML(beachData?.beachDescription?.introduction) ? <div className="line-clamp-3 text-sm text-black" dangerouslySetInnerHTML={{ __html: beachData?.beachDescription?.introduction }}></div> : <p className="line-clamp-3 text-sm text-black">{beachData?.beachDescription?.introduction}</p>}
                                    </div>
                                </div>
                            }
                            trigger="hover"
                            placement="rightTop"
                        >
                            <a
                                href={url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-sandee-blue font-semibold"
                                onMouseEnter={handleMouseEnter}
                            >
                                {name}
                            </a>
                        </Popover> : <a
                            href={url}
                            target="_blank"
                            rel="noopener noreferrer"
                            onMouseEnter={handleMouseEnter}
                            className="text-sandee-blue font-semibold"
                        >
                            {name}
                        </a>}
                    </div>
                    // </li>
                );
                // }
            }
        },
    });
};

// Socket event handlers
const setupSocketHandlers = (socket, setMessages, setIsTyping) => {
    if (!socket) return;

    const handleConnect = () => {
        console.log("Connected to socket:", socket.id);
    };

    const handleChatbotReply = (data) => {
        if (!data?.chunk) return;

        setMessages((prev) => {
            const lastMessage = prev[prev.length - 1];
            if (lastMessage.sender === "bot") {
                const updatedText = lastMessage.text === "typing..."
                    ? data.chunk
                    : lastMessage.text + data.chunk;
                console.log(data, "data")
                // Check if this is the last chunk (you might need to adjust this condition based on your backend)
                if (data.isLastChunk) {
                    setIsTyping(false);
                }

                return [
                    ...prev.slice(0, -1),
                    { sender: "bot", text: updatedText }
                ];
            }
            return prev;
        });
    };

    const handleError = (error) => {
        console.error("Socket error:", error);
        setIsTyping(false);
    };

    const handleConnectError = (err) => {
        console.error("Socket connection error:", err);
        setIsTyping(false);
    };

    // Add event listeners
    socket.on("connect", handleConnect);
    socket.on("chatbot_reply_chunks", handleChatbotReply);
    socket.on("error", handleError);
    socket.on("connect_error", handleConnectError);

    // Return cleanup function
    return () => {
        socket.off("connect", handleConnect);
        socket.off("chatbot_reply_chunks", handleChatbotReply);
        socket.off("error", handleError);
        socket.off("connect_error", handleConnectError);
    };
};

// Message handling utilities
const handleMessageSend = (input, socket, setMessages, setInput, setIsTyping, resetTextarea, map) => {
    if (!input.trim() || !socket) return;

    const inputText = transformBeachHTML(input.trim(), map, {});
    const userMessage = { sender: "user", text: inputText };

    setMessages((prev) => [...prev, userMessage]);
    setInput("");
    setIsTyping(true);
    resetTextarea();

    socket.emit("ask_to_chatbot", { message: input.trim() });
    setMessages((prev) => [...prev, { sender: "bot", text: "typing..." }]);
};

export default function ChatbotFinalsPage() {
    // const [isSidebarOpen, setIsSidebarOpen] = useState(true);
    const [messages, setMessages] = useState([
        { sender: 'bot', text: 'Hey there! Where would you like to go today?' },
        //         {
        //             sender: 'bot', text: `
        //             <h2>Santa Monica</h2>
        // <div class="custome-tooltip group relative inline-block"><h3 class="text-lg font-bold line-clamp-1 mb-0 leading-[24px]">
        //  <a href="https://sandee.com/united-states/california/santa-monica/santa-monica-pier-beach">Santa Monica Pier Beach</a>
        // </h3>
        // <span class="custome-tooltiptext opacity-0 group-hover:opacity-100 
        // transition-opacity duration-300 ease-in-out transform translate-y-2 
        // group-hover:translate-y-0 absolute z-10 text-sm rounded-md text-center 
        // text-black bg-white w-auto -left-0 -top-[calc(100%+(-120px))]  rounded-lg shadow-[0_6px_16px_0_rgba(0,0,0,0.08),_0_3px_6px_-4px_rgba(0,0,0,0.12),_0_9px_28px_8px_rgba(0,0,0,0.05)]">

        // <div role="tooltip">
        //   <div>
        //     <div class="w-64">
        //       <div class="relative w-full h-32 object-cover rounded-md">
        //         <img
        //           alt="Santa Monica Beach"
        //           class="rounded-md object-cover"
        //           loading="lazy"
        //           decoding="async"
        //           sizes="100vw"
        //           srcset="http://sandee-media.s3.amazonaws.com/ugp/59c2f49cb2333932644e3c8b/59ee973afb32b35c6e54e894.jpg"
        //           style="position: absolute; height: 100%; width: 100%; inset: 0px; color: transparent;"
        //         />
        //       </div>
        //      <div class="p-3 text-start">
        //      <p class="text-lg font-semibold pt-0 !mb-0 !mt-0">Santa Monica Beach</p>
        //      <p class="text-gray-400 text-[13px] mb-[5px] ">
        //      Santa Monica, United States</p>
        //      <div class="line-clamp-3 text-sm text-black">Santa Monica Beach, located in Santa Monica, California, is a 3.5-mile stretch of light brown sand along the Pacific coast. It is renowned for its vibrant beach culture, offering a mix of recreational activities like surfing, beach volleyball, and rollerblading. The beach is divided into three main sections: North Beach, Pier Beach, and South Beach. The iconic Santa Monica Pier, a historic landmark, features an amusement park and various dining options. The beach attracts millions of visitors annually and is celebrated for its accessibility and environmental stewardship.<br> Santa Monica Beach is not only a popular tourist destination but also a significant cultural and historical site, having been featured in numerous films and TV shows. Its scenic views and lively atmosphere make it a favorite among locals and visitors alike.<br> The beach's proximity to Los Angeles and its integration with the city's infrastructure make it an accessible and enjoyable destination for a wide range of activities and interests.
        //      </div>
        //      </div>
        //     </div>
        //   </div>
        // </div>
        // </span></div>
        //             ` },
        //         {
        //             sender: 'bot', text: `<h2>Santa Monica Beach</h2>
        // <div class="custome-tooltip group relative inline-block">
        //   <h3 class="text-lg font-bold line-clamp-1 mb-0 leading-[24px]">
        //     <a href="https://sandee.com/united-states/california/santa-monica/santa-monica-beach">Santa Monica Beach</a>
        //   </h3>
        //   <span class="custome-tooltiptext opacity-0 group-hover:opacity-100 transition-opacity duration-300 ease-in-out transform translate-y-2 group-hover:translate-y-0 absolute z-10 text-sm rounded-md text-center text-black bg-white w-auto -left-0 -top-[calc(100%+(-120px))] rounded-lg shadow-[0_6px_16px_0_rgba(0,0,0,0.08),_0_3px_6px_-4px_rgba(0,0,0,0.12),_0_9px_28px_8px_rgba(0,0,0,0.05)]">
        //     <div role="tooltip">
        //       <div>
        //         <div class="w-64">
        //           <div class="relative w-full h-32 object-cover rounded-md">
        //             <img alt="Santa Monica Beach" class="rounded-md object-cover" loading="lazy" decoding="async" sizes="100vw" srcset="http://sandee-media.s3.amazonaws.com/ugp/59c2f49cb2333932644e3c8b/59ee973afb32b35c6e54e894.jpg" style="position: absolute; height: 100%; width: 100%; inset: 0px; color: transparent;" />
        //           </div>
        //           <div class="p-3 text-start">
        //             <p class="text-lg font-semibold pt-0 !mb-0 !mt-0">Santa Monica Beach</p>
        //             <p class="text-gray-400 text-[13px] mb-[5px] ">California,United States</p>
        //             <div class="line-clamp-3 text-sm text-black">A sprawling white sand beach famous for its iconic pier and lively atmosphere, offering a quintessential Southern California experience.</div>
        //           </div>
        //         </div>
        //       </div>
        //     </div>
        //   </span>
        // </div>

        // <h3>Overview</h3>
        // <p>Santa Monica Beach, located in Southern California, is a quintessential coastal paradise that stretches along the Pacific Ocean. Known for its vibrant atmosphere, this beach is a popular destination for both locals and tourists alike. The beach features a wide expanse of soft sand and offers stunning views of the Pacific and the Santa Monica Pier.</p>
        // <p>Visitors can enjoy various outdoor activities such as volleyball, surfing, and biking along the beachfront path. The adjacent Santa Monica Pier is an attraction itself, with amusement rides, eateries, and shops that enrich the beach-going experience.</p>

        // <h3>Activities</h3>
        // <p>Santa Monica Beach is perfect for a variety of activities, from lounging on the sun-soaked sand to engaging in active pursuits. Surfing and paddleboarding are popular in the designated areas, while the Marvin Braude Bike Trail offers a scenic route for cyclists and skaters. Visitors also enjoy playing beach volleyball on the numerous courts available or simply taking a relaxing stroll along the iconic pier.</p>

        // <h3>Unique Features</h3>
        // <p>One of the most distinctive features of Santa Monica Beach is the historic Santa Monica Pier. The pier houses the Pacific Park, an amusement park with a solar-powered Ferris wheel and an arcade, making it a delightful attraction for all ages. Another unique aspect is the original Muscle Beach, located south of the pier, famous for its outdoor gymnastics equipment where fitness enthusiasts display their talents.</p>

        // <h3>Best Time to Visit</h3>
        // <p>The best time to visit Santa Monica Beach is from late spring through early fall, when the weather is warm and the skies are clear. July to September offers the ideal conditions for swimming and sunbathing.</p>

        // <h3>Nearby Hotels & Restaurants</h3>
        // <p>Near Santa Monica Beach, you can find a variety of accommodations and dining options. <a href="https://www.shuttersonthebeach.com/">Shutters on the Beach</a> and <a href="https://www.loewshotels.com/santa-monica">Loews Santa Monica Beach Hotel</a> are among the top luxury hotels with ocean views. For dining, check out <a href="https://www.thealbright.com/">The Albright</a>, known for its sustainable seafood, and <a href="https://blueplatesantamonica.com/">Blue Plate Taco</a>, which offers a modern Mexican menu with beachside views.</p>

        // <h3>Travel Tips</h3>
        // <p>Visitors should be aware that parking near Santa Monica Beach can be challenging, especially during peak times. Public parking lots are available but filling up quickly, so arriving early or using public transportation is advisable. Additionally, be mindful of safety regulations and lifeguard postings when swimming or engaging in water sports.</p>

        // <p>Explore this iconic beach and experience the essence of Southern California's beach culture!</p>`},

        // {
        //     sender: "user", text: <>{transformBeachHTML(`<p>Surat offers some delightful beaches that are perfect for relaxing by the Arabian Sea. Here are the top beaches you should consider visiting:</p>
        // <ul>
        //   <li><a href="https://sandee.com/india/gujarat/surat/pantai-jodoh">Pantai Jodoh</a>: A serene spot known for its beautiful sunsets and tranquil ambiance.</li>
        //   <li><a href="https://sandee.com/india/gujarat/surat/pantai-tete">Pantai Tete</a>: Ideal for those looking to escape the hustle and bustle and enjoy some quiet time by the sea.</li>
        //   <li><a href="https://sandee.com/india/gujarat/surat/tanjung-karang">Tanjung Karang</a>: Known for its clear waters and smooth sands, perfect for a day out with family or friends.</li>
        // </ul>
        // <p>Each beach offers a unique experience, promising plenty of beautiful views and relaxing moments.</p>`)}</>

        // }
    ]);
    const map = useRef(null);
    const textareaRef = useRef(null);
    const [showMessages, setShowMessages] = useState(false);
    const [input, setInput] = useState('');
    const [isCollapsed, setIsCollapsed] = useState(false);
    const [isMapCollapsed, setIsMapCollapsed] = useState(false);
    const [isMapShow, setIsMapShow] = useState(false);
    const [isTyping, setIsTyping] = useState(false);
    const [socket, setSocket] = useState(null);
    const chatEndRef = useRef(null); // Auto scroll reference
    useEffect(() => {
        // Trigger the animation when messages change
        if (messages?.findIndex((msg) => msg.sender === 'user') !== -1) {
            setShowMessages(true);
        } else {
            setShowMessages(false);
        }
    }, [messages]);
    useEffect(() => {
        const socketInstance = io("http://localhost:3005", {
            autoConnect: false,
            transports: ['websocket'],
            reconnection: true,
            reconnectionAttempts: 5,
            reconnectionDelay: 1000,
            reconnectionDelayMax: 5000,
        });

        socketInstance.connect();
        setSocket(socketInstance);

        return () => {
            socketInstance.disconnect();
        };
    }, []);

    // Socket event handlers setup
    useEffect(() => {
        const cleanup = setupSocketHandlers(socket, setMessages, setIsTyping);
        return cleanup;
    }, [socket]);

    // Message handling
    const sendMessage = () => {
        handleMessageSend(input, socket, setMessages, setInput, setIsTyping, resetTextarea, map);
    };

    // Auto-scroll to bottom when messages update
    useEffect(() => {
        chatEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }, [messages]);
    const autoResize = () => {
        if (textareaRef.current) {
            textareaRef.current.style.height = "auto"; // Reset height to auto
            textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`; // Set new height
        }
    };
    const handleInputChange = (e) => {
        setInput(e.target.value);
        autoResize();
    };

    const resetTextarea = () => {
        if (textareaRef.current) {
            textareaRef.current.style.height = "auto"; // Reset to original size
        }
    };

    const toggleSidebar = () => {
        setIsCollapsed(pr => !pr);
    }
    const toggleMapSidebar = () => {
        setIsMapCollapsed(pr => !pr);
        // setIsMapShow(pr => !pr);
    }
    const toggleMapShow = () => {
        setIsMapShow(pr => !pr);
    }
    // console.log(convertMarkdownToHTML(""), "convertMarkdownToHTML")
    // Function to simulate the typing effect
    const typeMessage = (message, beaches) => {
        let index = 0;

        // Check if the message is HTML or plain text
        const isHtmlMessage = isHTML(message);
        // If message is full HTML document, extract only body content
        let processedMessage = message;
        if (isHtmlMessage && message?.includes('<!DOCTYPE html>')) {
            const bodyMatch = message?.match(/<body[^>]*>([\s\S]*)<\/body>/i);
            if (bodyMatch && bodyMatch?.[1]) {
                processedMessage = bodyMatch?.[1];
            }
        }

        const plainText = isHtmlMessage ? processedMessage : `<div>${message}</div>`; // Use plain text for typing
        const convertedReactElements = transformBeachHTML(processedMessage, map, beaches); // Convert to React elements
        console.log(convertedReactElements, "convertedReactElements", plainText, processedMessage)
        setMessages((prev) => {
            let lastMessage = prev[prev.length - 1];

            // If the last message is from the bot and ends with "...", reset it
            if (lastMessage.sender === "bot" && lastMessage.text.endsWith("...")) {
                lastMessage.text = "";
            }

            // Get the partially typed plain text
            const typedText = plainText.substring(0, index + 1);

            // Update the last message with the partially typed plain text
            return [
                ...prev.slice(0, -1), // Keep all messages except the last one
                { sender: "bot", text: processedMessage }, // Update the last message with the partially typed text
            ];
        });
        setIsTyping(false); // Stop the typing indicator
    };

    return (
        <div className="flex h-screen w-full border-t-[1px] border-gray-200"
        >
            {/* Sidebar */}
            <SideBar isCollapsed={isCollapsed} toggleSidebar={toggleSidebar} setMessages={setMessages} />

            {/* Main Chat Area */}
            <main className={`flex flex-col bg-white transition-all duration-300 ${!isMapShow ? "w-full" : isMapCollapsed && isCollapsed ? "w-0" : isMapCollapsed ? 'w-0' : isCollapsed ? "w-[40%]" : isMapShow ? 'w-[50%]' : 'w-[100%]'}`}>
                <div
                    className='relative flex justify-end text-end cursor-pointer '
                    onClick={() => {
                        toggleMapShow()
                    }}
                >
                    <div className='bg-[#e7fdff] m-3 text-sandee-blue hover:bg-[#e7fdff] p-2 rounded-full transition-all duration-300'>
                        <EarthSideBarIcon
                            className=" "
                            fill={`${isMapShow ? "#00AAE3" : "black"}`}
                            onMou
                        // stroke={`${isMapShow ? "#00AAE3" : "currentColor"}`}
                        />
                    </div>
                </div>
                <div className="flex-1 p-4 overflow-y-auto space-y-4 " style={{ "--chat-form-width": "46.5rem" }}>
                    <div className='relative mx-auto max-w-[--chat-form-width]'>

                        {showMessages ? (
                            <>
                                {messages?.map((msg, index) => (
                                    <><div key={index} className={`group/msg relative z-0 flex gap-[--avatar-gap] leading-[--msg-leading] !m-0 p-3 ${msg.sender !== 'user' ? "hover:bg-[#f9f9f9] hover:rounded-3xl" : ""} `}>
                                        <div className="absolute inset-[calc(var(--hover-offset)*-1)] z-0 rounded-3xl transition-colors group-hover/msg:bg-subtle"></div>
                                        <div aria-hidden="true" className="relative z-1 mt-[calc((var(--msg-leading)-var(--msg-avatar-size))/2)] @lg:mt-0">
                                            <span data-slot="avatar" className="relative flex shrink-0 rounded-full font-medium size-[--avatar-size-sm] text-xs">
                                                <span className="flex size-full select-none items-center justify-center rounded-full uppercase bg-foreground text-background">
                                                    {msg.sender === 'user' ? <UserIcon width="35px" height="35px" className={`inline p-2 rounded-full ${msg.sender === 'user' ? "bg-[#e7fdff]" : "bg-[#e7fdff]"}  mr-2 text-gray-600`} /> :
                                                        <MultiStar width="35px" height="35px" className={`inline p-2 rounded-full ${msg.sender === 'user' ? "bg-black" : "bg-sandee-blue"}  mr-2 text-white`} />}
                                                </span>
                                            </span>
                                        </div>
                                        <div className="flex min-w-0 flex-1 flex-col pt-[calc((var(--msg-avatar-size)-var(--msg-leading))/2)]">
                                            <div className="flex flex-col gap-4">
                                                {msg?.text == "typing..." ?
                                                    <div className="flex h-10 cursor-wait items-center justify-start px-4">
                                                        <span className="flex items-center gap-[1em] text-[8px]">
                                                            <span className="size-[1em] dot-animation rounded-full bg-sandee-blue"></span>
                                                            <span className="size-[1em] dot-animation delay-200 rounded-full bg-sandee-blue"></span>
                                                            <span className="size-[1em] dot-animation delay-400 rounded-full bg-sandee-blue"></span>
                                                        </span>
                                                    </div> :
                                                    msg?.sender == "bot" ? <div className="boat-answer ck-content rounded-md p-3 prose-chat prose relative break-words e2e:message-bot">
                                                        {msg?.text != 'Hey there! Where would you like to go today?' ? <div>
                                                            {/* <DynamicTooltipRenderer htmlString={msg?.text} /> */}
                                                            <HeaderTab tab1={<div
                                                                className="boat-answer ck-content rounded-md p-3 prose-chat prose relative break-words e2e:message-bot"
                                                                dangerouslySetInnerHTML={{ __html: msg?.text }}
                                                            />} tab2={<>
                                                            </>} />
                                                        </div> :
                                                            <div>{msg?.text}</div>}
                                                    </div>
                                                        : <div
                                                            className="boat-answer ck-content rounded-md p-3 prose-chat prose relative break-words e2e:message-bot"
                                                            dangerouslySetInnerHTML={{ __html: msg?.text }}
                                                        />
                                                }
                                                {/* <p>{msg?.text}</p>
                                            </div> */}
                                            </div>
                                        </div>
                                    </div>
                                        {/* <div
                                        data-orientation="horizontal"
                                        role="none"
                                        data-slot="separator"
                                        className="shrink-0 h-px w-full  bg-gray-300 !my-0"
                                    ></div> */}
                                    </>
                                ))}

                                <div className='!m-0' ref={chatEndRef} /> {/* Auto scroll reference */}
                            </>
                        ) : (
                            <div className='pt-20 slide-in'>
                                <h2 className="mb-5 text-pretty text-2xl font-semibold tracking-tight sm:text-3xl xl:text-4xl">Where to today?</h2>
                                <div className="flex justify-start items-start gap-4">
                                    <span
                                        className="relative flex shrink-0 rounded-full font-medium text-xs">
                                        <span className="flex select-none items-center justify-center  uppercase ">
                                            <MultiStar className="m-2 bg-sandee-blue text-white p-2 rounded-full" />
                                        </span>
                                    </span>
                                    <p className="text-pretty text-md font-medium sm:text-balance sm:text-lg">
                                        Hey there, where would you like to go? I&apos;m here to assist you in planning your experience. Ask me anything travel related.
                                    </p>
                                </div>
                            </div>

                        )}
                    </div>
                </div>
                <div
                    className="p-4 bg-white shadow flex items-center gap-2 w-full"
                    style={{ "--chat-form-width": "46.5rem" }}
                >
                    {/* <div className="absolute inset-0 overflow-hidden rounded-t-2xl bg-background/80 backdrop-blur-md"></div> */}
                    <div className="rounded-t-2xl sm:px-5 w-full">
                        <div className="relative mx-auto max-w-[--chat-form-width]">
                            <div className="relative flex cursor-text gap-2 transition-colors hover:border-gray-7 rounded-t-2xl border-t px-container py-3 sm:rounded-3xl sm:border-2 sm:border-gray-5 sm:px-4 xs:gap-x-1 sm:!border-current">
                                <div className="mr-1 w-full flex items-center">
                                    <textarea
                                        ref={textareaRef}
                                        rows="1"
                                        className="scrollbar-hidden resize-none bg-transparent outline-none placeholder:text-muted focus:placeholder:text-gray-8 flex-1 disabled:text-gray-8 text-md xs:self-center sm:basis-full"
                                        name="message"
                                        placeholder="Ask anything..."
                                        autoComplete="off"
                                        maxLength="600"
                                        value={input}
                                        onChange={handleInputChange}
                                        onKeyDown={(e) => {
                                            if (isTyping) return;
                                            if (e.key === "Enter" && !e.shiftKey) {
                                                e.preventDefault(); // Prevents adding a new line in the textarea
                                                sendMessage();
                                            }
                                        }}
                                        style={{ height: "auto", overflow: "hidden" }} // Prevents scrollbar flickering
                                    />
                                </div>
                                <div className="flex items-start">
                                    <button
                                        className="group group/button relative z-0 border border-transparent inline-flex justify-center items-center rounded-full font-medium gap-[.3em] disabled:pointer-events-none transition-colors text-center py-[.25em] text-balance bg-primary text-primary-foreground hover:bg-primary-hover data-[state=open]:bg-primary-hover disabled:opacity-30 text-2xs leading-[1.125] px-0 shrink-0"
                                        onClick={sendMessage}
                                        disabled={isTyping}
                                    >
                                        <PlaneIcon className={`${input?.length > 0 ? "bg-sandee-blue text-white" : "text-black"} transition-all duration-300 rounded-full p-1`} />
                                    </button>
                                </div>
                            </div>
                            {/* <div className="absolute bottom-full right-container mb-4 rounded-full sm:right-[1.125rem] split:right-1/2 split:translate-x-1/2 pointer-events-none">
                                <div className="-mr-0.5 rounded-full border border-gray-5 bg-background shadow-[0_0_10px_rgba(0,0,0,.1)] transition-[transform,opacity] translate-y-2 opacity-0">
                                    <button
                                        data-variant="ghost"
                                        data-loading="false"
                                        className="group group/button relative z-0 border border-transparent inline-flex justify-center items-center rounded-full font-medium gap-[.3em] disabled:pointer-events-none disabled:opacity-50 transition-colors text-center py-[.25em] text-balance bg-transparent hover:bg-foreground/5 data-[state=open]:bg-foreground/5 data-[state=active]:border-current text-xs min-h-[--button-sm-size] leading-[1.125] shrink-0 size-[--button-sm-size] px-2.5"
                                        type="button"
                                    >
                                        <span aria-hidden="true" className="absolute left-1/2 top-1/2 size-9 -translate-x-1/2 -translate-y-1/2"></span>
                                        <span className="contents">
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                viewBox="0 0 24 24"
                                                fill="none"
                                                stroke="currentColor"
                                                strokeWidth="1.75"
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                className="shrink-0 transform-cpu size-[1em] text-[1.625em]"
                                            >
                                                <path d="M12 4.052v16.714M7.5 16.266l4.5 4.5 4.5-4.5"></path>
                                            </svg>
                                            <span className="sr-only">See latest messages</span>
                                        </span>
                                    </button>
                                </div>
                            </div> */}
                        </div>
                        <div className="relative text-center text-3xs leading-tight empty:hidden sm:text-2xs bg-gray-2 py-1.5 sm:bg-transparent">
                            <p className="text-muted text-sm flex items-center justify-center gap-1">
                                <InfoIcon2 width={17} height={17} />
                                Sandee can make mistakes. Check important info.
                            </p>
                        </div>
                    </div>
                </div>
            </main>

            {/* Right Map Section */}
            {/* <aside className={`${isCollapsed && isMapCollapsed ? "w-full" : isCollapsed ? "w-[50%]" : isMapCollapsed ? "w-full" : "w-2/5"} transition-all duration-300 hidden lg:block bg-gray-200 relative`}> */}
            <aside
                // className="hidden lg:block bg-gray-200 relative overflow-hidden"
                // style={{ width: `${100 - dividerPosition}%`, minWidth: '30%' }}
                className={`${!isMapShow ? "w-0" : isCollapsed && isMapCollapsed ? "w-full" : isCollapsed ? "w-[55%]" : isMapCollapsed ? "w-full" : "w-2/5"} transition-all duration-300   bg-gray-200 relative`}
            >
                {/* ${isMapShow ? "hidden lg:block" : "hidden"} */}
                {/* <iframe
                    className="w-full h-full"
                    src="https://www.google.com/maps/embed/v1/place?key=YOUR_GOOGLE_MAPS_API_KEY&q=Ahmedabad,India"
                    allowFullScreen
                ></iframe> */}
                <MapChatBot
                    toggleMapSidebar={toggleMapSidebar}
                    isMapCollapsed={isMapCollapsed}
                    isCollapsed={isCollapsed}
                    isMapShow={isMapShow}
                    latStr={null}
                    longStr={null}
                    map={map}
                    popupBeachData={
                        {}
                    }
                    isFilter={false}

                // isSharkToggle={true}
                // isNudeToggle={true}
                />
            </aside>
        </div>
    );
}
