'use client';

import { useEffect, useMemo, useRef, useState } from 'react';
import { CloseIcon, EarthSideBarIcon, ExternalLinkIcon, InfoIcon2, MapBeachIcon, MultiStar, PlaneIcon, UserIcon } from '../social-icons/icons';
import Image from 'next/image';
import mapboxgl from "!mapbox-gl"; // eslint-disable-line import/no-webpack-loader-syntax
import MapChatBot from './MapChatBot';
import axios from 'axios';
import { API_BASE_URL, convertMarkdownToHTML, defaultImage, isHTML, sanitizeHtml, storeBeachesData } from '@/helper/functions';
import { Drawer, Popover, Space } from 'antd';
import "./chatbot.css"
import SideBar from './sideBar';
import parse from "html-react-parser";
import HeaderTab from './HeaderTab';
import { io } from 'socket.io-client';
import ImageTab from './ImageTab';
import Link from 'next/link';
import CustomeImage from '../Common/CustomeImage';
mapboxgl.accessToken = process.env.NEXT_PUBLIC_MAPBOX_API_KEY;

export const GetChatResponse = async (data = {}) => {
    try {
        const response = await axios.post(
            `${API_BASE_URL}/general/chatWithBoat`, data
        );
        return response?.data;
    } catch (error) {
        return { status: "error", data: error };
    }
};

const updateMapDirection = (mapData) => {
    if (!mapData?.map) return;
    if (mapData?.lat && mapData?.lng) {
        new mapboxgl.Marker({ color: "blue" }).setLngLat([
            mapData.lat,
            mapData.lng,
        ])
            .addTo(mapData?.map?.current)
            .getElement();

        mapData?.map?.current?.flyTo({
            center: [
                mapData.lat,
                mapData.lng,
            ],
            zoom: 17,
            easing(t) {
                return t;
            },
            speed: 1.5,
            curve: 1,
            essential: true,
        });
    }
}

const transformBeachHTML = (htmlString = "", map, beaches, openDrawerWithUrl) => {
    const localAllData = beaches || {};
    let matchedBeachImages = [];

    const processedHtml = htmlString
        .replace(/\n\n/g, '<br />')
        .replace(/\\n/g, '<br />');

    const parsedHtml = parse(processedHtml, {
        replace: (domNode) => {
            if (domNode.name === "a" && domNode.attribs?.id === "beach-hover-link") {
                const beachName = domNode?.children?.[0]?.data;
                const url = domNode?.attribs?.href;
                if (!beachName) return;

                const matchedKey = Object.keys(localAllData).find(key => {
                    const [nameKey, citySlug, stateSlug, countrySlug] = key.split("||").map(v => v.trim());
                    return beachName?.includes(nameKey) && url?.includes(citySlug) && url?.includes(stateSlug) && url?.includes(countrySlug);
                });

                const beachData = matchedKey ? localAllData[matchedKey] : null;
                if (beachData && beachData?.id) {
                    matchedBeachImages.push(beachData?.id); // ✅ accumulate images from all beaches
                }

                const lat = beachData?.GeoLoc?.coordinates?.[0] ?? 0;
                const long = beachData?.GeoLoc?.coordinates?.[1] ?? 0;
                const description = beachData?.beachDescription?.introduction;
                const handleMouseEnter = () => {
                    if (lat && long) {
                        updateMapDirection({ lat: lat, lng: long, map: map });
                    }
                };

                return (
                    <div className='inline-flex items-center gap-1'>
                        {url?.includes("sandee.com") && <MapBeachIcon />}
                        <Popover
                            content={
                                <div className="w-64">
                                    {beachData?.images?.[0]?.imageUrl &&
                                        <div className='relative w-full h-32 object-cover rounded-md'>
                                            <CustomeImage
                                                src={beachData?.images?.[0]?.imageUrl ?? defaultImage}
                                                alt={beachData?.name || "Sandee Beach"}
                                                fill
                                                className="rounded-md object-cover" />
                                        </div>
                                    }
                                    <div className='p-3'>
                                        <h4 className="text-lg font-semibold">{beachData?.name}</h4>
                                        <p className='text-gray-400 text-[13px] mb-[5px]'>
                                            {`${beachData?.city?.name ? `${beachData?.city?.name},` : ""} ${beachData?.country?.name || ""}`}
                                        </p>
                                        {description ? (
                                            isHTML(description) ? <div className="line-clamp-3 text-sm text-black" dangerouslySetInnerHTML={{ __html: description }} />
                                                : <div className="line-clamp-3 text-sm text-black">
                                                    {description}
                                                </div>
                                        ) : ""}
                                    </div>
                                </div>
                            }
                            trigger="hover"
                            placement="rightTop"
                        >
                            <span
                                className="text-sandee-blue font-semibold cursor-pointer"
                                onClick={() => openDrawerWithUrl(`${url || "http://sandee.com"}`)}
                                // onClick={() => openDrawerWithUrl(`http://localhost:3000/india/gujarat/beyt-dwarka/beyt-dwarka-beach`)}
                                onMouseEnter={handleMouseEnter}
                            >
                                {beachName}
                            </span>
                            {/* <a
                                className="text-sandee-blue font-semibold cursor-pointer"
                                href={`${url}`}
                                target='_blank'
                                onMouseEnter={handleMouseEnter}
                            >
                                {beachName}
                            </a> */}
                        </Popover>
                    </div>
                );
            }
        },
    });

    return { parsedHtml, matchedBeachImages };
};

const setupSocketHandlers = (socket, setMessages, setIsTyping, map, beaches, setBeachesData) => {
    if (!socket) return;

    const handleBeachesResponse = (data) => {
        // console.log("Beaches response received:", data);
        if (data?.beaches && Array.isArray(data.beaches)) {
            setBeachesData(prevData => ({
                ...prevData,
                ...data.beaches.reduce((acc, beach) => {
                    acc[`${beach.name} || ${beach.citySlug} || ${beach.stateSlug} || ${beach.countrySlug}`] = beach;
                    return acc;
                }, {})
            }));
        }
    };

    const handleChatbotReply = (data) => {
        // Check if this is the last chunk
        // console.log(data, "data")
        if (data?.isLastChunk) {
            // console.log("Ending typing...");
            setIsTyping(false);
        }
        if (!data?.chunk) return;
        // Handle beaches array if present in response
        setMessages((prev) => {
            const lastMessage = prev[prev.length - 1];

            if (lastMessage.sender === "bot") {
                let updatedText;

                if (lastMessage.text === "typing...") {
                    updatedText = data.chunk;
                } else {
                    updatedText = lastMessage.text + data.chunk;
                }



                // console.log(updatedText, " updatedText")
                return [
                    ...prev.slice(0, -1),
                    {
                        sender: "bot",
                        text: updatedText,
                        // Store raw HTML for parsing later
                        rawHtml: updatedText
                    }
                ];
            }
            return prev;
        });
    };

    const handleConnect = () => {
        console.log("Connected to socket:", socket.id);
    };

    const handleError = (error) => {
        console.error("Socket error:", error);
        setIsTyping(false);
    };

    const handleConnectError = (err) => {
        console.error("Socket connection error:", err);
        setIsTyping(false);
    };

    // Add event listeners
    socket.on("connect", handleConnect);
    socket.on("chatbot_reply_chunks", handleChatbotReply);
    socket.on("chatbot_reply_beaches", handleBeachesResponse); // Add this line
    socket.on("error", handleError);
    socket.on("connect_error", handleConnectError);

    // Return cleanup function
    return () => {
        socket.off("connect", handleConnect);
        socket.off("chatbot_reply_chunks", handleChatbotReply);
        socket.off("chatbot_reply_beaches", handleBeachesResponse); // Add this line
        socket.off("error", handleError);
        socket.off("connect_error", handleConnectError);
    };
};

export default function ChatbotFinals2Page() {
    const [messages, setMessages] = useState([
        { sender: 'bot', text: 'Hey there! Where would you like to go today?' },
    ]);
    const map = useRef(null);
    const textareaRef = useRef(null);
    const [showMessages, setShowMessages] = useState(false);
    const [input, setInput] = useState('');
    const [isCollapsed, setIsCollapsed] = useState(false);
    const [isMapCollapsed, setIsMapCollapsed] = useState(false);
    const [isMapShow, setIsMapShow] = useState(false);
    const [isTyping, setIsTyping] = useState(false);
    const [socket, setSocket] = useState(null);
    const chatEndRef = useRef(null);
    const [beachesData, setBeachesData] = useState({});
    const [drawerVisible, setDrawerVisible] = useState(false);
    const [iframeUrl, setIframeUrl] = useState('');

    const openDrawerWithUrl = (url) => {
        setIframeUrl(url);
        setDrawerVisible(true);
    };
    const sessionId = useMemo(() =>
        Date.now().toString(36) + Math.random().toString(36).substr(2, 5),
        []);
    const closeDrawer = () => {
        setDrawerVisible(false);
        setIframeUrl('');
    };
    useEffect(() => {
        // console.log(API_BASE_URL.replace('/api/v1', ''), "localhost:3005")
        const socketInstance = io(API_BASE_URL.replace('/api/v1', ''), {
            autoConnect: false,
            transports: ['websocket'],
            reconnection: true,
            reconnectionAttempts: 5,
            reconnectionDelay: 1000,
            reconnectionDelayMax: 5000,
        });

        socketInstance.connect();
        setSocket(socketInstance);

        return () => {
            socketInstance.disconnect();
        };
    }, []);
    useEffect(() => {
        // Load beaches data from localStorage or API
        const loadBeachesData = async () => {
            const data = await storeBeachesData();
            setBeachesData(data);
        };
        loadBeachesData();
    }, []);

    useEffect(() => {
        const cleanup = setupSocketHandlers(socket, setMessages, setIsTyping, map, beachesData, setBeachesData);
        return cleanup;
    }, [socket, beachesData]);

    useEffect(() => {
        chatEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }, [messages]);

    useEffect(() => {
        if (messages?.findIndex((msg) => msg.sender === 'user') !== -1) {
            setShowMessages(true);
        } else {
            setShowMessages(false);
        }
    }, [messages]);

    const autoResize = () => {
        if (textareaRef.current) {
            textareaRef.current.style.height = "auto";
            textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
        }
    };

    const handleInputChange = (e) => {
        setInput(e.target.value);
        autoResize();
    };

    const resetTextarea = () => {
        if (textareaRef.current) {
            textareaRef.current.style.height = "auto";
        }
    };

    const handleNewChat = () => {
        setMessages([
            // { sender: 'bot', text: 'Hey there! Where would you like to go today?' },
        ]);
        setInput('');
        setIsTyping(false);
        resetTextarea();
    }
    const sendMessage = () => {
        if (!input.trim() || !socket || isTyping) return;
        const userMessage = { sender: "user", text: input.trim() };
        setMessages((prev) => [...prev, userMessage]);
        setInput("");
        setIsTyping(true);
        resetTextarea();
        // Send both events with the same message
        const messageData = {
            sessionId: sessionId,
            message: input.trim()
        };
        // console.log(messageData)
        socket.emit("ask_to_chatbot", messageData);
        // socket.emit("ask_to_chatbot", messageData);
        setMessages((prev) => [...prev, { sender: "bot", text: "typing..." }]);
    };

    const toggleSidebar = () => {
        setIsCollapsed(pr => !pr);
    };

    const toggleMapSidebar = () => {
        setIsMapCollapsed(pr => !pr);
    };

    const toggleMapShow = () => {
        setIsMapShow(pr => !pr);
    };

    return (
        <div className="flex h-screen w-full border-t-[1px] border-gray-200">
            {/* Sidebar */}
            <SideBar
                isCollapsed={isCollapsed}
                toggleSidebar={toggleSidebar}
                setMessages={setMessages}
                handleNewChat={handleNewChat} />

            {/* Main Chat Area */}
            <main className={`flex flex-col bg-white transition-all duration-300 ${!isMapShow ? "w-full" : isMapCollapsed && isCollapsed ? "w-0" : isMapCollapsed ? 'w-0' : isCollapsed ? "w-[40%]" : isMapShow ? 'w-[50%]' : 'w-[100%]'}`}>
                <div
                    className='relative flex justify-end text-end cursor-pointer'
                    onClick={toggleMapShow}
                >
                    <div className='bg-[#e7fdff] m-3 text-sandee-blue hover:bg-[#e7fdff] p-2 rounded-full transition-all duration-300'>
                        <EarthSideBarIcon
                            fill={`${isMapShow ? "#00AAE3" : "black"}`}
                        />
                    </div>
                </div>
                <div className="flex-1 p-4 overflow-y-auto space-y-4" style={{ "--chat-form-width": "46.5rem" }}>
                    <div className='relative mx-auto max-w-[--chat-form-width]'>
                        {showMessages ? (
                            <>
                                {messages?.map((msg, index) => (
                                    <div key={index} className={`group/msg relative z-0 flex gap-[--avatar-gap] leading-[--msg-leading] !m-0 p-3 ${msg.sender !== 'user' ? "hover:bg-[#f9f9f9] hover:rounded-3xl" : ""}`}>
                                        <div className="absolute inset-[calc(var(--hover-offset)*-1)] z-0 rounded-3xl transition-colors group-hover/msg:bg-subtle"></div>
                                        <div aria-hidden="true" className="relative z-1 mt-[calc((var(--msg-leading)-var(--msg-avatar-size))/2)] @lg:mt-0">
                                            <span className="relative flex shrink-0 rounded-full font-medium size-[--avatar-size-sm] text-xs">
                                                <span className="flex size-full select-none items-center justify-center rounded-full uppercase bg-foreground text-background">
                                                    {msg.sender === 'user' ?
                                                        <UserIcon width="35px" height="35px" className="inline p-2 rounded-full bg-[#e7fdff] mr-2 text-gray-600" /> :
                                                        <MultiStar width="35px" height="35px" className="inline p-2 rounded-full bg-sandee-blue mr-2 text-white" />
                                                    }
                                                </span>
                                            </span>
                                        </div>
                                        <div className="flex min-w-0 flex-1 flex-col pt-[calc((var(--msg-avatar-size)-var(--msg-leading))/2)]">
                                            <div className="flex flex-col gap-4">
                                                {msg?.text === "typing..." ? (
                                                    <div className="flex h-10 cursor-wait items-center justify-start px-4">
                                                        <span className="flex items-center gap-[1em] text-[8px]">
                                                            <span className="size-[1em] dot-animation rounded-full bg-sandee-blue"></span>
                                                            <span className="size-[1em] dot-animation delay-200 rounded-full bg-sandee-blue"></span>
                                                            <span className="size-[1em] dot-animation delay-400 rounded-full bg-sandee-blue"></span>
                                                        </span>
                                                    </div>
                                                ) : msg?.sender === "bot" ? (
                                                    <div className="boat-answer ck-content rounded-md p-3 prose-chat prose relative break-words e2e:message-bot">
                                                        {msg?.text !== 'Hey there! Where would you like to go today?' ? (
                                                            <div>
                                                                {(() => {
                                                                    const { parsedHtml, matchedBeachImages } = transformBeachHTML(msg?.text, map, beachesData, openDrawerWithUrl);
                                                                    return (
                                                                        <HeaderTab
                                                                            tab1={<>{parsedHtml}</>}
                                                                            beachIds={matchedBeachImages}
                                                                            tab2={matchedBeachImages?.length > 0 ? matchedBeachImages
                                                                                //  <><ImageTab MultipleImages={matchedBeachImages} /></>
                                                                                : null
                                                                            }
                                                                        />
                                                                    );
                                                                })()}
                                                            </div>
                                                        ) : (
                                                            <div>{msg?.text}</div>
                                                        )}
                                                    </div>
                                                ) : (
                                                    <div className="boat-answer ck-content rounded-md p-3 prose-chat prose relative break-words e2e:message-bot">
                                                        {msg?.text}
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                ))}
                                <div className='!m-0' ref={chatEndRef} />
                            </>
                        ) : (
                            <div className='pt-8 slide-in'>
                                <h2 className="mb-5 text-pretty text-2xl font-semibold tracking-tight sm:text-3xl xl:text-4xl">Where to today?</h2>
                                <div className="flex justify-start items-start gap-4">
                                    <span className="relative flex shrink-0 rounded-full font-medium text-xs">
                                        <span className="flex select-none items-center justify-center uppercase">
                                            <MultiStar className="m-2 bg-sandee-blue text-white p-2 rounded-full" />
                                        </span>
                                    </span>
                                    <p className="text-pretty text-md font-medium sm:text-balance sm:text-lg">
                                        Hey there, where would you like to go? I&apos;m here to assist you in planning your experience. Ask me anything travel related.
                                    </p>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
                <div className="p-4 bg-white shadow flex items-center gap-2 w-full" style={{ "--chat-form-width": "46.5rem" }}>
                    <div className="rounded-t-2xl sm:px-5 w-full">
                        <div className="relative mx-auto max-w-[--chat-form-width]">
                            <div className="relative flex cursor-text gap-2 transition-colors hover:border-gray-7 rounded-t-2xl border-t px-container py-3 sm:rounded-3xl sm:border-2 sm:border-gray-5 sm:px-4 xs:gap-x-1 sm:!border-current">
                                <div className="mr-1 w-full flex items-center">
                                    <textarea
                                        ref={textareaRef}
                                        rows="1"
                                        className="scrollbar-hidden resize-none bg-transparent outline-none placeholder:text-muted focus:placeholder:text-gray-8 flex-1 disabled:text-gray-8 text-md xs:self-center sm:basis-full"
                                        name="message"
                                        placeholder="Ask anything..."
                                        autoComplete="off"
                                        maxLength="600"
                                        value={input}
                                        onChange={handleInputChange}
                                        onKeyDown={(e) => {
                                            if (isTyping) return;
                                            if (e.key === "Enter" && !e.shiftKey) {
                                                e.preventDefault();
                                                sendMessage();
                                            }
                                        }}
                                        style={{ height: "auto", overflow: "hidden" }}
                                    />
                                </div>
                                <div className="flex items-start">
                                    <button
                                        className="group group/button relative z-0 border border-transparent inline-flex justify-center items-center rounded-full font-medium gap-[.3em] disabled:pointer-events-none transition-colors text-center py-[.25em] text-balance bg-primary text-primary-foreground hover:bg-primary-hover data-[state=open]:bg-primary-hover disabled:opacity-30 text-2xs leading-[1.125] px-0 shrink-0"
                                        onClick={sendMessage}
                                        disabled={isTyping}
                                    >
                                        <PlaneIcon className={`${input?.length > 0 ? "bg-sandee-blue text-white" : "text-black"} transition-all duration-300 rounded-full p-1`} />
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div className="relative text-center text-3xs leading-tight empty:hidden sm:text-2xs bg-gray-2 py-1.5 sm:bg-transparent">
                            <p className="text-muted text-sm flex items-center justify-center gap-1">
                                <InfoIcon2 width={17} height={17} />
                                Sandee can make mistakes. Check important info.
                            </p>
                        </div>
                    </div>
                </div>
            </main>

            {/* Right Map Section */}
            <aside className={`${!isMapShow ? "w-0" : isCollapsed && isMapCollapsed ? "w-full" : isCollapsed ? "w-[55%]" : isMapCollapsed ? "w-full" : "w-2/5"} transition-all duration-300 bg-gray-200 relative`}>
                <MapChatBot
                    toggleMapSidebar={toggleMapSidebar}
                    isMapCollapsed={isMapCollapsed}
                    isCollapsed={isCollapsed}
                    isMapShow={isMapShow}
                    latStr={null}
                    longStr={null}
                    map={map}
                    popupBeachData={{}}
                    isFilter={false}
                />
            </aside>
            {/* {console.log(iframeUrl)} */}

            <Drawer
                title={
                    <div className="flex items-center justify-between">
                        <p className="form-modal-head mb25 text-xl">{"Beach Details"}</p>
                        <Space>
                            <Link
                                href={iframeUrl}
                                target="_blank"
                                scroll={false}
                                className="cursor-pointer rounded-lg border-[1px] border-[#D9D9D9] w-8 h-8 flex items-center justify-center"
                            // onClick={closeDrawer}
                            >
                                <ExternalLinkIcon className="text-success-300" />
                            </Link>
                            <div
                                // href="/"
                                // scroll={false}
                                onClick={closeDrawer}
                                className="cursor-pointer rounded-lg border-[1px] border-[#D9D9D9] w-8 h-8 flex items-center justify-center"
                            >
                                <CloseIcon className="text-success-300" />
                            </div>
                        </Space>
                    </div>
                }
                placement="right"
                width={600}
                onClose={closeDrawer}
                open={drawerVisible}
                maskClosable={true}
                style={{ zIndex: 100 }}
                className="rounded-xl"
                closeIcon={false}
                styles={{
                    body: { padding: "16px 24px" },
                    footer: {
                        border: 0,
                        display: "flex",
                        flexDirection: "row-reverse",
                    },
                }}
            >
                <iframe
                    src={iframeUrl}
                    style={{ width: '100%', height: '100%', border: 'none' }}
                    title="Beach Link"
                />
            </Drawer>
        </div>
    );
}