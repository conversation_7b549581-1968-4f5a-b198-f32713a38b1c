'use client';

import React, { useEffect, useRef } from 'react';

const DynamicTooltipRenderer = ({ htmlString }) => {
    const containerRef = useRef(null);

    useEffect(() => {
        const container = containerRef.current;

        if (!container) return;

        const headers = container.querySelectorAll('h2');

        headers.forEach((header) => {
            const tooltip = header.nextElementSibling?.getAttribute('role') === 'tooltip'
                ? header.nextElementSibling
                : null;

            if (!tooltip) return;

            // Style tooltip initially
            tooltip.style.position = 'absolute';
            tooltip.style.background = '#fff';
            tooltip.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
            tooltip.style.border = '1px solid #ccc';
            tooltip.style.borderRadius = '6px';
            tooltip.style.padding = '12px';
            tooltip.style.zIndex = '999';
            tooltip.style.display = 'none';
            tooltip.style.width = '400px';

            header.style.position = 'relative';

            header.addEventListener('mouseenter', () => {
                const rect = header.getBoundingClientRect();
                tooltip.style.display = 'block';
                tooltip.style.top = `${header.offsetTop + header.offsetHeight + 8}px`;
                tooltip.style.left = `${header.offsetLeft}px`;
            });

            header.addEventListener('mouseleave', () => {
                tooltip.style.display = 'none';
            });

            tooltip.addEventListener('mouseenter', () => {
                tooltip.style.display = 'block';
            });

            tooltip.addEventListener('mouseleave', () => {
                tooltip.style.display = 'none';
            });
        });
    }, [htmlString]);

    return (
        <div ref={containerRef} dangerouslySetInnerHTML={{ __html: htmlString }} />
    );
};

export default DynamicTooltipRenderer;
