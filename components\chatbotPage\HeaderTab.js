"use client"
import { Tabs } from 'antd'
import React, { useEffect, useState } from 'react'
import ImageTab from './ImageTab';
import { API_BASE_URL, withErrorHandling } from '@/helper/functions';
import axios from 'axios';

const HeaderTab = ({ tab1, tab2, beachIds = [] }) => {
    const [activeKey, setActiveKey] = useState('1');
    const [beachImages, setBeachImages] = useState([]);
    const [loading, setLoading] = useState(false);

    // Fetch images only when tab2 is selected and we have beachIds
    const fetchBeachImages = async () => {
        if (!beachIds.length) return;

        try {
            setLoading(true);
            const response = await BeachgetMultipleImage(beachIds.join(','));
            if (response?.data) {
                setBeachImages(response?.data?.data || []);
            }
        } catch (error) {
            console.error('Error fetching beach images:', error);
        } finally {
            setLoading(false);
        }
    };

    const onChange = (key) => {
        setActiveKey(key);
        // Fetch images when switching to Images tab
        if (key === '2' && beachImages.length === 0) {
            fetchBeachImages();
        }
    };

    const items = [
        {
            key: '1',
            label: 'Search',
            children: tab1,
        },
        tab2 && {
            key: '2',
            label: 'Images',
            children: loading ? (
                <div>Loading images...</div>
            ) : (
                <ImageTab MultipleImages={beachImages} />
            ),
        },
    ].filter(Boolean); // Remove falsy items

    return (
        <div>
            <Tabs
                color='#00AAE3'
                tabBarStyle={{ color: '#00AAE3' }}
                activeKey={activeKey}
                items={items}
                onChange={onChange}
            />
        </div>
    );
};

export default HeaderTab;

// API helper function
export const BeachgetMultipleImage = withErrorHandling(async (beachIds) => {
    const response = await axios.get(
        `${API_BASE_URL}/image/getImagesOfManyBeaches?AllBeachIds=${beachIds}`
    );
    return response?.data;
});

// "use client"
// import { Tabs } from 'antd'
// import React, { useEffect, useState } from 'react'
// import ImageTab from './ImageTab';
// import { API_BASE_URL, withErrorHandling } from '@/helper/functions';
// import axios from 'axios';
// import ChatbotResponse from './chatbotResponse';



// const HeaderTab = ({ tab1, tab2 }) => {

//     // const { parsedHtml, matchedBeachImages } = transformBeachHTML(msg?.text, map, beachesData);
//     const onChange = (key) => {
//         // console.log(key);
//     };
//     const [MultipleImages, setMultipleImages] = useState();
//     const [beachData, setBeachData] = useState({});
//     const [
//         MultipleImages,
//     ] = await Promise.all([
//         BeachgetMultipleImage("59561"),

//     ]);

//     useEffect(() => {
//         BeachgetMultipleImage("59561").then((res) => {
//             // console.log(res, 'MultipleImagessssss');
//             setMultipleImages(res?.data)
//         })
//         BeachgetSingleBeach("seychelles/fregate/la-digue/anse-source-dargents").then((res) => {
//             // console.log(res, 'beachData');
//             setBeachData(res?.data)
//         })
//         // const response = axios.get(
//         //     `${API_BASE_URL}/image/getByBeach/${data}`
//         //     // `${API_BASE_URL}/beachMain/getMultipleImages/${data}`
//         // );
//     }, [])
//     // console.log(MultipleImages, 'MultipleImages')
//     const items = [
//         {
//             key: '1',
//             label: 'Search',
//             children: tab1,
//         },
//         tab2 && {
//             key: '2',
//             label: 'Images',
//             children: tab2,
//         },
//         // {
//         //   key: '3',
//         //   label: 'Tab 3',
//         //   children: 'Content of Tab Pane 3',
//         // },
//     ];


//     return (
//         <div>
//             <Tabs color='#00AAE3' tabBarStyle={{ color: '#00AAE3' }} defaultActiveKey="1" items={items} onChange={onChange} />
//         </div>
//     )
// }

// export default HeaderTab

// export const BeachgetMultipleImage = withErrorHandling(async (data) => {
//     // logController(
//     //   `${API_BASE_URL}/image/getByBeach/${data}\n`,
//     //   `${API_BASE_URL}/beachMain/getMultipleImages/${data}`
//     // );
//     const response = await axios.get(
//         `${API_BASE_URL}/image/getImagesOfManyBeaches?AllBeachIds=${data}`
//         // `${API_BASE_URL}/beachMain/getMultipleImages/${data}`
//     );
//     console.log(response, "response")
//     return response?.data;
// });

// export const BeachgetSingleBeach = withErrorHandling(async (data) => {
//     const response = await axios.get(
//         API_BASE_URL + `/beachMain/getOneBeachForUser/${data}`
//     );
//     return response?.data;
// });