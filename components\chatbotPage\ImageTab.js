import React, { useEffect, useState } from 'react'
import { CustomGrid } from '../Custom-Display'
import Image from 'next/image'
import { EyeIcon } from '../social-icons/icons'
import CopyRight from '../Common/CopyRight'
import { altText, blurDataURL, FinalImageGenerator, isEmptyObject } from '@/helper/functions'
import { Spin } from 'antd'
import CustomeImage from '../Common/CustomeImage'

const ImageTab = ({ MultipleImages, beachData = {} }) => {
    const [previewImageIndex, setPreviewImageIndex] = useState(null);
    const [isLoading, setIsLoading] = useState(false);

    const handleImageClick = (index) => {
        setPreviewImageIndex(index);
    };

    const handleClosePreview = () => {
        setPreviewImageIndex(null);
    };
    const handleNext = async () => {
        if (previewImageIndex < MultipleImages.length - 1) {
            triggerAnimation(async () => {
                const nextIndex = previewImageIndex + 1;
                setPreviewImageIndex(nextIndex);
            });
        }
    };

    const handlePrev = async () => {
        if (previewImageIndex > 0) {
            triggerAnimation(async () => {
                const prevIndex = previewImageIndex - 1;
                setPreviewImageIndex(prevIndex);
            });
        }
    };

    const triggerAnimation = async (updateIndex) => {
        await updateIndex(); // Update the index after preloading the image
    };

    const toggle = (e) => {
        e?.stopPropagation();

        // Close the photo preview if it's open
        if (previewImageIndex !== null) {
            setPreviewImageIndex(null);
            return;
        }

    };

    useEffect(() => {
        const handleKeyDown = (e) => {
            if (e.key === "Escape") {
                if (previewImageIndex !== null) {
                    // Close the photo preview first
                    setPreviewImageIndex(null);
                }
            }
        };


        window.removeEventListener("keydown", handleKeyDown);


        // Cleanup the listener
        return () => {
            window.removeEventListener("keydown", handleKeyDown);
        };
    }, [previewImageIndex]); // Depend on `open` and `previewImageIndex`

    // useEffect(() => {
    //     setIsClient(true);
    // }, []);


    // if(!isClient) {
    //     return null;
    // }
    return (
        <div>
            <div className="h-[580px] overflow-y-scroll overflow-hidden">
                {MultipleImages?.length && !isEmptyObject(MultipleImages?.[0]) ? (
                    <div className="mr-2 space-y-3">
                        <CustomGrid
                            data={MultipleImages}
                            className="gap-2 sm:gap-4"
                            Component={({ data: dataProps, index }) => (
                                <div
                                    onClick={() => handleImageClick(index)}
                                    className="relative hover:cursor-pointer overflow-hidden rounded-lg aspect-[3/2] w-full h-full group"
                                >
                                    <CustomeImage
                                        className="rounded-lg transition-transform duration-1000 ease-in-out transform group-hover:scale-125 w-full"
                                        src={FinalImageGenerator(dataProps, 1600, 3)}
                                        title={`Beach Image ${index + 1}`}
                                        alt={`Beach Image ${index + 1}`}
                                        fill
                                        blurDataURL={blurDataURL(1650, 1100)}
                                        placeholder="blur"
                                    />
                                    <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-40 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                        <EyeIcon width={24} height={24} stroke={"#ffffff"} className="text-white text-4xl" />
                                    </div>
                                    <CopyRight
                                        copyRightsData={[dataProps]}
                                        background={true}
                                        classNameExtra="!bottom-0 !left-1"
                                        customSize="xs:text-[6px] text-[8px]"
                                    />
                                </div>
                            )}
                            xs={1}
                            sm={2}
                            md={3}
                            lg={4}
                        />
                    </div>
                ) : (
                    <p className="flex items-center h-full justify-center text-xl">Beach Image data not found</p>
                )}
            </div>
            {previewImageIndex !== null && (
                <div className="fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center">
                    <button
                        className="absolute top-5 right-5 text-white text-2xl"
                        onClick={handleClosePreview}
                    >
                        &times;
                    </button>
                    <div className="relative w-3/4 h-3/4">
                        {isLoading ? (
                            <Spin className="absolute inset-0 m-auto flex justify-center items-center" size="large" />
                        ) : (
                            <div
                                className={`relative w-full h-full transition-all duration-300 opacity-100 translate-x-0
                                        `}
                            >
                                <CustomeImage
                                    src={FinalImageGenerator(MultipleImages[previewImageIndex], 1600, 3)}
                                    alt={`Beach Image preview ${previewImageIndex + 1}`}
                                    fill
                                    className="rounded-lg"
                                />
                            </div>
                        )}
                    </div>
                    {previewImageIndex > 0 && (
                        <button
                            className="absolute left-5 text-white text-3xl"
                            onClick={handlePrev}
                        >
                            &#10094;
                        </button>
                    )}
                    {previewImageIndex < MultipleImages.length - 1 && (
                        <button
                            className="absolute right-5 text-white text-3xl"
                            onClick={handleNext}
                        >
                            &#10095;
                        </button>
                    )}
                </div>
            )}
        </div>
    )
}

export default ImageTab
