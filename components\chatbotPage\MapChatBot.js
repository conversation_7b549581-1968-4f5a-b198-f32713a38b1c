"use client";
import React, { useRef, useEffect, useState, useContext, useMemo } from "react";
import mapboxgl from "!mapbox-gl"; // eslint-disable-line import/no-webpack-loader-syntax
import { valContext } from "@/helper/context/ValContext";
import {
    GetSharkAttackeByLatLong,
    PostSearchForMap,
    PostSharkForMap,
} from "@/app/(HomeHeader)/action";
import { CollapseLeftArrow, CollapseRightArrow } from "../social-icons/icons";
mapboxgl.accessToken = process.env.NEXT_PUBLIC_MAPBOX_API_KEY;

const MapChatBot = ({
    latStr,
    longStr,
    popupBeachData,
    isFilter = false,
    isNudeToggle = false,
    isSharkToggle = false,
    mode = "all",
    zoom = 2,
    map,
    toggleMapSidebar = () => { },
    isMapCollapsed = false,
    isCollapsed = false,
    isMapShow = false
}) => {
    const mapContainer = useRef(null);
    // const map = useRef(null);
    const [beachesCount, setBeachesCount] = useState([]);
    const [isLoadingCount, setIsLoadingCount] = useState(true);
    const [location, setLocation] = useState(null);
    const [loaded, setLoaded] = useState(false);
    const [hydrated, setHydrated] = useState(false);
    // const isSatelite = useMemo(
    //     () =>
    //         typeof window !== "undefined" ? +localStorage.getItem("isSatelite") : 0,
    //     []
    // );
    const [isswitchHandle, setIsSwitchHandle] = useState({
        //  isShark: isShark ?? 0,
        isSatelite: 0,
        // isSatelite: isSatelite ?? 0,
        //  isNude: isNude ?? 0,
        //  isWind: 0,
    });
    useEffect(() => {
        setHydrated(true);
    }, []);
    // const [open, setOpen] = useState({});
    // Function to fetch beaches count
    const FetchAndSetDelayedBeachesCount = async () => {
        if (!map.current) return;

        const bounds = map.current.getBounds();
        const payload = {
            point1: bounds.getSouthWest().toArray().reverse(),
            point2: bounds.getNorthEast().toArray().reverse(),
            point3: bounds.getNorthWest().toArray().reverse(),
            point4: bounds.getSouthEast().toArray().reverse(),
            // beachCategories: ["nude"],
        };

        try {
            const Results = await PostSearchForMap(payload,);
            const beachLists = Results?.data?.beaches?.rows;
            // Refine data for rendering beaches
            const AllBeachWithRefinedDataCount = beachLists?.map((el) => ({
                ...el,
                lat: el?.GeoLoc?.coordinates?.[1] ?? 0,
                long: el?.GeoLoc?.coordinates?.[0] ?? 0,
                locationAddress: {
                    link: `/map/${el?.nameSlug}/@${el?.GeoLoc?.coordinates?.[1] ?? 0},${el?.GeoLoc?.coordinates?.[0] ?? 0
                        }`,
                },
            }));
            setBeachesCount(AllBeachWithRefinedDataCount);
            setIsLoadingCount(false);
        } catch (error) {
            console.error("Error fetching beach data:", error);
        }
    };

    // Function to add beaches and clusters to the map
    const SetBeaches = () => {
        if (!mapContainer?.current || !beachesCount?.length || !loaded) return;

        if (map.current.getSource("beaches")) {
            map.current.getSource("beaches").setData({
                type: "FeatureCollection",
                features: beachesCount.map((beach) => ({
                    type: "Feature",
                    geometry: {
                        type: "Point",
                        coordinates: [beach.long, beach.lat],
                    },
                    properties: beach,
                })),
            });
        } else {
            // Wait for style to load before adding source
            if (!map.current.isStyleLoaded()) {
                map.current.once('style.load', () => {
                    SetBeaches();
                });
                return;
            }
            map.current.addSource("beaches", {
                type: "geojson",
                data: {
                    type: "FeatureCollection",
                    features: beachesCount.map((beach) => ({
                        type: "Feature",
                        geometry: {
                            type: "Point",
                            coordinates: [beach.long, beach.lat],
                        },
                        properties: beach,
                    })),
                },
                cluster: true,
                clusterMaxZoom: 14,
                clusterRadius: 50,
            });
            // Add cluster layer
            map.current.addLayer({
                id: "clusters",
                type: "circle",
                source: "beaches",
                filter: ["has", "point_count"],
                paint: {
                    "circle-color": [
                        "step",
                        ["get", "point_count"],
                        "#fff000",
                        100,
                        "#fff000",
                        750,
                        "#fff000",
                    ],
                    "circle-radius": [
                        "step",
                        ["get", "point_count"],
                        25,
                        100,
                        25,
                        750,
                        30,
                    ],
                },
            });

            // Add cluster count layer
            map.current.addLayer({
                id: "cluster-count",
                type: "symbol",
                source: "beaches",
                filter: ["has", "point_count"],
                layout: {
                    "text-field": ["get", "point_count_abbreviated"],
                    "text-font": ["DIN Offc Pro Medium", "Arial Unicode MS Bold"],
                    "text-size": 16,
                },
                paint: {
                    "text-color": "#000",
                }
            });

            // Load and add beach icons
            map.current.loadImage("/Marker4-4x.png", (error, image) => {
                if (error) throw error;
                map.current.addImage("beach-icon", image);

                // Add unclustered points layer  --- shark attack
                map.current.addLayer({
                    id: "unclustered-point",
                    type: "symbol",
                    source: "beaches",
                    filter: ["!", ["has", "point_count"]],
                    layout: {
                        "icon-image": "beach-icon",
                        "icon-size": 0.22,
                        "icon-allow-overlap": true,
                        "text-field": ["get", "name"],
                        "text-size": 18,
                        "text-offset": [1, 0],
                        "text-anchor": "left",
                        "text-font": ["Open Sans Bold", "Arial Unicode MS Bold"]
                    },
                    paint: {
                        "text-color": "#FF6B00", // Set text color FF6B00
                        "text-halo-color": "#fff", // Optional: Add a halo around the text for better readability
                        "text-halo-width": 1, // Optional: Set halo width
                    },
                });
            });

            if (latStr && longStr) {
                new mapboxgl.Marker({ color: "red" })
                    .setLngLat([longStr, latStr])
                    .addTo(map.current);
                // map.current.flyTo({ center: [longStr, latStr], zoom: 10 });
            }

            // Handle cluster clicks
            map.current.on("click", "clusters", (e) => {
                const features = map.current.queryRenderedFeatures(e.point, {
                    layers: ["clusters"],
                });
                const clusterId = features[0].properties.cluster_id;
                map.current
                    .getSource("beaches")
                    .getClusterExpansionZoom(clusterId, (err, zoom) => {
                        if (err) return;
                        map.current.easeTo({
                            center: features[0].geometry.coordinates,
                            zoom: zoom,
                        });
                    });
            });

            // Handle unclustered-point clicks
            map.current.on("click", "unclustered-point", async (e) => {
                const feature = e?.features?.[0];
                const address = JSON?.parse(feature?.properties?.locationAddress);
                if (address?.link) {
                    window.open(address.link, "_blank");
                }
            });

            map.current.on("mouseenter", "clusters", () => {
                map.current.getCanvas().style.cursor = "pointer";
            });
            map.current.on("mouseleave", "clusters", () => {
                map.current.getCanvas().style.cursor = "";
            });
            // Cursor handling for clusters and unclustered points
            map.current.on("mouseenter", ["clusters", "unclustered-point"], () => {
                map.current.getCanvas().style.cursor = "pointer";
            });
            map.current.on("mouseleave", ["clusters", "unclustered-point"], () => {
                map.current.getCanvas().style.cursor = "";
            });

            map.current.addControl(new mapboxgl.ScaleControl());
        }
    };

    // Fetch beaches count when map bounds change
    const handleMoveEnd = () => {
        setIsLoadingCount(true);
        FetchAndSetDelayedBeachesCount();
    };

    // Set the initial location
    const getLocation = () => {
        if (latStr && longStr) {
            setLocation([longStr, latStr]);
        } else {
            setLocation([-50.4117325, 34.020479]); // los angeles coordinates
        }
    };

    // Initialize map when location is set
    useEffect(() => {
        if (!location) return;
        map.current = new mapboxgl.Map({
            container: mapContainer.current,
            style: isswitchHandle?.isSatelite
                ? "mapbox://styles/mapbox/satellite-streets-v12"
                : "mapbox://styles/mapbox/streets-v12",
            center: location,
            zoom: zoom,
            minZoom: 2,
            maxBounds: [
                [-180, -85],
                [180, 85],
            ],
            projection: { name: "mercator" },
        });
        map.current?.addControl(new mapboxgl.NavigationControl(), "bottom-right");
        // map.current.dragRotate.disable();
        // map.current.touchZoomRotate.disableRotation();

        // Wait for both style and map to load
        map.current.on('style.load', () => {
            setLoaded(true);
        });

        map.current.on("load", () => {
            // setLoaded(true);
            handleMoveEnd();
        });

        map.current.on("moveend", handleMoveEnd);

        return () => {
            map.current.off("moveend", handleMoveEnd);
            map.current.remove();
        };
    }, [location, isswitchHandle?.isSatelite]);
    useEffect(() => {
        // console.log("resize")
        if (map.current) {
            map.current.resize();
        }
    }, [isMapCollapsed, isCollapsed, isMapShow]);
    // Load beaches after data is fetched
    useEffect(() => {
        SetBeaches();
    }, [beachesCount, loaded]);

    // Fetch beach data after the map is refreshed
    useEffect(() => {
        getLocation();
    }, []);
    const sateliteHandle = async () => {
        // await localStorage.setItem("isSatelite", isswitchHandle?.isSatelite == 0 ? 1 : 0);
        setIsSwitchHandle({ ...isswitchHandle, isSatelite: isswitchHandle?.isSatelite == 0 ? 1 : 0 });
        // await localStorage.setItem("isSatelite", isSatelite == 0 ? 1 : 0);
        // window.location.reload();
        // setIsSatelite(pr => !pr)
    };

    return (
        <>
            <div className="relative overflow-hidden w-full">
                {/* h-[calc(100vh-72px)]  for top header show this use */}
                <div
                    ref={mapContainer}
                    // className="h-screen p-0 rounded-[22px]"
                    // style={{ width: '100%' }} // Remove fixed width calculations
                    className={`${!isMapShow ? "w-0" : isMapCollapsed && isCollapsed ? "w-[calc(100vw-5.5rem)]" : isMapCollapsed ? "w-[calc(100vw-14rem)]" : isCollapsed ? "w-[calc(100vw-40rem)] min-w-full" : "w-[calc(100vw-46rem)]"}  h-screen p-0 rounded-[22px]`}
                />
                <button
                    onClick={sateliteHandle}
                    className="absolute h-[29px] md:bottom-4 bottom-4 right-[10px] py-1 px-2 text-sm shadow-md rounded-md bg-white z-10 hover:bg-gray-50 border border-gray-300"
                >

                    {hydrated && isswitchHandle?.isSatelite ? "Satellite view" : "Street view"}
                </button>
                {isLoadingCount ? (
                    <div className="absolute md:top-5 right-4 bg-sandee-blue py-1 rounded-lg md:px-4 px-2 text-white md:text-base text-[12px] top-4">
                        <span className="font-semibold">Loading Beaches...</span>
                    </div>
                ) : null}
                <div
                    onClick={() => toggleMapSidebar()}
                    className="absolute md:top-5 left-4 cursor-pointer bg-sandee-blue p-1 rounded-full  text-white md:text-base text-[12px] top-4">
                    <span className="font-semibold">
                        {isMapCollapsed ? <CollapseRightArrow /> : <CollapseLeftArrow className="" />}
                    </span>
                </div>
            </div>
        </>
    )
}

export default MapChatBot