import React, { useEffect, useState } from 'react'

const ChatbotResponse = ({ html }) => {
    const [renderedContent, setRenderedContent] = useState(null);

    useEffect(() => {
        if (!html) return;

        // Check if html is a string or an array of React elements/strings
        if (typeof html === 'string') {
            // Handle string HTML content (original behavior)
            const container = document.createElement('div');
            container.innerHTML = html;

            // Process and render HTML content with animation
            processStringHTML(container);
        } else if (Array.isArray(html)) {
            // Directly set the array content (React components/objects)
            setRenderedContent(html);
        } else {
            // Handle single React element or object
            setRenderedContent(html);
        }
    }, [html]);

    const processStringHTML = (container) => {
        const wordChunks = [];
        container.childNodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
                const tag = node.nodeName.toLowerCase();
                const text = node.textContent || '';
                const words = text.split(/\s+/);
                words.forEach(word => {
                    if (word.trim()) {
                        wordChunks.push({ tag, word });
                    }
                });
            } else if (node.nodeType === Node.TEXT_NODE) {
                // Handle text nodes too
                const text = node.textContent || '';
                const words = text.split(/\s+/);
                words.forEach(word => {
                    if (word.trim()) {
                        wordChunks.push({ tag: 'span', word });
                    }
                });
            }
        });

        if (wordChunks.length === 0) return;

        let renderedWords = [];
        let index = 0;

        // Initial render immediately to avoid empty content
        setRenderedContent(<div className="chat-bot-response">Loading...</div>);

        const interval = setInterval(() => {
            const current = wordChunks[index];
            const last = renderedWords[renderedWords.length - 1];

            if (last && last.key === current.tag) {
                // append word to existing tag
                renderedWords[renderedWords.length - 1] = React.cloneElement(last, {
                    children: (
                        <>
                            {last.props.children}&nbsp;{current.word}
                        </>
                    ),
                });
            } else {
                // create new tag block
                renderedWords.push(
                    React.createElement(current.tag, { key: current.tag + index }, current.word)
                );
            }

            // Update UI
            setRenderedContent(
                <div className="chat-bot-response">
                    {renderedWords.map((el, i) => (
                        <div key={i}>{el}</div>
                    ))}
                </div>
            );

            index++;
            if (index >= wordChunks.length) clearInterval(interval);
        }, 50); // Faster animation (50ms per word)

        return () => clearInterval(interval);
    };

    // Render the content
    if (!renderedContent) return null;

    return typeof renderedContent === 'object' && renderedContent.type ?
        renderedContent :
        <div className="chat-bot-response">{renderedContent}</div>;
}

export default ChatbotResponse
