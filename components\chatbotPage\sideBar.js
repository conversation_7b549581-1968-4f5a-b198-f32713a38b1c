"use client";
import React, { useState } from 'react'
import LogInButtonSideBar from '../LogInButton3'
import { BlogSideBarIcon, ChatHistoryIcon, EarthSideBarIcon, FaPlus, GreaterThenIcon, LessThenIcon, MapSideBarIcon, NewsIcon, NudeBeachSideBarIcon, SharkSideBarIcon, TextMesaageIcon } from '../social-icons/icons'
import Image from 'next/image'
import Link from 'next/link';

const SideBar = ({
    isCollapsed,
    toggleSidebar,
    setMessages,
    handleNewChat = () => { },
}) => {
    const [activeMenu, setActiveMenu] = useState("chat");

    const sideMenu = [
        {
            icon: <TextMesaageIcon
                className="text-xl"
                fill={`${activeMenu == "chat" ? "#00AAE3" : "none"}`}
                stroke={`${activeMenu == "chat" ? "#00AAE3" : "currentColor"}`}
            />,
            title: "Chat",
            key: "chat",
            isActive: activeMenu == "chat",
            onClick: () => setActiveMenu("chat"),
        },
        {
            icon: <ChatHistoryIcon
                className={`text-xl `}
                // stroke={`${activeMenu == "chatHistory" ? "#00AAE3" : "currentColor"}`}
                // strokeWidth={activeMenu == "chatHistory" ? 0 : 1.5}
                fill={`${activeMenu == "chatHistory" ? "#00AAE3" : "currentColor"}`}
            />,
            title: "Chat History",
            key: "chatHistory",
            isActive: activeMenu == "chatHistory",
            onClick: () => setActiveMenu("chatHistory"),
        },
        {
            icon: <EarthSideBarIcon
                className="text-xl"
                fill={`${activeMenu == "country" ? "#00AAE3" : "black"}`}
            // stroke={`${activeMenu == "country" ? "#00AAE3" : "currentColor"}`}
            />,
            title: "Countries",
            key: "country",
            isActive: activeMenu == "country",
            link: "/countries",
            onClick: () => {
                setActiveMenu("country");
            },
        },
        {
            icon: <BlogSideBarIcon
                className="text-xl"
                fill={`${activeMenu == "blog" ? "#00AAE3" : "black"}`}
            // stroke={`${activeMenu == "blog" ? "#00AAE3" : "currentColor"}`}
            />,
            title: "Blogs",
            key: "blog",
            link: "/blog",
            isActive: activeMenu == "blog",
            onClick: () => setActiveMenu("blog"),
        },
        {
            icon: <MapSideBarIcon
                className="text-xl"
                fill={`${activeMenu == "map" ? "#00AAE3" : "black"}`}
            // stroke={`${activeMenu == "map" ? "#00AAE3" : "currentColor"}`}
            />,
            title: "Map",
            key: "map",
            link: "/map",
            isActive: activeMenu == "map",
            onClick: () => setActiveMenu("map"),
        }, {
            icon: <NudeBeachSideBarIcon
                className="text-xl"
                fill={`${activeMenu == "nudeBeaches" ? "#00AAE3" : "black"}`}

            />,
            title: "Nude Beaches",
            key: "nudeBeaches",
            link: "/nude-beaches",
            isActive: activeMenu == "nudeBeaches",
            onClick: () => setActiveMenu("nudeBeaches"),
        },
        {
            icon: <SharkSideBarIcon
                className="text-xl"
                fill={`${activeMenu == "sharks" ? "#00AAE3" : "black"}`}
            // stroke={`${activeMenu == "sharks" ? "#00AAE3" : "currentColor"}`}
            />,
            title: "Sharks",
            key: "sharks",
            link: "/shark",
            isActive: activeMenu == "sharks",
            onClick: () => setActiveMenu("sharks"),
        },
        {
            icon: <NewsIcon
                className="text-xl"
                fill={`${activeMenu == "news" ? "#00AAE3" : "black"}`}
            // stroke={`${activeMenu == "news" ? "#00AAE3" : "currentColor"}`}
            />,
            title: "Beach News",
            key: "news",
            link: "/news",
            isActive: activeMenu == "news",
            onClick: () => setActiveMenu("news"),
        },

    ]
    return (
        <>
            <aside className={`flex flex-col justify-between bg-white text-black active:text-sandee-blue p-4 pt-6 !pb-12 transition-all duration-300 ${isCollapsed ? 'w-20' : 'w-64'}`}>
                <div className='flex flex-col space-y-6'>
                    <div className={`flex items-center ${isCollapsed ? 'justify-center' : 'justify-between'}`}>
                        <div className={`flex items-center justify-center ${isCollapsed ? 'hidden' : 'block'}`}>
                            <div className={`relative w-[130px] h-7 transition-all duration-300 `}>
                                <Image
                                    // src={"/static/images/Sandee-White.svg"}
                                    className=" "
                                    src={"/static/images/Sandee-Blue.webp"}
                                    fill
                                    sizes="1080"
                                    alt="Sandee logo Blue"
                                />
                            </div>
                        </div>
                        <button onClick={toggleSidebar} className={`text-black px-1 py-1 hover:bg-[#80808029] rounded-full active:text-sandee-blue text-2xl ${isCollapsed ? "flex justify-center items-center" : "self-end"}`}>
                            {isCollapsed ? <>
                                <LessThenIcon className="" />
                                {/* <MultiStar className="hidden hover:block" /> */}
                            </> :
                                <GreaterThenIcon className="" />
                            }
                        </button>
                    </div>
                    <button
                        onClick={() => handleNewChat()}
                        className={`mt-auto bg-[#e7fdff] text-sandee-blue hover:bg-[#e7fdff] p-2 rounded-full transition-all duration-300 ${isCollapsed ? 'w-12 h-12 flex items-center justify-center' : 'w-full'}`}>
                        {isCollapsed ? <FaPlus /> : 'New Chat'}
                    </button>
                    {/* <h1 className={`text-xl font-bold transition-all duration-300 ${isCollapsed ? 'hidden' : 'block'}`}>mindtrip.</h1> */}
                    <nav className="flex flex-col space-y-2 !mt-2">
                        {
                            sideMenu?.map((menu, index) => {
                                return !menu?.link ? <div key={`${menu?.key}-${index}`}
                                    onClick={menu?.onClick}
                                    className={`relative w-full flex items-center ${isCollapsed && "justify-center"}`}>
                                    <button className={`text-left px-[6px] w-full py-2 hover:bg-[#80808029] flex items-center ${isCollapsed ? "justify-center" : ""} rounded-full `}>
                                        {menu?.icon} {!isCollapsed && <span className="ml-2">{menu?.title}</span>}
                                    </button>
                                    {menu?.isActive && <div className={`absolute ${!isCollapsed && "hidden"} -right-4 inset-y-0 flex items-center`}
                                    >
                                        <div className="relative h-6 w-1 rounded-l-lg bg-sandee-blue "></div>
                                    </div>}
                                </div> : <Link key={`${menu?.key}-${index}`}
                                    href={menu?.link || "#"}
                                    target='_blank'
                                    // onClick={menu?.onClick}
                                    className={`relative w-full flex items-center ${isCollapsed && "justify-center"}`}>
                                    <button className={`text-left px-[6px] w-full py-2 hover:bg-[#80808029] flex items-center ${isCollapsed ? "justify-center" : ""} rounded-full `}>
                                        {menu?.icon} {!isCollapsed && <span className="ml-2">{menu?.title}</span>}
                                    </button>
                                    {menu?.isActive && <div className={`absolute ${!isCollapsed && "hidden"} -right-4 inset-y-0 flex items-center`}
                                    >
                                        <div className="relative h-6 w-1 rounded-l-lg bg-sandee-blue "></div>
                                    </div>}
                                </Link>
                            })
                        }
                        {/* <div className={`relative w-full flex items-center ${isCollapsed && "justify-center"}`}>
                            <button className={`text-left px-[6px] w-full py-2 hover:bg-[#80808029] flex items-center ${isCollapsed ? "justify-center" : ""} rounded-full `}>
                                <TextMesaageIcon className="text-xl" fill="currentColor" /> {!isCollapsed && <span className="ml-2">Chat</span>}
                            </button>
                            <div
                                className={`absolute ${!isCollapsed && "hidden"} -right-4 inset-y-0 flex items-center`}
                            >
                                <div className="relative h-6 w-1 rounded-l-lg bg-current "></div>
                            </div>
                        </div> */}
                        {/* <button className={`text-left px-[6px] py-2 hover:bg-[#80808029] flex items-center ${isCollapsed ? "justify-center" : ""} rounded-full active:bg-[#e7fdff]`}>
                            <BsRobot className="text-xl" /> {!isCollapsed && <span className="ml-2">Chat History</span>}
                        </button> */}
                        {/* <button className={`text-left px-[6px] py-2 hover:bg-[#80808029] flex items-center ${isCollapsed ? "justify-center" : ""} rounded-full active:bg-[#e7fdff]`}>
                            <FaPlus className="text-xl" /> {!isCollapsed && <span className="ml-2">Create</span>}
                        </button> */}
                    </nav>
                </div>
                <div className='mb-0'>
                    <LogInButtonSideBar isCollapsed={isCollapsed} />
                </div>
            </aside >
        </>
    )
}

export default SideBar