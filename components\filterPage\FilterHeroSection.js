"use client";
import React, { useEffect, useRef, useState } from "react";
import { FinalImageGenerator, blurDataURL } from "@/helper/functions";
import BreadCumber from "@/components/Common/BreadCumber";
import Image from "next/image";
import { CustomContainer } from "../Custom-Display";
import CopyRight from "../Common/CopyRight";
import CustomeImage from "../Common/CustomeImage";

const FilterPage_Hero_Section = ({ data, param }) => {
  const [isShow, setIsShow] = useState(false);
  const [isOverflow, setIsOverflow] = useState(false);
  const contentRef = useRef(null);
  let description = "";
  if (param == "Surfing") {
    description =
      "Experience the thrill of surfing with <PERSON><PERSON> - the ultimate beach guide. Discover the perfect waves and embrace the joy of riding the surf on pristine beaches. Dive into the world of sun, sea, and adventure with <PERSON><PERSON>, your go-to companion for an unforgettable surfing experience";
  } else if (param == "Family-Friendly") {
    description =
      "Discover the perfect seaside retreat for your family with <PERSON><PERSON>, the ultimate beach guide. Explore our curated list of family-friendly beaches, where sun, sand, and smiles await. Find the ideal destination for creating unforgettable memories with your loved ones";
  } else if (param == "Disability") {
    description =
      "Discover the ultimate beach experience with Sandee - the ultimate beach guide. Explore our curated list of 'All Disability Access Beaches' ensuring everyone can enjoy the sun and surf.";
  } else if (param == "LGBTQ") {
    description =
      "Discover the most welcoming LGBTQ+ beaches worldwide with Sandee - the ultimate beach guide. Explore inclusive destinations where sun, sea, and acceptance meet. Plan your perfect getaway with our curated LGBTQ+ beach recommendations for a memorable and inclusive beach experience";
  } else if (param == "Dog") {
    description =
      "Discover the best dog-friendly beaches with Sandee - the ultimate beach guide. Explore pristine shores where your furry friend can play and unwind. Plan your next seaside adventure with our comprehensive beach listings, ensuring a pawsitively delightful experience for both you and your four-legged companion";
  } else if (param == "Nude") {
    description =
      "Discover the allure of nude beaches with Sandee – the ultimate beach guide. Immerse yourself in natural beauty and liberating experiences as we unveil the world's best clothing-optional shores.";
  } else if (param == "Camping") {
    description =
      "Discover the joy of the great outdoors with Sandee - the ultimate beach guide. Unleash your adventurous spirit with the best camping gear and tips for an unforgettable outdoor experience. From cozy tents to essential camping accessories, Sandee has you covered";
  }
  useEffect(() => {
    const contentEl = contentRef.current;
    if (contentEl) {
      const lineHeight = parseFloat(
        window.getComputedStyle(contentEl).lineHeight
      );
      const maxHeight = lineHeight * 5; // Height for 5 lines
      if (contentEl.scrollHeight > maxHeight) {
        setIsOverflow(true); // Content is more than 5 lines
      } else {
        setIsOverflow(false); // Content fits within 5 lines
      }
    }
  }, [description]);
  return (
    <>
      <section className=" items-center  justify-around ">
        <div className="relative  md:h-[550px]  h-[260px]">
          <div className="absolute left-0 top-0  md:h-[550px] h-[260px] w-full ">
            <CustomeImage
              priority
              src={data?.image ?? FinalImageGenerator(data?.image, 1600, 3)}
              alt={`${data?.alterText ? data?.alterText : data?.name ? `${data?.name} Photo - Sandee` : "HomePage Sandee Photo "
                }`}
              className="object-cover bg-blend-overlay lg:rounded-b-none rounded-b-[40px] "
              fill
              blurDataURL={blurDataURL(1600, 800)}
              placeholder="blur"
            />
          </div>

          <div className="absolute flex justify-center items-center  md:h-[550px] h-[260px] w-full lg:rounded-b-none rounded-b-[40px]">
            <h1 className=" text-white font-bold text-sandee-4xl  w-full lg:w-1/2  sm:text-sandee-4xl md:text-sandee-6xl xl:text-sandee-7xl 3xl:text-sandee-8xl text-center">
              {data?.title ?? data?.name}
            </h1>
            {/* <CopyRight
              copyRightsData={[data?.image]}
              background={true}
              // styleExtra={{ bottom: "1px", left: "3px" }}
            /> */}
          </div>
        </div>

        <div className=" w-full pt-5 flex items-center">
          <CustomContainer>
            <BreadCumber
              data={[
                {
                  title: `${data?.name ?? "Country"}`,
                },
              ]}
            />
            <p className="text-sandee-24 leading-[32px] font-bold mt-1">
              {data?.name ?? data?.title}
            </p>
            <div className="text-black font-normal text-sandee-18 pt-2">
              <div
                ref={contentRef}
                className={`${!isShow
                  ? "clamp-text" // Limit to 5 lines
                  : ""
                  }`}
              >
                {description}
              </div>
              {description && isOverflow && (
                <button
                  onClick={() => setIsShow(!isShow)}
                  className="font-bold hover:underline text-sandee-orange"
                // className="font-bold hover:underline text-gray-500"
                >
                  {isShow ? "View Less" : "View More"}
                </button>
              )}
            </div>
          </CustomContainer>
        </div>
      </section>
      {/* <div className=" bg-gray-100 w-full p-4 flex items-center ">
        <CustomContainer>
          <BreadCumber
            data={[
              {
                title: `${data?.name ?? "Country"}`,
              },
            ]}
          />
        </CustomContainer>
      </div>
      <CustomContainer>
        <div className=" flex gap-2 flex-col lg:flex-row my-5">
          <div className=" w-full  ">
            <div className="relative h-[420px]">
              <div className="absolute left-0 bottom-0  h-[420px] w-full ">
                <Image
                  priority
                  src={data?.image ?? FinalImageGenerator(data?.image, 1600, 3)}
                  alt={`${
                    `${data?.name} Photo - Sandee` ?? "HomePage Sandee Photo "
                  }`}
                  className=" object-cover bg-blend-overlay rounded-sandee"
                  fill
                />
              </div>
              <div className="absolute right-0 bottom-0  h-[420px] w-full bg-transparent z-1 flex justify-center  items-center ">
                <h1 className=" text-white font-bold rounded-full border border-gray-300 bg-[#EBEBEB66] [text-shadow:0px_4px_4px_rgba(0,_0,_0,_0.25)]   text-[28px]  xs:text-[28px] px-3 xs:px-3  sm:text-sandee-32 sm:px-4 md:text-[40px] md:px-8 xl:text-[45px] top-[50%] left-[50%]  ">
                  {data?.title ?? data?.name}
                </h1>
              </div>
            </div>
          </div>
        </div>
      </CustomContainer> */}
    </>
  );
};

export default FilterPage_Hero_Section;
