import { HomePageData } from "@/data/HomePageData";
import React from "react";
import { CustomGrid } from "../Custom-Display";
import { FinalImageGenerator } from "@/helper/functions";
import ListCard from "../Cards/ListCard";
import Link from "next/link";
import { ExploreMoreArrow } from "../social-icons/icons";
import NameTitle from "../Common/NameTitle";

const Filter_List_Section = ({ data, details }) => {
  return (
    <>
      <NameTitle
        className=" mt-10"
        description={"OUR EXPERTLY CRAFTED LISTS"}
        name={`${details?.title} List`}
        type={2}
        extraButton={
          <div className=" hidden md:flex justify-end items-start w-3/12">
            <Link
              href={HomePageData.ListSection.button_link}
              className=" custom-hover-slide-button group"
            >
              <span className="custom-hover-slide group-hover:h-full"></span>
              <span className="custom-hover-slide-text group-hover:text-white ">
                {HomePageData.ListSection.button_text}
                <ExploreMoreArrow className=" ml-2 fill-sandee-orange group-hover:fill-white  h-4 w-4" />
              </span>
            </Link>
          </div>
        }
      />

      <CustomGrid
        data={data}
        className="gap-4 sm:gap-8 mb-6 mt-[14px]"
        Component={({ data: dataProps }) => {
          dataProps.link = `/list/${dataProps?.nameSlug}`; //
          dataProps.imageSrc = FinalImageGenerator(dataProps?.listicleImage);
          return ListCard({ data: { ...dataProps } });
        }}
        xs={1}
        sm={2}
        // md={2}
        lg={3}
        // xl={3}
      />
      <div className="flex md:hidden justify-center mb-6">
        <Link
          href={HomePageData.ListSection.button_link}
          className=" custom-hover-slide-button group"
        >
          <span className="custom-hover-slide group-hover:h-full"></span>
          <span className="custom-hover-slide-text group-hover:text-white ">
            {HomePageData.ListSection.button_text}
            <ExploreMoreArrow className=" ml-2 fill-sandee-orange group-hover:fill-white  h-4 w-4" />
          </span>
        </Link>
      </div>
    </>
  );
};

export default Filter_List_Section;
