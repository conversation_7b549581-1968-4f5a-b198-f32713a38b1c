"use client";
import { FinalImageGenerator } from "@/helper/functions";
import React, { useEffect, useState } from "react";
import { CustomGrid } from "../Custom-Display";
import BeachCard from "../Cards/BeachCard";
import NameTitle from "../Common/NameTitle";
import Pagination from "../Common/Pagination";
import { TaggetAllRelatedBeaches } from "@/app/(HeaderSlim)/(Filter Section)/action";

const Filter_In_World_Section = ({ data = [], details, FullCount }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [AllBeachPage, setAllBeachPage] = useState(data);
  const FetchOrSetBeach = async () => {
    const AllBeachesFilterResponse = await TaggetAllRelatedBeaches({
      page: currentPage,
      limit: FullCount?.limit,
    });
    setAllBeachPage(AllBeachesFilterResponse?.data?.rows);
  };

  useEffect(() => {
    if (currentPage > 1) {
      FetchOrSetBeach();
    }
  }, [currentPage]);

  return (
    <div>
      <NameTitle name={`All ${details?.title}`} />
      <CustomGrid
        data={AllBeachPage}
        className=" gap-4 mb-6 mt-[13px] "
        Component={({ data: dataProps }) => {
          if (dataProps?.city?.state?.country?.slug) {
            dataProps.link = `/${dataProps?.city?.state?.country?.slug}/${dataProps?.city?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.nameSlug}`; //
            dataProps.location = `${dataProps?.city?.name}, ${dataProps?.city?.state?.country?.code}`; //
          } else {
            dataProps.link = `/${dataProps?.country?.slug}/${dataProps?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.nameSlug}`; //
            dataProps.location = `${dataProps?.city?.name}, ${dataProps?.country?.code}`; //
          }
          dataProps.imageSrc = FinalImageGenerator(dataProps?.images?.[0]);
          return BeachCard({ data: { ...dataProps, ...dataProps } });
        }}
        xs={2}
        sm={2}
        md={3}
        lg={4}
        xl={5}
      />
      <Pagination
        className="pagination-bar"
        currentPage={currentPage}
        // siblingCount={3}
        // totalCount={
        //   AllCountries?.filter((el) =>
        //     el?.name?.toLowerCase()?.includes(query?.toLowerCase())
        //   )?.length
        // }
        totalCount={FullCount?.count || 0}
        pageSize={FullCount?.limit}
        onPageChange={(page) => {
          setCurrentPage(page);
          // setRefresh((prev) => !prev);
        }}
      />
    </div>
  );
};

export default Filter_In_World_Section;
