"use client";
import React, { useEffect, useRef, useState } from "react";
import { EditorContent, FinalImageGenerator, blurDataURL, processContent } from "@/helper/functions";
import BreadCumber from "@/components/Common/BreadCumber";
import Image from "next/image";
import { CustomContainer } from "../Custom-Display";
import LocationMap from "../Common/LocationMap";
import CopyRight from "../Common/CopyRight";
import NameTitle from "../Common/NameTitle";
import CustomeImage from "../Common/CustomeImage";

const Island_Hero_Section = ({ data }) => {
  const [isShow, setIsShow] = useState(false);
  const [isOverflow, setIsOverflow] = useState(false);

  data.lat = data?.GeoLoc?.coordinates?.length
    ? data?.GeoLoc?.coordinates?.[1]
    : 0;
  data.lon = data?.GeoLoc?.coordinates?.length
    ? data?.GeoLoc?.coordinates?.[0]
    : 0;


  // Process content fields in order of priority
  const fullContent =
    processContent(data?.summary) ||
    processContent(data?.overview) ||
    processContent(data?.description) ||
    "";
  const contentRef = useRef(null);
  const sanitizeContent =
    fullContent?.trim()?.length !== 0 &&
    fullContent?.trim() !== '<div class="spacing_overview"></div>';
  // Check if the content exceeds 5 lines
  useEffect(() => {
    const contentEl = contentRef.current;
    if (contentEl) {
      const lineHeight = parseFloat(
        window.getComputedStyle(contentEl).lineHeight
      );
      const maxHeight = lineHeight * 5; // Height for 5 lines
      if (contentEl.scrollHeight > maxHeight) {
        setIsOverflow(true); // Content is more than 5 lines
      } else {
        setIsOverflow(false); // Content fits within 5 lines
      }
    }
  }, [fullContent]);
  return (
    <>
      <section className=" items-center  justify-around ">
        <div className="relative  md:h-[550px]  h-[260px]">
          <div className="absolute left-0 top-0  md:h-[550px] h-[260px] w-full ">
            <CustomeImage
              priority
              src={FinalImageGenerator(
                data?.image || data?.image?.imageUrl,
                1600,
                3
              )}
              // alt={`${`${data?.name} Photo - Sandee` ?? "HomePage Sandee Photo "
              //   }`}
              alt={`${data?.alterText ? data?.alterText : data?.name ? `${data?.name} Photo - Sandee` : "HomePage Sandee Photo "
                }`}
              className="object-cover bg-blend-overlay lg:rounded-b-none rounded-b-[40px] "
              fill
              blurDataURL={blurDataURL(1600, 800)}
              placeholder="blur"
            />
          </div>

          <div className="absolute flex justify-center items-center  md:h-[550px] h-[260px] w-full lg:rounded-b-none rounded-b-[40px]">
            <h1 className=" text-white font-bold text-sandee-4xl  w-full lg:w-1/2  sm:text-sandee-4xl md:text-sandee-6xl xl:text-sandee-7xl 3xl:text-sandee-8xl text-center">
              {data?.name}
            </h1>
            <CopyRight
              copyRightsData={[data?.image]}
              background={true}
              classNameExtra={"bottom-2 lg:left-0 left-6"}
            // styleExtra={{ bottom: "1px", left: "3px" }}
            />
          </div>
        </div>

        <div className="  w-full pt-5 flex items-center">
          <CustomContainer>
            <BreadCumber
              data={[
                {
                  title: "All Countries",
                  to: `/countries`,
                },
                {
                  title: `${data?.country?.name ?? "Country Name"}`,
                  to: `/${data?.country?.slug}`,
                },
                {
                  title: `${data?.name ?? "Country"}`,
                },
              ]}
            />
            <p className="text-sandee-24 leading-[32px] font-bold mt-1">
              {data?.name}
            </p>
          </CustomContainer>
        </div>
        <CustomContainer>
          {(data?.summary || data?.overview || data?.description) &&
            sanitizeContent && (
              <div className="text-black font-normal text-sandee-18 pt-0">
                {/* <EditorContent ref={contentRef}
                  className={`${!isShow
                    ? "clamp-text" // Limit to 5 lines
                    : ""
                    }`} value={fullContent} /> */}
                <div
                  ref={contentRef}
                  className={`${!isShow
                    ? "clamp-text" // Limit to 5 lines
                    : ""
                    } ck-content`}
                  dangerouslySetInnerHTML={{
                    __html: fullContent,
                  }}
                ></div>

                {sanitizeContent && isOverflow && (
                  <button
                    onClick={() => setIsShow(!isShow)}
                    className="font-bold hover:underline text-sandee-orange"
                  // className="font-bold hover:underline text-gray-500"
                  >
                    {isShow ? "View Less" : "View More"}
                  </button>
                )}
              </div>
            )}
        </CustomContainer>
        <CustomContainer>
          <NameTitle
            name={`Map of Beaches in ${data?.name ?? "country"}`}
            type={4}
            className="mt-4"
          />
          <LocationMap latStr={data?.lat} longStr={data?.lon} zoom={9.4} />
        </CustomContainer>
        {/* <HeroSectionSCSDynamic Details={Details} /> */}
      </section>
      {/* <div className=" bg-gray-100 w-full p-4 flex items-center">
        <CustomContainer>
          <BreadCumber
            data={[
              {
                title: "All Countries",
                to: `/countries`,
              },
              {
                title: `${data?.country?.name ?? "Country Name"}`,
                to: `/${data?.country?.slug}`,
              },
              {
                title: `${data?.name ?? "Country"}`,
              },
            ]}
          />
        </CustomContainer>
      </div>
      <CustomContainer>
        <div className=" flex gap-2 flex-col lg:flex-row my-5">
          <div className=" w-full  ">
            <div className="relative h-[420px]">
              <div className="absolute left-0 bottom-0  h-[420px] w-full ">
                <Image
                  priority
                  src={FinalImageGenerator((data?.image || data?.image?.imageUrl), 1600, 3)}
                  alt={`${
                    `${data?.name} Photo - Sandee` ?? "HomePage Sandee Photo "
                  }`}
                  className=" object-cover bg-blend-overlay rounded-sandee"
                  fill
                />
              </div>
              <div className="absolute right-0 bottom-0  h-[420px] w-full bg-transparent z-1 flex justify-center  items-center ">
                <h1 className=" text-white font-bold rounded-full border border-gray-300 bg-[#EBEBEB66] [text-shadow:0px_4px_4px_rgba(0,_0,_0,_0.25)]   text-[28px]  xs:text-[28px] px-3 xs:px-3  sm:text-sandee-32 sm:px-4 md:text-[40px] md:px-8 xl:text-[45px] top-[50%] left-[50%]  ">
                  {data?.name}
                </h1>
              </div>
            </div>
          </div>
        </div>
      </CustomContainer>
      <CustomContainer>
        {(data?.summary || data?.overview || data?.description) && (
          <div
            className="text-black font-normal  text-sandee-18 pt-8"
            dangerouslySetInnerHTML={{
              __html:
                data?.summary ?? data?.overview ?? data?.description ?? "",
            }}
          ></div>
          // <p className=" text-black font-normal  text-sandee-18 pt-8">
          //   {`${data?.summary ?? data?.overview ?? data?.description ?? ""}`}
          // </p>
        )}
      </CustomContainer> */}
    </>
  );
};

export default Island_Hero_Section;
