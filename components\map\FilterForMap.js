"use client";
import React, { useCallback, useState } from "react";
import CustomButton from "../Custom-Button";
import {
  <PERSON><PERSON>,
  Card,
  Col,
  Config<PERSON><PERSON><PERSON>,
  Divider,
  Drawer,
  Row,
  Switch,
  Typography,
} from "antd";
import { BeachDetailsFilter } from "@/data/StaticFilterData";
import { debounce } from "lodash";
import SearchBar from "../SearchBar";
import { isMobileView } from "@/helper/functions";
const FilterForMap = ({
  activityStatus,
  setActivityStatus,
  openDrawer,
  setOpenDrawer,
  query,
  setQuery,
}) => {
  let isMobile = isMobileView();
  const [searchText, setSearchText] = useState("");
  const showDrawer = () => {
    setOpenDrawer(true);
  };
  const onClose = () => {
    setOpenDrawer(false);
  };
  const handleToggle = (itemId) => {
    setActivityStatus((prevStatus) => ({
      ...prevStatus,
      [itemId]: !!!prevStatus?.[itemId],
    }));
  };
  const allFalse = Object?.values(activityStatus)?.every(
    (value) => value === false
  );

  const handleChange = (value) => {
    setSearchText(value);
    delayedSearch(value);
  };
  const delayedSearch = useCallback(
    debounce((q) => sendQuery(q), 500),
    []
  );
  const sendQuery = (query) => {
    setQuery(query);
  };

  return (
    <>
      <ConfigProvider
        theme={{
          token: {
            colorPrimary: "#00aae3",
            colorBgBase: "#ffffff",
          },
          components: {
            Switch: {
              colorTextQuaternary: "rgba(0, 0, 0, 0.28)",
              colorTextTertiary: "rgba(0, 0, 0, 0.46)",
            },
          },
        }}
      >
        {/* <div className="w-full px-1 flex justify-between gap-x-1">
        <SearchBar onMap/>
    
          <div
            // className=" relative border border-primary-600 rounded-xl flex justify-center items-center px-2 cursor-pointer"
            className=" relative flex justify-center items-center cursor-pointer"
            onClick={showDrawer}
          >
            <svg
              viewBox="0 0 24 24"
              width={34}
              height={34}
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g id="SVGRepo_bgCarrier" strokeWidth={0} />
              <g
                id="SVGRepo_tracerCarrier"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <g id="SVGRepo_iconCarrier">
                {" "}
                <path
                  d="M17.8258 5H6.17422C5.31987 5 4.85896 6.00212 5.41496 6.65079L9.75926 11.7191C9.91461 11.9004 10 12.1312 10 12.3699V17.382C10 17.7607 10.214 18.107 10.5528 18.2764L12.5528 19.2764C13.2177 19.6088 14 19.1253 14 18.382V12.3699C14 12.1312 14.0854 11.9004 14.2407 11.7191L18.585 6.65079C19.141 6.00212 18.6801 5 17.8258 5Z"
                  stroke="#00aae3"
                  strokeWidth={2}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />{" "}
              </g>
            </svg>
            {Object.keys(activityStatus)?.length > 0 && !allFalse && (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                height={14}
                width={14}
                className="absolute -top-1 -right-0.5"
                viewBox="0 0 512 512"
              >
                <path d="M256 8C119 8 8 119 8 256s111 248 248 248 248-111 248-248S393 8 256 8zm80 248c0 44.1-35.9 80-80 80s-80-35.9-80-80 35.9-80 80-80 80 35.9 80 80z" />
              </svg>
            )}
          </div>
         
        </div> */}
        {/* <div className="px-3 w-full">
          <CustomButton
            type={4}
            onClick={showDrawer}
            style={{
              width: "100%",
            }}
          >
            <span className="text-bold text-base">Filter</span>
            {Object.keys(activityStatus)?.length > 0 && !allFalse && (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                height={14}
                width={14}
                className=" mx-2 mt-0.5"
                viewBox="0 0 512 512"
              >
                <path d="M256 8C119 8 8 119 8 256s111 248 248 248 248-111 248-248S393 8 256 8zm80 248c0 44.1-35.9 80-80 80s-80-35.9-80-80 35.9-80 80-80 80 35.9 80 80z" />
              </svg>
            )}
          </CustomButton>
        </div> */}

        <Drawer
          //   title="Basic Drawer"
          //   closable={false}
          onClose={onClose}
          open={openDrawer}
          placement={isMobile ? "bottom" : "left"}
          className="filter-select-category rounded-t-3xl"
          extra={
            <Button
              onClick={() => {
                setActivityStatus({});
              }}
              danger
            // type="link"
            >
              Clear
            </Button>
          }
        >
          <Row gutter={[32, 32]}>
            {BeachDetailsFilter?.map((category) => (
              <Col
                key={category?.title}
                xs={24}
                //   sm={12}
                //   md={8}
                //   lg={6}
                //   xl={4}
                //   xxl={4}
                style={{ padding: "5px" }}
              >
                <Card className=" w-full h-full rounded-lg shadow-lg ">
                  <Typography.Title
                    level={4}
                    className="!py-1 !my-1 flex items-center justify-center"
                  >
                    {category?.title}
                  </Typography.Title>
                  <Divider className="my-2" />
                  {category?.items?.map((item) => (
                    <Row key={item?.id} className="mb-2">
                      <Col xs={18} sm={14} md={16} lg={18}>
                        <p>{item?.label}</p>
                      </Col>
                      <Col xs={6}>
                        <Switch
                          id={item?.id}
                          // loading={API.isLoading}
                          checked={!!activityStatus?.[item?.id]}
                          onChange={() => handleToggle(item?.id)}
                        />
                      </Col>
                    </Row>
                  ))}
                </Card>
              </Col>
            ))}
          </Row>
        </Drawer>
      </ConfigProvider>
    </>
  );
};

export default FilterForMap;
