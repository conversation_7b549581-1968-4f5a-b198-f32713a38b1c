"use client";
import React, { useRef, useEffect } from "react";
import mapboxgl from "mapbox-gl";
let popup = "";
const MapBeachCard = ({ dataProps, map, renderPopupContent, children }) => {
  const handleMouseEnter = (beach) => {
    if (!map.current) return;

    const coordinates = [
      beach.GeoLoc?.coordinates[0],
      beach.GeoLoc?.coordinates[1],
    ];

    if (!popup) {
      popup = new mapboxgl.Popup({
        closeButton: false,
      });
    }

    const locationAddress = {
      link: `/map/${beach?.nameSlug}/@${beach?.GeoLoc?.coordinates?.[1] ?? 0},${
        beach?.GeoLoc?.coordinates?.[0] ?? 0
      }`,
      name: beach?.name,
      rating100: beach?.rating100,
      lat: beach?.GeoLoc?.coordinates?.[1] ?? 0,
      long: beach?.GeoLoc?.coordinates?.[0] ?? 0,
      city: beach?.city,
      state: beach?.state,
      country: beach?.country,
    };
    popup
      .setLngLat(coordinates)
      .setDOMContent(
        renderPopupContent(
          JSON.stringify(locationAddress),
          JSON.stringify(beach?.images),
          JSON.stringify(beach?.beachDescription),
          JSON.stringify(beach?.beachCategory),
        )
      )
      .addTo(map.current);

    const closeButton = document.getElementById("popup-close");
    if (closeButton) {
      closeButton.addEventListener("click", () => {
        popup.remove();
      });
    }
  };

  const handleMouseLeave = () => {
    if (popup) {
      popup.remove();
    }
  };

  useEffect(() => {
    const cardElement = document.querySelector(
      `.map-beach-card-${dataProps?.id}`
    );

    if (cardElement) {
      cardElement.addEventListener("mouseover", () =>
        handleMouseEnter(dataProps)
      );
      cardElement.addEventListener("mouseout", handleMouseLeave);
    }

    // Cleanup function to remove event listeners
    return () => {
      if (cardElement) {
        cardElement.removeEventListener("mouseover", () =>
          handleMouseEnter(dataProps)
        );
        cardElement.removeEventListener("mouseout", handleMouseLeave);
      }
    };
  }, [dataProps]);

  return (
    <div className={`map-beach-card-${dataProps?.id} cursor-pointer`}>
      
      {children}
    </div>
  );
};

export default MapBeachCard;
