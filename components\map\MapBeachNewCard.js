"use client"
import Image from "next/image";
import Link from "next/link";
import React from "react";
import { FinalImageGenerator, blurDataURL } from "@/helper/functions";
import CustomSwiper from "../Custom-Swiper";
import BeachCard from "../Cards/BeachCard";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/swiper-bundle.min.css";
import { Pagination } from "swiper";
import { PinLocation } from "../social-icons/icons";
import CustomeImage from "../Common/CustomeImage";
export function generateBreakpoints(minWidth, maxWidth, step, widthDivisor) {
  const breakpoints = {};
  for (let width = minWidth; width <= maxWidth; width += step) {
    breakpoints[width] = {
      slidesPerView: 1, // Ensure only 1 slide is visible at all screen sizes
    };
  }
  return breakpoints;
}

const MapBeachNewCard = ({
  data,
  copyRightsData,
  // className = "h-[200px]",
  className = "aspect-square sm:aspect-auto sm:h-[200px] lg:h-[190px]",
}) => {

  data.lat = data?.GeoLoc?.coordinates?.length
    ? data?.GeoLoc?.coordinates?.[1]
    : 0;
  data.lon = data?.GeoLoc?.coordinates?.length
    ? data?.GeoLoc?.coordinates?.[0]
    : 0;
  return (
    <>
      <Swiper
        modules={[Pagination]}
        pagination={{ clickable: true }}
        spaceBetween={50}
        slidesPerView={1}
      // onSlideChange={() => console.log("Slide change")}
      // onSwiper={(swiper) => console.log(swiper)}
      >
        {data?.images?.length ? (
          data?.images?.map((image, index) => {
            return (
              <SwiperSlide key={index + 1}>
                <div className="w-auto aspect-video relative rounded-2xl">
                  <CustomeImage
                    src={FinalImageGenerator(image)}
                    fill
                    className="object-cover rounded-2xl"
                    alt={'Sandee | Beach Card'}
                  />
                </div>
              </SwiperSlide>
            );
          })
        ) : (
          <iframe
            className=" bg-gray-200 w-full h-full rounded-sandee min-h-[280px] pointer-events-none"
            loading="lazy"
            src={`https://maps.google.com/?q=${data?.lat},${data?.lon}&ie=UTF8&iwloc=&output=embed&t=k&disableDefaultUI=true`}
          ></iframe>
        )}
      </Swiper>

      <div className="my-3">
        <p className="font-bold text-sandee-20">{data?.name}</p>
        <div className="flex gap-x-1 items-center mb-2"><PinLocation className={'w-4 h-4'} /> <span>{data?.state?.name},</span><span>{data?.country?.code}</span></div>
        <hr />
        <div className="mt-2">
        </div>
      </div>
    </>
  );
};

export default MapBeachNewCard;
