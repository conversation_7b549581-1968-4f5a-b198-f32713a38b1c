"use client";
import React, { useCallback, useState } from "react";
import { DownArrowIcon, UPArrowIcon } from "../social-icons/icons";
import { debounce } from "lodash";

const MapFilterByType = ({ typeFilter, setTypeFilter, setRefresh }) => {
  const [toggleBeachTypeFilter, setToggleBeachTypeFilter] = useState(true);
  const list = [
    { id: 1, label: "Best Beaches", value: "best" },
    { id: 2, label: "Family Friendly", value: "Family Friendly" },
    { id: 3, label: "Dog Friendly", value: "dogs" },
    { id: 4, label: "Camping", value: "camping" },
    { id: 5, label: "Disability", value: "disability" },
    { id: 6, label: "Surfing", value: "surfing" },
  ];
  const handleClick = (item) => {
    setTypeFilter((prev) => {
      // Check if the item is already in the array
      if (prev.includes(item?.value)) {
        // Remove the item if it already exists
        return prev.filter((value) => value !== item?.value);
      } else {
        // Add the item if it doesn't exist
        return [...prev, item?.value];
      }
    });
    delayedSearch();
  };

  const delayedSearch = useCallback(
    debounce((q) => sendQuery(), 700),
    []
  );
  const sendQuery = () => {
    setRefresh((prv) => !prv);
  };
  return (
    <>
      <div
        className="flex items-center justify-between gap-x-3 truncate cursor-pointer "
        onClick={() => {
          setToggleBeachTypeFilter(!toggleBeachTypeFilter);
        }}
      >
        <span className="font-semibold text-xl">Type Of Beaches</span>
        {toggleBeachTypeFilter ? (
          <span>
            <UPArrowIcon width={16} height={16} />
          </span>
        ) : (
          <span>
            <DownArrowIcon width={20} height={20} />
          </span>
        )}
      </div>
      {toggleBeachTypeFilter ? (
        <div className="my-4">
          <ul className="grid xl:grid-cols-12 grid-flow-row gap-4">
            {list?.map((i, index) => {
              return (
                <li
                  key={i?.id}
                  className={`${
                    typeFilter?.includes(i?.value)
                      ? "text-primary-600 font-semibold border-primary-600 shadow-[0px_4px_22px_0px_#319FD926]"
                      : ""
                  } xl:col-span-6 3xl:p-[10px] py-[10px] px-[20px] text-sm rounded-xl border border-[#E8E8E8] text-center hover:text-primary-600 hover:font-semibold bg-gray-100/50 hover:border-primary-600 hover:shadow-[0px_4px_22px_0px_#319FD926] cursor-pointer`}
                  onClick={() => {
                    handleClick(i);
                  }}
                >
                  {i?.label}
                </li>
              );
            })}
          </ul>
        </div>
      ) : null}
    </>
  );
};

export default MapFilterByType;
