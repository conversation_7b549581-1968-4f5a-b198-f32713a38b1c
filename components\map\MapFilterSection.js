"use client";
import React, { useEffect, useRef, useState } from "react";
import {
  DownArrowIcon,
  LeftSquareArrowIcon,
  LineDotFilterIcon,
  RightSquareArrowIcon,
  UPArrowIcon,
} from "../social-icons/icons";
import { Checkbox, Slider } from "antd";
import { BeachDetailsFilter } from "@/data/StaticFilterData";
import CustomButton from "../Custom-Button";
import { isEmptyObject } from "@/helper/functions";

const MapFilterSection = ({
  activityStatus,
  setActivityStatus,
  setRefresh,
  isFilterSubmit,
  setIsFilterSubmit,
  toggleSidebar,
  setToggleSidebar,
}) => {
  const [selectedId, setSelectedId] = useState("");
  const [openDrawerList, setOpenDrawerList] = useState([]);
  const [applyFiltered, setApplyFiltered] = useState({
    Amenities: false,
    Distance: false,
    Activities: false,
    Food: false,
    Access: false,
  });
  // const isOpenRef = useRef(null);
  const list = [
    { id: 1, label: "Amenities", values: "Amenities" },
    // { id: 2, label: "Distance away", values: "Distance" },
    { id: 3, label: "Activities", values: "Activities" },
    { id: 4, label: "Food", values: "Food" },
    { id: 5, label: "Access", values: "Access" },
  ];

  // const handleClickOutside = (event) => {
  //   if (isOpenRef?.current && !isOpenRef.current.contains(event.target)) {
  //     setSelectedId("");
  //   }
  // };

  // useEffect(() => {
  //   document?.addEventListener("mousedown", handleClickOutside);
  //   return () => {
  //     document?.removeEventListener("mousedown", handleClickOutside);
  //   };
  // }, []);
  const updateFilteredStatus = () => {
    let updatedFilters = { ...applyFiltered };
    if (isEmptyObject(activityStatus)) {
      setApplyFiltered({
        Amenities: false,
        Distance: false,
        Activities: false,
        Food: false,
        Access: false,
      });
      return;
    }

    BeachDetailsFilter.forEach((filterCategory) => {
      const categoryTitle = filterCategory.title;
      // Check if any selected filters match the items in this category
      const matchFound = filterCategory.items.some(
        (item) => activityStatus[item.value]
      );

      // Update the applyFiltered state for the category if a match is found
      if (matchFound) {
        updatedFilters[categoryTitle] = true;
      } else {
        updatedFilters[categoryTitle] = false;
      }
    });
    setApplyFiltered(updatedFilters);
  };

  const handleToggle = (itemId) => {
    setActivityStatus((prevStatus) => ({
      ...prevStatus,
      [itemId]: !!!prevStatus?.[itemId],
    }));
  };
  const onSubmit = (e, values) => {
    e.stopPropagation();
    setSelectedId(" ");
    setIsFilterSubmit(false);
  };

  const onAllClear = (e) => {
    setActivityStatus({});
    setSelectedId(" ");
    setRefresh((prv) => !prv);
    setApplyFiltered({
      Amenities: false,
      Distance: false,
      Activities: false,
      Food: false,
      Access: false,
    });
  };
  const onClear = (e) => {
    e.stopPropagation();
    const ids = openDrawerList?.map((item) => item.id);

    for (const key in activityStatus) {
      if (ids.includes(key)) {
        delete activityStatus[key];
      }
    }
    setActivityStatus(activityStatus);
    setSelectedId(" ");
    setIsFilterSubmit(false);
  };

  useEffect(() => {
    if (!isFilterSubmit) {
      updateFilteredStatus();
    }
  }, [isFilterSubmit]);

  return (
    <div className="relative flex justify-between items-center px-3 pb-1">
      <div className="flex">
      {!toggleSidebar ? (
        <div className="flex items-center justify-center px-2">
          <button
            className="rounded-[9px] border border-gray-400 bg-[#FAFBFB]  p-2 w-fit"
            onClick={() => {
              setToggleSidebar(!toggleSidebar);
            }}
          >
            {<RightSquareArrowIcon />}
          </button>
        </div>
      ) : null}

      <div className="flex items-center justify-center px-2">
        <LineDotFilterIcon />
      </div>

      <div className=" flex gap-[1.5rem] flex-wrap">
        {list?.map((i, index) => {
          const itemList = BeachDetailsFilter.find(
            (section) => section.title === i?.values
          )?.items;
          return (
            <button
              className={`border  xl:px-4 xl:py-2 p-2 rounded-[9px] relative ${
                applyFiltered[i?.values]
                  ? "border-primary-600"
                  : "border-gray-[#D0D5DD]"
              }`}
              key={i?.id}
              onClick={(e) => {
                e.stopPropagation();
                if (selectedId !== i?.id) {
                  setSelectedId(i?.id);
                } else {
                  setSelectedId(" ");
                }
                setIsFilterSubmit(true);
                setOpenDrawerList(itemList);
              }}
            >
              <p className="flex items-center justify-between gap-x-3 truncate font-semibold">
                <span>{i?.label}</span>
                {selectedId !== i?.id ? (
                  <span>
                    <DownArrowIcon className="opacity-50" />

                    {/* <UPArrowIcon className="opacity-50" /> */}
                  </span>
                ) : (
                  <span>
                    <DownArrowIcon className="opacity-50" />
                  </span>
                )}
              </p>
              {i?.id === selectedId && (
                <div
                  className="absolute w-[340px] max-h-[452px] bg-white shadow-lg z-[99] top-12 xl:left-0 right-0 rounded-lg px-5 pt-5 overflow-y-auto overflow-x-hidden"
                  // ref={isOpenRef}
                >
                  {itemList?.length ? (
                    <ul className="flex flex-col gap-y-2 ">
                      {itemList?.map((item) => (
                        <li
                          key={item.id}
                          className="flex justify-between items-center"
                        >
                          <span>{item.label}</span>
                          <span
                            className=""
                            onClick={(e) => {
                              e.stopPropagation();
                            }}
                          >
                            <Checkbox
                              onChange={() => {
                                handleToggle(item?.id);
                              }}
                              id={item?.id}
                              checked={!!activityStatus?.[item?.id]}
                            />
                          </span>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    selectedId === 2 && (
                      <>
                        <p className="mb-4 font-medium text-justify ms-1">
                          Distance away (50 miles away)
                        </p>
                        <Slider defaultValue={50} />
                      </>
                    )
                  )}

                  <div className="mt-4 sticky bottom-0 bg-white pb-5">
                    <hr />
                    <div className="flex justify-end items-center gap-x-5 mt-4">
                      <span
                        className="text-error-red-500 -mt-2 cursor-pointer hover:text-red-600 font-semibold hover:scale-105"
                        onClick={onClear}
                      >
                        Clear All
                      </span>
                      <CustomButton
                        onClick={(e) => {
                          onSubmit(e, i);
                        }}
                      >
                        Submit
                      </CustomButton>
                    </div>
                  </div>
                </div>
              )}
            </button>
          );
        })}
      </div>
      </div>
      <div
        className="text-error-red-500 cursor-pointer hover:text-red-600 font-semibold hover:scale-105"
        onClick={onAllClear}
      >
        Clear All
      </div>
    </div>
  );
};

export default MapFilterSection;
