"use client";
import { Editor<PERSON>ontent, FinalImageGenerator, defaultImage } from "@/helper/functions";
import React from "react";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/swiper-bundle.min.css";
import { Pagination } from "swiper";
import {
  CampingIcon,
  DisabilityIcon,
  DogIcon,
  FamilyIcon,
  GayIcon,
  NudeIcon,
  SurfingIcon,
} from "../social-icons/icons";
const MapLocationPopupCard = ({
  locationAddress,
  images,
  beachDescription,
  beachCategory
}) => {
  const address = JSON?.parse(locationAddress);
  const imageList = JSON?.parse(images);
  const description = JSON?.parse(beachDescription);
  const category = JSON?.parse(beachCategory);
  const firstImage = FinalImageGenerator(imageList[0]);
  const Tags = [
    {
      id: 1,
      label: "Nude",
      value: "nude",
      icon: <NudeIcon className="w-[22px] h-[22px]" />,
    },
    {
      id: 2,
      label: "Surfing",
      value: "surfing",
      icon: <SurfingIcon className="w-[18px] h-[18px]" />,
    },
    // { id: 3, label: "Family", value: "family",icon:<FamilyIcon className="w-[18px] h-[18px]"/> },
    {
      id: 4,
      label: "Gay",
      value: "lgbtqplus",
      icon: <GayIcon className="w-[18px] h-[18px]" />,
    },
    {
      id: 5,
      label: "Dog",
      value: "dogs",
      icon: <DogIcon className="w-[18px] h-[18px]" />,
    },
    {
      id: 6,
      label: "Camping",
      value: "camping",
      icon: <CampingIcon className="w-[18px] h-[18px]" />,
    },
    {
      id: 7,
      label: "Disability",
      value: "disability",
      icon: <DisabilityIcon className="w-[18px] h-[18px]" />,
    },
  ];
  // Check if restrooms, showers, and lifeguard are all true
  const hasAllCategories =
    category?.restRooms &&
    category?.showers &&
    category?.lifeguard;

  const amenitiesTag = Tags?.find((tag) => {
    if (tag?.value === "family" && hasAllCategories) {
      // Prioritize the Family tag only when all categories are true
      return true;
    }
    // For other tags, check if they exist in category and are true
    return category?.[tag?.value];
  });

  const selectedTag = amenitiesTag || {
    id: 0,
    label: "Family",
    value: "family",
    icon: <FamilyIcon className="w-[18px] h-[18px]" />,
  };
  return (
    <>
      {imageList?.length && firstImage !== defaultImage ? (
        <Swiper
          modules={[Pagination]}
          pagination={{ clickable: true }}
          spaceBetween={50}
          slidesPerView={1}
        // onSlideChange={() => console.log("Slide change")}
        // onSwiper={(swiper) => console.log(swiper)}
        >
          {imageList?.map((image, index) => {
            return (
              <SwiperSlide key={index + 1}>
                <div className="w-auto h-[273px] relative rounded-2xl">
                  <Image
                    src={FinalImageGenerator(image)}
                    fill
                    className="object-cover rounded-2xl"
                    alt="Sandee | Beach Card"
                  />
                </div>
              </SwiperSlide>
            );
          })}
        </Swiper>
      ) : (
        <div className="w-auto h-[273px] relative rounded-2xl">
          <iframe
            className="h-full w-full pointer-events-none rounded-2xl"
            loading="lazy"
            src={`https://maps.google.com/?q=${address?.lat},${address?.long}&ie=UTF8&iwloc=&output=embed&t=k&disableDefaultUI=true`}
          ></iframe>
        </div>
      )}

      <div class="flex flex-1 flex-col pt-4 text-sm !leading-tight @[21.375rem]/place-card:text-base px-4 pb-4">
        <div class="flex flex-1 flex-col">
          <div class="flex items-baseline gap-2.5">
            <div class="flex-1 font-semibold line-clamp-2">{address?.name}</div>
            <span class="flex shrink-0 text-foreground">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="shrink-0 transform-cpu size-[1em] relative top-[.125em] ml-[-2px] mr-[.125em] fill-current"
              >
                <path d="m12.63 4.401 2.044 4.128a.656.656 0 0 0 .527.385l4.513.669a.695.695 0 0 1 .386 1.196l-3.253 3.227a.682.682 0 0 0-.206.617l.785 4.538a.707.707 0 0 1-1.029.746l-4.063-2.147a.758.758 0 0 0-.668 0l-4.063 2.147a.707.707 0 0 1-1.029-.746l.785-4.59a.682.682 0 0 0-.206-.617L3.862 10.78a.695.695 0 0 1 .424-1.196l4.513-.669a.656.656 0 0 0 .527-.385L11.37 4.4a.695.695 0 0 1 1.26 0Z"></path>
              </svg>
              {((address?.rating100 ?? 80) / 20).toFixed()}
              <span class="ml-[.125em] text-[#00000080]">{`(${address?.rating100 ?? 80
                })`}</span>
            </span>
          </div>
          <div class="flex gap-1 leading-tight mt-1 text-[#00000080]">
            {selectedTag?.icon}
            <span class="capitalize">{selectedTag?.label}</span>
          </div>
          {/* <div class="mt-1 text-[#00000080]">{`${address?.country?.name}, ${address?.state?.name}, ${address?.city?.name}`}</div> */}
          <div class="mt-1 text-[#00000080] uppercase">{`${address?.city?.name}, ${address?.country?.code}`}</div>
          <EditorContent
            className="mt-2 line-clamp-3 leading-5 new-map-design-description"
            value={description?.summary?.replace("<p><br></p>", "") ||
              description?.introduction?.replace("<p><br></p>", "")} />
          {/* <div
            class="mt-2 line-clamp-3 leading-5 new-map-design-description"
            dangerouslySetInnerHTML={{
              __html:
                description?.summary?.replace("<p><br></p>", "") ||
                description?.introduction?.replace("<p><br></p>", ""),
            }}
          /> */}
        </div>
      </div>
    </>
  );
};

export default MapLocationPopupCard;
