import { SiteDataPageWise, siteMetadata } from "@/data/siteMetadata";
import React from "react";

const MapPageJSONLD = ({ listData }) => {
  const MapBreadCumber = {
    "@context": "https://schema.org/",
    "@type": "BreadcrumbList",
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        name: "Home",
        item: `https://sandee.com/`,
      },
      {
        "@type": "ListItem",
        position: 2,
        name: "Map",
        item: `https://sandee.com/map`,
      }
    ],
  };

  const webPageSchema = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    url: "https://sandee.com/map",
    name: SiteDataPageWise?.map?.title || "Map",
    description: SiteDataPageWise?.map?.description || "Map",
    publisher: {
      "@type": "Organization",
      name: siteMetadata?.title || "Sandee",
    },
  };

  return (
    <>
      <script
        type="application/ld+json"
        id="application/ld+jsonMapBreadCumber"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(MapBreadCumber) }}
      ></script>
      <script
        type="application/ld+json"
        id="application/ld+jsonMapBreadCumber"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(webPageSchema) }}
      ></script>
    </>
  );
};

export default MapPageJSONLD;
