"use client"
import React, { useEffect, useState } from 'react'
import MapWithBeachesNew from './MapWithBeachesNew'
import MapWithBeachesNewMob from './MapWithBeachesNewMob'
import { isMobileView } from '@/helper/functions'

const MapResposive = ({
    latStr,
    longStr,
    getSingleBeachMapCoordinateData,
    mode = "all",
    isFilter = true
}) => {
    const [isMobile, setIsMobile] = useState(null);

    useEffect(() => {
        setIsMobile(isMobileView());
    }, []);
    if (isMobile === null) {
        // Render a placeholder while determining the view
        return <div>Loading...</div>;
    }
    return (
        <>
            {isMobile ?
                <div className="block sm:hidden">
                    <MapWithBeachesNewMob
                        latStr={Number(latStr) || null}
                        longStr={Number(longStr) || null}
                        popupBeachData={
                            getSingleBeachMapCoordinateData?.data?.length
                                ? getSingleBeachMapCoordinateData?.data[0]
                                : {}
                        }
                        isFilter={isFilter}
                        mode={mode}
                    />
                </div> : <div className="hidden sm:block">
                    <MapWithBeachesNew
                        latStr={Number(latStr) || null}
                        longStr={Number(longStr) || null}
                        popupBeachData={
                            getSingleBeachMapCoordinateData?.data?.length
                                ? getSingleBeachMapCoordinateData?.data[0]
                                : {}
                        }
                        isFilter={isFilter}
                        mode={mode}
                    // isSharkToggle={true}
                    // isNudeToggle={true}
                    />
                </div>}
            {/* <div className="hidden sm:block">
        <MapWithBeachesNew
          latStr={Number(latStr) || null}
          longStr={Number(longStr) || null}
          popupBeachData={
            getSingleBeachMapCoordinateData?.data?.length
              ? getSingleBeachMapCoordinateData?.data[0]
              : {}
          }
          isFilter={true}
        // isSharkToggle={true}
        // isNudeToggle={true}
        />
      </div> */}

        </>
    )
}

export default MapResposive