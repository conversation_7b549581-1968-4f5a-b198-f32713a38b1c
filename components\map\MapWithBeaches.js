"use client";
import React, { useRef, useEffect, useState, useContext } from "react";
import mapboxgl from "!mapbox-gl"; // eslint-disable-line import/no-webpack-loader-syntax
import { CustomGrid } from "../Custom-Display";
import { FinalImageGenerator } from "@/helper/functions";
import BeachCard from "../Cards/BeachCard";
import FilterForMap from "./FilterForMap";
import InfiniteScroll from "react-infinite-scroll-component";
import { CancelIcon, CheckRoundIcon, LoaderIcon } from "../social-icons/icons";
import { createRoot } from "react-dom/client";
import { useRouter } from "next/navigation";
import { valContext } from "@/helper/context/ValContext";
import { PostSearchForMap } from "@/app/(HomeHeader)/action";
import SingleBeachShowModal from "./SingleBeachShowModal";
import ReadOnlyRating from "../Common/ReadOnlyRating";
import MapBeachCard from "./MapBeachCard";
import SearchBar from "../SearchBar";
mapboxgl.accessToken = process.env.NEXT_PUBLIC_MAPBOX_API_KEY;
export default function MapWithBeaches({ latStr, longStr, popupBeachData }) {
  let isPopupBeachData = Object.keys(popupBeachData).length > 0;
  const [beaches, setBeaches] = useState([]);
  const [beachesCount, setBeachesCount] = useState([]);
  const [isLoadingCount, setIsLoadingCount] = useState(true);
  const [loaded, setLoaded] = useState(false);
  const [location, setLocation] = useState(null);
  const [CurrentLocation, setCurrentLocation] = useState(null);
  const [refresh, setRefresh] = useState(false);
  const [openDrawer, setOpenDrawer] = useState(false);
  const [query, setQuery] = useState("");
  const {
    activityStatus,
    setActivityStatus,
    listicleLocation,
    setListicleLocation,
  } = useContext(valContext);
  const [loadoff, setLoadOff] = useState(true);
  const [pagination, setPagination] = useState({
    limit: 10,
    page: 1,
  });

  const mapContainer = useRef(null);
  const map = useRef(null);
  const router = useRouter();
  let popup = "";

  const renderPopupContent = (locationAddress) => {
    const address = JSON.parse(locationAddress);
    const popupContainer = document.createElement("a");
    createRoot(popupContainer).render(
      <div className="cursor-pointer ">
        {/* <Link href={`${address?.link}`}>{address?.name}</Link> */}
        <div
          className="flex justify-end m-1"
          id="popup-close"
          onClick={() => {
            popup.remove();
          }}
        >
          <div className="border-2 border-primary-600 rounded-lg transition-transform duration-300 transform hover:scale-105 w-fit">
            <CancelIcon width={12} height={12} fill={"#00aae3"} />
          </div>
        </div>

        <div
          onClick={() => {
            router?.push(address?.link);
            setTimeout(() => {
              popup.remove();
            }, 800);
          }}
          className="px-3"
        >
          <p className="text-base font-medium hover:underline">
            {address?.name}
          </p>
          <div className="flex gap-x-2 items-center text-sm mb-2">
            <span className="">{(address?.rating100 ?? 80) / 20}</span>
            <ReadOnlyRating
              rating={(address?.rating100 ?? 80) / 20}
              width="12"
              height="12"
            />
          </div>
        </div>
      </div>
    );
    return popupContainer;
  };

  const FetchAndSetDelayedBeachesCount = async () => {
    if (!map.current) return;
    const bounds = map.current.getBounds();
    const FilteredActivities = Object.fromEntries(
      Object.entries(activityStatus).filter(([key, value]) => value)
    );
    const FilteredActivitiesArray =
      Object.getOwnPropertyNames(FilteredActivities);
    const payload = {
      point1: bounds.getSouthWest().toArray().reverse(),
      point2: bounds.getNorthEast().toArray().reverse(),
      point3: bounds.getNorthWest().toArray().reverse(),
      point4: bounds.getSouthEast().toArray().reverse(),
      ...(FilteredActivitiesArray?.length
        ? { beachCategories: FilteredActivitiesArray }
        : {}),
      // ...FilteredActivities,
      getCounts: true,
    };
    const Results = await PostSearchForMap(payload);
    const beachLists = Results?.data?.beaches?.rows;
    if (listicleLocation) {
      setListicleLocation(null);
    }

    const AllBeachWithRefinedDataCount = beachLists?.map((el) => ({
      ...el,
      lat: el?.GeoLoc?.coordinates?.[1] ?? 0,
      long: el?.GeoLoc?.coordinates?.[0] ?? 0,
      locationAddress: {
        link: `/map/${el?.nameSlug}/@${el?.GeoLoc?.coordinates?.[1] ?? 0},${
          el?.GeoLoc?.coordinates?.[0] ?? 0
        }`,
        name: el?.name,
        rating100: el?.rating100,
      },
    }));

    setBeachesCount(AllBeachWithRefinedDataCount);
    setIsLoadingCount(false);
  };

  const FetchAndSetDelayedBeaches = async () => {
    if (!map.current) return;
    const bounds = map.current.getBounds();
    const FilteredActivities = Object.fromEntries(
      Object.entries(activityStatus).filter(([key, value]) => value)
    );
    const FilteredActivitiesArray =
      Object.getOwnPropertyNames(FilteredActivities);
    const payload = {
      point1: bounds.getSouthWest().toArray().reverse(),
      point2: bounds.getNorthEast().toArray().reverse(),
      point3: bounds.getNorthWest().toArray().reverse(),
      point4: bounds.getSouthEast().toArray().reverse(),
      ...(FilteredActivitiesArray?.length
        ? { beachCategories: FilteredActivitiesArray }
        : {}),
      getCounts: false,
      ...(query?.length ? { searchName: query } : {}),
    };

    const Results = await PostSearchForMap(payload, { ...pagination });
    const beachLists = Results?.data?.beaches?.rows;
    const beachListsCounts = Results?.data?.beaches?.count;
    const AllBeachWithRefinedData = beachLists?.map((el) => ({
      ...el,
      lat: el?.GeoLoc?.coordinates?.[1] ?? 0,
      long: el?.GeoLoc?.coordinates?.[0] ?? 0,
      locationAddress: {
        link: `/map/${el?.nameSlug}/@${el?.GeoLoc?.coordinates?.[1] ?? 0},${
          el?.GeoLoc?.coordinates?.[0] ?? 0
        }`,
        name: el?.name,
        rating100: el?.rating100,
      },
    }));

    if (beachLists?.length && pagination.page > 1) {
      setBeaches((prev) => [...prev, ...AllBeachWithRefinedData]);
      if (beachListsCounts <= 10) {
        setLoadOff(false);
      }
    } else if (beachLists?.length && pagination.page === 1) {
      setBeaches((prev) => [...AllBeachWithRefinedData]);
      if (beachListsCounts <= 10) {
        setLoadOff(false);
      }
    } else {
      setLoadOff(false);
    }
  };

  const SetBeaches = () => {
    if (!mapContainer.current || !beachesCount?.length || !loaded) {
      return;
    }

    if (map.current.getSource("beaches")) {
      map.current.getSource("beaches").setData({
        type: "FeatureCollection",
        features: beachesCount?.map((beach) => ({
          type: "Feature",
          geometry: {
            type: "Point",
            coordinates: [beach.long, beach.lat],
          },
          properties: beach,
        })),
      });
    } else {
      map.current.addSource("beaches", {
        type: "geojson",

        data: {
          type: "FeatureCollection",
          crs: {
            type: "name",
            properties: {
              name: "urn:ogc:def:crs:OGC:1.3:CRS84",
            },
          },
          features: beachesCount?.map((beach) => ({
            type: "Feature",
            geometry: {
              type: "Point",
              coordinates: [beach.long, beach.lat, 0],
            },
            properties: beach,
          })),
        },
        cluster: true,
        clusterMaxZoom: 14, // Max zoom to cluster points on
        clusterRadius: 50, // Radius of each cluster when clustering points (defaults to 50)
      });
      map.current.addLayer({
        id: "clusters",
        type: "circle",
        source: "beaches",
        filter: ["has", "point_count"],
        paint: {
          // Use step expressions (https://docs.mapbox.com/style-spec/reference/expressions/#step)
          // with three steps to implement three types of circles:
          //   * Blue, 20px circles when point count is less than 100
          //   * Yellow, 25px circles when point count is between 100 and 750
          //   * Pink, 30px circles when point count is greater than or equal to 750
          "circle-color": [
            "step",
            ["get", "point_count"],
            "#fff000",
            100,
            "#fff000",
            750,
            "#f28cb1",
          ],
          "circle-radius": [
            "step",
            ["get", "point_count"],
            25,
            100,
            25,
            750,
            30,
          ],
        },
      });

      map.current.addLayer({
        id: "cluster-count",
        type: "symbol",
        source: "beaches",
        filter: ["has", "point_count"],
        layout: {
          "text-field": ["get", "point_count_abbreviated"],
          "text-font": ["DIN Offc Pro Medium", "Arial Unicode MS Bold"],
          "text-size": 16,
        },
      });
      map.current.loadImage("/marker2.png", (error, image) => {
        if (error) throw error;
        map.current.addImage("beach-icon", image);

        // Add the symbol layer with the beach icon and beach name
        map.current.addLayer({
          id: "unclustered-point",
          type: "symbol",
          source: "beaches",
          filter: ["!", ["has", "point_count"]],
          layout: {
            "icon-image": "beach-icon",
            "icon-size": 1, // Adjust the size of the icon
            "icon-allow-overlap": true,
            "text-field": ["get", "name"], // Assuming your data source has a 'beachName' property
            "text-size": 18, // Adjust the size of the text
            "text-offset": [1, 0], // Position the text slightly below the icon
            "text-anchor": "left", // Anchor text to be above the icon
          },
          paint: {
            // "text-color": "rgb(25, 128, 57)", // Set text color
            "text-color": "#FF6B00", // Set text color
            "text-width": 12,
            "text-halo-color": "#fff", // Optional: Add a halo around the text for better readability
            "text-halo-width": 2, // Optional: Set halo width
          },
        });
      });

      // map.current.loadImage("/marker.png", (error, image) => {
      //   if (error) throw error;
      //   map.current.addImage("beach-icon", image);

      //   // Add the symbol layer with the beach icon
      //   map.current.addLayer({
      //     id: "unclustered-point",
      //     type: "symbol",
      //     source: "beaches",
      //     filter: ["!", ["has", "point_count"]],
      //     layout: {
      //       "icon-image": "beach-icon",
      //       // "icon-size": 1, // Adjust the size of the icon
      //       // "icon-allow-overlap": true
      //     },
      //     paint: {
      //       // "icon-opacity": 0.9
      //     },
      //   });
      // });
      // map.current.addLayer({
      //   id: "unclustered-point",
      //   type: "circle",
      //   source: "beaches",
      //   filter: ["!", ["has", "point_count"]],
      //   paint: {
      //     "circle-radius": 10,
      //     "circle-color": ["get", "color"], // use color from feature properties
      //     "circle-color": "#FF6B00", // use color from feature properties
      //     "circle-stroke-color": "white",
      //     "circle-stroke-width": 1,
      //     "circle-opacity": 0.9,
      //   },
      // });
      // Add current location marker
      if (CurrentLocation) {
        new mapboxgl.Marker({ color: "red" }) // Blue marker for current CurrentLocation
          .setLngLat([CurrentLocation[0], CurrentLocation[1]])
          .addTo(map.current);
      }
      // inspect a cluster on click
      map.current.on("click", "clusters", (e) => {
        const features = map.current.queryRenderedFeatures(e.point, {
          layers: ["clusters"],
        });
        const clusterId = features[0].properties.cluster_id;
        map.current
          .getSource("beaches")
          .getClusterExpansionZoom(clusterId, (err, zoom) => {
            if (err) return;

            map.current.easeTo({
              center: features[0].geometry.coordinates,
              zoom: zoom,
            });
          });
      });

      popup = new mapboxgl.Popup({
        closeButton: false,
        // closeOnClick: false,
      });
      map.current.on("click", "unclustered-point", (e) => {
        const coordinates = e.features[0].geometry.coordinates.slice();

        const { locationAddress } = e.features[0].properties;
        while (Math.abs(e.lngLat.lng - coordinates[0]) > 180) {
          coordinates[0] += e.lngLat.lng > coordinates[0] ? 360 : -360;
        }

        popup
          .setLngLat(coordinates)
          .setDOMContent(renderPopupContent(locationAddress))
          // .setHTML(`${locationAdress}`)
          .addTo(map.current);

        const closeButton = document.getElementById("popup-close");
        if (closeButton) {
          closeButton.addEventListener("click", () => {
            popup.remove();
          });
        }
      });

      map.current.on("mouseenter", "clusters", () => {
        map.current.getCanvas().style.cursor = "pointer";
      });
      map.current.on("mouseleave", "clusters", () => {
        map.current.getCanvas().style.cursor = "";
      });
      map.current.on("mouseenter", "unclustered-point", () => {
        map.current.getCanvas().style.cursor = "pointer";
      });
      map.current.on("mouseleave", "unclustered-point", () => {
        map.current.getCanvas().style.cursor = "";
      });
      map.current.addControl(new mapboxgl.ScaleControl());
    }
  };

  useEffect(() => {
    if (latStr && longStr && map) {
      map?.current?.jumpTo({ center: [longStr, latStr], zoom: 12 });
      var container = document?.querySelector("#map-beach-modal");
      if (container) container.scrollTop = 0;
    }
  }, [latStr, longStr]);

  const handleMoveEnd = () => {
    // setIsloading(true);
    setRefresh((prev) => !prev);
  };

  const getLocation = () => {
    if (typeof window !== "undefined" && window.navigator.geolocation) {
      window.navigator.geolocation.getCurrentPosition(
        (position) => {
          setLocation([position.coords.longitude, position.coords.latitude]);
          setCurrentLocation([
            position.coords.longitude,
            position.coords.latitude,
          ]);
        },
        (err) => {
          if (listicleLocation?.length)
            setLocation([listicleLocation[0], listicleLocation[1]]);
          else if (latStr && longStr) setLocation([longStr, latStr]);
          else setLocation([-118.4117325, 34.020479]); // los angeles coordinates
        }
      );
    } else {
      if (listicleLocation?.length)
        setLocation([listicleLocation[0], listicleLocation[1]]);
      else if (latStr && longStr) setLocation([longStr, latStr]);
      else setLocation([-118.4117325, 34.020479]); // los angeles coordinates
    }
  };

  useEffect(() => {
    if (!location) return;

    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: "mapbox://styles/mapbox/streets-v12",
      center: location,
      zoom: listicleLocation?.length && !CurrentLocation ? 4 : 9.4,
      minZoom: 3,
      maxBounds: [
        [-180, -85], // Southwest coordinates
        [180, 85], // Northeast coordinates
      ],
      projection: { name: "mercator" },
    });

    map.current.on("load", () => {
      setLoaded(true);
      // setIsloading(true);
      setRefresh((prev) => !prev);
    });
    if (latStr && longStr) {
      map?.current?.jumpTo({ center: [longStr, latStr], zoom: 12 });
    }
    map.current.on("moveend", handleMoveEnd);

    return () => {
      map.current.off("moveend", handleMoveEnd);
      map.current.remove();
    };
  }, [location]);

  useEffect(() => {
    getLocation();
  }, []);

  useEffect(() => {
    SetBeaches();
  }, [beachesCount, loaded]);

  useEffect(() => {
    if (!openDrawer) {
      // setIsloading(true);
      setIsLoadingCount(true);
      setPagination({
        limit: 10,
        page: 1,
      });
      setBeaches([]);
      setLoadOff(true);
    }
    const getData = setTimeout(() => {
      if (!!!openDrawer) {
        FetchAndSetDelayedBeaches();
        FetchAndSetDelayedBeachesCount();
      }
    }, 700);
    return () => clearTimeout(getData);
  }, [refresh, openDrawer]);

  useEffect(() => {
    if (pagination?.page >= 2) {
      // setIsloading(true);
      FetchAndSetDelayedBeaches();
    }
  }, [pagination.page]);

  useEffect(() => {
    FetchAndSetDelayedBeaches();
  }, [query]);
  const fetchScrollLoaddata = () => {
    setPagination((prev) => ({ ...prev, page: prev?.page + 1 }));
  };

  return (
    <>
      <h1 className="hidden">Map</h1>
      {Object.keys(popupBeachData).length === 0 ? (
        <h2 className="hidden">Beaches</h2>
      ) : null}
      {Object.keys(popupBeachData).length > 0 ? (
        <h2 className="hidden">{popupBeachData?.name}</h2>
      ) : null}
      <div className="grid md:grid-flow-col md:grid-cols-12 md:col-span-12">
        <div
          className={`relative h-full side-panel-search map-container md:col-span-4 lg:col-span-3 xl:col-span-3 4xl:col-span-2 md:order-1 ${
            isPopupBeachData ? `order-3` : `order-2`
          }`}
        >
          <div className="flex items-center justify-center text-nowrap p-1 md:h-24 h-12 ">
            <FilterForMap
              activityStatus={activityStatus}
              setActivityStatus={setActivityStatus}
              openDrawer={openDrawer}
              setOpenDrawer={setOpenDrawer}
              setQuery={setQuery}
              query={query}
            />
          </div>
          <div
            className="overflow-y-scroll map-sidebar p-4 shadow-inner scrollable-container"
            id="beachMapList"
          >
            <InfiniteScroll
              dataLength={beaches?.length}
              next={fetchScrollLoaddata}
              hasMore={loadoff}
              loader={
                <div className="mt-7">
                  <LoaderIcon />
                </div>
              }
              scrollableTarget="beachMapList"
              endMessage={
                pagination?.page >= 1 && beaches?.length >= 1 && !loadoff ? (
                  <div className="text-center mt-3">
                    <div className="flex justify-center mt-3 mb-1">
                      <CheckRoundIcon />
                    </div>
                    <b>{`You reached the end of the coast! Clear your filters or move the map to a different area.`}</b>
                  </div>
                ) : (
                  <div className="text-red-600 p-3 rounded-lg border border-red-600">{`You reached the end of the coast! Clear your filters or move the map to a different area.`}</div>
                )
              }
            >
              <CustomGrid
                data={beaches}
                className="gap-4 sm:gap-8"
                Component={({ data: dataProps }) => {
                  dataProps.link = `/map/${dataProps?.nameSlug}/@${
                    dataProps?.GeoLoc?.coordinates?.[1] ?? 0
                  },${dataProps?.GeoLoc?.coordinates?.[0] ?? 0}`;
                  if (dataProps?.city?.state?.country?.slug) {
                    // dataProps.link = `/${dataProps?.city?.state?.country?.slug}/${dataProps?.city?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.nameSlug}`; //
                    dataProps.location = `${dataProps?.city?.state?.country?.name}, ${dataProps?.city?.state?.name}, ${dataProps?.city?.name}`; //
                  } else if (dataProps?.country?.slug) {
                    // dataProps.link = `/${dataProps?.country?.slug}/${dataProps?.state?.slug}/${dataProps?.city?.slug}/${dataProps?.nameSlug}`; //
                    dataProps.location = `${dataProps?.country?.name}, ${dataProps?.state?.name}, ${dataProps?.city?.name}`; //
                  } else {
                    // dataProps.link = `/${dataProps?.countrySlug}/${dataProps?.stateSlug}/${dataProps?.citySlug}/${dataProps?.nameSlug}`; //
                    dataProps.location = `${dataProps?.countryName}, ${dataProps?.stateName}, ${dataProps?.cityName}`; //
                  }
                  dataProps.imageSrc = FinalImageGenerator(
                    dataProps?.images?.[0]
                  );
                  // dataProps.target = "_blank";
                  return (
                    <MapBeachCard
                      dataProps={dataProps}
                      map={map}
                      renderPopupContent={renderPopupContent}
                    >
                      {BeachCard({
                        data: { ...dataProps, ...dataProps },
                        className:
                          "lg:aspect-square aspect-video w-full sm:h-[200px] lg:h-[190px]",
                      })}
                    </MapBeachCard>
                  );
                }}
                xs={1}
                xl={1}
              />
            </InfiniteScroll>
          </div>
        </div>
        {Object.keys(popupBeachData).length > 0 ? (
          <div
            className={`relative h-full side-panel-search map-container md:col-span-4 lg:col-span-4 xl:col-span-3 order-2 md:hidden block overflow-hidden`}
          >
            <div
              className="overflow-y-scroll h-[88vh] p-4 shadow-inner scrollable-container"
              id="map-beach-mobile-modal"
            >
              <SingleBeachShowModal data={popupBeachData} />
            </div>
          </div>
        ) : null}
        {/* <div className={`relative ${isPopupBeachData ? `md:col-span-8 lg:col-span-9 xl:col-span-9 md:order-2 order-1`:`md:col-span-8 lg:col-span-9 xl:col-span-9 md:order-2 order-1`}`}> */}
        <div
          className={`relative z-40 md:col-span-8 lg:col-span-9 xl:col-span-9 4xl:col-span-10 md:order-2 order-1`}
        >
          {Object.keys(popupBeachData).length > 0 ? (
            <div
              className={`absolute z-50 top-3 left-3 bottom-3 rounded-xl w-[350px] xl:w-[370px] 2xl:w-[400px] 4xl:w-[490px] bg-white md:block hidden overflow-hidden`}
            >
              <div
                className="overflow-y-scroll h-[88vh] p-4 shadow-inner scrollable-container"
                id="map-beach-modal"
              >
                <SingleBeachShowModal data={popupBeachData} />
              </div>
            </div>
          ) : null}

          <div className="relative z-[30] h-full w-full md:min-h-[90vh] shadow-inner  p-1">
            <div
              ref={mapContainer}
              className="md:map-container md:h-full w-full md:min-h-[90vh] h-[275px] p-1 border-2 border-gray-800 rounded-2xl"
            />
          </div>
          {/* <div className="absolute z-40 md:top-[22px] left-5 top-4 w-[410px]">
            <SearchBar onMap />
          </div> */}
          {isLoadingCount ? (
            <div className="absolute md:top-8 right-4 bg-black md:py-2 py-1 rounded-lg md:px-4 px-2 text-white md:text-base text-[12px] top-4">
              <span className="font-semibold">Loading beaches on map...</span>
            </div>
          ) : null}
        </div>
      </div>
    </>
  );
}
