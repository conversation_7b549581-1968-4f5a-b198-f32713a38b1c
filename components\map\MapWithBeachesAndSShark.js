"use client";
import React, { useRef, useEffect, useState, useContext } from "react";
import mapboxgl from "!mapbox-gl"; // eslint-disable-line import/no-webpack-loader-syntax
// import { CustomGrid } from "../Custom-Display";
// import { FinalImageGenerator } from "@/helper/functions";
// import BeachCard from "../Cards/BeachCard";
// import InfiniteScroll from "react-infinite-scroll-component";
import {
    CheckRoundIcon,
    LeftSquareArrowIcon,
    LineDotFilterIcon,
    LoaderIcon,
} from "../social-icons/icons";
// import { createRoot } from "react-dom/client";
import { valContext } from "@/helper/context/ValContext";
import { PostSearchForMap, PostSharkForMap } from "@/app/(HomeHeader)/action";
import SingleBeachShowModal from "./SingleBeachShowModal";
// import MapBeachCard from "./MapBeachCard";
import SearchBar from "../SearchBar";
// import MapFilterSection from "./MapFilterSection";
// import MapFilterByType from "./MapFilterByType";
// import MapLocationPopupCard from "./MapLocationPopupCard";
import { useRouter } from "next/navigation";
// import SingleBeachModalNew from "./SingleBeachModalNew";
import FilterForMap from "./FilterForMap";
import { Switch } from "antd";
mapboxgl.accessToken = process.env.NEXT_PUBLIC_MAPBOX_API_KEY;
// import MapboxGeocoder from "@mapbox/mapbox-gl-geocoder";
// import "@mapbox/mapbox-gl-geocoder/dist/mapbox-gl-geocoder.css";
export default function MapWithBeachesAndSharkNew({ latStr, longStr, popupBeachData }) {
    let isPopupBeachData = Object.keys(popupBeachData).length > 0;
    const [beaches, setBeaches] = useState([]);
    const [sharkBeachesCount, setSharkBeachesCount] = useState([])
    const [isShark, setIsShark] = useState(localStorage.getItem("isShark") ?? false);

    const [beachesCount, setBeachesCount] = useState([]);
    const [isLoadingCount, setIsLoadingCount] = useState(true);
    const [loaded, setLoaded] = useState(false);
    const [count, setCount] = useState(0);
    const [location, setLocation] = useState(null);
    const [CurrentLocation, setCurrentLocation] = useState(null);
    const [refresh, setRefresh] = useState(false);
    const [isFilterSubmit, setIsFilterSubmit] = useState(false);
    const [query, setQuery] = useState("");
    const [toggleSidebar, setToggleSidebar] = useState(true);
    const [typeFilter, setTypeFilter] = useState([]);
    const [openDrawer, setOpenDrawer] = useState(false);
    const [is3D, setIs3D] = useState(true);
    const isSharkOn = false;
    const rotationAnimation = useRef(null);
    const {
        activityStatus,
        setActivityStatus,
        listicleLocation,
        setListicleLocation,
    } = useContext(valContext);
    const [loadoff, setLoadOff] = useState(true);
    const [pagination, setPagination] = useState({
        limit: 10,
        page: 1,
    });

    const mapContainer = useRef(null);
    const map = useRef(null);
    const router = useRouter();
    // let popup = "";
    // const renderPopupContent = (
    //   locationAddress,
    //   images,
    //   beachDescription,
    //   beachCategory
    // ) => {
    //   const address = JSON?.parse(locationAddress);
    //   const popupContainer = document.createElement("a");
    //   createRoot(popupContainer).render(
    //     <div
    //       className="cursor-pointer rounded-2xl overflow-hidden relative"
    //       id="popup-content"
    //       onClick={() => {
    //         router?.push(address?.link);
    //         setTimeout(() => {
    //           popup.remove();
    //         }, 800);
    //       }}
    //     >
    //       <MapLocationPopupCard
    //         locationAddress={locationAddress}
    //         images={images}
    //         beachDescription={beachDescription}
    //         beachCategory={beachCategory}
    //         popup={popup}
    //       />
    //     </div>
    //   );
    //   return popupContainer;
    // };

    const FetchAndSetDelayedBeachesCount = async () => {
        if (!map.current) return;
        const bounds = map.current.getBounds();
        const FilteredActivities = Object.fromEntries(
            Object.entries(activityStatus).filter(([key, value]) => value)
        );
        const FilteredActivitiesArray =
            Object.getOwnPropertyNames(FilteredActivities);
        const payload = {
            point1: bounds.getSouthWest().toArray().reverse(),
            point2: bounds.getNorthEast().toArray().reverse(),
            point3: bounds.getNorthWest().toArray().reverse(),
            point4: bounds.getSouthEast().toArray().reverse(),
            ...(FilteredActivitiesArray?.length || typeFilter?.length
                ? { beachCategories: [...FilteredActivitiesArray, ...typeFilter] }
                : {}),
            // ...FilteredActivities,
            // getCounts: true,
        };

        //API calling for beach count
        const Results = await PostSearchForMap(payload);
        const beachLists = Results?.data?.beaches?.rows;
        if (listicleLocation) {
            setListicleLocation(null);
        }

        const AllBeachWithRefinedDataCount = beachLists?.map((el) => ({
            ...el,
            lat: el?.GeoLoc?.coordinates?.[1] ?? 0,
            long: el?.GeoLoc?.coordinates?.[0] ?? 0,
            // locationAddress: {
            //   // link: `/map/${el?.nameSlug}/@${el?.GeoLoc?.coordinates?.[1] ?? 0},${
            //   //   el?.GeoLoc?.coordinates?.[0] ?? 0
            //   // }`,
            //   link: el?.city?.state?.country?.slug
            //     ? `/${el?.city?.state?.country?.slug}/${el?.city?.state?.slug}/${el?.city?.slug}/${el?.nameSlug}`
            //     : el?.country?.slug
            //     ? `/${el?.country?.slug}/${el?.state?.slug}/${el?.city?.slug}/${el?.nameSlug}`
            //     : `/${el?.countrySlug}/${el?.stateSlug}/${el?.citySlug}/${el?.nameSlug}`,
            //   lat: el?.GeoLoc?.coordinates?.[1] ?? 0,
            //   long: el?.GeoLoc?.coordinates?.[0] ?? 0,
            //   name: el?.name,
            //   rating100: el?.rating100,
            //   city: el?.city,
            //   state: el?.state,
            //   country: el?.country,
            // },
        }));

        setBeachesCount(AllBeachWithRefinedDataCount);
        setIsLoadingCount(false);
    };

    const FetchSharkAndSetDelayedBeachesCount = async () => {
        if (!map.current) return;

        const bounds = map.current.getBounds();
        const payload = {
            point1: bounds.getSouthWest().toArray().reverse(),
            point2: bounds.getNorthEast().toArray().reverse(),
            point3: bounds.getNorthWest().toArray().reverse(),
            point4: bounds.getSouthEast().toArray().reverse(),
        };

        try {

            const Results = await PostSharkForMap(payload);
            const beachLists = Results?.data?.data?.rows;
            // Refine data for rendering beaches
            const AllBeachWithRefinedDataCount = beachLists?.map((el) => ({
                ...el,
                name: el?.AllBeach?.name,
                lat: el?.AllBeach?.GeoLoc?.coordinates?.[1] ?? 0,
                long: el?.AllBeach?.GeoLoc?.coordinates?.[0] ?? 0,
                locationAddress: {
                    link: `/map/${el?.AllBeach?.nameSlug}/@${el?.AllBeach?.GeoLoc?.coordinates?.[1] ?? 0},${el?.AllBeach?.GeoLoc?.coordinates?.[0] ?? 0
                        }`,
                },
            }));

            setSharkBeachesCount(AllBeachWithRefinedDataCount);
            setIsLoadingCount(false);
        } catch (error) {
            console.error("Error fetching beach data:", error);
        }
    };

    const SetBeaches = async () => {

        if (!mapContainer.current || !beachesCount?.length || !loaded) {
            return;
        }

        // chat gpt convert
        const addMapLayers = (iconUrl, iconSize) => {
            map.current.loadImage(iconUrl, (error, image) => {
                if (error) {
                    console.error("Image load error:", error);
                    return;
                }
                map.current.addImage("beach-icon", image);

                map.current.addLayer({
                    id: "unclustered-point",
                    type: "symbol",
                    source: "beaches",
                    filter: ["!", ["has", "point_count"]],
                    layout: {
                        "icon-image": "beach-icon",
                        "icon-size": iconSize,
                        "icon-allow-overlap": true,
                        "text-field": ["get", "name"],
                        "text-size": 18,
                        "text-offset": [1, 0],
                        "text-anchor": "left",
                        "text-font": ["Open Sans Bold", "Arial Unicode MS Bold"],
                    },
                    paint: {
                        "text-color": isShark ? "red" : "#FF6B00",
                        "text-halo-color": "#fff",
                        "text-halo-width": 1,
                    },
                });
            });
        };

        // Conditionally add data and layers based on `isShark`
        if (isShark) {
            addMapLayers(isShark ? "/shark1.png" : "/Marker4.png", isShark ? 0.4 : 1);
            // map.current.removeSource("beaches")
            // ?.then(() => {
            //   console.log("WORKING THENNNN")
            // });
        }
        if (map.current.getSource("beaches")) {
            map.current.getSource("beaches").setData({
                type: "FeatureCollection",
                features: (isShark ? sharkBeachesCount : beachesCount)?.map((beach) => ({
                    type: "Feature",
                    geometry: {
                        type: "Point",
                        coordinates: [beach.long, beach.lat],
                    },
                    properties: beach,
                })),
            });
        } else {
            map.current.addSource("beaches", {
                type: "geojson",
                data: {
                    type: "FeatureCollection",
                    features: (isShark ? sharkBeachesCount : beachesCount)?.map((beach) => ({
                        type: "Feature",
                        geometry: {
                            type: "Point",
                            coordinates: [beach.long, beach.lat],
                        },
                        properties: beach,
                    })),
                },
                cluster: true,
                clusterMaxZoom: 14,
                clusterRadius: 50,
            });

            map.current.addLayer({
                id: "clusters",
                type: "circle",
                source: "beaches",
                filter: ["has", "point_count"],
                paint: {
                    "circle-color": ["step", ["get", "point_count"], "#fff000", 100, "#fff000", 750, "#fff000"],
                    "circle-radius": ["step", ["get", "point_count"], 25, 100, 25, 750, 30],
                },
            });

            map.current.addLayer({
                id: "cluster-count",
                type: "symbol",
                source: "beaches",
                filter: ["has", "point_count"],
                layout: {
                    "text-field": ["get", "point_count_abbreviated"],
                    "text-font": ["DIN Offc Pro Medium", "Arial Unicode MS Bold"],
                    "text-size": 16,
                },
            });

            // addMapLayers(isShark ? "/shark1.png" : "/Marker4.png", isShark ? 0.4 : 1);
            if (isShark) {
                addMapLayers("/shark1.png", isShark ? 0.4 : 1);
            } else {
                addMapLayers("/Marker4.png", isShark ? 0.4 : 1);
            }
            if (CurrentLocation) {
                new mapboxgl.Marker({ color: "red" })
                    .setLngLat([CurrentLocation[0], CurrentLocation[1]])
                    .addTo(map.current);
            }

            // Event handlers for cluster and unclustered points
            map.current.on("click", "clusters", (e) => {
                const features = map.current.queryRenderedFeatures(e.point, { layers: ["clusters"] });
                if (features.length) {
                    const clusterId = features[0].properties.cluster_id;
                    map.current.getSource("beaches").getClusterExpansionZoom(clusterId, (err, zoom) => {
                        if (err) return;
                        map.current.easeTo({
                            center: features[0].geometry.coordinates,
                            zoom: zoom,
                        });
                    });
                }
            });

            map.current.on("click", "unclustered-point", (e) => {
                const el = e.features[0].properties;
                const coordinatesData = JSON.parse(el?.GeoLoc);
                router.push(
                    `/map/${el?.nameSlug}/@${coordinatesData?.coordinates?.[1] ?? 0},${coordinatesData?.coordinates?.[0] ?? 0}`
                );
            });

            map.current.on("mouseenter", "clusters", () => {
                map.current.getCanvas().style.cursor = "pointer";
            });
            map.current.on("mouseleave", "clusters", () => {
                map.current.getCanvas().style.cursor = "";
            });
            map.current.on("mouseenter", "unclustered-point", () => {
                map.current.getCanvas().style.cursor = "pointer";
            });
            map.current.on("mouseleave", "unclustered-point", () => {
                map.current.getCanvas().style.cursor = "";
            });
            map.current.addControl(new mapboxgl.ScaleControl());
        }
        // ===============================================================

        // if (map.current.getSource("beaches")) {
        //   map.current.getSource("beaches").setData({
        //     type: "FeatureCollection",
        //     features: isShark ? sharkBeachesCount?.map((beach) => ({
        //       type: "Feature",
        //       geometry: {
        //         type: "Point",
        //         coordinates: [beach.long, beach.lat],
        //       },
        //       properties: beach,
        //     })) : beachesCount?.map((beach) => ({
        //       type: "Feature",
        //       geometry: {
        //         type: "Point",
        //         coordinates: [beach.long, beach.lat],
        //       },
        //       properties: beach,
        //     })),
        //   });
        //   // if (isShark) {
        //   //   console.log(isShark, "checkkkkkk")
        //   //   map.current.loadImage("/shark1.png", (error, image) => {
        //   //     if (error) throw error;
        //   //     map.current.addImage("beach-icon", image);

        //   //     // Add unclustered points layer  --- shark attack
        //   //     map.current.addLayer({
        //   //       id: "unclustered-point",
        //   //       type: "symbol",
        //   //       source: "beaches",
        //   //       filter: ["!", ["has", "point_count"]],
        //   //       layout: {
        //   //         "icon-image": "beach-icon",
        //   //         "icon-size": 0.4,
        //   //         "icon-allow-overlap": true,
        //   //         "text-field": ["get", "name"],
        //   //         "text-size": 18,
        //   //         "text-offset": [1, 0],
        //   //         "text-anchor": "left",
        //   //         "text-font": ["Open Sans Bold", "Arial Unicode MS Bold"]
        //   //       },
        //   //       paint: {
        //   //         "text-color": "red", // Set text color FF6B00
        //   //         "text-halo-color": "#fff  ", // Optional: Add a halo around the text for better readability
        //   //         "text-halo-width": 1, // Optional: Set halo width
        //   //       },
        //   //     });
        //   //   });
        //   // }
        //   // else {
        //   //   console.log(isShark, "checkkkkkk2222")
        //   //   map.current.loadImage("/Marker4.png", (error, image) => {
        //   //     if (error) throw error;
        //   //     map.current.addImage("beach-icon", image);

        //   //     // Add the symbol layer with the beach icon and beach name
        //   //     map.current.addLayer({
        //   //       id: "unclustered-point",
        //   //       type: "symbol",
        //   //       source: "beaches",
        //   //       filter: ["!", ["has", "point_count"]],
        //   //       layout: {
        //   //         "icon-image": "beach-icon",
        //   //         "icon-size": 1,
        //   //         "icon-allow-overlap": true,
        //   //         "text-field": ["get", "name"], // Assuming your data source has a 'beachName' property
        //   //         "text-size": 18, // Adjust the size of the text
        //   //         "text-offset": [1, 0], // Position the text slightly below the icon
        //   //         "text-anchor": "left", // Anchor text to be above the icon
        //   //         "text-font": ["Open Sans Bold", "Arial Unicode MS Bold"],
        //   //       },
        //   //       paint: {
        //   //         "text-color": "#FF6B00", // Set text color FF6B00
        //   //         "text-halo-color": "#fff  ", // Optional: Add a halo around the text for better readability
        //   //         "text-halo-width": 1, // Optional: Set halo width
        //   //       },
        //   //     });
        //   //   });

        //   //   if (CurrentLocation) {
        //   //     new mapboxgl.Marker({ color: "red" }) // Blue marker for current CurrentLocation
        //   //       .setLngLat([CurrentLocation[0], CurrentLocation[1]])
        //   //       .addTo(map.current);
        //   //   }

        //   //   map.current.on("click", "clusters", (e) => {
        //   //     const features = map.current.queryRenderedFeatures(e.point, {
        //   //       layers: ["clusters"],
        //   //     });
        //   //     // if (features.length > 0 && is3D) {
        //   //     //   stopRotation(); // Stop rotation when clicking a cluster or point
        //   //     //   setTimeout(() => {
        //   //     //     startRotation(); // Restart rotation after a delay
        //   //     //   }, 2000); // Adjust the delay as needed (e.g., 2000ms = 2 seconds)
        //   //     // }
        //   //     const clusterId = features[0].properties.cluster_id;
        //   //     map.current
        //   //       .getSource("beaches")
        //   //       .getClusterExpansionZoom(clusterId, (err, zoom) => {
        //   //         if (err) return;

        //   //         map.current.easeTo({
        //   //           center: features[0].geometry.coordinates,
        //   //           zoom: zoom,
        //   //         });
        //   //       });
        //   //   });

        //   //   // popup = new mapboxgl.Popup({
        //   //   //   closeButton: false,
        //   //   //   // closeOnClick: true,
        //   //   //   // closeOnMove: true,
        //   //   // });

        //   //   map.current.on("click", "unclustered-point", (e) => {
        //   //     const coordinates = e.features[0].geometry.coordinates.slice();
        //   //     // const { locationAddress, images, beachDescription, beachCategory } =
        //   //     //   e.features[0].properties;
        //   //     const el = e.features[0].properties;
        //   //     const coordinatesData = JSON.parse(el?.GeoLoc);

        //   //     while (Math.abs(e.lngLat.lng - coordinates[0]) > 180) {
        //   //       coordinates[0] += e.lngLat.lng > coordinates[0] ? 360 : -360;
        //   //     }

        //   //     router.push(
        //   //       `/map/${el?.nameSlug}/@${coordinatesData?.coordinates?.[1] ?? 0},${coordinatesData?.coordinates?.[0] ?? 0
        //   //       }`
        //   //     );

        //   //     // popup
        //   //     //   .setLngLat(coordinates)
        //   //     //   .setDOMContent(
        //   //     //     renderPopupContent(
        //   //     //       locationAddress,
        //   //     //       images,
        //   //     //       beachDescription,
        //   //     //       beachCategory
        //   //     //     )
        //   //     //   )
        //   //     //   // .setHTML(`${locationAdress}`)
        //   //     //   .addTo(map.current);
        //   //     // const closeButton = document.getElementById("popup-close");
        //   //     // if (closeButton) {
        //   //     //   closeButton.addEventListener("click", () => {
        //   //     //     popup.remove();
        //   //     //   });
        //   //     // }
        //   //   });

        //   //   // map.current.on("mouseup", "unclustered-point", () => {
        //   //   //   popup.remove();
        //   //   //   map.current.getCanvas().style.cursor = "pointer";
        //   //   // });

        //   //   map.current.on("mouseenter", "clusters", () => {
        //   //     map.current.getCanvas().style.cursor = "pointer";
        //   //   });
        //   //   map.current.on("mouseleave", "clusters", () => {
        //   //     map.current.getCanvas().style.cursor = "";
        //   //   });
        //   //   map.current.on("mouseenter", "unclustered-point", () => {
        //   //     map.current.getCanvas().style.cursor = "pointer";
        //   //     // const popupContent = document.getElementById("mapboxgl-popup");
        //   //   });
        //   //   map.current.on("mouseleave", "unclustered-point", () => {
        //   //     map.current.getCanvas().style.cursor = "";
        //   //   });
        //   //   map.current.addControl(new mapboxgl.ScaleControl());
        //   // }
        // } else {
        //   map?.current?.addSource("beaches", {
        //     type: "geojson",

        //     data: {
        //       type: "FeatureCollection",
        //       crs: {
        //         type: "name",
        //         properties: {
        //           name: "urn:ogc:def:crs:OGC:1.3:CRS84",
        //         },
        //       },
        //       features: isShark ? sharkBeachesCount?.map((beach) => ({
        //         type: "Feature",
        //         geometry: {
        //           type: "Point",
        //           coordinates: [beach.long, beach.lat, 0],
        //         },
        //         properties: beach,
        //       })) : beachesCount?.map((beach) => ({
        //         type: "Feature",
        //         geometry: {
        //           type: "Point",
        //           coordinates: [beach.long, beach.lat, 0],
        //         },
        //         properties: beach,
        //       })),
        //     },
        //     cluster: true,
        //     clusterMaxZoom: 14, // Max zoom to cluster points on
        //     clusterRadius: 50, // Radius of each cluster when clustering points (defaults to 50)
        //   });
        //   map.current.addLayer({
        //     id: "clusters",
        //     type: "circle",
        //     source: "beaches",
        //     filter: ["has", "point_count"],
        //     paint: {
        //       "circle-color": [
        //         "step",
        //         ["get", "point_count"],
        //         "#fff000",
        //         100,
        //         "#fff000",
        //         750,
        //         "#fff000",
        //       ],
        //       "circle-radius": [
        //         "step",
        //         ["get", "point_count"],
        //         25,
        //         100,
        //         25,
        //         750,
        //         30,
        //       ],
        //     },
        //   });

        //   map.current.addLayer({
        //     id: "cluster-count",
        //     type: "symbol",
        //     source: "beaches",
        //     filter: ["has", "point_count"],
        //     layout: {
        //       "text-field": ["get", "point_count_abbreviated"],
        //       "text-font": ["DIN Offc Pro Medium", "Arial Unicode MS Bold"],
        //       "text-size": 16,
        //     },
        //   });

        //   console.log(isShark, "fisrtttttt")
        //   if (isShark) {
        //     console.log(isShark, "checkkkkkk")
        //     map.current.loadImage("/shark1.png", (error, image) => {
        //       if (error) throw error;
        //       map.current.addImage("beach-icon", image);

        //       // Add unclustered points layer  --- shark attack
        //       map.current.addLayer({
        //         id: "unclustered-point",
        //         type: "symbol",
        //         source: "beaches",
        //         filter: ["!", ["has", "point_count"]],
        //         layout: {
        //           "icon-image": "beach-icon",
        //           "icon-size": 0.4,
        //           "icon-allow-overlap": true,
        //           "text-field": ["get", "name"],
        //           "text-size": 18,
        //           "text-offset": [1, 0],
        //           "text-anchor": "left",
        //           "text-font": ["Open Sans Bold", "Arial Unicode MS Bold"]
        //         },
        //         paint: {
        //           "text-color": "red", // Set text color FF6B00
        //           "text-halo-color": "#fff  ", // Optional: Add a halo around the text for better readability
        //           "text-halo-width": 1, // Optional: Set halo width
        //         },
        //       });
        //     });
        //   }
        //   else {
        //     console.log(isShark, "checkkkkkk2222")
        //     map.current.loadImage("/Marker4.png", (error, image) => {
        //       if (error) throw error;
        //       map.current.addImage("beach-icon", image);

        //       // Add the symbol layer with the beach icon and beach name
        //       map.current.addLayer({
        //         id: "unclustered-point",
        //         type: "symbol",
        //         source: "beaches",
        //         filter: ["!", ["has", "point_count"]],
        //         layout: {
        //           "icon-image": "beach-icon",
        //           "icon-size": 1,
        //           "icon-allow-overlap": true,
        //           "text-field": ["get", "name"], // Assuming your data source has a 'beachName' property
        //           "text-size": 18, // Adjust the size of the text
        //           "text-offset": [1, 0], // Position the text slightly below the icon
        //           "text-anchor": "left", // Anchor text to be above the icon
        //           "text-font": ["Open Sans Bold", "Arial Unicode MS Bold"],
        //         },
        //         paint: {
        //           "text-color": "#FF6B00", // Set text color FF6B00
        //           "text-halo-color": "#fff  ", // Optional: Add a halo around the text for better readability
        //           "text-halo-width": 1, // Optional: Set halo width
        //         },
        //       });
        //     });

        //     if (CurrentLocation) {
        //       new mapboxgl.Marker({ color: "red" }) // Blue marker for current CurrentLocation
        //         .setLngLat([CurrentLocation[0], CurrentLocation[1]])
        //         .addTo(map.current);
        //     }

        //     map.current.on("click", "clusters", (e) => {
        //       const features = map.current.queryRenderedFeatures(e.point, {
        //         layers: ["clusters"],
        //       });
        //       // if (features.length > 0 && is3D) {
        //       //   stopRotation(); // Stop rotation when clicking a cluster or point
        //       //   setTimeout(() => {
        //       //     startRotation(); // Restart rotation after a delay
        //       //   }, 2000); // Adjust the delay as needed (e.g., 2000ms = 2 seconds)
        //       // }
        //       const clusterId = features[0].properties.cluster_id;
        //       map.current
        //         .getSource("beaches")
        //         .getClusterExpansionZoom(clusterId, (err, zoom) => {
        //           if (err) return;

        //           map.current.easeTo({
        //             center: features[0].geometry.coordinates,
        //             zoom: zoom,
        //           });
        //         });
        //     });

        //     // popup = new mapboxgl.Popup({
        //     //   closeButton: false,
        //     //   // closeOnClick: true,
        //     //   // closeOnMove: true,
        //     // });

        //     map.current.on("click", "unclustered-point", (e) => {
        //       const coordinates = e.features[0].geometry.coordinates.slice();
        //       // const { locationAddress, images, beachDescription, beachCategory } =
        //       //   e.features[0].properties;
        //       const el = e.features[0].properties;
        //       const coordinatesData = JSON.parse(el?.GeoLoc);

        //       while (Math.abs(e.lngLat.lng - coordinates[0]) > 180) {
        //         coordinates[0] += e.lngLat.lng > coordinates[0] ? 360 : -360;
        //       }

        //       router.push(
        //         `/map/${el?.nameSlug}/@${coordinatesData?.coordinates?.[1] ?? 0},${coordinatesData?.coordinates?.[0] ?? 0
        //         }`
        //       );

        //       // popup
        //       //   .setLngLat(coordinates)
        //       //   .setDOMContent(
        //       //     renderPopupContent(
        //       //       locationAddress,
        //       //       images,
        //       //       beachDescription,
        //       //       beachCategory
        //       //     )
        //       //   )
        //       //   // .setHTML(`${locationAdress}`)
        //       //   .addTo(map.current);
        //       // const closeButton = document.getElementById("popup-close");
        //       // if (closeButton) {
        //       //   closeButton.addEventListener("click", () => {
        //       //     popup.remove();
        //       //   });
        //       // }
        //     });

        //     // map.current.on("mouseup", "unclustered-point", () => {
        //     //   popup.remove();
        //     //   map.current.getCanvas().style.cursor = "pointer";
        //     // });

        //     map.current.on("mouseenter", "clusters", () => {
        //       map.current.getCanvas().style.cursor = "pointer";
        //     });
        //     map.current.on("mouseleave", "clusters", () => {
        //       map.current.getCanvas().style.cursor = "";
        //     });
        //     map.current.on("mouseenter", "unclustered-point", () => {
        //       map.current.getCanvas().style.cursor = "pointer";
        //       // const popupContent = document.getElementById("mapboxgl-popup");
        //     });
        //     map.current.on("mouseleave", "unclustered-point", () => {
        //       map.current.getCanvas().style.cursor = "";
        //     });
        //     map.current.addControl(new mapboxgl.ScaleControl());
        //   }
        // }
    };

    // useEffect(() => {
    //   if (latStr && longStr && map) {
    //     map?.current?.jumpTo({ center: [longStr, latStr], zoom: 12 });
    //     var container = document?.querySelector("#map-beach-modal");
    //     if (container) container.scrollTop = 0;
    //   }
    // }, [latStr, longStr]);

    const handleMoveEnd = () => {
        // setIsloading(true);
        setRefresh((prev) => !prev);
    };

    const getLocation = () => {
        if (typeof window !== "undefined" && window.navigator.geolocation) {
            window.navigator.geolocation.getCurrentPosition(
                (position) => {
                    setLocation([position.coords.longitude, position.coords.latitude]);
                    setCurrentLocation([
                        position.coords.longitude,
                        position.coords.latitude,
                    ]);
                },
                (err) => {
                    if (listicleLocation?.length)
                        setLocation([listicleLocation[0], listicleLocation[1]]);
                    else if (latStr && longStr) setLocation([longStr, latStr]);
                    else setLocation([-118.4117325, 34.020479]); // los angeles coordinates
                }
            );
        } else {
            if (listicleLocation?.length)
                setLocation([listicleLocation[0], listicleLocation[1]]);
            else if (latStr && longStr) setLocation([longStr, latStr]);
            else setLocation([-118.4117325, 34.020479]); // los angeles coordinates
        }
    };
    // const geocoder = new MapboxGeocoder({
    //   accessToken: mapboxgl.accessToken,
    //   mapboxgl: mapboxgl,
    //   marker: true, // Disable default marker for results
    //   placeholder: "Search for locations",
    //   localGeocoderOnly: true,
    //   localGeocoder: async (query) => {
    //     try {
    //       // Make the API call directly and synchronously
    //       const response = await fetch(
    //         `${API_BASE_URL}/beachMain/searchByName/${query}`
    //       );
    //       const data = await response.json();
    //       console.log("API Data:", data);

    //       // Verify response format and return early if data is incorrect
    //       if (!data || data.status !== "success" || !Array.isArray(data.data)) {
    //         console.error("Unexpected data format:", data);
    //         return [{ place_name: "No results found", center: [0, 0] }];
    //       }

    //       // Map the data to Mapbox's expected format
    //       const results = data.data
    //         .map((item) => {
    //           const coordinates = item.GeoLoc?.coordinates;
    //           if (!coordinates || coordinates.length !== 2) return null;

    //           return {
    //             type: "Feature",
    //             geometry: {
    //               type: "Point",
    //               coordinates: [coordinates[0], coordinates[1]],
    //             },
    //             place_name: item.name,
    //             center: [coordinates[0], coordinates[1]], // [longitude, latitude]
    //             place_type: ["my-data"],
    //             properties: {
    //               id: item.id,
    //               citySlug: item.citySlug,
    //               stateSlug: item.stateSlug,
    //               countrySlug: item.countrySlug,
    //               nameSlug: item.nameSlug,
    //             },
    //           };
    //         })
    //         .filter(Boolean); // Filter out any null entries

    //       // Return results array, or a default item if empty
    //       return results.length
    //         ? results
    //         : [{ place_name: "No results found", center: [0, 0] }];
    //     } catch (error) {
    //       console.error("Error in Geocoder function:", error);
    //       return [{ place_name: "No results found", center: [0, 0] }];
    //     }
    //   },
    // });

    useEffect(() => {
        if (!location) return;

        map.current = new mapboxgl.Map({
            container: mapContainer.current,
            style: "mapbox://styles/mapbox/satellite-v9",
            center: location,
            zoom: listicleLocation?.length && !CurrentLocation ? 4 : 9.4,
            minZoom: 3,
            maxBounds: [
                [-180, -85], // Southwest coordinates
                [180, 85], // Northeast coordinates
            ],
            projection: { name: "mercator" },
            pitch: 0,
            bearing: 0,
        });

        map.current?.addControl(new mapboxgl.NavigationControl(), "bottom-right");

        // map.current.addControl(geocoder, "top-left");

        map.current.on("load", () => {
            map.current.addSource("mapbox-dem", {
                type: "raster-dem",
                url: "mapbox://mapbox.mapbox-terrain-dem-v1",
                tileSize: 512,
                maxzoom: 14,
            });

            // map.current.addLayer({
            //   id: "3d-buildings",
            //   source: "composite",
            //   "source-layer": "building",
            //   filter: ["==", "extrude", "true"],
            //   type: "fill-extrusion",
            //   minzoom: 15,
            //   paint: {
            //     "fill-extrusion-color": "#aaa",
            //     "fill-extrusion-height": ["get", "height"],
            //     "fill-extrusion-base": ["get", "min_height"],
            //     "fill-extrusion-opacity": 0.6,
            //   },
            // });

            // map.current.setLayoutProperty("3d-buildings", "visibility", "none");
            map.current?.setTerrain({ source: "mapbox-dem", exaggeration: 1.5 });
            map.current?.setPitch(60);
            map.current?.setBearing(-17.6);
            setLoaded(true);
            // setIsloading(true);
            setRefresh((prev) => !prev);
        });

        if (latStr && longStr) {
            map?.current?.jumpTo({ center: [longStr, latStr], zoom: 12 });
        }

        map.current.on("moveend", handleMoveEnd);

        return () => {
            map.current.off("moveend", handleMoveEnd);
            map.current.remove();
        };
    }, [location]);

    // useEffect(() => {
    //   geocoder.on("result", (e) => {
    //     // if (Object.keys(popupBeachData).length > 0) {
    //     router.push("/map");
    //     // }
    //   });
    // }, [geocoder]);

    // const rotateMap = () => {
    //   map.current.setBearing((map.current.getBearing() + 0.1) % 360);
    //   rotationAnimation.current = requestAnimationFrame(rotateMap);
    // };

    // const startRotation = () => {
    //   if (!rotationAnimation.current) {
    //     rotationAnimation.current = requestAnimationFrame(rotateMap);
    //   }
    // };

    // const stopRotation = () => {
    //   if (rotationAnimation.current) {
    //     cancelAnimationFrame(rotationAnimation.current);
    //     rotationAnimation.current = null;
    //   }
    // };

    const toggle3D = () => {
        setIs3D(!is3D);

        if (!is3D) {
            map.current.setTerrain({ source: "mapbox-dem", exaggeration: 1.5 });
            map.current.setPitch(60);
            map.current.setBearing(-17.6);
            // startRotation();
        } else {
            map.current.setTerrain(null);
            map.current.setPitch(0);
            map.current.setBearing(0);
            // stopRotation();
        }
    };

    // // Pause rotation on interaction
    // useEffect(() => {
    //   if (map.current) {
    //     map.current.on('mousedown', stopRotation);
    //     map.current.on('touchstart', stopRotation);
    //     map.current.on('mouseup', () => is3D && startRotation());
    //     map.current.on('touchend', () => is3D && startRotation());
    //   }
    //   return () => {
    //     if (map.current) {
    //       map.current.off('mousedown', stopRotation);
    //       map.current.off('touchstart', stopRotation);
    //       map.current.off('mouseup', startRotation);
    //       map.current.off('touchend', startRotation);
    //     }
    //   };
    // }, [is3D]);
    // Utility function to toggle marker images


    useEffect(() => {
        getLocation();
    }, []);

    useEffect(() => {
        SetBeaches();
    }, [beachesCount, sharkBeachesCount, isShark, loaded, is3D]);

    useEffect(() => {
        if (!openDrawer) {
            setIsLoadingCount(true);
            setPagination({
                limit: 10,
                page: 1,
            });
            setBeaches([]);
            setLoadOff(true);
        }
        const getData = setTimeout(() => {
            if (!!!openDrawer) {
                // FetchAndSetDelayedBeaches();
                FetchAndSetDelayedBeachesCount();
            }
        }, 700);
        const getSharkData = setTimeout(() => {
            if (isShark) {
                // FetchAndSetDelayedBeaches();
                FetchSharkAndSetDelayedBeachesCount();
            }
        }, 700);
        return () => clearTimeout(getData, getSharkData);
    }, [refresh, openDrawer, isShark]);

    // useEffect(() => {
    //   if (pagination?.page >= 2) {
    //     // setIsloading(true);
    //     FetchAndSetDelayedBeaches();
    //   }
    // }, [pagination.page]);

    // useEffect(() => {
    //   FetchAndSetDelayedBeaches();
    // }, [query]);

    // const fetchScrollLoaddata = () => {
    //   setPagination((prev) => ({ ...prev, page: prev?.page + 1 }));
    // };

    // useEffect(() => {
    //   // Resize map when toggling to full screen
    //   if (map.current) {
    //     map.current.resize();
    //   }
    // }, [toggleSidebar]);
    const handleToggle = () => {

    }

    return (
        <>
            <h1 className="hidden">Map</h1>
            {Object.keys(popupBeachData).length === 0 ? (
                <h2 className="hidden">Beaches</h2>
            ) : null}
            {Object.keys(popupBeachData).length > 0 ? (
                <h2 className="hidden">{popupBeachData?.name}</h2>
            ) : null}
            <section className="">
                <div className="relative z-[60] h-full w-full min-h-[90vh] shadow-inner p-1">
                    {Object.keys(popupBeachData).length > 0 ? (
                        <div
                            className={`absolute z-50 top-16 right-3 bottom-20 rounded-xl w-[350px] xl:w-[370px] 2xl:w-[400px] 4xl:w-[490px] bg-white md:block hidden overflow-hidden`}
                        >
                            <div
                                className="overflow-y-scroll h-full p-4 shadow-inner scrollable-container"
                                id="map-beach-modal"
                            >
                                <SingleBeachShowModal data={popupBeachData} />
                            </div>
                        </div>
                    ) : null}
                    <div
                        ref={mapContainer}
                        className="map-container h-full w-full min-h-[90vh] p-1 border-2 border-gray-800 rounded-2xl"
                    />
                    <FilterForMap
                        activityStatus={activityStatus}
                        setActivityStatus={setActivityStatus}
                        openDrawer={openDrawer}
                        setOpenDrawer={setOpenDrawer}
                        setQuery={setQuery}
                        query={query}
                    />
                    <div className="absolute top-4 left-4 z-10 md:hidden">
                        <SearchBar
                            onMap
                            mapData={{
                                map: map,
                                mapboxgl: mapboxgl,
                                isPopupBeachData: isPopupBeachData,
                            }}
                        />
                    </div>
                    <div className="absolute xs:bottom-4 xs:left-1/2 xs:transform xs:-translate-x-1/2 md:top-4 md:left-4 z-10 flex gap-x-2 items-center">
                        <div className="md:block hidden">
                            <SearchBar
                                onMap
                                mapData={{
                                    map: map,
                                    mapboxgl: mapboxgl,
                                    isPopupBeachData: isPopupBeachData,
                                }}
                            />
                        </div>
                        <button
                            className="md:rounded-md md:border md:border-gray-200 md:bg-white bg-sandee-blue md:p-[7.5px] xs:py-1 xs:px-4 rounded-full w-fit shadow-md"
                            onClick={() => {
                                setOpenDrawer(!openDrawer);
                            }}
                        >
                            <div className="flex items-center gap-x-2">
                                <LineDotFilterIcon className="w-5 h-5 md:stroke-black stroke-white" />
                                <span className="md:hidden text-white">Filter</span>
                            </div>
                        </button>
                        <Switch
                            id={"switch-sharks"}
                            // loading={API.isLoading}
                            checked={isShark}
                            onChange={() => {
                                // localStorage.setItem("isShark", !isShark);
                                // window.location.reload();
                                // setIsShark(pr => !pr)
                            }}
                        />
                    </div>
                    {isLoadingCount ? (
                        <div className="absolute z-10 md:top-4 md:right-4 xs:bottom-4 xs:left-4 bg-sandee-blue py-1 rounded-lg md:px-4 px-2 text-white md:text-base text-[12px] ">
                            <span className="font-semibold">Loading Beaches...</span>
                        </div>
                    ) : null}
                    <button
                        onClick={toggle3D}
                        className="absolute md:bottom-9 bottom-14 right-28 py-1 px-2 text-sm shadow-md rounded-md bg-white z-10 hover:bg-gray-50 border border-gray-300"
                    >
                        {is3D ? "2D" : "3D"}
                    </button>
                </div>
            </section>
        </>
    );
}
