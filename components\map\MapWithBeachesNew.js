"use client";
import React, { useRef, useEffect, useState, useContext, useMemo } from "react";
import mapboxgl from "!mapbox-gl"; // eslint-disable-line import/no-webpack-loader-syntax
// import { CustomGrid } from "../Custom-Display";
// import { FinalImageGenerator } from "@/helper/functions";
// import BeachCard from "../Cards/BeachCard";
// import InfiniteScroll from "react-infinite-scroll-component";
import {
  CheckRoundIcon,
  LeftSquareArrowIcon,
  LineDotFilterIcon,
  SharkIcon,
  LoaderIcon,
} from "../social-icons/icons";
// import { createRoot } from "react-dom/client";
import { valContext } from "@/helper/context/ValContext";
import {
  GetSharkAttackeByID,
  GetSharkAttackeByLatLong,
  PostSearchForMap,
  PostSharkForMap,
} from "@/app/(HomeHeader)/action";
import SingleBeachShowModal from "./SingleBeachShowModal";
// import MapBeachCard from "./MapBeachCard";
import SearchBar from "../SearchBar";
// import MapFilterSection from "./MapFilterSection";
// import MapFilterByType from "./MapFilterByType";
// import MapLocationPopupCard from "./MapLocationPopupCard";
import {
  useParams,
  usePathname,
  useRouter,
  useSearchParams,
} from "next/navigation";
// import SingleBeachModalNew from "./SingleBeachModalNew";
import FilterForMap from "./FilterForMap";
import { Switch } from "antd";
import SharkAttacksModal from "../Shark/SharkAttacksModal";
import Image from "next/image";
import useSessionStorage from "@/helper/hook/useSessionStorage";
import MapBottomButtons from "./map-bottom-buttons";
import SharkAttacksModalNew from "../Shark/sharkAttackModalNew";
import { handleWindSwitch } from "@/helper/mapConstant";
import { isMobileView } from "@/helper/functions";
mapboxgl.accessToken = process.env.NEXT_PUBLIC_MAPBOX_API_KEY;
// import MapboxGeocoder from "@mapbox/mapbox-gl-geocoder";
// import "@mapbox/mapbox-gl-geocoder/dist/mapbox-gl-geocoder.css";

export default function MapWithBeachesNew({
  latStr,
  longStr,
  popupBeachData,
  isFilter = false,
  isNudeToggle = false,
  isSharkToggle = false,
  mode = "all",
}) {
  let isPopupBeachData = Object.keys(popupBeachData).length > 0;
  const [beaches, setBeaches] = useState([]);
  const [beachesCount, setBeachesCount] = useState([]);
  const [isLoadingCount, setIsLoadingCount] = useState(true);
  const [loaded, setLoaded] = useState(false);
  const [count, setCount] = useState(0);
  const [location, setLocation] = useState(null);
  const [CurrentLocation, setCurrentLocation] = useState(null);
  const [refresh, setRefresh] = useState(false);
  const [isFilterSubmit, setIsFilterSubmit] = useState(false);
  const [query, setQuery] = useState("");
  const [toggleSidebar, setToggleSidebar] = useState(true);
  const [typeFilter, setTypeFilter] = useState([]);
  const [openDrawer, setOpenDrawer] = useState(false);
  const [is3D, setIs3D] = useState(false);
  // const [isSatelite, setIsSatelite] = useState(0);
  // const isSateliteOn = +localStorage.getItem("isSatelite")
  // const isSharks = localStorage.getItem("isShark");
  // const [isShark, setIsShark] = useState(0);
  // const isMobile = isMobileView();
  const isSatelite = useMemo(
    () =>
      typeof window !== "undefined" ? +localStorage.getItem("isSatelite") : 0,
    []
  );
  const isShark = useMemo(
    () =>
      typeof window !== "undefined" && mode !== "nude"
        ? +localStorage.getItem("isShark") || mode == "shark"
        : 0,
    []
  );
  const isNude = useMemo(
    () =>
      typeof window !== "undefined"
        ? +localStorage.getItem("isNude") || mode == "nude"
        : 0,
    []
  );
  // const isWind = useMemo(() => typeof window !== "undefined" ? +localStorage.getItem("isWind") : 0, []);
  const [isswitchHandle, setIsSwitchHandle] = useState({
    isShark: isShark ?? 0,
    isSatelite: isSatelite ?? 0,
    isNude: isNude ?? 0,
    isWind: 0,
  });
  // const [isswitchHandle, setIsSwitchHandle] = useState({
  //   isShark: 0,
  //   isSatelite: 0,
  //   isNude: 0,
  // });
  // console.log(isswitchHandle, "isswitchHandle");
  // console.log(isSatelite, "isSatelite");
  useEffect(() => {
    // setIsShark(+localStorage.getItem("isShark"))
    // setIsSatelite(+localStorage.getItem("isSatelite"))
    // if (mode == "shark") +localStorage.setItem("isShark", 1)
    // if (mode == "nude") +localStorage.setItem("isNude", 1)
    // setIsSwitchHandle({
    //   isShark: mode !== "nude" ? +localStorage.getItem("isShark") || mode == "shark" : 0,
    //   isSatelite: +localStorage.getItem("isSatelite"),
    //   isNude: +localStorage.getItem("isNude") || mode == "nude",
    // })
  }, []);
  const {
    activityStatus,
    setActivityStatus,
    listicleLocation,
    setListicleLocation,
  } = useContext(valContext);
  const [loadoff, setLoadOff] = useState(true);
  const [pagination, setPagination] = useState({
    limit: 10,
    page: 1,
  });
  const [open, setOpen] = useState({});
  const pathname = usePathname();
  const mapContainer = useRef(null);
  const map = useRef(null);
  const router = useRouter();
  // const params = useParams();
  // const searchParams = useSearchParams();
  // const lat = searchParams.get('lat');
  // const long = searchParams.get('long');
  // const zoom = searchParams.get('zoom');
  const [latLocal, setLatLocal] = useSessionStorage("lat", null);
  const [longLocal, setLongLocal] = useSessionStorage("long", null);
  const [zoomLocal, setZoomLocal] = useSessionStorage("zoom", null);
  // let popup = "";
  // const renderPopupContent = (
  //   locationAddress,
  //   images,
  //   beachDescription,
  //   beachCategory
  // ) => {
  //   const address = JSON?.parse(locationAddress);
  //   const popupContainer = document.createElement("a");
  //   createRoot(popupContainer).render(
  //     <div
  //       className="cursor-pointer rounded-2xl overflow-hidden relative"
  //       id="popup-content"
  //       onClick={() => {
  //         router?.push(address?.link);
  //         setTimeout(() => {
  //           popup.remove();
  //         }, 800);
  //       }}
  //     >
  //       <MapLocationPopupCard
  //         locationAddress={locationAddress}
  //         images={images}
  //         beachDescription={beachDescription}
  //         beachCategory={beachCategory}
  //         popup={popup}
  //       />
  //     </div>
  //   );
  //   return popupContainer;
  // };
  const FetchAndSetDelayedBeachesCount = async () => {
    if (!map.current) return;
    const bounds = map.current.getBounds();
    const FilteredActivities = Object.fromEntries(
      Object.entries(activityStatus).filter(([key, value]) => value)
    );
    const FilteredActivitiesArray =
      Object.getOwnPropertyNames(FilteredActivities);
    const payload = {
      point1: bounds.getSouthWest().toArray().reverse(),
      point2: bounds.getNorthEast().toArray().reverse(),
      point3: bounds.getNorthWest().toArray().reverse(),
      point4: bounds.getSouthEast().toArray().reverse(),
      ...(FilteredActivitiesArray?.length || typeFilter?.length
        ? { beachCategories: [...FilteredActivitiesArray, ...typeFilter] }
        : {}),
      // ...FilteredActivities,
      // getCounts: true,
    };

    let Result = null;
    let beachList = [];

    if (isswitchHandle?.isShark || mode == "shark") {
      Result = await PostSharkForMap(payload);
      beachList = Result?.data?.data?.rows;
    } else {
      try {
        if (isswitchHandle?.isNude) {
          payload.beachCategories =
            FilteredActivitiesArray?.length || typeFilter?.length
              ? [...FilteredActivitiesArray, ...typeFilter, "nude"]
              : ["nude"];
        }
        Result = await PostSearchForMap(payload);
        beachList = Result?.data?.beaches?.rows;
      } catch (error) {
        console.log(error);
      }
    }
    // const Results = await isShark ? PostSharkForMap(payload) : PostSearchForMap(payload);
    // const Results = await PostSharkForMap(payload, token);
    const beachLists = beachList;
    if (listicleLocation) {
      setListicleLocation(null);
    }

    const AllBeachWithRefinedDataCount = beachLists?.map((el) => ({
      ...el,
      lat: el?.GeoLoc?.coordinates?.[1] ?? 0,
      long: el?.GeoLoc?.coordinates?.[0] ?? 0,
      // locationAddress: {
      //   // link: `/map/${el?.nameSlug}/@${el?.GeoLoc?.coordinates?.[1] ?? 0},${
      //   //   el?.GeoLoc?.coordinates?.[0] ?? 0
      //   // }`,
      //   link: el?.city?.state?.country?.slug
      //     ? `/${el?.city?.state?.country?.slug}/${el?.city?.state?.slug}/${el?.city?.slug}/${el?.nameSlug}`
      //     : el?.country?.slug
      //     ? `/${el?.country?.slug}/${el?.state?.slug}/${el?.city?.slug}/${el?.nameSlug}`
      //     : `/${el?.countrySlug}/${el?.stateSlug}/${el?.citySlug}/${el?.nameSlug}`,
      //   lat: el?.GeoLoc?.coordinates?.[1] ?? 0,
      //   long: el?.GeoLoc?.coordinates?.[0] ?? 0,
      //   name: el?.name,
      //   rating100: el?.rating100,
      //   city: el?.city,
      //   state: el?.state,
      //   country: el?.country,
      // },
    }));
    setBeachesCount(AllBeachWithRefinedDataCount);
    setIsLoadingCount(false);
  };
  const SetBeaches = () => {
    if (!mapContainer.current || !loaded) {
      return;
    }

    if (map.current.getSource("beaches")) {
      map.current.getSource("beaches").setData({
        type: "FeatureCollection",
        features: beachesCount?.length
          ? beachesCount?.map((beach) => ({
            type: "Feature",
            geometry: {
              type: "Point",
              coordinates: [beach.long, beach.lat],
            },
            properties: beach,
          }))
          : [],
      });
    } else {
      map?.current?.addSource("beaches", {
        type: "geojson",

        data: {
          type: "FeatureCollection",
          crs: {
            type: "name",
            properties: {
              name: "urn:ogc:def:crs:OGC:1.3:CRS84",
            },
          },
          features: beachesCount?.length
            ? beachesCount?.map((beach) => ({
              type: "Feature",
              geometry: {
                type: "Point",
                coordinates: [beach.long, beach.lat, 0],
              },
              properties: beach,
            }))
            : [],
        },
        cluster: true,
        clusterMaxZoom: 14, // Max zoom to cluster points on
        clusterRadius: 50, // Radius of each cluster when clustering points (defaults to 50)
      });
      map.current.addLayer({
        id: "clusters",
        type: "circle",
        source: "beaches",
        filter: ["has", "point_count"],
        paint: {
          "circle-color": [
            "step",
            ["get", "point_count"],
            isswitchHandle?.isShark || mode == "shark" ? "#fa1b1b" : "#fff000",
            100,
            isswitchHandle?.isShark || mode == "shark" ? "#fa1b1b" : "#fff000",
            750,
            isswitchHandle?.isShark || mode == "shark" ? "#fa1b1b" : "#fff000",
          ],
          "circle-radius": [
            "step",
            ["get", "point_count"],
            25,
            100,
            25,
            750,
            30,
          ],
        },
      });

      map.current.addLayer({
        id: "cluster-count",
        type: "symbol",
        source: "beaches",
        filter: ["has", "point_count"],
        layout: {
          "text-field": ["get", "point_count_abbreviated"],
          "text-font": ["DIN Offc Pro Medium", "Arial Unicode MS Bold"],
          "text-size": 16,
        },
        paint: {
          "text-color":
            isswitchHandle?.isShark || mode == "shark" ? "#fff" : "#000",
        },
      });
      map.current.loadImage(
        isswitchHandle?.isShark || mode == "shark"
          ? "/shark1.png"
          : "/Marker4-4x.png",
        (error, image) => {
          if (error) throw error;
          // const scaledImage = new Image();
          // scaledImage.src = image;
          map.current.addImage("beach-icon", image);

          // Add the symbol layer with the beach icon and beach name
          map.current.addLayer({
            id: "unclustered-point",
            type: "symbol",
            source: "beaches",
            filter: ["!", ["has", "point_count"]],
            layout: {
              "icon-image": "beach-icon",
              "icon-size":
                isswitchHandle?.isShark || mode == "shark" ? 0.4 : 0.22,
              "icon-allow-overlap": true,
              "text-field": ["get", "name"], // Assuming your data source has a 'beachName' property
              "text-size": 18, // Adjust the size of the text
              "text-offset": [1, 0], // Position the text slightly below the icon
              "text-anchor": "left", // Anchor text to be above the icon
              "text-font": ["Open Sans Bold", "Arial Unicode MS Bold"],
            },
            paint: {
              "text-color":
                isswitchHandle?.isShark || mode == "shark" ? "red" : "#FF6B00", // Set text color FF6B00
              "text-halo-color": "#fff  ", // Optional: Add a halo around the text for better readability
              "text-halo-width": 1, // Optional: Set halo width
            },
          });
        }
      );

      if (CurrentLocation) {
        new mapboxgl.Marker({ color: "red" }) // Blue marker for current CurrentLocation
          .setLngLat([CurrentLocation[0], CurrentLocation[1]])
          .addTo(map.current);
      }

      map.current.on("click", "clusters", (e) => {
        const features = map.current.queryRenderedFeatures(e.point, {
          layers: ["clusters"],
        });
        // if (features.length > 0 && is3D) {
        //   stopRotation(); // Stop rotation when clicking a cluster or point
        //   setTimeout(() => {
        //     startRotation(); // Restart rotation after a delay
        //   }, 2000); // Adjust the delay as needed (e.g., 2000ms = 2 seconds)
        // }
        const clusterId = features[0].properties.cluster_id;
        map.current
          .getSource("beaches")
          .getClusterExpansionZoom(clusterId, (err, zoom) => {
            if (err) return;

            map.current.easeTo({
              center: features[0].geometry.coordinates,
              zoom: zoom,
            });
          });
      });

      // popup = new mapboxgl.Popup({
      //   closeButton: false,
      //   // closeOnClick: true,
      //   // closeOnMove: true,
      // });
      map.current.on("click", "unclustered-point", async (e) => {
        if (!isswitchHandle?.isShark && (mode === "all" || mode === "nude")) {
          const coordinates = e.features[0].geometry.coordinates.slice();
          // const { locationAddress, images, beachDescription, beachCategory } =
          //   e.features[0].properties;
          const el = e.features[0].properties;
          const coordinatesData = JSON.parse(el?.GeoLoc);

          while (Math.abs(e.lngLat.lng - coordinates[0]) > 180) {
            coordinates[0] += e.lngLat.lng > coordinates[0] ? 360 : -360;
          }

          if (mode == "nude") {
            router.push(
              `/${pathname?.split("/")[1]}/nude-beaches/${el?.nameSlug}/@${coordinatesData?.coordinates?.[1] ?? 0
              },${coordinatesData?.coordinates?.[0] ?? 0}`
            );
          } else {
            router.push(
              `/${pathname?.split("/")[1]}/${el?.nameSlug}/@${coordinatesData?.coordinates?.[1] ?? 0
              },${coordinatesData?.coordinates?.[0] ?? 0}`
            );
          }
          // router.push(
          //   `/${pathname?.split("/")[1]}/${el?.nameSlug}/@${coordinatesData?.coordinates?.[1] ?? 0},${coordinatesData?.coordinates?.[0] ?? 0
          //   }`
          // );
        } else {
          const feature = e.features[0];
          // const address = JSON.parse(feature.properties.locationAddress);

          try {
            const el = e.features[0].properties;
            const coordinatesData = JSON.parse(el?.GeoLoc);
            const AllBeach = JSON.parse(el?.AllBeach);
            const cityName = JSON.parse(el?.city);
            const state = JSON.parse(el?.state);
            const country = JSON.parse(el?.country);
            const Results = await GetSharkAttackeByLatLong(
              {
                page: 1,
                limit: 5,
                // coordinates: `${coordinatesData?.coordinates?.[1] ?? 0},${coordinatesData?.coordinates?.[0] ?? 0}`
              },
              `${coordinatesData?.coordinates?.[1] ?? 0},${coordinatesData?.coordinates?.[0] ?? 0
              }`
            );
            let newLink = "";
            const res = Results?.data;
            if (Results?.status === "success") {
              if (res?.city?.state?.country?.slug) {
                newLink = `/${res?.city?.state?.country?.slug}/${res?.city?.state?.slug}/${res?.city?.slug}/${res?.AllBeach?.nameSlug}`; //
                res.link = `/${res?.city?.state?.country?.slug}/${res?.city?.state?.slug}/${res?.city?.slug}/${res?.AllBeach?.nameSlug}`; //
                res.location = `${res?.city?.name}, ${res?.city?.state?.country?.code}`; //
              } else {
                newLink = `/${res?.country?.slug}/${res?.state?.slug}/${res?.city?.slug}/${res?.AllBeach?.nameSlug}`; //
                res.link = `/${res?.country?.slug}/${res?.state?.slug}/${res?.city?.slug}/${res?.AllBeach?.nameSlug}`; //
                res.location = `${res?.city?.name}, ${res?.country?.code}`; //
              }
              setOpen({
                ...res,
                ...res?.AllBeach,
                coordinatesData: coordinatesData,
                newLink:
                  country?.slug &&
                    state?.slug &&
                    cityName?.slug &&
                    AllBeach?.nameSlug
                    ? `/${country?.slug}/${state?.slug}/${cityName?.slug}/${AllBeach?.nameSlug}`
                    : null,
                beachName: AllBeach?.name,
                cityName: cityName?.name,
                stateName: state?.name,
                countryName: country?.name,
              });
            }

            // setOpen({
            //   name: "Kukio Beach Park",
            //   date: "2023-01-01",
            //   fatal: true,
            //   newLink: "#",
            //   sharkSpecy: {
            //     name: "Tiger Shark",
            //   },
            //   sharkSize: "5.5 feet (1.7 meters)",
            //   victimCount: 6,
            //   summary: "Ethan Davis, 23, sustained lacerations to his legs while spearfishing at Spanish Cay.",
            // })
          } catch (error) {
            console.log(error);
          }
        }
        // popup
        //   .setLngLat(coordinates)
        //   .setDOMContent(
        //     renderPopupContent(
        //       locationAddress,
        //       images,
        //       beachDescription,
        //       beachCategory
        //     )
        //   )
        //   // .setHTML(`${locationAdress}`)
        //   .addTo(map.current);
        // const closeButton = document.getElementById("popup-close");
        // if (closeButton) {
        //   closeButton.addEventListener("click", () => {
        //     popup.remove();
        //   });
        // }
      });

      // map.current.on("mouseup", "unclustered-point", () => {
      //   popup.remove();
      //   map.current.getCanvas().style.cursor = "pointer";
      // });

      map.current.on("mouseenter", "clusters", () => {
        map.current.getCanvas().style.cursor = "pointer";
      });
      map.current.on("mouseleave", "clusters", () => {
        map.current.getCanvas().style.cursor = "";
      });
      map.current.on("mouseenter", "unclustered-point", () => {
        map.current.getCanvas().style.cursor = "pointer";
        // const popupContent = document.getElementById("mapboxgl-popup");
      });
      map.current.on("mouseleave", "unclustered-point", () => {
        map.current.getCanvas().style.cursor = "";
      });
      map.current.addControl(new mapboxgl.ScaleControl());
    }
  };

  // useEffect(() => {
  //   if (latStr && longStr && map) {
  //     map?.current?.jumpTo({ center: [longStr, latStr], zoom: 12 });
  //     var container = document?.querySelector("#map-beach-modal");
  //     if (container) container.scrollTop = 0;
  //   }
  // }, [latStr, longStr]);

  const handleMoveEnd = (e, zoomIndex) => {
    const exstingPath = window.location?.pathname;
    const newUrl = exstingPath?.split("/@")[0];
    //local storage
    setZoomLocal(zoomIndex ?? 0);
    setLatLocal(e?.lat?.toFixed(6) ?? 0);
    setLongLocal(e?.lng?.toFixed(6) ?? 0);

    // const path = `${newUrl}/@${e?.lat?.toFixed(6) ?? 0},${e?.lng?.toFixed(6) ?? 0}`
    // console.log(newUrl)
    // ======================================
    // if (newUrl == "/map") {
    //   router.push(
    //     `${newUrl}?lat=${e?.lat?.toFixed(6) ?? 0}&long=${e?.lng?.toFixed(6) ?? 0}&zoom=${zoomIndex}`
    //     // `/map/dabhari-beach/@${e?.lng?.toFixed(6) ?? 0},${e?.lat?.toFixed(6) ?? 0}`
    //   );
    // }
    // ========================================
    //  else {
    //   router.push(
    //     // `/map?lat=${e?.lat?.toFixed(6) ?? 0}&long=${e?.lng?.toFixed(6) ?? 0}`
    //     `/map/dabhari-beach/@${e?.lng?.toFixed(6) ?? 0},${e?.lat?.toFixed(6) ?? 0}`
    //   );
    // }
    // setIsloading(true);
    setRefresh((prev) => !prev);
  };

  const getLocation = () => {
    if (typeof window !== "undefined" && window.navigator.geolocation) {
      window.navigator.geolocation.getCurrentPosition(
        (position) => {
          setLocation([position.coords.longitude, position.coords.latitude]);
          setCurrentLocation([
            position.coords.longitude,
            position.coords.latitude,
          ]);
        },
        (err) => {
          if (listicleLocation?.length)
            setLocation([listicleLocation[0], listicleLocation[1]]);
          else if (latStr && longStr) setLocation([longStr, latStr]);
          else setLocation([-118.4117325, 34.020479]); // los angeles coordinates
        }
      );
    } else {
      if (listicleLocation?.length)
        setLocation([listicleLocation[0], listicleLocation[1]]);
      else if (latStr && longStr) setLocation([longStr, latStr]);
      else setLocation([-118.4117325, 34.020479]); // los angeles coordinates
    }
  };

  // const handleWindSwitch = async () => {
  //   if (!mapContainer.current || !loaded) return;

  //   console.log(isswitchHandle?.isWind, map?.current?.getSource("raster-array-source"));

  //   const removeSourceAndLayer = async () => {
  //     if (map?.current?.getLayer('wind-layer')) {
  //       map.current?.removeLayer('wind-layer');
  //     }
  //     if (map?.current?.getSource('raster-array-source')) {
  //       map.current?.removeSource('raster-array-source');
  //     }
  //   };

  //   const waitForSourceRemoval = (sourceId) => {
  //     return new Promise((resolve) => {
  //       const checkSource = () => {
  //         if (!map?.current?.getSource(sourceId)) {
  //           resolve();
  //         } else {
  //           setTimeout(checkSource, 50); // Retry after 50ms
  //         }
  //       };
  //       checkSource();
  //     });
  //   };

  //   if (map?.current?.getSource("raster-array-source")) {
  //     console.log("Removing existing source and layer");
  //     if (!isswitchHandle?.isWind) {
  //       console.log("REMOVE");
  //       await removeSourceAndLayer();
  //       await waitForSourceRemoval('raster-array-source');
  //     }
  //   } else {
  //     console.log("Adding new source and layer");
  //     map.current.addSource('raster-array-source', {
  //       'type': 'raster-array',
  //       'url': 'mapbox://mapbox.gfs-winds',
  //       'tileSize': 1024,
  //     });
  //     map.current.addLayer({
  //       'id': 'wind-layer',
  //       'type': 'raster-particle',
  //       'source': 'raster-array-source',
  //       'source-layer': '10winds',
  //       'paint': {
  //         'raster-particle-speed-factor': 0.4,
  //         'raster-particle-fade-opacity-factor': 0.9,
  //         'raster-particle-reset-rate-factor': 0.4,
  //         'raster-particle-count': 4000,
  //         'raster-particle-max-speed': 40,
  //         'raster-particle-color': [
  //           'interpolate',
  //           ['linear'],
  //           ['raster-particle-speed'],
  //           1.5,
  //           'rgba(134,163,171,256)',
  //           2.5,
  //           'rgba(126,152,188,256)',
  //           4.12,
  //           'rgba(110,143,208,256)',
  //           4.63,
  //           'rgba(110,143,208,256)',
  //           6.17,
  //           'rgba(15,147,167,256)',
  //           7.72,
  //           'rgba(15,147,167,256)',
  //           9.26,
  //           'rgba(57,163,57,256)',
  //           10.29,
  //           'rgba(57,163,57,256)',
  //           11.83,
  //           'rgba(194,134,62,256)',
  //           13.37,
  //           'rgba(194,134,63,256)',
  //           14.92,
  //           'rgba(200,66,13,256)',
  //           16.46,
  //           'rgba(200,66,13,256)',
  //           18.0,
  //           'rgba(210,0,50,256)',
  //           20.06,
  //           'rgba(215,0,50,256)',
  //           21.6,
  //           'rgba(175,80,136,256)',
  //           23.66,
  //           'rgba(175,80,136,256)',
  //           25.21,
  //           'rgba(117,74,147,256)',
  //           27.78,
  //           'rgba(117,74,147,256)',
  //           29.32,
  //           'rgba(68,105,141,256)',
  //           31.89,
  //           'rgba(68,105,141,256)',
  //           33.44,
  //           'rgba(194,251,119,256)',
  //           42.18,
  //           'rgba(194,251,119,256)',
  //           43.72,
  //           'rgba(241,255,109,256)',
  //           48.87,
  //           'rgba(241,255,109,256)',
  //           50.41,
  //           'rgba(256,256,256,256)',
  //           57.61,
  //           'rgba(256,256,256,256)',
  //           59.16,
  //           'rgba(0,256,256,256)',
  //           68.93,
  //           'rgba(0,256,256,256)',
  //           69.44,
  //           'rgba(256,37,256,256)'
  //         ]
  //       }
  //     });
  //     map.current.on("click", "wind-layer", (e) => {
  //       console.log(e, "wind pin data");
  //     });
  //   }

  // }
  useEffect(() => {
    handleWindSwitch({
      mapContainer,
      loaded,
      map,
      isswitchHandle,
    });
  }, [loaded, isswitchHandle?.isWind]);

  // const handleDefaultLocation = () => {
  //   if (listicleLocation?.length) {
  //     setLocation([listicleLocation[0], listicleLocation[1]]);
  //   } else if (latStr && longStr) {
  //     setLocation([longStr, latStr]);
  //   } else {
  //     setLocation([-118.4117325, 34.020479]); // Los Angeles coordinates
  //   }
  // };

  // const geocoder = new MapboxGeocoder({
  //   accessToken: mapboxgl.accessToken,
  //   mapboxgl: mapboxgl,
  //   marker: true, // Disable default marker for results
  //   placeholder: "Search for locations",
  //   localGeocoderOnly: true,
  //   localGeocoder: async (query) => {
  //     try {
  //       // Make the API call directly and synchronously
  //       const response = await fetch(
  //         `${API_BASE_URL}/beachMain/searchByName/${query}`
  //       );
  //       const data = await response.json();
  //       console.log("API Data:", data);

  //       // Verify response format and return early if data is incorrect
  //       if (!data || data.status !== "success" || !Array.isArray(data.data)) {
  //         console.error("Unexpected data format:", data);
  //         return [{ place_name: "No results found", center: [0, 0] }];
  //       }

  //       // Map the data to Mapbox's expected format
  //       const results = data.data
  //         .map((item) => {
  //           const coordinates = item.GeoLoc?.coordinates;
  //           if (!coordinates || coordinates.length !== 2) return null;

  //           return {
  //             type: "Feature",
  //             geometry: {
  //               type: "Point",
  //               coordinates: [coordinates[0], coordinates[1]],
  //             },
  //             place_name: item.name,
  //             center: [coordinates[0], coordinates[1]], // [longitude, latitude]
  //             place_type: ["my-data"],
  //             properties: {
  //               id: item.id,
  //               citySlug: item.citySlug,
  //               stateSlug: item.stateSlug,
  //               countrySlug: item.countrySlug,
  //               nameSlug: item.nameSlug,
  //             },
  //           };
  //         })
  //         .filter(Boolean); // Filter out any null entries

  //       // Return results array, or a default item if empty
  //       return results.length
  //         ? results
  //         : [{ place_name: "No results found", center: [0, 0] }];
  //     } catch (error) {
  //       console.error("Error in Geocoder function:", error);
  //       return [{ place_name: "No results found", center: [0, 0] }];
  //     }
  //   },
  // });

  useEffect(() => {
    if (!location) return;

    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: isswitchHandle?.isSatelite
        ? "mapbox://styles/mapbox/satellite-streets-v12"
        : "mapbox://styles/mapbox/streets-v12",
      // show3dObjects: true,
      center: location,
      zoom: listicleLocation?.length && !CurrentLocation ? 4 : 9.4,
      minZoom: 3,
      maxBounds: [
        [-180, -85], // Southwest coordinates
        [180, 85], // Northeast coordinates
      ],
      projection: { name: "mercator" },
      pitch: 0,
      bearing: 0,
    });

    map.current?.addControl(new mapboxgl.NavigationControl(), "bottom-right");

    // map.current.addControl(geocoder, "top-left");

    map.current.on("load", () => {
      // map.current.addSource("mapbox-dem", {
      //   type: "raster-dem",
      //   url: "mapbox://mapbox.mapbox-terrain-dem-v1",
      //   tileSize: 512,
      //   maxzoom: 14,
      // });

      // map.current.addLayer({
      //   id: "3d-buildings",
      //   source: "composite",
      //   "source-layer": "building",
      //   filter: ["==", "extrude", "true"],
      //   type: "fill-extrusion",
      //   minzoom: 15,
      //   paint: {
      //     "fill-extrusion-color": "#aaa",
      //     "fill-extrusion-height": ["get", "height"],
      //     "fill-extrusion-base": ["get", "min_height"],
      //     "fill-extrusion-opacity": 0.6,
      //   },
      // });

      // map.current.setLayoutProperty("3d-buildings", "visibility", "none");

      map.current.addSource("mapbox-dem", {
        type: "raster-dem",
        url: "mapbox://mapbox.mapbox-terrain-dem-v1",
        tileSize: 512,
        maxzoom: 14,
      });
      if (is3D) {
        map.current.setTerrain({ source: "mapbox-dem", exaggeration: 1.5 });
        map.current.setPitch(60);
        map.current.setBearing(-17.6);
        // startRotation();
      } else {
        map.current.setTerrain(null);
        map.current.setPitch(0);
        map.current.setBearing(0);
        // stopRotation();
      }

      // map.current.addLayer({
      //   id: "3d-buildings",
      //   source: "composite",
      //   "source-layer": "building",
      //   type: "fill-extrusion",
      //   minzoom: 15, // Only show buildings at zoom 15 and above
      //   paint: {
      //     "fill-extrusion-color": "#aaa", // Building color
      //     "fill-extrusion-height": ["get", "height"],
      //     "fill-extrusion-base": ["get", "min_height"],
      //     "fill-extrusion-opacity": 0.6,
      //   },
      // });
      setLoaded(true);
      // setIsloading(true);
      setRefresh((prev) => !prev);
    });

    // Optional: Add 3D buildings

    if (latStr && longStr) {
      map?.current?.jumpTo({ center: [longStr, latStr], zoom: 12 });
    } else if (latLocal && longLocal && zoomLocal) {
      map?.current?.jumpTo({ center: [longLocal, latLocal], zoom: zoomLocal });
    }

    map.current.on("moveend", (e) => {
      const geoLoc = map.current.getCenter();
      const getzoom = map.current.getZoom();
      // handleMoveEnd(e)
      handleMoveEnd(geoLoc, getzoom?.toFixed(2));
    });

    return () => {
      map.current.off("moveend", handleMoveEnd);
      map.current.remove();
    };
  }, [location, isswitchHandle?.isSatelite]);
  // useEffect(() => {
  //   geocoder.on("result", (e) => {
  //     // if (Object.keys(popupBeachData).length > 0) {
  //     router.push("/map");
  //     // }
  //   });
  // }, [geocoder]);

  // const rotateMap = () => {
  //   map.current.setBearing((map.current.getBearing() + 0.1) % 360);
  //   rotationAnimation.current = requestAnimationFrame(rotateMap);
  // };

  // const startRotation = () => {
  //   if (!rotationAnimation.current) {
  //     rotationAnimation.current = requestAnimationFrame(rotateMap);
  //   }
  // };

  //Rotation map ==================================

  // Pause rotation on interaction
  // useEffect(() => {
  //   if (map.current) {
  //     map.current.on('mousedown', stopRotation);
  //     map.current.on('touchstart', stopRotation);
  //     map.current.on('mouseup', () => is3D && startRotation());
  //     map.current.on('touchend', () => is3D && startRotation());
  //   }
  //   return () => {
  //     if (map.current) {
  //       map.current.off('mousedown', stopRotation);
  //       map.current.off('touchstart', stopRotation);
  //       map.current.off('mouseup', startRotation);
  //       map.current.off('touchend', startRotation);
  //     }
  //   };
  // }, [is3D]);

  useEffect(() => {
    getLocation();
  }, []);

  useEffect(() => {
    SetBeaches();
  }, [beachesCount, loaded, is3D]);

  useEffect(() => {
    if (!openDrawer) {
      setIsLoadingCount(true);
      setPagination({
        limit: 10,
        page: 1,
      });
      setBeaches([]);
      setLoadOff(true);
    }
    const getData = setTimeout(() => {
      if (!!!openDrawer) {
        // FetchAndSetDelayedBeaches();
        FetchAndSetDelayedBeachesCount();
      }
    }, 700);
    return () => clearTimeout(getData);
  }, [refresh, openDrawer]);

  // useEffect(() => {
  //   if (pagination?.page >= 2) {
  //     // setIsloading(true);
  //     FetchAndSetDelayedBeaches();
  //   }
  // }, [pagination.page]);

  // useEffect(() => {
  //   FetchAndSetDelayedBeaches();
  // }, [query]);

  // const fetchScrollLoaddata = () => {
  //   setPagination((prev) => ({ ...prev, page: prev?.page + 1 }));
  // };

  // useEffect(() => {
  //   // Resize map when toggling to full screen
  //   if (map.current) {
  //     map.current.resize();
  //   }
  // }, [toggleSidebar]);

  return (
    <>
      <h1 className="hidden">Map</h1>
      {Object.keys(popupBeachData).length === 0 ? (
        <h2 className="hidden">Beaches</h2>
      ) : null}
      {Object.keys(popupBeachData).length > 0 ? (
        <h2 className="hidden">{popupBeachData?.name}</h2>
      ) : null}
      <section className="">
        <div className="relative z-[60] h-full w-full min-h-[90vh] shadow-inner p-1">
          {Object.keys(popupBeachData).length > 0 ? (
            <div
              className={`absolute z-50 top-16 right-3 bottom-20 rounded-xl w-[350px] xl:w-[370px] 2xl:w-[400px] 4xl:w-[490px] bg-white md:block hidden overflow-hidden`}
            >
              <div
                className="overflow-y-scroll h-full p-4 shadow-inner scrollable-container"
                id="map-beach-modal"
              >
                <SingleBeachShowModal data={popupBeachData} mode={mode} />
              </div>
            </div>
          ) : null}
          <div
            ref={mapContainer}
            className="map-container h-full w-full min-h-[90vh] p-1 border-2 border-gray-800 rounded-2xl"
          />
          <MapBottomButtons
            map={map}
            isFilter={isFilter}
            isNudeToggle={isNudeToggle}
            isSharkToggle={isSharkToggle}
            setIs3D={setIs3D}
            is3D={is3D}
            isswitchHandle={isswitchHandle}
            isPopupBeachData={isPopupBeachData}
            isLoadingCount={isLoadingCount}
            activityStatus={activityStatus}
            setActivityStatus={setActivityStatus}
            openDrawer={openDrawer}
            setOpenDrawer={setOpenDrawer}
            setQuery={setQuery}
            query={query}
            setIsSwitchHandle={setIsSwitchHandle}
          />
        </div>
      </section>
      <SharkAttacksModalNew open={open} setOpen={setOpen} />
    </>
  );
}
