"use client";
import React, { useRef, useEffect, useState, useContext, useMemo } from "react";
import mapboxgl from "!mapbox-gl"; // eslint-disable-line import/no-webpack-loader-syntax
import { valContext } from "@/helper/context/ValContext";
import {
  GetSharkAttackeByLatLong,
  PostSearchForMap,
  PostSharkForMap,
} from "@/app/(HomeHeader)/action";
import SingleBeachShowModal from "./SingleBeachShowModal";
import { useParams, usePathname, useRouter } from "next/navigation";
import useSessionStorage from "@/helper/hook/useSessionStorage";
import MapBottomButtons from "./map-bottom-buttons";
import SharkAttacksModalNew from "../Shark/sharkAttackModalNew";
import { handleWindSwitch } from "@/helper/mapConstant";
import MapBeachCard from "./MapBeachCard";
import { CustomGrid } from "../Custom-Display";
import { CheckRoundIcon, LoaderIcon } from "../social-icons/icons";
import InfiniteScroll from "react-infinite-scroll-component";
import FilterForMap from "./FilterForMap";
import { FinalImageGenerator, isMobileView } from "@/helper/functions";
import MapBottomButtonsMob from "./map-bottom-buttons-mobile";
import SingleBeachShowModalMob from "./SingleBeachShowModalMob";
mapboxgl.accessToken = process.env.NEXT_PUBLIC_MAPBOX_API_KEY;

const MapWithBeachesNewMob = ({
  latStr,
  longStr,
  popupBeachData,
  isFilter = false,
  isNudeToggle = false,
  isSharkToggle = false,
  mode = "all",
}) => {
  let isPopupBeachData = Object.keys(popupBeachData).length > 0;
  const [beaches, setBeaches] = useState([]);
  const [beachesCount, setBeachesCount] = useState([]);
  const [isLoadingCount, setIsLoadingCount] = useState(true);
  const [loaded, setLoaded] = useState(false);
  const [location, setLocation] = useState(null);
  const [CurrentLocation, setCurrentLocation] = useState(null);
  const [refresh, setRefresh] = useState(false);
  const [query, setQuery] = useState("");
  const [typeFilter, setTypeFilter] = useState([]);
  const [openDrawer, setOpenDrawer] = useState(false);
  const [is3D, setIs3D] = useState(false);
  const isMobile = isMobileView();
  const isSatelite = useMemo(
    () =>
      typeof window !== "undefined" ? +localStorage.getItem("isSatelite") : 0,
    []
  );
  const isShark = useMemo(
    () =>
      typeof window !== "undefined" && mode !== "nude"
        ? +localStorage.getItem("isShark") || mode == "shark"
        : 0,
    []
  );
  const isNude = useMemo(
    () =>
      typeof window !== "undefined"
        ? +localStorage.getItem("isNude") || mode == "nude"
        : 0,
    []
  );
  // const isWind = useMemo(() => typeof window !== "undefined" ? +localStorage.getItem("isWind") : 0, []);
  const [isswitchHandle, setIsSwitchHandle] = useState({
    isShark: isShark ?? 0,
    isSatelite: isSatelite ?? 0,
    isNude: isNude ?? 0,
    isWind: 0,
  });

  const {
    activityStatus,
    setActivityStatus,
    listicleLocation,
    setListicleLocation,
  } = useContext(valContext);
  const [loadoff, setLoadOff] = useState(true);
  const [pagination, setPagination] = useState({
    limit: 10,
    page: 1,
  });
  const [open, setOpen] = useState({});
  const pathname = usePathname();
  const mapContainer = useRef(null);
  const map = useRef(null);
  const router = useRouter();
  const [latLocal, setLatLocal] = useSessionStorage("lat", null);
  const [longLocal, setLongLocal] = useSessionStorage("long", null);
  const [zoomLocal, setZoomLocal] = useSessionStorage("zoom", null);

  const FetchAndSetDelayedBeachesCount = async () => {
    if (!map.current) return;
    const bounds = map.current.getBounds();
    const FilteredActivities = Object.fromEntries(
      Object.entries(activityStatus).filter(([key, value]) => value)
    );
    const FilteredActivitiesArray =
      Object.getOwnPropertyNames(FilteredActivities);
    const payload = {
      point1: bounds.getSouthWest().toArray().reverse(),
      point2: bounds.getNorthEast().toArray().reverse(),
      point3: bounds.getNorthWest().toArray().reverse(),
      point4: bounds.getSouthEast().toArray().reverse(),
      ...(FilteredActivitiesArray?.length || typeFilter?.length
        ? { beachCategories: [...FilteredActivitiesArray, ...typeFilter] }
        : {}),
      // ...FilteredActivities,
      // getCounts: true,
    };

    let Result = null;
    let beachList = [];

    if (isswitchHandle?.isShark || mode == "shark") {
      Result = await PostSharkForMap(payload);
      beachList = Result?.data?.data?.rows;
    } else {
      try {
        if (isswitchHandle?.isNude) {
          payload.beachCategories =
            FilteredActivitiesArray?.length || typeFilter?.length
              ? [...FilteredActivitiesArray, ...typeFilter, "nude"]
              : ["nude"];
        }
        Result = await PostSearchForMap(payload);
        beachList = Result?.data?.beaches?.rows;
      } catch (error) {
        console.log(error);
      }
    }
    // const Results = await isShark ? PostSharkForMap(payload) : PostSearchForMap(payload);
    // const Results = await PostSharkForMap(payload, token);
    const beachLists = beachList;
    if (listicleLocation) {
      setListicleLocation(null);
    }

    const AllBeachWithRefinedDataCount = beachLists?.map((el) => ({
      ...el,
      lat: el?.GeoLoc?.coordinates?.[1] ?? 0,
      long: el?.GeoLoc?.coordinates?.[0] ?? 0,
    }));
    setBeachesCount(AllBeachWithRefinedDataCount);
    setIsLoadingCount(false);
  };
  const SetBeaches = () => {
    if (!mapContainer.current || !loaded) {
      return;
    }

    if (map.current.getSource("beaches")) {
      map.current.getSource("beaches").setData({
        type: "FeatureCollection",
        features: beachesCount?.length
          ? beachesCount?.map((beach) => ({
            type: "Feature",
            geometry: {
              type: "Point",
              coordinates: [beach.long, beach.lat],
            },
            properties: beach,
          }))
          : [],
      });
    } else {
      map?.current?.addSource("beaches", {
        type: "geojson",

        data: {
          type: "FeatureCollection",
          crs: {
            type: "name",
            properties: {
              name: "urn:ogc:def:crs:OGC:1.3:CRS84",
            },
          },
          features: beachesCount?.length
            ? beachesCount?.map((beach) => ({
              type: "Feature",
              geometry: {
                type: "Point",
                coordinates: [beach.long, beach.lat, 0],
              },
              properties: beach,
            }))
            : [],
        },
        cluster: true,
        clusterMaxZoom: 14, // Max zoom to cluster points on
        clusterRadius: 50, // Radius of each cluster when clustering points (defaults to 50)
      });
      map.current.addLayer({
        id: "clusters",
        type: "circle",
        source: "beaches",
        filter: ["has", "point_count"],
        paint: {
          "circle-color": [
            "step",
            ["get", "point_count"],
            isswitchHandle?.isShark || mode == "shark" ? "#fa1b1b" : "#fff000",
            100,
            isswitchHandle?.isShark || mode == "shark" ? "#fa1b1b" : "#fff000",
            750,
            isswitchHandle?.isShark || mode == "shark" ? "#fa1b1b" : "#fff000",
          ],
          "circle-radius": [
            "step",
            ["get", "point_count"],
            25,
            100,
            25,
            750,
            30,
          ],
        },
      });

      map.current.addLayer({
        id: "cluster-count",
        type: "symbol",
        source: "beaches",
        filter: ["has", "point_count"],
        layout: {
          "text-field": ["get", "point_count_abbreviated"],
          "text-font": ["DIN Offc Pro Medium", "Arial Unicode MS Bold"],
          "text-size": 16,
        },
        paint: {
          "text-color":
            isswitchHandle?.isShark || mode == "shark" ? "#fff" : "#000",
        },
      });
      map.current.loadImage(
        isswitchHandle?.isShark || mode == "shark"
          ? "/shark1.png"
          : "/Marker4-4x.png",
        (error, image) => {
          if (error) throw error;
          // const scaledImage = new Image();
          // scaledImage.src = image;
          map.current.addImage("beach-icon", image);

          // Add the symbol layer with the beach icon and beach name
          map.current.addLayer({
            id: "unclustered-point",
            type: "symbol",
            source: "beaches",
            filter: ["!", ["has", "point_count"]],
            layout: {
              "icon-image": "beach-icon",
              "icon-size":
                isswitchHandle?.isShark || mode == "shark" ? 0.4 : 0.22,
              "icon-allow-overlap": true,
              "text-field": ["get", "name"], // Assuming your data source has a 'beachName' property
              "text-size": 18, // Adjust the size of the text
              "text-offset": [1, 0], // Position the text slightly below the icon
              "text-anchor": "left", // Anchor text to be above the icon
              "text-font": ["Open Sans Bold", "Arial Unicode MS Bold"],
            },
            paint: {
              "text-color":
                isswitchHandle?.isShark || mode == "shark" ? "red" : "#FF6B00", // Set text color FF6B00
              "text-halo-color": "#fff  ", // Optional: Add a halo around the text for better readability
              "text-halo-width": 1, // Optional: Set halo width
            },
          });
        }
      );

      if (CurrentLocation) {
        new mapboxgl.Marker({ color: "red" }) // Blue marker for current CurrentLocation
          .setLngLat([CurrentLocation[0], CurrentLocation[1]])
          .addTo(map.current);
      }

      map.current.on("click", "clusters", (e) => {
        const features = map.current.queryRenderedFeatures(e.point, {
          layers: ["clusters"],
        });
        const clusterId = features[0].properties.cluster_id;
        map.current
          .getSource("beaches")
          .getClusterExpansionZoom(clusterId, (err, zoom) => {
            if (err) return;

            map.current.easeTo({
              center: features[0].geometry.coordinates,
              zoom: zoom,
            });
          });
      });

      map.current.on("click", "unclustered-point", async (e) => {
        if (!isswitchHandle?.isShark && (mode === "all" || mode === "nude")) {
          const coordinates = e.features[0].geometry.coordinates.slice();
          // const { locationAddress, images, beachDescription, beachCategory } =
          //   e.features[0].properties;
          const el = e.features[0].properties;
          const coordinatesData = JSON.parse(el?.GeoLoc);

          while (Math.abs(e.lngLat.lng - coordinates[0]) > 180) {
            coordinates[0] += e.lngLat.lng > coordinates[0] ? 360 : -360;
          }

          if (mode == "nude") {
            router.push(
              `/${pathname?.split("/")[1]}/nude-beaches/${el?.nameSlug}/@${coordinatesData?.coordinates?.[1] ?? 0
              },${coordinatesData?.coordinates?.[0] ?? 0}`
            );
          } else {
            router.push(
              `/${pathname?.split("/")[1]}/${el?.nameSlug}/@${coordinatesData?.coordinates?.[1] ?? 0
              },${coordinatesData?.coordinates?.[0] ?? 0}`
            );
          }
          // router.push(
          //   `/${pathname?.split("/")[1]}/${el?.nameSlug}/@${coordinatesData?.coordinates?.[1] ?? 0},${coordinatesData?.coordinates?.[0] ?? 0
          //   }`
          // );
        } else {
          const feature = e.features[0];
          // const address = JSON.parse(feature.properties.locationAddress);

          try {
            const el = e.features[0].properties;
            const coordinatesData = JSON.parse(el?.GeoLoc);
            const AllBeach = JSON.parse(el?.AllBeach);
            const cityName = JSON.parse(el?.city);
            const state = JSON.parse(el?.state);
            const country = JSON.parse(el?.country);
            const Results = await GetSharkAttackeByLatLong(
              {
                page: 1,
                limit: 5,
                // coordinates: `${coordinatesData?.coordinates?.[1] ?? 0},${coordinatesData?.coordinates?.[0] ?? 0}`
              },
              `${coordinatesData?.coordinates?.[1] ?? 0},${coordinatesData?.coordinates?.[0] ?? 0
              }`
            );
            let newLink = "";
            const res = Results?.data;
            if (Results?.status === "success") {
              if (res?.city?.state?.country?.slug) {
                newLink = `/${res?.city?.state?.country?.slug}/${res?.city?.state?.slug}/${res?.city?.slug}/${res?.AllBeach?.nameSlug}`; //
                res.link = `/${res?.city?.state?.country?.slug}/${res?.city?.state?.slug}/${res?.city?.slug}/${res?.AllBeach?.nameSlug}`; //
                res.location = `${res?.city?.name}, ${res?.city?.state?.country?.code}`; //
              } else {
                newLink = `/${res?.country?.slug}/${res?.state?.slug}/${res?.city?.slug}/${res?.AllBeach?.nameSlug}`; //
                res.link = `/${res?.country?.slug}/${res?.state?.slug}/${res?.city?.slug}/${res?.AllBeach?.nameSlug}`; //
                res.location = `${res?.city?.name}, ${res?.country?.code}`; //
              }
              setOpen({
                ...res,
                ...res?.AllBeach,
                coordinatesData: coordinatesData,
                newLink:
                  country?.slug &&
                    state?.slug &&
                    cityName?.slug &&
                    AllBeach?.nameSlug
                    ? `/${country?.slug}/${state?.slug}/${cityName?.slug}/${AllBeach?.nameSlug}`
                    : null,
                beachName: AllBeach?.name,
                cityName: cityName?.name,
                stateName: state?.name,
                countryName: country?.name,
              });
            }
          } catch (error) {
            console.log(error);
          }
        }
      });

      map.current.on("mouseenter", "clusters", () => {
        map.current.getCanvas().style.cursor = "pointer";
      });
      map.current.on("mouseleave", "clusters", () => {
        map.current.getCanvas().style.cursor = "";
      });
      map.current.on("mouseenter", "unclustered-point", () => {
        map.current.getCanvas().style.cursor = "pointer";
        // const popupContent = document.getElementById("mapboxgl-popup");
      });
      map.current.on("mouseleave", "unclustered-point", () => {
        map.current.getCanvas().style.cursor = "";
      });
      map.current.addControl(new mapboxgl.ScaleControl());
    }
  };

  const handleMoveEnd = (e, zoomIndex) => {
    const exstingPath = window.location?.pathname;
    const newUrl = exstingPath?.split("/@")[0];
    //local storage
    setZoomLocal(zoomIndex ?? 0);
    setLatLocal(e?.lat?.toFixed(6) ?? 0);
    setLongLocal(e?.lng?.toFixed(6) ?? 0);

    setRefresh((prev) => !prev);
  };

  const getLocation = () => {
    if (typeof window !== "undefined" && window.navigator.geolocation) {
      window.navigator.geolocation.getCurrentPosition(
        (position) => {
          setLocation([position.coords.longitude, position.coords.latitude]);
          setCurrentLocation([
            position.coords.longitude,
            position.coords.latitude,
          ]);
        },
        (err) => {
          if (listicleLocation?.length)
            setLocation([listicleLocation[0], listicleLocation[1]]);
          else if (latStr && longStr) setLocation([longStr, latStr]);
          else setLocation([-118.4117325, 34.020479]); // los angeles coordinates
        }
      );
    } else {
      if (listicleLocation?.length)
        setLocation([listicleLocation[0], listicleLocation[1]]);
      else if (latStr && longStr) setLocation([longStr, latStr]);
      else setLocation([-118.4117325, 34.020479]); // los angeles coordinates
    }
  };

  useEffect(() => {
    handleWindSwitch({
      mapContainer,
      loaded,
      map,
      isswitchHandle,
    });
  }, [loaded, isswitchHandle?.isWind]);

  useEffect(() => {
    if (!location) return;

    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: isswitchHandle?.isSatelite
        ? "mapbox://styles/mapbox/satellite-streets-v12"
        : "mapbox://styles/mapbox/streets-v12",
      // show3dObjects: true,
      center: location,
      zoom: listicleLocation?.length && !CurrentLocation ? 4 : 9.4,
      minZoom: 3,
      maxBounds: [
        [-180, -85], // Southwest coordinates
        [180, 85], // Northeast coordinates
      ],
      projection: { name: "mercator" },
      pitch: 0,
      bearing: 0,
    });

    map.current?.addControl(new mapboxgl.NavigationControl(), "bottom-right");

    // map.current.addControl(geocoder, "top-left");

    map.current.on("load", () => {
      map.current.addSource("mapbox-dem", {
        type: "raster-dem",
        url: "mapbox://mapbox.mapbox-terrain-dem-v1",
        tileSize: 512,
        maxzoom: 14,
      });
      if (is3D) {
        map.current.setTerrain({ source: "mapbox-dem", exaggeration: 1.5 });
        map.current.setPitch(60);
        map.current.setBearing(-17.6);
        // startRotation();
      } else {
        map.current.setTerrain(null);
        map.current.setPitch(0);
        map.current.setBearing(0);
        // stopRotation();
      }
      setLoaded(true);
      // setIsloading(true);
      setRefresh((prev) => !prev);
    });

    // Optional: Add 3D buildings

    if (latStr && longStr) {
      map?.current?.jumpTo({ center: [longStr, latStr], zoom: 12 });
    } else if (latLocal && longLocal && zoomLocal) {
      map?.current?.jumpTo({ center: [longLocal, latLocal], zoom: zoomLocal });
    }

    map.current.on("moveend", (e) => {
      const geoLoc = map.current.getCenter();
      const getzoom = map.current.getZoom();
      // handleMoveEnd(e)
      handleMoveEnd(geoLoc, getzoom?.toFixed(2));
    });

    return () => {
      map.current.off("moveend", handleMoveEnd);
      map.current.remove();
    };
  }, [location, isswitchHandle?.isSatelite]);

  useEffect(() => {
    getLocation();
  }, []);

  useEffect(() => {
    SetBeaches();
  }, [beachesCount, loaded, is3D]);

  useEffect(() => {
    if (!openDrawer) {
      setIsLoadingCount(true);
      setPagination({
        limit: 10,
        page: 1,
      });
      setBeaches([]);
      setLoadOff(true);
    }
    const getData = setTimeout(() => {
      if (!!!openDrawer) {
        // FetchAndSetDelayedBeaches();
        FetchAndSetDelayedBeachesCount();
      }
    }, 700);
    return () => clearTimeout(getData);
  }, [refresh, openDrawer]);

  return (
    <>
      <h1 className="hidden">Map</h1>
      {Object.keys(popupBeachData).length === 0 ? (
        <h2 className="hidden">Beaches</h2>
      ) : null}
      {Object.keys(popupBeachData).length > 0 ? (
        <h2 className="hidden">{popupBeachData?.name}</h2>
      ) : null}
      {/* <section className=""> */}
      <div className="grid md:grid-flow-col md:grid-cols-12 md:col-span-12 mr-2 ml-4">
        <div
          className={`relative side-panel-search h-[10vh] md:col-span-4 lg:col-span-3 xl:col-span-3 4xl:col-span-2 md:order-1 ${isPopupBeachData ? `order-3` : `order-2`
            }`}
        ></div>
        <div
          className={`relative z-40 md:col-span-8 lg:col-span-9 xl:col-span-9 4xl:col-span-10 md:order-2 order-1`}
        >
          {/* <div className="relative z-[60] h-full w-full min-h-[90vh] shadow-inner p-1"> */}
          {/* {Object.keys(popupBeachData).length > 0 ? ( */}
          {/* <div
                        className={`absolute z-50 top-16 right-3 bottom-20 rounded-xl w-[350px] xl:w-[370px] 2xl:w-[400px] 4xl:w-[490px] bg-white md:block hidden overflow-hidden`}
                    >
                        <div
                            className="overflow-y-scroll h-full p-4 shadow-inner scrollable-container"
                            id="map-beach-modal"
                        > */}
          <SingleBeachShowModalMob
            data={popupBeachData}
            mode={mode}
            openDrawer={isMobile &&
              Object.keys(popupBeachData).length > 0 &&
              popupBeachData?.lat != 0 &&
              popupBeachData?.long != 0
            }
          />
          {/* </div>
                    </div> */}
          {/* ) : null} */}
          <div className="relative z-[30] w-full md:min-h-[90vh] shadow-inner">
            <div
              ref={mapContainer}
              className="md:map-container md:h-full w-full md:min-h-[90vh] h-[72vh] p-1 border-2 border-gray-800 rounded-2xl"
            />
          </div>
          <MapBottomButtonsMob
            map={map}
            isFilter={isFilter}
            isNudeToggle={isNudeToggle}
            isSharkToggle={isSharkToggle}
            setIs3D={setIs3D}
            is3D={is3D}
            isswitchHandle={isswitchHandle}
            isPopupBeachData={isPopupBeachData}
            isLoadingCount={isLoadingCount}
            activityStatus={activityStatus}
            setActivityStatus={setActivityStatus}
            openDrawer={openDrawer}
            setOpenDrawer={setOpenDrawer}
            setQuery={setQuery}
            query={query}
            setIsSwitchHandle={setIsSwitchHandle}
          />
          {/* </div> */}
        </div>
      </div>
      {/* </section> */}
      <SharkAttacksModalNew open={open} setOpen={setOpen} />
    </>
  );
};

export default MapWithBeachesNewMob;
