import { siteMetadata } from "@/data/siteMetadata";
import React from "react";

const SingleBeachMapJSONLD = ({ mapData }) => {
  const MapBeachBreadCumber = {
    "@context": "https://schema.org/",
    "@type": "BreadcrumbList",
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        name: "Home",
        item: `https://sandee.com/`,
      },
      {
        "@type": "ListItem",
        position: 2,
        name: "Map",
        item: `https://sandee.com/map`,
      },
      {
        "@type": "ListItem",
        position: 3,
        name: mapData?.beachName,
        item: `https://sandee.com/map/${mapData?.beachSlug}/${mapData?.coordinates}`,
      },
    ],
  };


  const beachLocation = `${mapData?.city ?? ""}, ${mapData?.state ?? ""
    }, ${mapData?.country ?? ""} `;

  const webPageSchema = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    url: `https://sandee.com/map/${mapData?.beachSlug}/${mapData?.coordinates}`,
    name: `${mapData?.beachName} Map, ${beachLocation} | Sandee`,
    description: `${mapData?.beachName}: Discover this iconic ${beachLocation} location with our interactive map. Explore nearby attractions, amenities, and more with Sandee.`,
    publisher: {
      "@type": "Organization",
      name: siteMetadata?.title || "Sandee",
    },
  };

  return (
    <>
      <script
        type="application/ld+json"
        id="application/ld+jsonMapBeachBreadCumber"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(MapBeachBreadCumber) }}
      ></script>
      <script
        type="application/ld+json"
        id="application/ld+jsonMapBeachWebPage"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(webPageSchema) }}
      ></script>
    </>
  );
};

export default SingleBeachMapJSONLD;
