"use client";
import React, { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, Drawer, Space } from "antd";
import { blurDataURL, defaultImage, EditorContent, scrollToSection } from "@/helper/functions";
import Image from "next/image";
import AuthWrapper from "../Common/AuthWrapper";
import { ReviewWriteIcon } from "../social-icons/icons";
import BeachReviewModal from "../BeachPage/BeachReviewModal";
import Link from "next/link";
import { BeachPageData } from "@/data/beachPageData";
import CustomeImage from "../Common/CustomeImage";

const SingleBeachModalNew = ({ data }) => {
  const [open, setOpen] = useState(true);
  useEffect(() => {
    setOpen(true);
  }, [data]);

  const [currentTab, setCurrentTab] = useState("overview");
  const [showTitleDiv, setShowTitleDiv] = useState(false);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [beachLink, setBeachLink] = useState("");
  const section1Ref = useRef(null);
  const contentRef = useRef(null);

  data.lat = data?.GeoLoc?.coordinates?.length
    ? data?.GeoLoc?.coordinates?.[1]
    : 0;
  data.lon = data?.GeoLoc?.coordinates?.length
    ? data?.GeoLoc?.coordinates?.[0]
    : 0;
  useEffect(() => {
    if (data?.city?.state?.country?.slug) {
      setBeachLink(
        `/${data?.city?.state?.country?.slug}/${data?.city?.state?.slug}/${data?.city?.slug}/${data?.nameSlug}`
      );
    } else if (data?.country?.slug) {
      setBeachLink(
        `/${data?.country?.slug}/${data?.state?.slug}/${data?.city?.slug}/${data?.nameSlug}`
      );
    } else {
      setBeachLink(
        `/${data?.countrySlug}/${data?.stateSlug}/${data?.citySlug}/${data?.nameSlug}`
      );
    }
  }, [data]);

  //   const icons = [
  //     {
  //       id: 1,
  //       component: (
  //         <>
  //           <Link
  //             className="flex flex-col items-center group cursor-pointer "
  //             href={`https://maps.google.com/?q=${data?.address}`}
  //             target="_blank"
  //             key={1}
  //           >
  //             <div className="md:p-3 p-2 rounded-full border group-hover:border-primary-600 w-fit ">
  //               <LocationDirectionIcon width={22} height={22} fill={"#00aae3"} />
  //             </div>
  //             <p className="2xl:text-sm text-[12px] mt-1 group-hover:text-primary-600 truncate">
  //               Direction
  //             </p>
  //           </Link>
  //         </>
  //       ),
  //     },
  //     {
  //       id: 2,
  //       component: (
  //         <>
  //           <Link
  //             className="flex flex-col items-center group cursor-pointer"
  //             href={beachLink}
  //             target="_blank"
  //             key={2}
  //           >
  //             <div className="md:p-3 p-2 rounded-full border group-hover:border-primary-600 w-fit">
  //               <EyeIcon width={20} height={20} stroke={"#00aae3"} />
  //             </div>
  //             <p className="2xl:text-sm text-[12px] mt-1 group-hover:text-primary-600 truncate">
  //               View Beach
  //             </p>
  //           </Link>
  //         </>
  //       ),
  //     },
  //     {
  //       id: 3,
  //       component: (
  //         <>
  //           <AuthWrapper
  //             key={3}
  //             WithoutLogIn={
  //               <>
  //                 <div className="flex flex-col items-center group cursor-pointer">
  //                   <div className="md:p-3 p-2 rounded-full border group-hover:border-primary-600 w-fit">
  //                     <ReviewWriteIcon width={20} height={20} fill={"#00aae3"} />
  //                   </div>
  //                   <p className="2xl:text-sm text-[12px] mt-1 group-hover:text-primary-600 truncate">
  //                     Write Review
  //                   </p>
  //                 </div>
  //               </>
  //             }
  //             WithLogIn={
  //               <BeachReviewModal beachData={data} Other={TabAddReview} />
  //             }
  //           />
  //         </>
  //       ),
  //     },
  //     {
  //       id: 4,
  //       component: (
  //         <>
  //           <AuthWrapper
  //             key={4}
  //             WithoutLogIn={
  //               <>
  //                 <div className="flex flex-col items-center group cursor-pointer">
  //                   <div className="md:p-3 p-2 rounded-full border group-hover:border-primary-600 w-fit">
  //                     <AddPhoto width={20} height={20} fill={"#00aae3"} />
  //                   </div>
  //                   <p className="2xl:text-sm text-[12px] mt-1 group-hover:text-primary-600 truncate">
  //                     Add Photos
  //                   </p>
  //                 </div>
  //               </>
  //             }
  //             WithLogIn={
  //               <BeachPhotoUploadModal beachData={data} Other={TabAddPhoto} />
  //             }
  //           />
  //         </>
  //       ),
  //     },
  //   ];

  const BasicArray = [
    {
      IconKey: "phoneBlue",
      SVG: `https://images.sandee.com/Icons/phone.svg`,
      detail: data?.beachBasicDetail?.phoneNumber,
      display: data?.beachBasicDetail?.phoneNumber,
    },
    {
      IconKey: "rulerBlue",
      SVG: `https://images.sandee.com/Icons/ruler.svg`,
      detail: `${data?.beachBasicDetail?.size} ${data?.beachBasicDetail?.unit} `,
      display: data?.beachBasicDetail?.size && data?.beachBasicDetail?.unit,
    },
    {
      IconKey: "offerBlue",
      detail: "Entry Fee",
      SVG: `https://images.sandee.com/Icons/offer.svg`,
      display: data?.beachCategory?.restaurant,
    },
    {
      IconKey: "toiletBlue",
      detail: "Restroom Available",
      SVG: `https://images.sandee.com/Icons/toilet.svg`,
      display: data?.beachCategory?.restRooms,
    },
    {
      IconKey: "clockBlue",
      SVG: `https://images.sandee.com/Icons/clock.svg`,
      detail: data?.beachBasicDetail?.hours,
      display: data?.beachBasicDetail?.hours,
    },

    {
      IconKey: "sandBlue",
      SVG: `https://images.sandee.com/Icons/sand.svg`,
      detail: data?.beachBasicDetail?.sandColor?.join(",") || "",
      display: data?.beachBasicDetail?.sandColor?.join(","),
    },

    {
      detail: "Parking",
      IconKey: "parkingBlue",
      SVG: `https://images.sandee.com/Icons/parking.svg`,
      display: data?.beachBasicDetail?.parking,
    },

    // {
    //   IconKey: "shower",
    //   SVG: `https://images.sandee.com/Icons/shower.svg`,
    //   detail: data?.beachBasicDetail?.shower || "Shower",
    //   display: data?.beachBasicDetail?.shower,
    // },

    {
      IconKey: "pinBlue",
      SVG: `https://images.sandee.com/Icons/pin.svg`,
      detail: data?.address,
      display: data?.address,
      link: `https://maps.google.com/?q=${data?.address}`,
    },
  ];
  const DATA = BeachPageData?.beachInformation?.map((el) => ({
    title: el?.title,
    items: el?.items,
    display:
      el?.items.filter((elp) => data?.beachCategory?.[elp?.id])?.length !== 0,
  }));
  useEffect(() => {
    const handleScroll = () => {
      // Show the div if scrolled down more than 200px
      if (window.scrollY > 200) {
        setShowTitleDiv(true);
      } else {
        setShowTitleDiv(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  const tabList = [
    { id: 1, label: "Overview", value: "overview" },
    { id: 2, label: "Basic Details", value: "basicDetails" },
    { id: 3, label: "All Amenities", value: "amenities" },
  ];
  const onClose = () => {
    setOpen(false);
  };
  return (
    <>
      <Drawer
        // title={`large Drawer`}
        placement="right"
        // size={"large"}
        headerStyle={{ display: "none" }}
        onClose={onClose}
        open={open}
        bodyStyle={{ padding: 0 }}
        // extra={
        //   <Space>
        //     <Button onClick={onClose}>Cancel</Button>
        //     <Button type="primary" onClick={onClose}>
        //       OK
        //     </Button>
        //   </Space>
        // }
        width={isFullScreen ? "75%" : "40%"}
        mask={false}
        // width={650}
        className={`shadow-xl `}
      >
        <div className="px-7">
          <div className=" bg-white flex justify-between items-center sticky z-50 overflow-hidden top-0 py-3">
            <button
              onClick={() => {
                setIsFullScreen((prv) => !prv);
              }}
              className={`${isFullScreen ? "-scale-x-100" : ""}`}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="size-[1em] text-[1.7em]"
              >
                <path d="M3.573 6.017v13.204m8.762-1.489L7.223 12.62m0 0 5.112-5.114M7.223 12.62h13.204" />
              </svg>
            </button>
            <button
              onClick={() => {
                setOpen((prv) => !prv);
              }}
            >
              <span className="contents">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="size-[1em] text-[1.7em]"
                >
                  <path d="M19 5 5 19M5 5l14 14" />
                </svg>
              </span>
            </button>
          </div>
          <div className="py-7">
            <div className="flex ">
              <div className="flex-1">
                <h2 className="text-pretty text-2xl font-semibold ">
                  {data?.name}
                </h2>
                <div className="mt-1 text-[#00000080]">
                  <span className="text-black">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="shrink-0 transform-cpu size-[1em] relative top-[.125em] mr-1 inline-block fill-current align-baseline text-[1.125em]"
                    >
                      <path d="m12.63 4.401 2.044 4.128a.656.656 0 0 0 .527.385l4.513.669a.695.695 0 0 1 .386 1.196l-3.253 3.227a.682.682 0 0 0-.206.617l.785 4.538a.707.707 0 0 1-1.029.746l-4.063-2.147a.758.758 0 0 0-.668 0l-4.063 2.147a.707.707 0 0 1-1.029-.746l.785-4.59a.682.682 0 0 0-.206-.617L3.862 10.78a.695.695 0 0 1 .424-1.196l4.513-.669a.656.656 0 0 0 .527-.385L11.37 4.4a.695.695 0 0 1 1.26 0Z" />
                    </svg>
                    <span className="underline-offset-2 group-hover:underline">
                      {(data?.rating100 ?? 80) / 20}
                    </span>
                  </span>
                  {/* <span className="mx-1">·</span> */}
                  {/* <span className="group-hover:text-black">1.6k reviews</span> */}
                  <span className="mx-1">·</span>
                  {`${data?.city?.name}, ${data?.state?.name}`}
                  <div className="mt-1 flex items-center leading-tight">
                    <div className="flex gap-1 leading-tight">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="shrink-0 transform-cpu size-[1em] relative top-[.0625em] text-[1.125em]"
                      >
                        <path d="M3.643 17.786S6.214 15.214 12 15.214s8.357 2.572 8.357 2.572M12 15.214v5.143M6.214 20.357l4.5-5.104M17.786 20.357l-4.5-5.104M7.551 12.643c-.02-.214-.02-.43 0-.643a4.5 4.5 0 1 1 9 0c.02.214.02.429 0 .643M3.643 12h1.286M6.214 6.214l.643.643M12 3.643v1.286M17.786 6.214l-.643.643M20.357 12h-1.286" />
                      </svg>
                      <span className="capitalize">attraction</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="relative flex aspect-almost-square auto-rows-fr grid-cols-4 rounded-b-2xl outline-none xl:grid xl:aspect-[1024/508] xl:gap-2 xl:overflow-hidden xl:rounded-2xl">
            <div className="relative row-span-2 col-span-2">
              <CustomeImage
                alt="Primary Image"
                width={508}
                height={508}
                className="absolute inset-0 size-full"
                blurDataURL={blurDataURL(508, 508)}
                src={data?.images[0]?.imageUrl}
              />
            </div>
            {data?.images?.slice(1, data?.images?.length)?.map((img, index) => (
              <div className="relative" key={index}>
                <CustomeImage
                  alt={`Sandee Thumbnail ${index + 1}`}
                  width={250}
                  height={250}
                  blurDataURL={blurDataURL(250, 250)}
                  className="absolute inset-0 size-full"
                  src={img?.imageUrl}
                />
              </div>
            ))}
            <div className="absolute bottom-2 right-2 hidden xl:block">
              <span
                data-variant="knockout"
                className="group group/button relative z-0 border border-transparent inline-flex justify-center items-center rounded-full font-medium outline-none gap-[.3em] disabled:pointer-events-none transition-colors text-center py-[.25em] text-balance bg-white text-black hover:bg-white/95 disabled:opacity-30 text-xs min-h-8 px-3 leading-[1.125] font-semibold"
                type="button"
              >
                <span className="contents">Show all photos</span>
              </span>
            </div>
          </div>

          {/* <div
          className="absolute inset-x-0 top-0 z-2 flex h-12 gap-2.5 border-b border-separator bg-white leading-tight px-7"
          style={{ opacity: 1, transform: "translateY(0%) translateZ(0px)" }}
        >
          <div className="flex-1 self-center truncate font-semibold">
            Santa Monica Beach
          </div>
          <div className="flex gap-1" />
        </div> */}

          <div
            className="sticky z-1 overflow-hidden bg-white h-12 top-10"
          //   style={{ height: 48, top: 48 }}
          //   className="bg-white h-12 "
          >
            <div className=" flex h-full overflow-x-scroll no-scrollbar scroll-smooth">
              <ul className="flex shrink-0 grow items-end gap-1.5 border-b border-separator font-medium sm:gap-2.5">
                {tabList?.map((i, index) => {
                  return (
                    <li key={i?.id}>
                      <button
                        type="button"
                        className={`${currentTab === i?.value
                          ? "border-b-2 border-current"
                          : ""
                          } -mb-px block  px-2 py-1.5 hover:text-black text-black`}
                        onClick={() => {
                          setCurrentTab(i?.value);
                          section1Ref.current.scrollIntoView({
                            behavior: "smooth",
                          });
                          contentRef.current.scrollTop = 500;
                          // const section = document.getElementById(i?.value);
                          // section.scrollIntoView({
                          //   behavior: "smooth",
                          //   block: "start",
                          // });
                        }}
                      >
                        {i?.label}
                      </button>
                    </li>
                  );
                })}
              </ul>
            </div>
          </div>
          <div ref={contentRef} className="">
            {data?.beachDescription?.introduction && (
              <div className="mt-5 " id="overview" ref={section1Ref}>
                <p className="text-xl  font-medium mb-2">Overview</p>
                <EditorContent className="text-black text-sm mt-2 beachDescription" value={data?.beachDescription?.introduction} />
                {/* <div
                  className="text-black text-sm mt-2 beachDescription"
                  dangerouslySetInnerHTML={{
                    __html: data?.beachDescription?.introduction,
                  }}
                ></div> */}
              </div>
            )}
            <p className="text-xl font-medium my-4 mt-3" id={"basicDetails"}>
              Basic Details
            </p>
            <div className="text-sm flex flex-col gap-y-5 mt-3 mb-5">
              {BasicArray.filter((data) => data?.display).map((data) => (
                <div key={data?.IconKey} className={`${data?.link ? "" : ""}`}>
                  <div className="capitalize flex items-start gap-3 font-normal text-black">
                    <Image
                      src={`/static/icons/BasicDetails/${data?.IconKey}.svg`}
                      width={20}
                      height={20}
                      alt="IN"
                    // className="p-1"
                    />
                    {data?.link ? (
                      <span className="w-5/6 ">
                        <Link
                          target="_blank"
                          href={data?.link}
                          className=" hover:underline"
                        >
                          {data?.detail}
                        </Link>
                      </span>
                    ) : (
                      <span className="w-5/6 line-clamp-1 text-start leading-snug">
                        {data?.detail}
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
            <div id={"amenities"}>
              {DATA?.map((el) => el?.display).some((el) => el === true) ? (
                <>
                  <p className="text-xl  font-medium my-3">{`All Amenities at ${data?.name}`}</p>
                  <div className=" grid grid-cols-3 gap-3">
                    {DATA?.map(({ title, items, display }) => (
                      <div
                        key={title}
                        className={`${!display
                          ? "hidden list-should-hide "
                          : "flex flex-col gap-3"
                          }`}
                      >
                        <>
                          <p
                            className="text-black text-[18px] font-medium"
                            id={title}
                          >
                            {title}
                          </p>
                          {items?.map((link) => {
                            return (
                              <li
                                key={link?.id}
                                className={` list-none ${!data?.beachCategory?.[link?.id]
                                  ? "hidden list-should-hide "
                                  : ""
                                  }`}
                              >
                                <p className="text-sm capitalize m-0 gap-3 text-wrap flex text-ellipsis overflow-hidden  items-center py-0  font-normal transition-colors   text-black leading-snug">
                                  <span className="w-[20px]">
                                    {data?.beachCategory?.[link?.id] ? (
                                      link?.icon ? (
                                        <Image
                                          src={`/static/icons/BeachAmenities/${link?.id}Blue.svg`}
                                          width={20}
                                          height={20}
                                          alt="Icon for Beach Amenities"
                                        />
                                      ) : (
                                        <svg
                                          xmlns="http://www.w3.org/2000/svg"
                                          width={20}
                                          height={20}
                                          viewBox="0 0 16 16"
                                          fill="none"
                                          className=" p-[2px]"
                                        >
                                          <path
                                            d="M15.75 8C15.75 12.2802 12.2802 15.75 8 15.75C3.71978 15.75 0.25 12.2802 0.25 8C0.25 3.71978 3.71978 0.25 8 0.25C12.2802 0.25 15.75 3.71978 15.75 8ZM7.10356 12.1036L12.8536 6.35356C13.0488 6.15831 13.0488 5.84172 12.8536 5.64647L12.1465 4.93937C11.9512 4.74409 11.6346 4.74409 11.4393 4.93937L6.75 9.62869L4.56066 7.43934C4.36541 7.24409 4.04881 7.24409 3.85353 7.43934L3.14644 8.14644C2.95119 8.34169 2.95119 8.65828 3.14644 8.85353L6.39644 12.1035C6.59172 12.2988 6.90828 12.2988 7.10356 12.1036Z"
                                            fill="#00AAE3"
                                          />
                                        </svg>
                                      )
                                    ) : (
                                      <svg
                                        fill="#4a4a4a"
                                        width={20}
                                        height={20}
                                        version="1.1"
                                        id="Layer_1"
                                        xmlns="http://www.w3.org/2000/svg"
                                        xmlnsXlink="http://www.w3.org/1999/xlink"
                                        viewBox="0 0 300.003 300.003"
                                        xmlSpace="preserve"
                                        stroke="#4a4a4a"
                                        className=" p-[2px]"
                                      >
                                        <g
                                          id="SVGRepo_bgCarrier"
                                          strokeWidth={0}
                                        />
                                        <g
                                          id="SVGRepo_tracerCarrier"
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                        />
                                        <g id="SVGRepo_iconCarrier">
                                          <g>
                                            <g>
                                              <path d="M150,0C67.159,0,0.001,67.159,0.001,150c0,82.838,67.157,150.003,149.997,150.003S300.002,232.838,300.002,150 C300.002,67.159,232.839,0,150,0z M206.584,207.171c-5.989,5.984-15.691,5.984-21.675,0l-34.132-34.132l-35.686,35.686 c-5.986,5.984-15.689,5.984-21.672,0c-5.989-5.991-5.989-15.691,0-21.68l35.683-35.683L95.878,118.14 c-5.984-5.991-5.984-15.691,0-21.678c5.986-5.986,15.691-5.986,21.678,0l33.222,33.222l31.671-31.673 c5.986-5.984,15.694-5.986,21.675,0c5.989,5.991,5.989,15.697,0,21.678l-31.668,31.671l34.13,34.132 C212.57,191.475,212.573,201.183,206.584,207.171z" />{" "}
                                            </g>
                                          </g>
                                        </g>
                                      </svg>
                                    )}
                                  </span>

                                  {link?.label}
                                </p>
                              </li>
                            );
                          })}
                        </>
                      </div>
                    ))}
                  </div>
                </>
              ) : null}
            </div>
          </div>
        </div>
      </Drawer>
    </>
  );
};

export default SingleBeachModalNew;
