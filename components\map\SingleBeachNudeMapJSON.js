import { SiteDataPageWise, siteMetadata } from "@/data/siteMetadata";
import React from "react";

const SingleBeachNudeMapJSON = ({ mapData }) => {
  const MapBeachBreadCumber = {
    "@context": "https://schema.org/",
    "@type": "BreadcrumbList",
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        name: "Home",
        item: `https://sandee.com/`,
      },
      {
        "@type": "ListItem",
        position: 2,
        name: "Map",
        item: `https://sandee.com/maps`,
      },
      {
        "@type": "ListItem",
        position: 3,
        name: "Nude Beaches",
        item: `https://sandee.com/maps/nude-beaches`,
      },
      // {
      //   "@type": "ListItem",
      //   position: 4,
      //   name: mapData?.beachName,
      //   item: `https://sandee.com/maps/nude-beaches/${mapData?.beachSlug}/${mapData?.coordinates}`,
      // },
    ],
  };
  if (mapData?.latStr && mapData?.longStr) {
    MapBeachBreadCumber.itemListElement.push({
      "@type": "ListItem",
      position: 4,
      name: mapData?.beachName,
      item: `https://sandee.com/maps/nude-beaches/${mapData?.beachSlug}/${mapData?.coordinates}`,
    })
  }
  const webPageSchema = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    url: `https://sandee.com/maps/nude-beaches`,
    name: SiteDataPageWise.nudeMap.title,
    description: SiteDataPageWise.nudeMap.description,
    publisher: {
      "@type": "Organization",
      name: siteMetadata?.title || "Sandee",
    },
  };

  return (
    <>
      <script
        type="application/ld+json"
        id="application/ld+jsonMapBeachBreadCumber"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(MapBeachBreadCumber) }}
      ></script>
      <script
        type="application/ld+json"
        id="application/ld+jsonMapBeachBreadCumber"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(webPageSchema) }}
      ></script>
    </>
  );
};

export default SingleBeachNudeMapJSON;
