"use client";
import {
  <PERSON><PERSON>ontent,
  FinalImageGenerator,
  altText,
  blurDataURL,
  defaultImage,
} from "@/helper/functions";
import Image from "next/image";
import React, { memo, useEffect, useState } from "react";
import ReadOnlyRating from "../Common/ReadOnlyRating";
import {
  AddPhoto,
  CancelIcon,
  EyeIcon,
  LocationDirectionIcon,
  ReviewWriteIcon,
} from "../social-icons/icons";
import Link from "next/link";
import AuthWrapper from "../Common/AuthWrapper";
import BeachPhotoUploadModal from "../BeachPage/BeachPhotoUploadModal";
import BeachReviewModal from "../BeachPage/BeachReviewModal";
import "../../app/(HeaderSlim)/(Single Beach Page)/[countrySlug]/[stateSlug]/[citySlug]/[nameSlug]/beachPage.css";
import { BeachPageData } from "@/data/beachPageData";
import CopyRight from "../Common/CopyRight";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/swiper-bundle.min.css";
import { Pagination } from "swiper";
import CustomeImage from "../Common/CustomeImage";

const TabAddPhoto = ({ onClick }) => {
  return (
    <>
      <div
        className="flex flex-col items-center group cursor-pointer"
        onClick={onClick}
      >
        <div className="md:p-3 p-2 rounded-full border group-hover:border-primary-600 w-fit">
          <AddPhoto width={20} height={20} fill={"#00aae3"} />
        </div>
        <p className="2xl:text-sm text-[12px] mt-1 group-hover:text-primary-600 truncate">
          Add Photos
        </p>
      </div>
    </>
  );
};
const TabAddReview = ({ onClick }) => {
  return (
    <>
      <div
        className="flex flex-col items-center group cursor-pointer"
        onClick={onClick}
      >
        <div className="md:p-3 p-2 rounded-full border group-hover:border-primary-600 w-fit">
          <ReviewWriteIcon width={20} height={20} fill={"#00aae3"} />
        </div>
        <p className="2xl:text-sm text-[12px] mt-1 group-hover:text-primary-600 truncate">
          Write Review
        </p>
      </div>
    </>
  );
};

export function generateBreakpoints(minWidth, maxWidth, step, widthDivisor) {
  const breakpoints = {};
  for (let width = minWidth; width <= maxWidth; width += step) {
    breakpoints[width] = {
      slidesPerView: 1, // Ensure only 1 slide is visible at all screen sizes
    };
  }
  return breakpoints;
}

const SingleBeachShowModal = ({ data, mode }) => {
  const [beachLink, setBeachLink] = useState("");
  data.lat = data?.GeoLoc?.coordinates?.length
    ? data?.GeoLoc?.coordinates?.[1]
    : 0;
  data.lon = data?.GeoLoc?.coordinates?.length
    ? data?.GeoLoc?.coordinates?.[0]
    : 0;
  useEffect(() => {
    if (data?.city?.state?.country?.slug) {
      setBeachLink(
        `/${data?.city?.state?.country?.slug}/${data?.city?.state?.slug}/${data?.city?.slug}/${data?.nameSlug}`
      );
    } else if (data?.country?.slug) {
      setBeachLink(
        `/${data?.country?.slug}/${data?.state?.slug}/${data?.city?.slug}/${data?.nameSlug}`
      );
    } else {
      setBeachLink(
        `/${data?.countrySlug}/${data?.stateSlug}/${data?.citySlug}/${data?.nameSlug}`
      );
    }
  }, [data]);

  const icons = [
    {
      id: 1,
      component: (
        <>
          <Link
            className={`flex ${data?.address ? "" : "hidden"} flex-col items-center group cursor-pointer`}
            href={`https://maps.google.com/?q=${data?.address}`}
            target="_blank"
            key={1}
          >
            <div className="md:p-3 p-2 rounded-full border group-hover:border-primary-600 w-fit ">
              <LocationDirectionIcon width={22} height={22} fill={"#00aae3"} />
            </div>
            <p className="2xl:text-sm text-[12px] mt-1 group-hover:text-primary-600 truncate">
              Directions
            </p>
          </Link>
        </>
      ),
    },
    {
      id: 2,
      component: (
        <>
          <Link
            className="flex flex-col items-center group cursor-pointer"
            href={beachLink}
            target="_blank"
            key={2}
          >
            <div className="md:p-3 p-2 rounded-full border group-hover:border-primary-600 w-fit">
              <EyeIcon width={20} height={20} stroke={"#00aae3"} />
            </div>
            <p className="2xl:text-sm text-[12px] mt-1 group-hover:text-primary-600 truncate">
              View Beach
            </p>
          </Link>
        </>
      ),
    },
    {
      id: 3,
      component: (
        <>
          <AuthWrapper
            key={3}
            WithoutLogIn={
              <>
                <div className="flex flex-col items-center group cursor-pointer">
                  <div className="md:p-3 p-2 rounded-full border group-hover:border-primary-600 w-fit">
                    <ReviewWriteIcon width={20} height={20} fill={"#00aae3"} />
                  </div>
                  <p className="2xl:text-sm text-[12px] mt-1 group-hover:text-primary-600 truncate">
                    Write Review
                  </p>
                </div>
              </>
            }
            WithLogIn={
              <BeachReviewModal beachData={data} Other={TabAddReview} />
            }
          />
        </>
      ),
    },
    {
      id: 4,
      component: (
        <>
          <AuthWrapper
            key={4}
            WithoutLogIn={
              <>
                <div className="flex flex-col items-center group cursor-pointer">
                  <div className="md:p-3 p-2 rounded-full border group-hover:border-primary-600 w-fit">
                    <AddPhoto width={20} height={20} fill={"#00aae3"} />
                  </div>
                  <p className="2xl:text-sm text-[12px] mt-1 group-hover:text-primary-600 truncate">
                    Add Photos
                  </p>
                </div>
              </>
            }
            WithLogIn={
              <BeachPhotoUploadModal beachData={data} Other={TabAddPhoto} />
            }
          />
        </>
      ),
    },
  ];

  const BasicArray = [
    {
      IconKey: "phoneBlue",
      SVG: `https://images.sandee.com/Icons/phone.svg`,
      detail: data?.beachBasicDetail?.phoneNumber,
      display: data?.beachBasicDetail?.phoneNumber,
    },
    {
      IconKey: "rulerBlue",
      SVG: `https://images.sandee.com/Icons/ruler.svg`,
      detail: `${data?.beachBasicDetail?.size} ${data?.beachBasicDetail?.unit} `,
      display: data?.beachBasicDetail?.size && data?.beachBasicDetail?.unit,
    },
    {
      IconKey: "offerBlue",
      detail: "Entry Fee",
      SVG: `https://images.sandee.com/Icons/offer.svg`,
      display: data?.beachCategory?.restaurant,
    },
    {
      IconKey: "toiletBlue",
      detail: "Restroom Available",
      SVG: `https://images.sandee.com/Icons/toilet.svg`,
      display: data?.beachCategory?.restRooms,
    },
    {
      IconKey: "clockBlue",
      SVG: `https://images.sandee.com/Icons/clock.svg`,
      detail: data?.beachBasicDetail?.hours,
      display: data?.beachBasicDetail?.hours,
    },

    {
      IconKey: "sandBlue",
      SVG: `https://images.sandee.com/Icons/sand.svg`,
      detail: data?.beachBasicDetail?.sandColor?.join(",") || "",
      display: data?.beachBasicDetail?.sandColor?.join(","),
    },

    {
      detail: "Parking",
      IconKey: "parkingBlue",
      SVG: `https://images.sandee.com/Icons/parking.svg`,
      display: data?.beachBasicDetail?.parking,
    },

    // {
    //   IconKey: "shower",
    //   SVG: `https://images.sandee.com/Icons/shower.svg`,
    //   detail: data?.beachBasicDetail?.shower || "Shower",
    //   display: data?.beachBasicDetail?.shower,
    // },

    {
      IconKey: "pinBlue",
      SVG: `https://images.sandee.com/Icons/pin.svg`,
      detail: data?.address,
      display: data?.address,
      link: `https://maps.google.com/?q=${data?.address}`,
    },
  ];
  const DATA = BeachPageData?.beachInformation?.map((el) => ({
    title: el?.title,
    items: el?.items,
    display:
      el?.items.filter((elp) => data?.beachCategory?.[elp?.id])?.length !== 0,
  }));

  return (
    <section className="pt-1 pb-4 md:ps-1">
      {/* -mt-0.5    */}
      <Link
        className="flex justify-end mb-3 cursor-pointer absolute top-1 right-1.5"
        href={mode == "nude" ? "/maps/nude-beaches" : "/map"}
      >
        <CancelIcon
          width={16}
          height={16}
          fill={"#00aae3"}
          strokeWidth={12}
          className="border-2 border-primary-600 rounded-full transition-transform duration-300 transform hover:scale-105"
        />
      </Link>
      <Swiper
        modules={[Pagination]}
        pagination={{
          clickable: true,
          dynamicBullets: true,
          dynamicMainBullets: 20,
        }}
        spaceBetween={50}
        slidesPerView={1}
      // onSlideChange={() => console.log("Slide change")}
      // onSwiper={(swiper) => console.log(swiper)}
      >
        {data?.images?.length ? (
          data?.images?.map((image, index) => {
            return (
              <SwiperSlide key={index + 1}>
                <div className="w-auto aspect-video relative rounded-2xl">
                  <CustomeImage
                    src={FinalImageGenerator(image)}
                    fill
                    className="object-cover rounded-2xl"
                    alt={altText(image)}
                  />
                  <CopyRight
                    copyRightsData={data?.images}
                    // background={true}
                    classNameExtra={"bottom-0 left-1"}
                  />
                </div>
              </SwiperSlide>
            );
          })
        ) : (
          <iframe
            className=" bg-gray-200 w-full h-full rounded-sandee aspect-video pointer-events-none"
            loading="lazy"
            src={`https://maps.google.com/?q=${data?.lat},${data?.lon}&ie=UTF8&iwloc=&output=embed&t=k&disableDefaultUI=true`}
          ></iframe>
        )}
      </Swiper>
      {/* <div className="relative  overflow-hidden  w-full h-[250px] rounded-3xl">
        {FinalImageGenerator(data?.images?.[0]) &&
        FinalImageGenerator(data?.images?.[0]) !== defaultImage ? (
          <div>
            <Image
              className=""
              src={FinalImageGenerator(data?.images?.[0])}
              alt={`${
                data?.location ? `Sandee ${data?.name} Photo` : `${data?.name}`
              }`}
              fill
              onError={(event) => {
                event.target.src =
                  "https://images.sandee.com/images/header/Default-Header.avif";
                event.target.alt = "Image Not Found";
              }}
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              blurDataURL={blurDataURL(300, 200)}
              placeholder="blur"
            />
            <CopyRight
              copyRightsData={data?.images}
              background={true}
            />
          </div>
        ) : (
          <iframe
            className=" bg-gray-200 w-full h-full rounded-sandee min-h-[280px] pointer-events-none"
            loading="lazy"
            src={`https://maps.google.com/?q=${data?.lat},${data?.lon}&ie=UTF8&iwloc=&output=embed&t=k&disableDefaultUI=true`}
          ></iframe>
        )}
      </div> */}
      <div className="md:px-3 px-1">
        <div className="py-5">
          <p className="font-semibold text-2xl line-clamp-2" title={data?.name}>
            {data?.name}
          </p>
          {/* <div className="flex gap-x-2 items-center my-1">
            <span className="">{(data?.rating100 ?? 80) / 20}</span>
            <ReadOnlyRating
              rating={(data?.rating100 ?? 80) / 20}
              width="16"
              height="16"
            />
          </div> */}
        </div>

        <div className="flex justify-between items-center">
          {icons?.map((i, index) => {
            return i?.component;
          })}
        </div>
        {data?.beachDescription?.introduction && (
          <div className="mt-5 ">
            <p className="text-xl  font-medium mb-2">Overview</p>
            <EditorContent className="text-black text-sm mt-2 beachDescription" value={data?.beachDescription?.introduction} />
            {/* <div
              className="text-black text-sm mt-2 beachDescription"
              dangerouslySetInnerHTML={{
                __html: data?.beachDescription?.introduction,
              }}
            ></div> */}
          </div>
        )}
        {/* <p className="text-xl font-medium my-4">Basic Details</p>
        <div className="text-sm flex flex-col gap-y-5 mt-3 mb-5">
          {BasicArray.filter((data) => data?.display).map((data) => (
            <div key={data?.IconKey} className={`${data?.link ? "" : ""}`}>
              <div className="capitalize flex items-start gap-3 font-normal text-black">
                <Image
                  src={`/static/icons/BasicDetails/${data?.IconKey}.svg`}
                  width={20}
                  height={20}
                  alt="IN"
                  // className="p-1"
                />
                {data?.link ? (
                  <span className="w-5/6 ">
                    <Link
                      target="_blank"
                      href={data?.link}
                      className=" hover:underline"
                    >
                      {data?.detail}
                    </Link>
                  </span>
                ) : (
                  <span className="w-5/6 line-clamp-1 text-start leading-snug">
                    {data?.detail}
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>
        {DATA?.map((el) => el?.display).some((el) => el === true) ? (
          <>
            <p className="text-xl  font-medium my-3">{`All Amenities at ${data?.name}`}</p>
            <div className=" grid grid-cols-1 gap-3">
              {DATA?.map(({ title, items, display }) => (
                <div
                  key={title}
                  className={`${
                    !display
                      ? "hidden list-should-hide "
                      : "flex flex-col gap-3"
                  }`}
                >
                  <>
                    <p
                      className="text-black text-[18px] font-medium"
                      id={title}
                    >
                      {title}
                    </p>
                    {items?.map((link) => {
                      return (
                        <li
                          key={link?.id}
                          className={` list-none ${
                            !data?.beachCategory?.[link?.id]
                              ? "hidden list-should-hide "
                              : ""
                          }`}
                        >
                          <p className="text-sm capitalize m-0 gap-3 text-wrap flex text-ellipsis overflow-hidden  items-center py-0  font-normal transition-colors   text-black leading-snug">
                            <span className="w-[20px]">
                              {data?.beachCategory?.[link?.id] ? (
                                link?.icon ? (
                                  <Image
                                    src={`/static/icons/BeachAmenities/${link?.id}Blue.svg`}
                                    width={20}
                                    height={20}
                                    alt="Icon for Beach Amenities"
                                  />
                                ) : (
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width={20}
                                    height={20}
                                    viewBox="0 0 16 16"
                                    fill="none"
                                    className=" p-[2px]"
                                  >
                                    <path
                                      d="M15.75 8C15.75 12.2802 12.2802 15.75 8 15.75C3.71978 15.75 0.25 12.2802 0.25 8C0.25 3.71978 3.71978 0.25 8 0.25C12.2802 0.25 15.75 3.71978 15.75 8ZM7.10356 12.1036L12.8536 6.35356C13.0488 6.15831 13.0488 5.84172 12.8536 5.64647L12.1465 4.93937C11.9512 4.74409 11.6346 4.74409 11.4393 4.93937L6.75 9.62869L4.56066 7.43934C4.36541 7.24409 4.04881 7.24409 3.85353 7.43934L3.14644 8.14644C2.95119 8.34169 2.95119 8.65828 3.14644 8.85353L6.39644 12.1035C6.59172 12.2988 6.90828 12.2988 7.10356 12.1036Z"
                                      fill="#00AAE3"
                                    />
                                  </svg>
                                )
                              ) : (
                                <svg
                                  fill="#4a4a4a"
                                  width={20}
                                  height={20}
                                  version="1.1"
                                  id="Layer_1"
                                  xmlns="http://www.w3.org/2000/svg"
                                  xmlnsXlink="http://www.w3.org/1999/xlink"
                                  viewBox="0 0 300.003 300.003"
                                  xmlSpace="preserve"
                                  stroke="#4a4a4a"
                                  className=" p-[2px]"
                                >
                                  <g id="SVGRepo_bgCarrier" strokeWidth={0} />
                                  <g
                                    id="SVGRepo_tracerCarrier"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                  />
                                  <g id="SVGRepo_iconCarrier">
                                    <g>
                                      <g>
                                        <path d="M150,0C67.159,0,0.001,67.159,0.001,150c0,82.838,67.157,150.003,149.997,150.003S300.002,232.838,300.002,150 C300.002,67.159,232.839,0,150,0z M206.584,207.171c-5.989,5.984-15.691,5.984-21.675,0l-34.132-34.132l-35.686,35.686 c-5.986,5.984-15.689,5.984-21.672,0c-5.989-5.991-5.989-15.691,0-21.68l35.683-35.683L95.878,118.14 c-5.984-5.991-5.984-15.691,0-21.678c5.986-5.986,15.691-5.986,21.678,0l33.222,33.222l31.671-31.673 c5.986-5.984,15.694-5.986,21.675,0c5.989,5.991,5.989,15.697,0,21.678l-31.668,31.671l34.13,34.132 C212.57,191.475,212.573,201.183,206.584,207.171z" />{" "}
                                      </g>
                                    </g>
                                  </g>
                                </svg>
                              )}
                            </span>

                            {link?.label}
                          </p>
                        </li>
                      );
                    })}
                  </>
                </div>
              ))}
            </div>
          </>
        ) : null} */}
      </div>
    </section>
  );
};

export default memo(SingleBeachShowModal);
