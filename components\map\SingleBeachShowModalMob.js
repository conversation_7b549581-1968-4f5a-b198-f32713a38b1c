"use client";
import {
  <PERSON><PERSON>ontent,
  FinalImageGenerator,
  altText,
  blurDataURL,
  defaultImage,
  isMobileView,
} from "@/helper/functions";
import Image from "next/image";
import React, { memo, useEffect, useState } from "react";
import ReadOnlyRating from "../Common/ReadOnlyRating";
import {
  AddPhoto,
  CancelIcon,
  EyeIcon,
  LocationDirectionIcon,
  ReviewWriteIcon,
} from "../social-icons/icons";
import Link from "next/link";
import AuthWrapper from "../Common/AuthWrapper";
import BeachPhotoUploadModal from "../BeachPage/BeachPhotoUploadModal";
import BeachReviewModal from "../BeachPage/BeachReviewModal";
import "../../app/(HeaderSlim)/(Single Beach Page)/[countrySlug]/[stateSlug]/[citySlug]/[nameSlug]/beachPage.css";
import { BeachPageData } from "@/data/beachPageData";
import CopyRight from "../Common/CopyRight";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/swiper-bundle.min.css";
import { Pagination } from "swiper";
import { Button, Card, Col, ConfigProvider, Divider, Drawer, Row, Typography } from "antd";
import CustomeImage from "../Common/CustomeImage";

const TabAddPhoto = ({ onClick }) => {
  return (
    <>
      <div
        className="flex flex-col items-center group cursor-pointer"
        onClick={onClick}
      >
        <div className="md:p-3 p-2 rounded-full border group-hover:border-primary-600 w-fit">
          <AddPhoto width={20} height={20} fill={"#00aae3"} />
        </div>
        <p className="2xl:text-sm text-[12px] mt-1 group-hover:text-primary-600 truncate">
          Add Photos
        </p>
      </div>
    </>
  );
};
const TabAddReview = ({ onClick }) => {
  return (
    <>
      <div
        className="flex flex-col items-center group cursor-pointer"
        onClick={onClick}
      >
        <div className="md:p-3 p-2 rounded-full border group-hover:border-primary-600 w-fit">
          <ReviewWriteIcon width={20} height={20} fill={"#00aae3"} />
        </div>
        <p className="2xl:text-sm text-[12px] mt-1 group-hover:text-primary-600 truncate">
          Write Review
        </p>
      </div>
    </>
  );
};

export function generateBreakpoints(minWidth, maxWidth, step, widthDivisor) {
  const breakpoints = {};
  for (let width = minWidth; width <= maxWidth; width += step) {
    breakpoints[width] = {
      slidesPerView: 1, // Ensure only 1 slide is visible at all screen sizes
    };
  }
  return breakpoints;
}

const SingleBeachShowModalMob = ({ data, mode, openDrawer = false }) => {
  const [beachLink, setBeachLink] = useState("");
  data.lat = data?.GeoLoc?.coordinates?.length
    ? data?.GeoLoc?.coordinates?.[1]
    : 0;
  data.lon = data?.GeoLoc?.coordinates?.length
    ? data?.GeoLoc?.coordinates?.[0]
    : 0;
  let isMobile = isMobileView();

  useEffect(() => {
    if (data?.city?.state?.country?.slug) {
      setBeachLink(
        `/${data?.city?.state?.country?.slug}/${data?.city?.state?.slug}/${data?.city?.slug}/${data?.nameSlug}`
      );
    } else if (data?.country?.slug) {
      setBeachLink(
        `/${data?.country?.slug}/${data?.state?.slug}/${data?.city?.slug}/${data?.nameSlug}`
      );
    } else {
      setBeachLink(
        `/${data?.countrySlug}/${data?.stateSlug}/${data?.citySlug}/${data?.nameSlug}`
      );
    }
  }, [data]);

  const icons = [
    {
      id: 1,
      component: (
        <>
          <Link
            className={`flex ${data?.address ? "" : "hidden"} flex-col items-center group cursor-pointer`}
            href={`https://maps.google.com/?q=${data?.address}`}
            target="_blank"
            key={1}
          >
            <div className="md:p-3 p-2 rounded-full border group-hover:border-primary-600 w-fit ">
              <LocationDirectionIcon width={22} height={22} fill={"#00aae3"} />
            </div>
            <p className="2xl:text-sm text-[12px] mt-1 group-hover:text-primary-600 truncate">
              Directions
            </p>
          </Link>
        </>
      ),
    },
    {
      id: 2,
      component: (
        <>
          <Link
            className="flex flex-col items-center group cursor-pointer"
            href={beachLink}
            target="_blank"
            key={2}
          >
            <div className="md:p-3 p-2 rounded-full border group-hover:border-primary-600 w-fit">
              <EyeIcon width={20} height={20} stroke={"#00aae3"} />
            </div>
            <p className="2xl:text-sm text-[12px] mt-1 group-hover:text-primary-600 truncate">
              View Beach
            </p>
          </Link>
        </>
      ),
    },
    {
      id: 3,
      component: (
        <>
          <AuthWrapper
            key={3}
            WithoutLogIn={
              <>
                <div className="flex flex-col items-center group cursor-pointer">
                  <div className="md:p-3 p-2 rounded-full border group-hover:border-primary-600 w-fit">
                    <ReviewWriteIcon width={20} height={20} fill={"#00aae3"} />
                  </div>
                  <p className="2xl:text-sm text-[12px] mt-1 group-hover:text-primary-600 truncate">
                    Write Review
                  </p>
                </div>
              </>
            }
            WithLogIn={
              <BeachReviewModal beachData={data} Other={TabAddReview} />
            }
          />
        </>
      ),
    },
    {
      id: 4,
      component: (
        <>
          <AuthWrapper
            key={4}
            WithoutLogIn={
              <>
                <div className="flex flex-col items-center group cursor-pointer">
                  <div className="md:p-3 p-2 rounded-full border group-hover:border-primary-600 w-fit">
                    <AddPhoto width={20} height={20} fill={"#00aae3"} />
                  </div>
                  <p className="2xl:text-sm text-[12px] mt-1 group-hover:text-primary-600 truncate">
                    Add Photos
                  </p>
                </div>
              </>
            }
            WithLogIn={
              <BeachPhotoUploadModal beachData={data} Other={TabAddPhoto} />
            }
          />
        </>
      ),
    },
  ];

  const BasicArray = [
    {
      IconKey: "phoneBlue",
      SVG: `https://images.sandee.com/Icons/phone.svg`,
      detail: data?.beachBasicDetail?.phoneNumber,
      display: data?.beachBasicDetail?.phoneNumber,
    },
    {
      IconKey: "rulerBlue",
      SVG: `https://images.sandee.com/Icons/ruler.svg`,
      detail: `${data?.beachBasicDetail?.size} ${data?.beachBasicDetail?.unit} `,
      display: data?.beachBasicDetail?.size && data?.beachBasicDetail?.unit,
    },
    {
      IconKey: "offerBlue",
      detail: "Entry Fee",
      SVG: `https://images.sandee.com/Icons/offer.svg`,
      display: data?.beachCategory?.restaurant,
    },
    {
      IconKey: "toiletBlue",
      detail: "Restroom Available",
      SVG: `https://images.sandee.com/Icons/toilet.svg`,
      display: data?.beachCategory?.restRooms,
    },
    {
      IconKey: "clockBlue",
      SVG: `https://images.sandee.com/Icons/clock.svg`,
      detail: data?.beachBasicDetail?.hours,
      display: data?.beachBasicDetail?.hours,
    },

    {
      IconKey: "sandBlue",
      SVG: `https://images.sandee.com/Icons/sand.svg`,
      detail: data?.beachBasicDetail?.sandColor?.join(",") || "",
      display: data?.beachBasicDetail?.sandColor?.join(","),
    },

    {
      detail: "Parking",
      IconKey: "parkingBlue",
      SVG: `https://images.sandee.com/Icons/parking.svg`,
      display: data?.beachBasicDetail?.parking,
    },
    {
      IconKey: "pinBlue",
      SVG: `https://images.sandee.com/Icons/pin.svg`,
      detail: data?.address,
      display: data?.address,
      link: `https://maps.google.com/?q=${data?.address}`,
    },
  ];
  const DATA = BeachPageData?.beachInformation?.map((el) => ({
    title: el?.title,
    items: el?.items,
    display:
      el?.items.filter((elp) => data?.beachCategory?.[elp?.id])?.length !== 0,
  }));

  return (
    <>
      {/* <section className="pt-1 pb-4 md:ps-1">
      
      <Link
        className="flex justify-end mb-3 cursor-pointer absolute top-1 right-1.5"
        href={mode == "nude" ? "/maps/nude-beaches" : "/map"}
      >
        <CancelIcon
          width={16}
          height={16}
          fill={"#00aae3"}
          strokeWidth={12}
          className="border-2 border-primary-600 rounded-full transition-transform duration-300 transform hover:scale-105"
        />
      </Link>
      <Swiper
        modules={[Pagination]}
        pagination={{
          clickable: true,
          dynamicBullets: true,
          dynamicMainBullets: 20,
        }}
        spaceBetween={50}
        slidesPerView={1}
      // onSlideChange={() => console.log("Slide change")}
      // onSwiper={(swiper) => console.log(swiper)}
      >
        {data?.images?.length ? (
          data?.images?.map((image, index) => {
            return (
              <SwiperSlide key={index + 1}>
                <div className="w-auto aspect-video relative rounded-2xl">
                  <Image
                    src={FinalImageGenerator(image)}
                    fill
                    className="object-cover rounded-2xl"
                    alt={altText(image)}
                  />
                  <CopyRight
                    copyRightsData={data?.images}
                    // background={true}
                    classNameExtra={"bottom-0 left-1"}
                  />
                </div>
              </SwiperSlide>
            );
          })
        ) : (
          <iframe
            className=" bg-gray-200 w-full h-full rounded-sandee aspect-video pointer-events-none"
            loading="lazy"
            src={`https://maps.google.com/?q=${data?.lat},${data?.lon}&ie=UTF8&iwloc=&output=embed&t=k&disableDefaultUI=true`}
          ></iframe>
        )}
      </Swiper>
      <div className="md:px-3 px-1">
        <div className="py-5">
          <p className="font-semibold text-2xl line-clamp-2" title={data?.name}>
            {data?.name}
          </p>
        </div>
        <div className="flex justify-between items-center">
          {icons?.map((i, index) => {
            return i?.component;
          })}
        </div>
        {data?.beachDescription?.introduction && (
          <div className="mt-5 ">
            <p className="text-xl  font-medium mb-2">Overview</p>
            <EditorContent 
            className="text-black text-sm mt-2 beachDescription" 
            value={data?.beachDescription?.introduction} />
          </div>
        )}
      </div>
    </section> */}
      <ConfigProvider
        theme={{
          token: {
            colorPrimary: "#00aae3",
            colorBgBase: "#ffffff",
          },
          components: {
            Switch: {
              colorTextQuaternary: "rgba(0, 0, 0, 0.28)",
              colorTextTertiary: "rgba(0, 0, 0, 0.46)",
            },
          },
        }}
      >
        <Drawer
          onClose={() => { }}

          open={openDrawer}
          placement={isMobile ? "bottom" : "left"}
          className="filter-select-category beach-detail-map rounded-t-3xl"
          closable={false}
        >

          <Link
            className="flex justify-end mb-3 cursor-pointer absolute top-5 right-5"
            href={mode == "nude" ? "/maps/nude-beaches" : "/map"}
          >
            <CancelIcon
              width={16}
              height={16}
              fill={"#00aae3"}
              strokeWidth={12}
              className="border-2 border-primary-600 rounded-full transition-transform duration-300 transform hover:scale-105"
            />
          </Link>
          <Swiper
            modules={[Pagination]}
            pagination={{
              clickable: true,
              dynamicBullets: true,
              dynamicMainBullets: 20,
            }}
            spaceBetween={50}
            slidesPerView={1}
          // onSlideChange={() => console.log("Slide change")}
          // onSwiper={(swiper) => console.log(swiper)}
          >
            {data?.images?.length ? (
              data?.images?.map((image, index) => {
                return (
                  <SwiperSlide key={index + 1}>
                    <div className="w-auto aspect-video relative rounded-2xl">
                      <CustomeImage
                        src={FinalImageGenerator(image)}
                        fill
                        className="object-cover rounded-2xl"
                        alt={altText(image)}
                      />
                      <CopyRight
                        copyRightsData={data?.images}
                        // background={true}
                        classNameExtra={"bottom-0 left-1"}
                      />
                    </div>
                  </SwiperSlide>
                );
              })
            ) : (
              <iframe
                className=" bg-gray-200 w-full h-full rounded-sandee aspect-video pointer-events-none"
                loading="lazy"
                src={`https://maps.google.com/?q=${data?.lat},${data?.lon}&ie=UTF8&iwloc=&output=embed&t=k&disableDefaultUI=true`}
              ></iframe>
            )}
          </Swiper>
          <div className="md:px-3 px-1">
            <div className="py-5">
              <p className="font-semibold text-2xl line-clamp-2" title={data?.name}>
                {data?.name}
              </p>
            </div>
            <div className="flex justify-between items-center">
              {icons?.map((i, index) => {
                return i?.component;
              })}
            </div>
            {data?.beachDescription?.introduction && (
              <div className="mt-5 ">
                <p className="text-xl  font-medium mb-2">Overview</p>
                <EditorContent
                  className="text-black text-sm mt-2 beachDescription"
                  value={data?.beachDescription?.introduction} />
              </div>
            )}
          </div>
          {/* </Row> */}
        </Drawer>
      </ConfigProvider>

    </>
  );
};

export default memo(SingleBeachShowModalMob);
