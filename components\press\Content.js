import React from "react";
import { CustomContainer } from "../Custom-Display";
import NameTitle from "../Common/NameTitle";

const PressContent = () => {
  return (
    <CustomContainer>
      <div style={{ textAlign: "justify" }}>
        <NameTitle name={`Helping others navigate the world's beaches`} />
        <p style={{ lineHeight: "1.2" }} dir="ltr">
          <span
            style={{
              backgroundColor: "transparent",
              color: "#000000",

              fontSize: "12pt",
            }}
          >
            <span
              style={{
                fontStyle: "normal",
                fontVariant: "normal",
                fontWeight: 400,
                textDecoration: "none",
                verticalAlign: "baseline",
                whiteSpace: "pre-wrap",
              }}
            >
              <PERSON><PERSON> is committed to delivering the best beach data on the
              planet. Over 9 years, we have collected the largest, most
              authoritative, most comprehensive, and most trusted website for
              beaches around the world &ndash; more than 100,000 beaches in 212
              countries.
            </span>
          </span>
        </p>
        <ul style={{ marginBottom: 0, marginTop: 10, paddingInlineStart: 48 }}>
          <li
            style={{
              backgroundColor: "transparent",
              color: "#000000",

              fontSize: "12pt",
              fontStyle: "normal",
              fontVariant: "normal",
              fontWeight: 400,
              listStyleType: "disc",
              textDecoration: "none",
              verticalAlign: "baseline",
              whiteSpace: "pre",
            }}
            dir="ltr"
            aria-level={1}
          >
            <span className="ck-list-bogus-paragraph">
              <span
                style={{
                  backgroundColor: "transparent",
                  color: "#000000",

                  fontSize: "12pt",
                }}
              >
                <span
                  style={{
                    fontStyle: "normal",
                    fontVariant: "normal",
                    fontWeight: 400,
                    textDecoration: "none",
                    verticalAlign: "baseline",
                    whiteSpace: "pre-wrap",
                  }}
                >
                  Unrivaled Beach Data: Sandee.com boasts the most extensive
                  collection of beach information worldwide, spanning over
                  100,000 beaches in 212 countries.
                </span>
              </span>
            </span>
          </li>
          <li
            style={{
              backgroundColor: "transparent",
              color: "#000000",

              fontSize: "12pt",
              fontStyle: "normal",
              fontVariant: "normal",
              fontWeight: 400,
              listStyleType: "disc",
              textDecoration: "none",
              verticalAlign: "baseline",
              whiteSpace: "pre",
            }}
            dir="ltr"
            aria-level={1}
          >
            <span className="ck-list-bogus-paragraph">
              <span
                style={{
                  backgroundColor: "transparent",
                  color: "#000000",

                  fontSize: "12pt",
                }}
              >
                <span
                  style={{
                    fontStyle: "normal",
                    fontVariant: "normal",
                    fontWeight: 400,
                    textDecoration: "none",
                    verticalAlign: "baseline",
                    whiteSpace: "pre-wrap",
                  }}
                >
                  Reliable and Authoritative: With 9 years of experience, Sandee
                  has become the go-to source for accurate and authoritative
                  beach information. Journalists and travelers alike trust our
                  data for its reliability.
                </span>
              </span>
            </span>
          </li>
          <li
            style={{
              backgroundColor: "transparent",
              color: "#000000",

              fontSize: "12pt",
              fontStyle: "normal",
              fontVariant: "normal",
              fontWeight: 400,
              listStyleType: "disc",
              textDecoration: "none",
              verticalAlign: "baseline",
              whiteSpace: "pre",
            }}
            dir="ltr"
            aria-level={1}
          >
            <span className="ck-list-bogus-paragraph">
              <span
                style={{
                  backgroundColor: "transparent",
                  color: "#000000",

                  fontSize: "12pt",
                }}
              >
                <span
                  style={{
                    fontStyle: "normal",
                    fontVariant: "normal",
                    fontWeight: 400,
                    textDecoration: "none",
                    verticalAlign: "baseline",
                    whiteSpace: "pre-wrap",
                  }}
                >
                  Global Coverage: Sandee.com offers comprehensive coverage,
                  ensuring that no matter where you are, you can access detailed
                  beach information, making Sandee an ideal resource for
                  international press coverage.
                </span>
              </span>
            </span>
          </li>
          <li
            style={{
              backgroundColor: "transparent",
              color: "#000000",

              fontSize: "12pt",
              fontStyle: "normal",
              fontVariant: "normal",
              fontWeight: 400,
              listStyleType: "disc",
              textDecoration: "none",
              verticalAlign: "baseline",
              whiteSpace: "pre",
            }}
            dir="ltr"
            aria-level={1}
          >
            <span className="ck-list-bogus-paragraph">
              <span
                style={{
                  backgroundColor: "transparent",
                  color: "#000000",

                  fontSize: "12pt",
                }}
              >
                <span
                  style={{
                    fontStyle: "normal",
                    fontVariant: "normal",
                    fontWeight: 400,
                    textDecoration: "none",
                    verticalAlign: "baseline",
                    whiteSpace: "pre-wrap",
                  }}
                >
                  Supporting Environmental Initiatives: Sandee is committed to
                  supporting environmental initiatives and maintaining healthy
                  beach and ocean ecosystems to preserve marine life for future
                  generations!
                </span>
              </span>
            </span>
          </li>
        </ul>
        <p>
          <br data-cke-filler="true" />
        </p>
      </div>
    </CustomContainer>
  );
};

export default PressContent;
