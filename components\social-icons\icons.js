export function Facebook(svgProps) {
  return (
    <svg
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        d="M8.72444 19.1192H12.1674V12.2247H15.2695L15.6103 8.79891H12.1674V7.06883C12.1674 6.84055 12.2581 6.62161 12.4195 6.4602C12.5809 6.29878 12.7998 6.20809 13.0281 6.20809H15.6103V2.76514H13.0281C11.8867 2.76514 10.7921 3.21856 9.98496 4.02566C9.17786 4.83276 8.72444 5.92742 8.72444 7.06883V8.79891H7.00296L6.66211 12.2247H8.72444V19.1192Z"
      // fill="#F4F4F4"
      />
    </svg>
  );
}

export function Github(svgProps) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" {...svgProps}>
      <path d="M12 .297c-6.63 0-12 5.373-12 12 0 5.303 3.438 9.8 8.205 11.385.6.113.82-.258.82-.577 0-.285-.01-1.04-.015-2.04-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.744.084-.729.084-.729 1.205.084 1.838 1.236 1.838 1.236 1.07 1.835 2.809 1.305 3.495.998.108-.776.417-1.305.76-1.605-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.465-2.38 1.235-3.22-.135-.303-.54-1.523.105-3.176 0 0 1.005-.322 3.3 1.23.96-.267 1.98-.399 3-.405 1.02.006 2.04.138 3 .405 2.28-1.552 3.285-1.23 3.285-1.23.645 1.653.24 2.873.12 3.176.765.84 1.23 1.91 1.23 3.22 0 4.61-2.805 5.625-5.475 5.92.42.36.81 1.096.81 2.22 0 1.606-.015 2.896-.015 3.286 0 .315.21.69.825.57C20.565 22.092 24 17.592 24 12.297c0-6.627-5.373-12-12-12"></path>
    </svg>
  );
}

export function Linkedin(svgProps) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" {...svgProps}>
      <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433a2.062 2.062 0 01-2.063-2.065 2.064 2.064 0 112.063 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"></path>
    </svg>
  );
}

export function Mail(svgProps) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" {...svgProps}>
      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
    </svg>
  );
}

export function Twitter(svgProps) {
  return (
    <svg
      viewBox="0 0 18 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        d="M14.1227 0.95166H16.822L10.9247 7.69256L17.8629 16.8636H12.4306L8.17623 11.3007L3.30723 16.8636H0.60647L6.9146 9.65322L0.258789 0.952394H5.82902L9.67478 6.03704L14.1227 0.95166ZM13.1758 15.2484H14.6714L5.01629 2.48248H3.41139L13.1758 15.2484Z"
      // fill="#F4F4F4"
      />
    </svg>
  );
}
// <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" {...svgProps}>
//   <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"></path>
// </svg>;
export function Youtube(svgProps) {
  return (
    <svg
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        d="M9.26286 13.5246L13.7301 10.9424L9.26286 8.36019V13.5246ZM19.213 6.78504C19.3249 7.18959 19.4024 7.73185 19.454 8.42044C19.5143 9.10903 19.5401 9.70294 19.5401 10.2194L19.5917 10.9424C19.5917 12.8274 19.454 14.2132 19.213 15.0998C18.9978 15.8744 18.4986 16.3737 17.7239 16.5888C17.3194 16.7007 16.5791 16.7782 15.443 16.8299C14.324 16.8901 13.2997 16.9159 12.3529 16.9159L10.9843 16.9676C7.37784 16.9676 5.13132 16.8299 4.24476 16.5888C3.47009 16.3737 2.97086 15.8744 2.75568 15.0998C2.64378 14.6952 2.56632 14.153 2.51467 13.4644C2.45442 12.7758 2.4286 12.1819 2.4286 11.6654L2.37695 10.9424C2.37695 9.05739 2.51467 7.6716 2.75568 6.78504C2.97086 6.01037 3.47009 5.51115 4.24476 5.29596C4.6493 5.18407 5.38954 5.1066 6.52571 5.05495C7.64467 4.9947 8.66895 4.96888 9.61576 4.96888L10.9843 4.91724C14.5908 4.91724 16.8374 5.05495 17.7239 5.29596C18.4986 5.51115 18.9978 6.01037 19.213 6.78504Z"
      // fill="#F4F4F4"
      />
    </svg>
  );
}

// <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" {...svgProps}>
//   <path d="M23.499 6.203a3.008 3.008 0 00-2.089-2.089c-1.87-.501-9.4-.501-9.4-.501s-7.509-.01-9.399.501a3.008 3.008 0 00-2.088 2.09A31.258 31.26 0 000 12.01a31.258 31.26 0 00.523 5.785 3.008 3.008 0 002.088 2.089c1.869.502 9.4.502 9.4.502s7.508 0 9.399-.502a3.008 3.008 0 002.089-2.09 31.258 31.26 0 00.5-5.784 31.258 31.26 0 00-.5-5.808zm-13.891 9.4V8.407l6.266 3.604z"></path>
// </svg>
export function Mastodon(svgProps) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" {...svgProps}>
      <path d="M23.268 5.313c-.35-2.578-2.617-4.61-5.304-5.004C17.51.242 15.792 0 11.813 0h-.03c-3.98 0-4.835.242-5.288.309C3.882.692 1.496 2.518.917 5.127.64 6.412.61 7.837.661 9.143c.074 1.874.088 3.745.26 5.611.118 1.24.325 2.47.62 3.68.55 2.237 2.777 4.098 4.96 4.857 2.336.792 4.849.923 7.256.38.265-.061.527-.132.786-.213.585-.184 1.27-.39 1.774-.753a.057.057 0 0 0 .023-.043v-1.809a.052.052 0 0 0-.02-.041.053.053 0 0 0-.046-.01 20.282 20.282 0 0 1-4.709.545c-2.73 0-3.463-1.284-3.674-1.818a5.593 5.593 0 0 1-.319-1.433.053.053 0 0 1 .066-.054c1.517.363 3.072.546 4.632.546.376 0 .75 0 1.125-.01 1.57-.044 3.224-.124 4.768-.422.038-.008.077-.015.11-.024 2.435-.464 4.753-1.92 4.989-5.604.008-.145.03-1.52.03-1.67.002-.512.167-3.63-.024-5.545zm-3.748 9.195h-2.561V8.29c0-1.309-.55-1.976-1.67-1.976-1.23 0-1.846.79-1.846 2.35v3.403h-2.546V8.663c0-1.56-.617-2.35-1.848-2.35-1.112 0-1.668.668-1.67 1.977v6.218H4.822V8.102c0-1.31.337-2.35 1.011-3.12.696-.77 1.608-1.164 2.74-1.164 1.311 0 2.302.5 2.962 1.498l.638 1.06.638-1.06c.66-.999 1.65-1.498 2.96-1.498 1.13 0 2.043.395 2.74 1.164.675.77 1.012 1.81 1.012 3.12z" />
    </svg>
  );
}

export function Threads(svgProps) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" {...svgProps}>
      <path d="M12.186 24h-.007c-3.581-.024-6.334-1.205-8.184-3.509C2.35 18.44 1.5 15.586 1.472 12.01v-.017c.03-3.579.879-6.43 2.525-8.482C5.845 1.205 8.6.024 12.18 0h.014c2.746.02 5.043.725 6.826 2.098 1.677 1.29 2.858 3.13 3.509 5.467l-2.04.569c-1.104-3.96-3.898-5.984-8.304-6.015-2.91.022-5.11.936-6.54 2.717C4.307 6.504 3.616 8.914 3.589 12c.027 3.086.718 5.496 2.057 7.164 1.43 1.783 3.631 2.698 6.54 2.717 2.623-.02 4.358-.631 5.8-2.045 1.647-1.613 1.618-3.593 1.09-4.798-.31-.71-.873-1.3-1.634-1.75-.192 1.352-.622 2.446-1.284 3.272-.886 1.102-2.14 1.704-3.73 1.79-1.202.065-2.361-.218-3.259-.801-1.063-.689-1.685-1.74-1.752-2.964-.065-1.19.408-2.285 1.33-3.082.88-.76 2.119-1.207 3.583-1.291a13.853 13.853 0 0 1 3.02.142c-.126-.742-.375-1.332-.75-1.757-.513-.586-1.308-.883-2.359-.89h-.029c-.844 0-1.992.232-2.721 1.32L7.734 7.847c.98-1.454 2.568-2.256 4.478-2.256h.044c3.194.02 5.097 1.975 5.287 5.388.108.046.216.094.321.142 1.49.7 2.58 1.761 3.154 3.07.797 1.82.871 4.79-1.548 7.158-1.85 1.81-4.094 2.628-7.277 2.65Zm1.003-11.69c-.242 0-.487.007-.739.021-1.836.103-2.98.946-2.916 2.143.067 1.256 1.452 1.839 2.784 1.767 1.224-.065 2.818-.543 3.086-3.71a10.5 10.5 0 0 0-2.215-.221z" />
    </svg>
  );
}

export function Instagram(svgProps) {
  return (
    <svg
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        d="M12.1011 2.30029C13.0694 2.30288 13.5609 2.30804 13.9853 2.32009L14.1523 2.32612C14.3451 2.333 14.5353 2.34161 14.7651 2.35194C15.6809 2.39497 16.3058 2.53958 16.8541 2.75218C17.4222 2.97081 17.9008 3.2669 18.3793 3.74461C18.817 4.17489 19.1557 4.69537 19.3718 5.26984C19.5844 5.81813 19.729 6.44303 19.772 7.35971C19.7824 7.58867 19.791 7.77889 19.7978 7.97256L19.803 8.13954C19.8159 8.56303 19.8211 9.05451 19.8228 10.0228L19.8237 10.6649V11.7925C19.8258 12.4203 19.8192 13.0482 19.8039 13.6758L19.7987 13.8428C19.7918 14.0365 19.7832 14.2267 19.7729 14.4556C19.7298 15.3723 19.5835 15.9964 19.3718 16.5455C19.1563 17.1203 18.8176 17.6409 18.3793 18.0707C17.9489 18.5083 17.4285 18.8469 16.8541 19.0632C16.3058 19.2758 15.6809 19.4204 14.7651 19.4634C14.5609 19.473 14.3566 19.4816 14.1523 19.4892L13.9853 19.4944C13.5609 19.5065 13.0694 19.5125 12.1011 19.5142L11.459 19.5151H10.3323C9.70419 19.5172 9.07607 19.5106 8.44814 19.4953L8.28116 19.4901C8.07683 19.4824 7.87255 19.4735 7.66832 19.4634C6.75249 19.4204 6.12759 19.2758 5.57844 19.0632C5.00405 18.8475 4.48377 18.5087 4.05407 18.0707C3.616 17.6406 3.27702 17.1201 3.06078 16.5455C2.84818 15.9972 2.70358 15.3723 2.66054 14.4556C2.65095 14.2514 2.64234 14.0471 2.63472 13.8428L2.63041 13.6758C2.61455 13.0482 2.60738 12.4204 2.6089 11.7925V10.0228C2.60649 9.39502 2.61281 8.76719 2.62783 8.13954L2.63386 7.97256C2.64074 7.77889 2.64935 7.58867 2.65968 7.35971C2.70272 6.44303 2.84732 5.81899 3.05992 5.26984C3.27607 4.69478 3.61572 4.17414 4.05494 3.74461C4.48452 3.3069 5.00445 2.96821 5.57844 2.75218C6.12759 2.53958 6.75163 2.39497 7.66832 2.35194C7.89727 2.34161 8.08836 2.333 8.28116 2.32612L8.44814 2.32095C9.07579 2.30566 9.70361 2.29906 10.3314 2.30115L12.1011 2.30029ZM11.2163 6.60399C10.0749 6.60399 8.98021 7.05741 8.17311 7.86451C7.36601 8.67161 6.91259 9.76627 6.91259 10.9077C6.91259 12.0491 7.36601 13.1437 8.17311 13.9508C8.98021 14.7579 10.0749 15.2114 11.2163 15.2114C12.3577 15.2114 13.4524 14.7579 14.2594 13.9508C15.0665 13.1437 15.52 12.0491 15.52 10.9077C15.52 9.76627 15.0665 8.67161 14.2594 7.86451C13.4524 7.05741 12.3577 6.60399 11.2163 6.60399ZM11.2163 8.32546C11.5554 8.32541 11.8912 8.39214 12.2045 8.52186C12.5178 8.65157 12.8025 8.84173 13.0423 9.08147C13.2821 9.32121 13.4724 9.60584 13.6022 9.91911C13.732 10.2324 13.7989 10.5681 13.7989 10.9072C13.799 11.2463 13.7322 11.5821 13.6025 11.8955C13.4728 12.2088 13.2827 12.4935 13.0429 12.7333C12.8032 12.9731 12.5185 13.1633 12.2053 13.2932C11.892 13.423 11.5562 13.4898 11.2171 13.4899C10.5323 13.4899 9.8755 13.2178 9.39124 12.7336C8.90698 12.2493 8.63493 11.5925 8.63493 10.9077C8.63493 10.2228 8.90698 9.56603 9.39124 9.08178C9.8755 8.59752 10.5323 8.32546 11.2171 8.32546M15.736 5.31288C15.4507 5.31288 15.177 5.42623 14.9752 5.62801C14.7735 5.82978 14.6601 6.10345 14.6601 6.3888C14.6601 6.67415 14.7735 6.94782 14.9752 7.14959C15.177 7.35137 15.4507 7.46472 15.736 7.46472C16.0214 7.46472 16.295 7.35137 16.4968 7.14959C16.6986 6.94782 16.8119 6.67415 16.8119 6.3888C16.8119 6.10345 16.6986 5.82978 16.4968 5.62801C16.295 5.42623 16.0214 5.31288 15.736 5.31288Z"
      // fill="#F4F4F4"
      />
    </svg>
  );
}

export function NewTwitter(svgProps) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" {...svgProps}>
      <path d="M12 0C8.74 0 8.333.015 7.053.072 5.775.132 4.905.333 4.14.63c-.789.306-1.459.717-2.126 1.384S.935 3.35.63 4.14C.333 4.905.131 5.775.072 7.053.012 8.333 0 8.74 0 12s.015 3.667.072 4.947c.06 1.277.261 2.148.558 2.913.306.788.717 1.459 1.384 2.126.667.666 1.336 1.079 2.126 1.384.766.296 1.636.499 2.913.558C8.333 23.988 8.74 24 12 24s3.667-.015 4.947-.072c1.277-.06 2.148-.262 2.913-.558.788-.306 1.459-.718 2.126-1.384.666-.667 1.079-1.335 1.384-2.126.296-.765.499-1.636.558-2.913.06-1.28.072-1.687.072-4.947s-.015-3.667-.072-4.947c-.06-1.277-.262-2.149-.558-2.913-.306-.789-.718-1.459-1.384-2.126C21.319 1.347 20.651.935 19.86.63c-.765-.297-1.636-.499-2.913-.558C15.667.012 15.26 0 12 0zm0 2.16c3.203 0 3.585.016 4.85.071 1.17.055 1.805.249 2.227.415.562.217.96.477 1.382.896.419.42.679.819.896 1.381.164.422.36 1.057.413 2.227.057 1.266.07 1.646.07 4.85s-.015 3.585-.074 4.85c-.061 1.17-.256 1.805-.421 2.227-.224.562-.479.96-.899 1.382-.419.419-.824.679-1.38.896-.42.164-1.065.36-2.235.413-1.274.057-1.649.07-4.859.07-3.211 0-3.586-.015-4.859-.074-1.171-.061-1.816-.256-2.236-.421-.569-.224-.96-.479-1.379-.899-.421-.419-.69-.824-.9-1.38-.165-.42-.359-1.065-.42-2.235-.045-1.26-.061-1.649-.061-4.844 0-3.196.016-3.586.061-4.861.061-1.17.255-1.814.42-2.234.21-.57.479-.96.9-1.381.419-.419.81-.689 1.379-.898.42-.166 1.051-.361 2.221-.421 1.275-.045 1.65-.06 4.859-.06l.045.03zm0 3.678c-3.405 0-6.162 2.76-6.162 6.162 0 3.405 2.76 6.162 6.162 6.162 3.405 0 6.162-2.76 6.162-6.162 0-3.405-2.76-6.162-6.162-6.162zM12 16c-2.21 0-4-1.79-4-4s1.79-4 4-4 4 1.79 4 4-1.79 4-4 4zm7.846-10.405c0 .795-.646 1.44-1.44 1.44-.795 0-1.44-.646-1.44-1.44 0-.794.646-1.439 1.44-1.439.793-.001 1.44.645 1.44 1.439z" />
    </svg>
  );
}

export function ExploreMoreArrow(svgProps) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 8 14" {...svgProps}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.70685 6.29306C7.89433 6.48059 7.99964 6.73489 7.99964 7.00006C7.99964 7.26522 7.89433 7.51953 7.70685 7.70706L2.04985 13.3641C1.95761 13.4596 1.84726 13.5358 1.72526 13.5882C1.60326 13.6406 1.47204 13.6682 1.33926 13.6693C1.20648 13.6705 1.0748 13.6452 0.951901 13.5949C0.829005 13.5446 0.717352 13.4703 0.623459 13.3765C0.529567 13.2826 0.455314 13.1709 0.405033 13.048C0.354752 12.9251 0.32945 12.7934 0.330604 12.6607C0.331758 12.5279 0.359344 12.3967 0.411753 12.2747C0.464162 12.1526 0.540344 12.0423 0.635854 11.9501L5.58586 7.00006L0.635854 2.05006C0.453696 1.86146 0.352902 1.60885 0.35518 1.34666C0.357459 1.08446 0.462628 0.833648 0.648036 0.64824C0.833444 0.462832 1.08426 0.357663 1.34645 0.355384C1.60865 0.353106 1.86125 0.4539 2.04985 0.636058L7.70685 6.29306Z"
      />
    </svg>
  );
}

export function PinLocation(svgProps) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13 14" {...svgProps}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.27441 0.333313C7.86571 0.333313 9.39184 0.965454 10.5171 2.09067C11.6423 3.21589 12.2744 4.74201 12.2744 6.33331C12.2744 8.38265 11.1571 10.06 9.97975 11.2633C9.39142 11.8579 8.7496 12.3971 8.06241 12.874L7.77841 13.0673L7.64508 13.156L7.39375 13.316L7.16975 13.4526L6.89241 13.614C6.70408 13.7212 6.49111 13.7775 6.27441 13.7775C6.05772 13.7775 5.84474 13.7212 5.65641 13.614L5.37908 13.4526L5.03241 13.2393L4.90441 13.156L4.63108 12.974C3.88973 12.4722 3.19913 11.8993 2.56908 11.2633C1.39175 10.0593 0.274414 8.38265 0.274414 6.33331C0.274414 4.74201 0.906555 3.21589 2.03177 2.09067C3.15699 0.965454 4.68312 0.333313 6.27441 0.333313ZM6.27441 1.66665C5.03674 1.66665 3.84975 2.15831 2.97458 3.03348C2.09941 3.90865 1.60775 5.09564 1.60775 6.33331C1.60775 7.88131 2.45575 9.23998 3.52175 10.3306C3.98016 10.7946 4.47558 11.2204 5.00308 11.604L5.30841 11.8213C5.40708 11.89 5.50175 11.954 5.59308 12.0133L5.85308 12.18L6.08175 12.3193L6.27441 12.432L6.57775 12.2526L6.82241 12.0993C6.95241 12.0166 7.09241 11.924 7.24041 11.8213L7.54575 11.604C8.07325 11.2204 8.56867 10.7946 9.02708 10.3306C10.0931 9.24065 10.9411 7.88131 10.9411 6.33331C10.9411 5.09564 10.4494 3.90865 9.57425 3.03348C8.69908 2.15831 7.51209 1.66665 6.27441 1.66665ZM6.27441 3.66665C6.98166 3.66665 7.65994 3.9476 8.16003 4.44769C8.66013 4.94779 8.94108 5.62607 8.94108 6.33331C8.94108 7.04056 8.66013 7.71883 8.16003 8.21893C7.65994 8.71903 6.98166 8.99998 6.27441 8.99998C5.56717 8.99998 4.88889 8.71903 4.3888 8.21893C3.8887 7.71883 3.60775 7.04056 3.60775 6.33331C3.60775 5.62607 3.8887 4.94779 4.3888 4.44769C4.88889 3.9476 5.56717 3.66665 6.27441 3.66665ZM6.27441 4.99998C5.92079 4.99998 5.58165 5.14046 5.33161 5.3905C5.08156 5.64055 4.94108 5.97969 4.94108 6.33331C4.94108 6.68693 5.08156 7.02607 5.33161 7.27612C5.58165 7.52617 5.92079 7.66665 6.27441 7.66665C6.62804 7.66665 6.96717 7.52617 7.21722 7.27612C7.46727 7.02607 7.60775 6.68693 7.60775 6.33331C7.60775 5.97969 7.46727 5.64055 7.21722 5.3905C6.96717 5.14046 6.62804 4.99998 6.27441 4.99998Z"
      />
    </svg>
  );
}

export function BecahIcon(svgProps) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 15" {...svgProps}>
      <path d="M11.629 1.20172C11.1287 0.912853 10.5765 0.725349 10.0038 0.649912C9.43116 0.574476 8.84922 0.612585 8.29126 0.762063C7.73331 0.911541 7.21026 1.16946 6.75199 1.52109C6.29372 1.87273 5.9092 2.31119 5.62039 2.81143L5.45467 3.09829C5.39796 3.19673 5.38265 3.31367 5.41212 3.42339C5.44159 3.53311 5.51341 3.62665 5.61182 3.68343L8.73239 5.48515L6.86496 8.75372L6.8261 8.75315C5.02153 8.75315 3.45867 9.8 2.52782 11.28C2.67113 11.2294 2.8231 11.2078 2.97484 11.2166C3.12659 11.2254 3.27505 11.2644 3.41153 11.3313C3.54802 11.3982 3.6698 11.4916 3.76973 11.6062C3.86966 11.7207 3.94575 11.854 3.99353 11.9983C4.3341 13.0257 5.57524 12.9697 5.88839 12.0594C5.96586 11.8345 6.11162 11.6393 6.30532 11.5011C6.49903 11.363 6.73103 11.2887 6.96896 11.2887C7.20689 11.2887 7.43889 11.363 7.63259 11.5011C7.8263 11.6393 7.97205 11.8345 8.04953 12.0594C8.36267 12.9697 9.60382 13.0257 9.94439 11.9977C10.0229 11.7609 10.1768 11.5563 10.3826 11.4152C10.5883 11.274 10.8346 11.2041 11.0838 11.216C10.3227 10.04 9.15924 9.14743 7.7941 8.85543L9.47467 5.91372L12.4878 7.65372C12.5366 7.68189 12.5904 7.70017 12.6463 7.70752C12.7021 7.71487 12.7588 7.71114 12.8132 7.69655C12.8676 7.68195 12.9186 7.65678 12.9633 7.62247C13.008 7.58816 13.0454 7.54538 13.0735 7.49658L13.2392 7.21029C13.5281 6.71004 13.7156 6.15779 13.791 5.58508C13.8663 5.01238 13.8282 4.43043 13.6787 3.87247C13.5291 3.31451 13.2711 2.79147 12.9195 2.33322C12.5678 1.87497 12.1293 1.49048 11.629 1.20172ZM3.31582 12.2223C3.28647 12.1341 3.22926 12.0579 3.1528 12.005C3.07634 11.9522 2.98478 11.9257 2.89192 11.9294C2.79906 11.9331 2.70994 11.967 2.63797 12.0258C2.56601 12.0846 2.51512 12.1652 2.49296 12.2554C2.34724 12.8509 2.07124 13.1777 1.75696 13.3674C1.42782 13.5663 1.00839 13.6429 0.540388 13.6429C0.426724 13.6429 0.317715 13.688 0.237342 13.7684C0.156969 13.8488 0.111816 13.9578 0.111816 14.0714C0.111816 14.1851 0.156969 14.2941 0.237342 14.3745C0.317715 14.4549 0.426724 14.5 0.540388 14.5C1.08724 14.5 1.68324 14.4131 2.19982 14.1011C2.50809 13.9152 2.76794 13.6589 2.9581 13.3531C3.98039 14.6571 5.97639 14.6503 6.96896 13.3394C7.97753 14.6709 10.0204 14.6571 11.0278 13.2903C11.3013 13.6625 11.658 13.9658 12.0694 14.1758C12.4808 14.3858 12.9356 14.4968 13.3975 14.5C13.5112 14.5 13.6202 14.4549 13.7006 14.3745C13.7809 14.2941 13.8261 14.1851 13.8261 14.0714C13.8261 13.9578 13.7809 13.8488 13.7006 13.7684C13.6202 13.688 13.5112 13.6429 13.3975 13.6429C12.5547 13.6429 11.7147 13.0943 11.4375 12.2269C11.4101 12.1405 11.356 12.0651 11.2831 12.0114C11.2101 11.9578 11.1221 11.9286 11.0315 11.9281C10.9409 11.9276 10.8525 11.9558 10.779 12.0086C10.7054 12.0615 10.6505 12.1363 10.6221 12.2223C10.0718 13.8846 7.91467 13.8634 7.3741 12.2914C7.34502 12.2071 7.29035 12.134 7.21773 12.0822C7.14511 12.0304 7.05815 12.0026 6.96896 12.0026C6.87977 12.0026 6.79281 12.0304 6.72019 12.0822C6.64756 12.134 6.5929 12.2071 6.56382 12.2914C6.02324 13.8634 3.86667 13.884 3.31582 12.2223Z" />
    </svg>
  );
}

export function StateIcon(svgProps) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" {...svgProps}>
      <path d="M6.47117 3.19533C6.27584 3 5.96184 3 5.33317 3H5.1665V2C5.1665 1.86739 5.11383 1.74021 5.02006 1.64645C4.92629 1.55268 4.79911 1.5 4.6665 1.5C4.5339 1.5 4.40672 1.55268 4.31295 1.64645C4.21918 1.74021 4.1665 1.86739 4.1665 2V3H3.99984C3.37117 3 3.05717 3 2.86184 3.19533C2.6665 3.39067 2.6665 3.70467 2.6665 4.33333V4.46933C2.80117 4.434 2.93717 4.40933 3.0705 4.39133C3.49984 4.33333 4.02984 4.33333 4.6065 4.33333H4.72584C5.3025 4.33333 5.83184 4.33333 6.26184 4.39133C6.39517 4.40867 6.53117 4.434 6.66584 4.46933V4.33333C6.66584 3.70467 6.6665 3.39067 6.47117 3.19533Z" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.3335 14.1666C1.20089 14.1666 1.07371 14.2193 0.979943 14.3131C0.886175 14.4068 0.833496 14.534 0.833496 14.6666C0.833496 14.7992 0.886175 14.9264 0.979943 15.0202C1.07371 15.1139 1.20089 15.1666 1.3335 15.1666H14.6668C14.7994 15.1666 14.9266 15.1139 15.0204 15.0202C15.1142 14.9264 15.1668 14.7992 15.1668 14.6666C15.1668 14.534 15.1142 14.4068 15.0204 14.3131C14.9266 14.2193 14.7994 14.1666 14.6668 14.1666H14.0002V5.18129C14.0002 4.28795 14.0002 3.84062 13.7628 3.49795C13.5255 3.15529 13.1068 2.99795 12.2695 2.68462C10.6328 2.07062 9.81483 1.76395 9.24083 2.16129C8.66683 2.55995 8.66683 3.43329 8.66683 5.18129V6.99995H8.72683C9.3035 6.99995 9.83283 6.99995 10.2628 7.05795C10.7368 7.12129 11.2382 7.27129 11.6495 7.68329C12.0622 8.09529 12.2122 8.59662 12.2762 9.06995C12.3335 9.50062 12.3335 10.03 12.3335 10.6073V14.1666H11.3335V10.6666C11.3335 9.40929 11.3335 8.78129 10.9428 8.39062C10.5522 7.99995 9.92416 7.99995 8.66683 7.99995H7.3335C6.07616 7.99995 5.44816 7.99995 5.0575 8.39062C4.66683 8.78129 4.66683 9.40929 4.66683 10.6666V14.1666H3.66683V10.6066C3.66683 10.03 3.66683 9.50062 3.72483 9.07062C3.78816 8.59662 3.93816 8.09529 4.35016 7.68395C4.76216 7.27129 5.2635 7.12129 5.73683 7.05729C6.1675 6.99995 6.69683 6.99995 7.27416 6.99995H7.32216C7.2955 6.37595 7.21216 5.99329 6.94283 5.72395C6.55216 5.33329 5.92416 5.33329 4.66683 5.33329C3.4095 5.33329 2.7815 5.33329 2.39083 5.72395C2.00016 6.11462 2.00016 6.74262 2.00016 7.99995V14.1666H1.3335ZM6.16683 9.99995C6.16683 9.86734 6.21951 9.74017 6.31328 9.6464C6.40704 9.55263 6.53422 9.49995 6.66683 9.49995H9.3335C9.4661 9.49995 9.59328 9.55263 9.68705 9.6464C9.78082 9.74017 9.8335 9.86734 9.8335 9.99995C9.8335 10.1326 9.78082 10.2597 9.68705 10.3535C9.59328 10.4473 9.4661 10.5 9.3335 10.5H6.66683C6.53422 10.5 6.40704 10.4473 6.31328 10.3535C6.21951 10.2597 6.16683 10.1326 6.16683 9.99995ZM6.16683 12C6.16683 11.8673 6.21951 11.7402 6.31328 11.6464C6.40704 11.5526 6.53422 11.5 6.66683 11.5H9.3335C9.4661 11.5 9.59328 11.5526 9.68705 11.6464C9.78082 11.7402 9.8335 11.8673 9.8335 12C9.8335 12.1326 9.78082 12.2597 9.68705 12.3535C9.59328 12.4473 9.4661 12.5 9.3335 12.5H6.66683C6.53422 12.5 6.40704 12.4473 6.31328 12.3535C6.21951 12.2597 6.16683 12.1326 6.16683 12Z"
      />
    </svg>
  );
}

export function SearchIcon(svgProps) {
  return (
    <svg
      aria-hidden="true"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 20 20"
      {...svgProps}
    >
      <path
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="2"
        d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"
      />
    </svg>
  );
}

export function CancelIcon(svgProps) {
  return (
    <svg
      viewBox="0 -0.5 25 25"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <g
        id="SVGRepo_bgCarrier"
        strokeWidth={svgProps?.strokeWidth ? svgProps?.strokeWidth : 1}
      />
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <g id="SVGRepo_iconCarrier">
        <path d="M6.96967 16.4697C6.67678 16.7626 6.67678 17.2374 6.96967 17.5303C7.26256 17.8232 7.73744 17.8232 8.03033 17.5303L6.96967 16.4697ZM13.0303 12.5303C13.3232 12.2374 13.3232 11.7626 13.0303 11.4697C12.7374 11.1768 12.2626 11.1768 11.9697 11.4697L13.0303 12.5303ZM11.9697 11.4697C11.6768 11.7626 11.6768 12.2374 11.9697 12.5303C12.2626 12.8232 12.7374 12.8232 13.0303 12.5303L11.9697 11.4697ZM18.0303 7.53033C18.3232 7.23744 18.3232 6.76256 18.0303 6.46967C17.7374 6.17678 17.2626 6.17678 16.9697 6.46967L18.0303 7.53033ZM13.0303 11.4697C12.7374 11.1768 12.2626 11.1768 11.9697 11.4697C11.6768 11.7626 11.6768 12.2374 11.9697 12.5303L13.0303 11.4697ZM16.9697 17.5303C17.2626 17.8232 17.7374 17.8232 18.0303 17.5303C18.3232 17.2374 18.3232 16.7626 18.0303 16.4697L16.9697 17.5303ZM11.9697 12.5303C12.2626 12.8232 12.7374 12.8232 13.0303 12.5303C13.3232 12.2374 13.3232 11.7626 13.0303 11.4697L11.9697 12.5303ZM8.03033 6.46967C7.73744 6.17678 7.26256 6.17678 6.96967 6.46967C6.67678 6.76256 6.67678 7.23744 6.96967 7.53033L8.03033 6.46967ZM8.03033 17.5303L13.0303 12.5303L11.9697 11.4697L6.96967 16.4697L8.03033 17.5303ZM13.0303 12.5303L18.0303 7.53033L16.9697 6.46967L11.9697 11.4697L13.0303 12.5303ZM11.9697 12.5303L16.9697 17.5303L18.0303 16.4697L13.0303 11.4697L11.9697 12.5303ZM13.0303 11.4697L8.03033 6.46967L6.96967 7.53033L11.9697 12.5303L13.0303 11.4697Z" />
      </g>
    </svg>
  );
}

export function ArrowIcon(svgProps) {
  return (
    <svg
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      {...svgProps}
    >
      <g id="SVGRepo_bgCarrier" strokeWidth={0} />
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <g id="SVGRepo_iconCarrier">
        <path
          d="M6 12H18M18 12L13 7M18 12L13 17"
          strokeWidth={2}
          strokeLinecap="round"
          strokeLinejoin="round"
          stroke="#fff"
        />
      </g>
    </svg>
  );
}

export function BlogIcon(svgProps) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      version="1.1"
      id="Layer_1"
      // x="0px"
      // y="0px"
      viewBox="0 0 113.8 122.9"
      // style={{ enableBackground: "new 0 0 113.8 122.9" }}
      xmlSpace="preserve"
      {...svgProps}
    >
      <g>
        <path d="M46.5,12.6c11.8,0,30.3,0.1,42.1,0.1c1.3,0,2.6,0.1,3.9,0.4c1.2,0.2,2.4,0.6,3.6,1.1c1.1,0.5,2.2,1.1,3.3,1.8 c1,0.7,2,1.5,2.9,2.4c0.9,0.9,1.7,1.9,2.4,2.9c0.7,1,1.3,2.1,1.8,3.3c0.5,1.1,0.8,2.3,1.1,3.6c0.2,1.3,0.4,2.5,0.4,3.9v39.1 c0,1.3-0.1,2.6-0.4,3.9c-0.2,1.3-0.6,2.4-1.1,3.6c-0.5,1.1-1.1,2.2-1.8,3.3c-0.7,1-1.5,2-2.4,2.9l0,0c-1,0.9-2,1.8-3,2.4 c-1,0.7-2.1,1.3-3.2,1.8c-1.1,0.5-2.3,0.8-3.5,1.1c-1.2,0.2-2.5,0.4-3.9,0.4H73.8c-0.5,0-0.9,0.1-1.3,0.3c-0.4,0.2-0.7,0.5-1,0.8 l0,0c-1.2,1.5-2.4,3-3.7,4.5c-1.3,1.4-2.6,2.8-4.1,4.1c-1.4,1.3-2.9,2.5-4.4,3.7c-1.5,1.2-3.1,2.3-4.7,3.4c-1.5,1-3.2,2-4.9,2.9 c-1.7,0.9-3.4,1.7-5.1,2.5c-0.2,0.1-0.4,0.1-0.6,0c-0.3-0.2-0.4-0.6-0.3-0.9c0.3-0.6,0.6-1.1,0.9-1.7c0.3-0.5,0.6-1.1,0.8-1.7l0,0 c0.5-1.1,1-2.2,1.4-3.4c0.5-1.2,0.9-2.4,1.3-3.6c0.4-1.1,0.8-2.3,1.1-3.5c0.4-1.2,0.7-2.4,1-3.6c0.1-0.4,0.2-0.7,0.2-1.1 c0-0.8-0.3-1.5-0.9-2.1l0,0c-0.5-0.5-1.3-0.9-2.1-0.9H30.4c-1.3,0-2.6-0.1-3.9-0.4c-1.2-0.2-2.4-0.6-3.5-1l0,0 c-1.1-0.4-2.2-1-3.2-1.7c-1.1-0.7-2.1-1.6-3.1-2.5l0,0c-0.9-0.9-1.7-1.9-2.4-2.9c-0.7-1-1.3-2.1-1.8-3.3c-0.5-1.1-0.8-2.3-1.1-3.6 c-0.2-1.3-0.4-2.5-0.4-3.9V46.3c0-4-5.2-4-6-1.2v25.9c0,1.7,0.2,3.4,0.5,5c0.3,1.6,0.8,3.2,1.4,4.7c0.6,1.5,1.4,2.9,2.3,4.3 c0.9,1.4,2,2.7,3.2,3.9c1.2,1.2,2.5,2.3,3.9,3.2c1.3,0.9,2.8,1.7,4.3,2.3l0,0c1.5,0.6,3.1,1.1,4.7,1.4c1.6,0.3,3.3,0.5,5,0.5h12.3 c0.1,0,0.2,0,0.2,0c0.4,0.1,0.6,0.5,0.4,0.9l0,0c-0.2,0.8-0.5,1.5-0.7,2.3l0,0c-0.4,1.1-0.8,2.2-1.2,3.3c-0.4,1-0.9,2.1-1.3,3.1 c0,0.1,0,0.1,0,0.2c-0.4,1-0.9,2-1.5,3c-0.6,1-1.2,1.9-2,2.9c-0.7,0.9-1.6,1.9-2.5,2.9l0,0c-0.9,1-1.9,1.9-3,2.9 c-0.6,0.6-0.9,1.3-1,2.1c0,0.8,0.2,1.5,0.8,2.1c0.4,0.4,0.9,0.7,1.4,0.9c0.5,0.2,1.1,0.2,1.6,0c2.2-0.6,4.4-1.3,6.6-2 c2.1-0.7,4.2-1.5,6.2-2.4c2-0.9,4-1.8,5.9-2.8c1.9-1,3.7-2.1,5.5-3.3l0,0c1.8-1.1,3.5-2.4,5.1-3.7c1.7-1.3,3.2-2.7,4.8-4.1l0,0 c1.3-1.2,2.5-2.5,3.7-3.8c1.2-1.3,2.4-2.7,3.5-4.1c0.1-0.2,0.3-0.3,0.6-0.3h13c1.7,0,3.4-0.2,5-0.5c1.6-0.3,3.2-0.8,4.7-1.4l0,0 c1.5-0.6,2.9-1.4,4.3-2.3c1.4-0.9,2.7-2,3.9-3.2c1.2-1.2,2.3-2.5,3.2-3.9c0.9-1.4,1.7-2.8,2.3-4.3c0.6-1.5,1.1-3.1,1.4-4.7 c0.3-1.6,0.5-3.3,0.5-5V31.9c0-1.7-0.2-3.4-0.5-5c-0.3-1.6-0.8-3.2-1.4-4.7c-0.6-1.5-1.4-2.9-2.3-4.3c-0.9-1.4-2-2.7-3.2-3.9 c-1.2-1.2-2.5-2.3-3.9-3.2c-1.3-0.9-2.8-1.7-4.3-2.3l0,0c-1.5-0.6-3.1-1.1-4.7-1.4c-1.6-0.3-3.3-0.5-5-0.5 c-12.3,0-31.2-0.1-43.5-0.1C42.2,7.4,42,12.6,46.5,12.6L46.5,12.6L46.5,12.6z M24.3,39.2c1.9,0.6,3.8,1.2,5.7,1.8 c1.9,0.6,3.8,1.3,5.7,1.9c4.5,1.4,7,2.3,7.5,2.4c0.5,0.1,0.2-1.9-0.9-6.3l-3.6-13.7l-0.3-0.3L24.3,39.2L24.3,39.2L24.3,39.2 L24.3,39.2L24.3,39.2z M33.2,19.7L14.9,0.5C14.3,0,13.7-0.2,13,0.2L0.4,12.4c-0.5,0.6-0.6,1.3,0.1,2l18.6,19.5L33.2,19.7L33.2,19.7 L33.2,19.7z M42.1,72.9c-1.6,0-2.8-1.2-2.9-2.8c0-1.6,1.2-2.8,2.8-2.9L67,66.8l5.7-0.4c1.6-0.1,2.9,1.1,3,2.6 c0.1,1.6-1.1,2.9-2.6,3l-5.7,0.4C67.4,72.4,44.6,72.8,42.1,72.9L42.1,72.9L42.1,72.9z M51,55.1c-1.6,0-2.8-1.3-2.8-2.8 c0-1.6,1.3-2.8,2.8-2.8h35.6c1.6,0,2.8,1.3,2.8,2.8c0,1.6-1.3,2.8-2.8,2.8H51L51,55.1L51,55.1z M63.6,38.2c-1.6,0-2.8-1.3-2.8-2.8 s1.3-2.8,2.8-2.8h23c1.6,0,2.8,1.3,2.8,2.8c0,1.6-1.3,2.8-2.8,2.8H63.6L63.6,38.2L63.6,38.2z" />
      </g>
    </svg>
  );
}

export function ListIcon(svgProps) {
  return (
    <svg
      version="1.1"
      id="Capa_1"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      viewBox="0 0 487.3 487.3"
      xmlSpace="preserve"
      {...svgProps}
    >
      <g id="SVGRepo_bgCarrier" strokeWidth={0} />
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <g id="SVGRepo_iconCarrier">
        <g>
          <g>
            <path d="M487.2,69.7c0,12.9-10.5,23.4-23.4,23.4h-322c-12.9,0-23.4-10.5-23.4-23.4s10.5-23.4,23.4-23.4h322.1 C476.8,46.4,487.2,56.8,487.2,69.7z M463.9,162.3H141.8c-12.9,0-23.4,10.5-23.4,23.4s10.5,23.4,23.4,23.4h322.1 c12.9,0,23.4-10.5,23.4-23.4C487.2,172.8,476.8,162.3,463.9,162.3z M463.9,278.3H141.8c-12.9,0-23.4,10.5-23.4,23.4 s10.5,23.4,23.4,23.4h322.1c12.9,0,23.4-10.5,23.4-23.4C487.2,288.8,476.8,278.3,463.9,278.3z M463.9,394.3H141.8 c-12.9,0-23.4,10.5-23.4,23.4s10.5,23.4,23.4,23.4h322.1c12.9,0,23.4-10.5,23.4-23.4C487.2,404.8,476.8,394.3,463.9,394.3z M38.9,30.8C17.4,30.8,0,48.2,0,69.7s17.4,39,38.9,39s38.9-17.5,38.9-39S60.4,30.8,38.9,30.8z M38.9,146.8 C17.4,146.8,0,164.2,0,185.7s17.4,38.9,38.9,38.9s38.9-17.4,38.9-38.9S60.4,146.8,38.9,146.8z M38.9,262.8 C17.4,262.8,0,280.2,0,301.7s17.4,38.9,38.9,38.9s38.9-17.4,38.9-38.9S60.4,262.8,38.9,262.8z M38.9,378.7 C17.4,378.7,0,396.1,0,417.6s17.4,38.9,38.9,38.9s38.9-17.4,38.9-38.9C77.8,396.2,60.4,378.7,38.9,378.7z" />
          </g>
        </g>
      </g>
    </svg>
  );
}

export function CopyIcon(svgProps) {
  return <svg {...svgProps} viewBox="0 0 24 24" width="22" height="22" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g id="Edit / Copy"> <path id="Vector" d="M9 9V6.2002C9 5.08009 9 4.51962 9.21799 4.0918C9.40973 3.71547 9.71547 3.40973 10.0918 3.21799C10.5196 3 11.0801 3 12.2002 3H17.8002C18.9203 3 19.4801 3 19.9079 3.21799C20.2842 3.40973 20.5905 3.71547 20.7822 4.0918C21.0002 4.51962 21.0002 5.07967 21.0002 6.19978V11.7998C21.0002 12.9199 21.0002 13.48 20.7822 13.9078C20.5905 14.2841 20.2839 14.5905 19.9076 14.7822C19.4802 15 18.921 15 17.8031 15H15M9 9H6.2002C5.08009 9 4.51962 9 4.0918 9.21799C3.71547 9.40973 3.40973 9.71547 3.21799 10.0918C3 10.5196 3 11.0801 3 12.2002V17.8002C3 18.9203 3 19.4801 3.21799 19.9079C3.40973 20.2842 3.71547 20.5905 4.0918 20.7822C4.5192 21 5.07899 21 6.19691 21H11.8036C12.9215 21 13.4805 21 13.9079 20.7822C14.2842 20.5905 14.5905 20.2839 14.7822 19.9076C15 19.4802 15 18.921 15 17.8031V15M9 9H11.8002C12.9203 9 13.4801 9 13.9079 9.21799C14.2842 9.40973 14.5905 9.71547 14.7822 10.0918C15 10.5192 15 11.079 15 12.1969L15 15" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path> </g> </g></svg>
}

export function GoogleIcon(svgProps) {
  return (
    <svg
      version="1.1"
      id="Capa_1"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      viewBox="0 0 210 210"
      xmlSpace="preserve"
      {...svgProps}
    >
      <g id="SVGRepo_bgCarrier" strokeWidth={0} />
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <g id="SVGRepo_iconCarrier">
        <path d="M0,105C0,47.103,47.103,0,105,0c23.383,0,45.515,7.523,64.004,21.756l-24.4,31.696C133.172,44.652,119.477,40,105,40 c-35.841,0-65,29.159-65,65s29.159,65,65,65c28.867,0,53.398-18.913,61.852-45H105V85h105v20c0,57.897-47.103,105-105,105 S0,162.897,0,105z" />
      </g>
    </svg>
  );
}

export function SunSet(svgProps) {
  return (
    <svg
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <g clipPath="url(#clip0_1035_11630)">
        <path d="M7.646 4.854C7.69245 4.90056 7.74762 4.93751 7.80837 4.96271C7.86911 4.98792 7.93423 5.00089 8 5.00089C8.06577 5.00089 8.13089 4.98792 8.19163 4.96271C8.25238 4.93751 8.30755 4.90056 8.354 4.854L9.854 3.354C9.90049 3.30751 9.93736 3.25232 9.96252 3.19158C9.98768 3.13084 10.0006 3.06574 10.0006 3C10.0006 2.93426 9.98768 2.86916 9.96252 2.80842C9.93736 2.74768 9.90049 2.69249 9.854 2.646C9.80751 2.59951 9.75232 2.56264 9.69158 2.53748C9.63084 2.51232 9.56574 2.49937 9.5 2.49937C9.43426 2.49937 9.36916 2.51232 9.30842 2.53748C9.24768 2.56264 9.19249 2.59951 9.146 2.646L8.5 3.293V1.5C8.5 1.36739 8.44732 1.24021 8.35355 1.14645C8.25979 1.05268 8.13261 1 8 1C7.86739 1 7.74021 1.05268 7.64645 1.14645C7.55268 1.24021 7.5 1.36739 7.5 1.5V3.293L6.854 2.646C6.80751 2.59951 6.75232 2.56264 6.69158 2.53748C6.63084 2.51232 6.56574 2.49937 6.5 2.49937C6.43426 2.49937 6.36916 2.51232 6.30842 2.53748C6.24768 2.56264 6.19249 2.59951 6.146 2.646C6.09951 2.69249 6.06264 2.74768 6.03748 2.80842C6.01232 2.86916 5.99937 2.93426 5.99937 3C5.99937 3.06574 6.01232 3.13084 6.03748 3.19158C6.06264 3.25232 6.09951 3.30751 6.146 3.354L7.646 4.854ZM2.343 4.344C2.43676 4.25026 2.56392 4.19761 2.6965 4.19761C2.82908 4.19761 2.95624 4.25026 3.05 4.344L4.464 5.757C4.55508 5.8513 4.60548 5.9776 4.60434 6.1087C4.6032 6.2398 4.55061 6.3652 4.45791 6.45791C4.3652 6.55061 4.2398 6.6032 4.1087 6.60434C3.9776 6.60548 3.8513 6.55508 3.757 6.464L2.343 5.05C2.24926 4.95624 2.19661 4.82908 2.19661 4.6965C2.19661 4.56392 2.24926 4.43776 2.343 4.344ZM13.657 4.344C13.7504 4.43772 13.8029 4.56466 13.8029 4.697C13.8029 4.82934 13.7504 4.95628 13.657 5.05L12.243 6.464C12.1969 6.51176 12.1417 6.54985 12.0807 6.57605C12.0197 6.60226 11.9541 6.61605 11.8877 6.61663C11.8213 6.6172 11.7555 6.60455 11.694 6.57941C11.6326 6.55427 11.5767 6.51714 11.5298 6.4702C11.4829 6.42325 11.4457 6.36742 11.4206 6.30598C11.3954 6.24453 11.3828 6.17869 11.3834 6.1123C11.384 6.04591 11.3977 5.9803 11.4239 5.9193C11.4502 5.8583 11.4882 5.80312 11.536 5.757L12.95 4.343C13.0438 4.24926 13.1709 4.19661 13.3035 4.19661C13.4361 4.19661 13.5632 4.25026 13.657 4.344ZM11.709 11.5C11.9542 10.8928 12.0466 10.2348 11.978 9.58359C11.9095 8.93239 11.6822 8.30798 11.316 7.76516C10.9497 7.22235 10.4559 6.77774 9.87773 6.47036C9.29957 6.16298 8.65479 6.00223 8 6.00223C7.34521 6.00223 6.70043 6.16298 6.12227 6.47036C5.54411 6.77774 5.05025 7.22235 4.68405 7.76516C4.31784 8.30798 4.0905 8.93239 4.02197 9.58359C3.95344 10.2348 4.04582 10.8928 4.291 11.5H0.5C0.367392 11.5 0.240215 11.5527 0.146447 11.6464C0.0526784 11.7402 0 11.8674 0 12C0 12.1326 0.0526784 12.2598 0.146447 12.3536C0.240215 12.4473 0.367392 12.5 0.5 12.5H15.5C15.6326 12.5 15.7598 12.4473 15.8536 12.3536C15.9473 12.2598 16 12.1326 16 12C16 11.8674 15.9473 11.7402 15.8536 11.6464C15.7598 11.5527 15.6326 11.5 15.5 11.5H11.71H11.709ZM0 10C0 9.86739 0.0526784 9.74021 0.146447 9.64645C0.240215 9.55268 0.367392 9.5 0.5 9.5H2.5C2.63261 9.5 2.75979 9.55268 2.85355 9.64645C2.94732 9.74021 3 9.86739 3 10C3 10.1326 2.94732 10.2598 2.85355 10.3536C2.75979 10.4473 2.63261 10.5 2.5 10.5H0.5C0.367392 10.5 0.240215 10.4473 0.146447 10.3536C0.0526784 10.2598 0 10.1326 0 10ZM13 10C13 9.86739 13.0527 9.74021 13.1464 9.64645C13.2402 9.55268 13.3674 9.5 13.5 9.5H15.5C15.6326 9.5 15.7598 9.55268 15.8536 9.64645C15.9473 9.74021 16 9.86739 16 10C16 10.1326 15.9473 10.2598 15.8536 10.3536C15.7598 10.4473 15.6326 10.5 15.5 10.5H13.5C13.3674 10.5 13.2402 10.4473 13.1464 10.3536C13.0527 10.2598 13 10.1326 13 10Z" />
      </g>
      <defs>
        <clipPath id="clip0_1035_11630">
          <rect width={16} height={16} fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function SunRise(svgProps) {
  return (
    <svg
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <g clipPath="url(#clip0_1035_11625)">
        <path d="M7.646 1.14592C7.69245 1.09935 7.74762 1.06241 7.80837 1.0372C7.86911 1.012 7.93423 0.999023 8 0.999023C8.06577 0.999023 8.13089 1.012 8.19163 1.0372C8.25238 1.06241 8.30755 1.09935 8.354 1.14592L9.854 2.64592C9.90049 2.69241 9.93736 2.74759 9.96252 2.80833C9.98768 2.86907 10.0006 2.93417 10.0006 2.99992C10.0006 3.06566 9.98768 3.13076 9.96252 3.1915C9.93736 3.25224 9.90049 3.30743 9.854 3.35392C9.80751 3.40041 9.75232 3.43728 9.69158 3.46244C9.63084 3.4876 9.56574 3.50055 9.5 3.50055C9.43426 3.50055 9.36916 3.4876 9.30842 3.46244C9.24768 3.43728 9.19249 3.40041 9.146 3.35392L8.5 2.70692V4.49992C8.5 4.63253 8.44732 4.7597 8.35355 4.85347C8.25979 4.94724 8.13261 4.99992 8 4.99992C7.86739 4.99992 7.74021 4.94724 7.64645 4.85347C7.55268 4.7597 7.5 4.63253 7.5 4.49992V2.70692L6.854 3.35392C6.80751 3.40041 6.75232 3.43728 6.69158 3.46244C6.63084 3.4876 6.56574 3.50055 6.5 3.50055C6.43426 3.50055 6.36916 3.4876 6.30842 3.46244C6.24768 3.43728 6.19249 3.40041 6.146 3.35392C6.09951 3.30743 6.06264 3.25224 6.03748 3.1915C6.01232 3.13076 5.99937 3.06566 5.99937 2.99992C5.99937 2.93417 6.01232 2.86907 6.03748 2.80833C6.06264 2.74759 6.09951 2.69241 6.146 2.64592L7.646 1.14592ZM2.343 4.34292C2.43676 4.24918 2.56392 4.19652 2.6965 4.19652C2.82908 4.19652 2.95624 4.24918 3.05 4.34292L4.464 5.75692C4.55508 5.85122 4.60548 5.97752 4.60434 6.10862C4.6032 6.23972 4.55061 6.36512 4.45791 6.45783C4.3652 6.55053 4.2398 6.60312 4.1087 6.60425C3.9776 6.60539 3.8513 6.555 3.757 6.46392L2.343 5.04992C2.24926 4.95615 2.19661 4.829 2.19661 4.69642C2.19661 4.56383 2.24926 4.43668 2.343 4.34292ZM13.657 4.34292C13.7507 4.43668 13.8034 4.56383 13.8034 4.69642C13.8034 4.829 13.7507 4.95615 13.657 5.04992L12.243 6.46392C12.1969 6.51167 12.1417 6.54976 12.0807 6.57597C12.0197 6.60217 11.9541 6.61597 11.8877 6.61654C11.8213 6.61712 11.7555 6.60447 11.694 6.57933C11.6326 6.55419 11.5767 6.51706 11.5298 6.47011C11.4829 6.42317 11.4457 6.36734 11.4206 6.30589C11.3954 6.24445 11.3828 6.17861 11.3834 6.11222C11.384 6.04583 11.3977 5.98022 11.4239 5.91921C11.4502 5.85821 11.4882 5.80304 11.536 5.75692L12.95 4.34292C13.0438 4.24918 13.1709 4.19652 13.3035 4.19652C13.4361 4.19652 13.5632 4.24918 13.657 4.34292ZM11.709 11.4999C11.9542 10.8928 12.0466 10.2347 11.978 9.5835C11.9095 8.93231 11.6822 8.3079 11.316 7.76508C10.9497 7.22227 10.4559 6.77765 9.87773 6.47027C9.29957 6.16289 8.65479 6.00215 8 6.00215C7.34521 6.00215 6.70043 6.16289 6.12227 6.47027C5.54411 6.77765 5.05025 7.22227 4.68405 7.76508C4.31784 8.3079 4.0905 8.93231 4.02197 9.5835C3.95344 10.2347 4.04582 10.8928 4.291 11.4999H0.5C0.367392 11.4999 0.240215 11.5526 0.146447 11.6464C0.0526784 11.7401 0 11.8673 0 11.9999C0 12.1325 0.0526784 12.2597 0.146447 12.3535C0.240215 12.4472 0.367392 12.4999 0.5 12.4999H15.5C15.6326 12.4999 15.7598 12.4472 15.8536 12.3535C15.9473 12.2597 16 12.1325 16 11.9999C16 11.8673 15.9473 11.7401 15.8536 11.6464C15.7598 11.5526 15.6326 11.4999 15.5 11.4999H11.71H11.709ZM0 9.99992C0 9.86731 0.0526784 9.74013 0.146447 9.64636C0.240215 9.5526 0.367392 9.49992 0.5 9.49992H2.5C2.63261 9.49992 2.75979 9.5526 2.85355 9.64636C2.94732 9.74013 3 9.86731 3 9.99992C3 10.1325 2.94732 10.2597 2.85355 10.3535C2.75979 10.4472 2.63261 10.4999 2.5 10.4999H0.5C0.367392 10.4999 0.240215 10.4472 0.146447 10.3535C0.0526784 10.2597 0 10.1325 0 9.99992ZM13 9.99992C13 9.86731 13.0527 9.74013 13.1464 9.64636C13.2402 9.5526 13.3674 9.49992 13.5 9.49992H15.5C15.6326 9.49992 15.7598 9.5526 15.8536 9.64636C15.9473 9.74013 16 9.86731 16 9.99992C16 10.1325 15.9473 10.2597 15.8536 10.3535C15.7598 10.4472 15.6326 10.4999 15.5 10.4999H13.5C13.3674 10.4999 13.2402 10.4472 13.1464 10.3535C13.0527 10.2597 13 10.1325 13 9.99992Z" />
      </g>
      <defs>
        <clipPath id="clip0_1035_11625">
          <rect width={16} height={16} fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function Pencil(svgProps) {
  return (
    <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" {...svgProps}>
      <path d="M20.71 7.04006C21.1 6.65006 21.1 6.00006 20.71 5.63006L18.37 3.29006C18 2.90006 17.35 2.90006 16.96 3.29006L15.12 5.12006L18.87 8.87006M3 17.2501V21.0001H6.75L17.81 9.93006L14.06 6.18006L3 17.2501Z" />
    </svg>
  );
}

export function AddPhoto(svgProps) {
  return (
    <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" {...svgProps}>
      <path d="M3 6C2.45 6 2 6.45 2 7V20C2 21.1 2.9 22 4 22H17C17.55 22 18 21.55 18 21C18 20.45 17.55 20 17 20H5C4.45 20 4 19.55 4 19V7C4 6.45 3.55 6 3 6ZM20 2H8C6.9 2 6 2.9 6 4V16C6 17.1 6.9 18 8 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2ZM18 11H15V14C15 14.55 14.55 15 14 15C13.45 15 13 14.55 13 14V11H10C9.45 11 9 10.55 9 10C9 9.45 9.45 9 10 9H13V6C13 5.45 13.45 5 14 5C14.55 5 15 5.45 15 6V9H18C18.55 9 19 9.45 19 10C19 10.55 18.55 11 18 11Z" />
    </svg>
  );
}

export function SortOrderIcon(svgProps) {
  return (
    <svg
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path d="M9 16.25C9 16.0511 9.07902 15.8603 9.21967 15.7197C9.36032 15.579 9.55109 15.5 9.75 15.5H14.25C14.4489 15.5 14.6397 15.579 14.7803 15.7197C14.921 15.8603 15 16.0511 15 16.25C15 16.4489 14.921 16.6397 14.7803 16.7803C14.6397 16.921 14.4489 17 14.25 17H9.75C9.55109 17 9.36032 16.921 9.21967 16.7803C9.07902 16.6397 9 16.4489 9 16.25ZM6 11.75C6 11.5511 6.07902 11.3603 6.21967 11.2197C6.36032 11.079 6.55109 11 6.75 11H17.25C17.4489 11 17.6397 11.079 17.7803 11.2197C17.921 11.3603 18 11.5511 18 11.75C18 11.9489 17.921 12.1397 17.7803 12.2803C17.6397 12.421 17.4489 12.5 17.25 12.5H6.75C6.55109 12.5 6.36032 12.421 6.21967 12.2803C6.07902 12.1397 6 11.9489 6 11.75ZM3 7.25C3 7.05109 3.07902 6.86032 3.21967 6.71967C3.36032 6.57902 3.55109 6.5 3.75 6.5H20.25C20.4489 6.5 20.6397 6.57902 20.7803 6.71967C20.921 6.86032 21 7.05109 21 7.25C21 7.44891 20.921 7.63968 20.7803 7.78033C20.6397 7.92098 20.4489 8 20.25 8H3.75C3.55109 8 3.36032 7.92098 3.21967 7.78033C3.07902 7.63968 3 7.44891 3 7.25Z" />
    </svg>
  );
}

export function AddImageIcon(svgProps) {
  return (
    <svg
      viewBox="0 0 24 24"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      {...svgProps}
    >
      <g id="SVGRepo_bgCarrier" strokeWidth={0} />
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <g id="SVGRepo_iconCarrier">
        <g
          id="Product-Icons"
          stroke="none"
          strokeWidth={1}
          fill="none"
          fillRule="evenodd"
        >
          <g
            id="ic_fluent_image_add_24_regular"
            fill="#212121"
            fillRule="nonzero"
          >
            <path
              d="M18.75,4 C20.5449254,4 22,5.45507456 22,7.25 L22,18.75 C22,20.5449254 20.5449254,22 18.75,22 L7.25,22 C5.45507456,22 4,20.5449254 4,18.75 L4,12.5018674 C4.47425417,12.6996032 4.97687415,12.842783 5.50009292,12.9235662 L5.5,18.75 C5.5,18.9584012 5.53642824,19.1582941 5.60326447,19.3436585 L11.4257839,13.6429919 C12.2588664,12.8272921 13.5674613,12.7885018 14.4457696,13.5265833 L14.5741754,13.6431221 L20.396372,19.3446658 C20.4634397,19.1590183 20.5,18.9587787 20.5,18.75 L20.5,7.25 C20.5,6.28350169 19.7164983,5.5 18.75,5.5 L12.9235662,5.50009292 C12.842783,4.97687415 12.6996032,4.47425417 12.5018674,4 L18.75,4 Z M12.558795,14.6439914 L12.4752034,14.7147748 L6.66845098,20.4010512 C6.85040089,20.4651384 7.04612926,20.5 7.25,20.5 L18.75,20.5 C18.9534932,20.5 19.1488742,20.4652674 19.330538,20.401407 L13.5246673,14.7148182 C13.259617,14.4552555 12.8501251,14.4316429 12.558795,14.6439914 Z M16.252115,7.5 C17.4959237,7.5 18.50423,8.50830622 18.50423,9.75211499 C18.50423,10.9959237 17.4959237,12.00423 16.252115,12.00423 C15.0083062,12.00423 14,10.9959237 14,9.75211499 C14,8.50830622 15.0083062,7.5 16.252115,7.5 Z M6.5,1 C9.53756612,1 12,3.46243388 12,6.5 C12,9.53756612 9.53756612,12 6.5,12 C3.46243388,12 1,9.53756612 1,6.5 C1,3.46243388 3.46243388,1 6.5,1 Z M16.252115,9 C15.8367333,9 15.5,9.33673335 15.5,9.75211499 C15.5,10.1674966 15.8367333,10.50423 16.252115,10.50423 C16.6674966,10.50423 17.00423,10.1674966 17.00423,9.75211499 C17.00423,9.33673335 16.6674966,9 16.252115,9 Z M6.5,2.9992349 L6.41012437,3.00729057 C6.20603131,3.04433453 6.04509963,3.20526621 6.00805567,3.40935926 L6,3.4992349 L5.99964979,5.9992349 L3.49764979,6 L3.40777416,6.00805567 C3.2036811,6.04509963 3.04274942,6.20603131 3.00570546,6.41012437 L2.99764979,6.5 L3.00570546,6.58987563 C3.04274942,6.79396869 3.2036811,6.95490037 3.40777416,6.99194433 L3.49764979,7 L6.00064979,6.9992349 L6.00110764,9.5034847 L6.00916331,9.59336034 C6.04620728,9.79745339 6.20713895,9.95838507 6.41123201,9.99542903 L6.50110764,10.0034847 L6.59098327,9.99542903 C6.79507633,9.95838507 6.95600801,9.79745339 6.99305197,9.59336034 L7.00110764,9.5034847 L7.00064979,6.9992349 L9.5045655,7 L9.59444113,6.99194433 C9.79853418,6.95490037 9.95946586,6.79396869 9.99650983,6.58987563 L10.0045655,6.5 L9.99650983,6.41012437 C9.95946586,6.20603131 9.79853418,6.04509963 9.59444113,6.00805567 L9.5045655,6 L6.99964979,5.9992349 L7,3.4992349 L6.99194433,3.40935926 C6.95490037,3.20526621 6.79396869,3.04433453 6.58987563,3.00729057 L6.5,2.9992349 Z"
              id="Color"
            ></path>
          </g>
        </g>
      </g>
    </svg>
  );
}

export function DeleteIcon(svgProps) {
  return (
    <svg
      version="1.1"
      id="Capa_1"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      viewBox="0 0 307.252 307.252"
      xmlSpace="preserve"
      {...svgProps}
    >
      <g id="SVGRepo_bgCarrier" strokeWidth={0} />
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <g id="SVGRepo_iconCarrier">
        <path
          id="XMLID_564_"
          d="M184.581,230.833l9.521-100.238c0.575-6.048,5.931-10.48,11.991-9.911 c6.048,0.575,10.485,5.943,9.911,11.991l-9.521,100.238c-0.541,5.694-5.332,9.961-10.938,9.961c-0.348,0-0.699-0.017-1.053-0.05 C188.444,242.249,184.007,236.881,184.581,230.833z M45.306,37.023h216.644c6.075,0,11-4.925,11-11s-4.925-11-11-11h-61.998 c0.49-1.246,0.759-2.604,0.759-4.023c0-6.075-4.925-11-11-11h-72.165c-6.075,0-11,4.925-11,11c0,1.42,0.269,2.777,0.759,4.023 H45.306c-6.075,0-11,4.925-11,11S39.23,37.023,45.306,37.023z M153.624,260.644c6.075,0,11-4.925,11-11V113.864 c0-6.075-4.925-11-11-11s-11,4.925-11,11v135.779C142.624,255.719,147.549,260.644,153.624,260.644z M273.279,68.477l-25.58,228.996 c-0.622,5.568-5.329,9.779-10.932,9.779H70.484c-5.603,0-10.31-4.211-10.932-9.779L33.973,68.477 c-0.348-3.11,0.646-6.222,2.733-8.555c2.086-2.333,5.068-3.666,8.198-3.666h217.443c3.13,0,6.112,1.333,8.198,3.666 C272.633,62.255,273.627,65.366,273.279,68.477z M250.051,78.256H57.201l23.123,206.996h146.604L250.051,78.256z M100.772,232.913 c0.54,5.694,5.33,9.961,10.938,9.961c0.348,0,0.699-0.017,1.053-0.05c6.048-0.575,10.485-5.943,9.911-11.991l-9.52-100.238 c-0.575-6.048-5.938-10.484-11.991-9.911c-6.048,0.575-10.485,5.943-9.911,11.991L100.772,232.913z"
        />
      </g>
    </svg>
  );
}

export function AddPhotosSwiper(svgProps) {
  return (
    <svg
      viewBox="0 0 77 49"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path d="M29.6427 6C29.0927 6 28.6427 6.45 28.6427 7V20C28.6427 21.1 29.5427 22 30.6427 22H43.6427C44.1927 22 44.6427 21.55 44.6427 21C44.6427 20.45 44.1927 20 43.6427 20H31.6427C31.0927 20 30.6427 19.55 30.6427 19V7C30.6427 6.45 30.1927 6 29.6427 6ZM46.6427 2H34.6427C33.5427 2 32.6427 2.9 32.6427 4V16C32.6427 17.1 33.5427 18 34.6427 18H46.6427C47.7427 18 48.6427 17.1 48.6427 16V4C48.6427 2.9 47.7427 2 46.6427 2ZM44.6427 11H41.6427V14C41.6427 14.55 41.1927 15 40.6427 15C40.0927 15 39.6427 14.55 39.6427 14V11H36.6427C36.0927 11 35.6427 10.55 35.6427 10C35.6427 9.45 36.0927 9 36.6427 9H39.6427V6C39.6427 5.45 40.0927 5 40.6427 5C41.1927 5 41.6427 5.45 41.6427 6V9H44.6427C45.1927 9 45.6427 9.45 45.6427 10C45.6427 10.55 45.1927 11 44.6427 11Z" />
      <path d="M0.9227 45L4.2967 34.92H6.0327L9.4067 45H8.1117L4.9547 35.648H5.3467L2.2177 45H0.9227ZM2.5467 42.641V41.472H7.7827V42.641H2.5467ZM13.6278 45.21C12.9231 45.21 12.3188 45.035 11.8148 44.685C11.3108 44.3303 10.9211 43.852 10.6458 43.25C10.3751 42.648 10.2398 41.969 10.2398 41.213C10.2398 40.457 10.3751 39.778 10.6458 39.176C10.9211 38.574 11.3108 38.1003 11.8148 37.755C12.3188 37.405 12.9208 37.23 13.6208 37.23C14.3348 37.23 14.9345 37.4027 15.4198 37.748C15.9051 38.0933 16.2715 38.567 16.5188 39.169C16.7708 39.771 16.8968 40.4523 16.8968 41.213C16.8968 41.9643 16.7708 42.6433 16.5188 43.25C16.2715 43.852 15.9051 44.3303 15.4198 44.685C14.9345 45.035 14.3371 45.21 13.6278 45.21ZM13.7818 44.069C14.2905 44.069 14.7081 43.9477 15.0348 43.705C15.3615 43.4577 15.6018 43.1193 15.7558 42.69C15.9145 42.256 15.9938 41.7637 15.9938 41.213C15.9938 40.653 15.9145 40.1607 15.7558 39.736C15.6018 39.3067 15.3638 38.973 15.0418 38.735C14.7198 38.4923 14.3115 38.371 13.8168 38.371C13.3035 38.371 12.8788 38.497 12.5428 38.749C12.2115 39.001 11.9641 39.3417 11.8008 39.771C11.6421 40.2003 11.5628 40.681 11.5628 41.213C11.5628 41.7497 11.6445 42.235 11.8078 42.669C11.9711 43.0983 12.2161 43.439 12.5428 43.691C12.8741 43.943 13.2871 44.069 13.7818 44.069ZM15.9938 45V39.435H15.8538V34.92H17.1068V45H15.9938ZM22.036 45.21C21.3313 45.21 20.727 45.035 20.223 44.685C19.719 44.3303 19.3293 43.852 19.054 43.25C18.7833 42.648 18.648 41.969 18.648 41.213C18.648 40.457 18.7833 39.778 19.054 39.176C19.3293 38.574 19.719 38.1003 20.223 37.755C20.727 37.405 21.329 37.23 22.029 37.23C22.743 37.23 23.3427 37.4027 23.828 37.748C24.3133 38.0933 24.6797 38.567 24.927 39.169C25.179 39.771 25.305 40.4523 25.305 41.213C25.305 41.9643 25.179 42.6433 24.927 43.25C24.6797 43.852 24.3133 44.3303 23.828 44.685C23.3427 45.035 22.7453 45.21 22.036 45.21ZM22.19 44.069C22.6987 44.069 23.1163 43.9477 23.443 43.705C23.7697 43.4577 24.01 43.1193 24.164 42.69C24.3227 42.256 24.402 41.7637 24.402 41.213C24.402 40.653 24.3227 40.1607 24.164 39.736C24.01 39.3067 23.772 38.973 23.45 38.735C23.128 38.4923 22.7197 38.371 22.225 38.371C21.7117 38.371 21.287 38.497 20.951 38.749C20.6197 39.001 20.3723 39.3417 20.209 39.771C20.0503 40.2003 19.971 40.681 19.971 41.213C19.971 41.7497 20.0527 42.235 20.216 42.669C20.3793 43.0983 20.6243 43.439 20.951 43.691C21.2823 43.943 21.6953 44.069 22.19 44.069ZM24.402 45V39.435H24.262V34.92H25.515V45H24.402ZM30.279 45V34.92H34.262C34.36 34.92 34.4766 34.9247 34.612 34.934C34.7473 34.9387 34.878 34.9527 35.004 34.976C35.55 35.06 36.0073 35.2467 36.376 35.536C36.7493 35.8253 37.0293 36.1917 37.216 36.635C37.4026 37.0737 37.496 37.5613 37.496 38.098C37.496 38.63 37.4003 39.1177 37.209 39.561C37.0223 39.9997 36.7423 40.366 36.369 40.66C36.0003 40.9493 35.5453 41.136 35.004 41.22C34.878 41.2387 34.7473 41.2527 34.612 41.262C34.4766 41.2713 34.36 41.276 34.262 41.276H31.525V45H30.279ZM31.525 40.093H34.22C34.3086 40.093 34.409 40.0883 34.521 40.079C34.6376 40.0697 34.7473 40.0533 34.85 40.03C35.172 39.9553 35.4333 39.82 35.634 39.624C35.8393 39.4233 35.9886 39.19 36.082 38.924C36.1753 38.6533 36.222 38.378 36.222 38.098C36.222 37.818 36.1753 37.545 36.082 37.279C35.9886 37.0083 35.8393 36.7727 35.634 36.572C35.4333 36.3713 35.172 36.236 34.85 36.166C34.7473 36.138 34.6376 36.1217 34.521 36.117C34.409 36.1077 34.3086 36.103 34.22 36.103H31.525V40.093ZM44.2752 45V41.213C44.2752 40.8537 44.2426 40.5083 44.1772 40.177C44.1166 39.8457 44.0069 39.5493 43.8482 39.288C43.6942 39.022 43.4842 38.812 43.2182 38.658C42.9569 38.504 42.6256 38.427 42.2242 38.427C41.9116 38.427 41.6269 38.4807 41.3702 38.588C41.1182 38.6907 40.9012 38.8493 40.7192 39.064C40.5372 39.2787 40.3949 39.5493 40.2922 39.876C40.1942 40.198 40.1452 40.5783 40.1452 41.017L39.3262 40.765C39.3262 40.0323 39.4569 39.4047 39.7182 38.882C39.9842 38.3547 40.3552 37.951 40.8312 37.671C41.3119 37.3863 41.8766 37.244 42.5252 37.244C43.0152 37.244 43.4329 37.321 43.7782 37.475C44.1236 37.629 44.4106 37.8343 44.6392 38.091C44.8679 38.343 45.0476 38.6277 45.1782 38.945C45.3089 39.2577 45.3999 39.5773 45.4512 39.904C45.5072 40.226 45.5352 40.5317 45.5352 40.821V45H44.2752ZM38.8852 45V34.92H40.0052V40.485H40.1452V45H38.8852ZM50.5751 45.21C49.8238 45.21 49.1751 45.0397 48.6291 44.699C48.0878 44.3583 47.6701 43.887 47.3761 43.285C47.0821 42.683 46.9351 41.9923 46.9351 41.213C46.9351 40.4197 47.0845 39.7243 47.3831 39.127C47.6818 38.5297 48.1041 38.0653 48.6501 37.734C49.1961 37.398 49.8378 37.23 50.5751 37.23C51.3311 37.23 51.9821 37.4003 52.5281 37.741C53.0741 38.077 53.4918 38.546 53.7811 39.148C54.0751 39.7453 54.2221 40.4337 54.2221 41.213C54.2221 42.0017 54.0751 42.697 53.7811 43.299C53.4871 43.8963 53.0671 44.3653 52.5211 44.706C51.9751 45.042 51.3265 45.21 50.5751 45.21ZM50.5751 44.027C51.3591 44.027 51.9425 43.7657 52.3251 43.243C52.7078 42.7203 52.8991 42.0437 52.8991 41.213C52.8991 40.359 52.7055 39.68 52.3181 39.176C51.9308 38.6673 51.3498 38.413 50.5751 38.413C50.0478 38.413 49.6138 38.532 49.2731 38.77C48.9325 39.008 48.6781 39.337 48.5101 39.757C48.3421 40.177 48.2581 40.6623 48.2581 41.213C48.2581 42.0623 48.4541 42.7437 48.8461 43.257C49.2381 43.7703 49.8145 44.027 50.5751 44.027ZM59.9843 45C59.5363 45.0887 59.093 45.1237 58.6543 45.105C58.2203 45.091 57.833 45.0047 57.4923 44.846C57.1517 44.6827 56.8927 44.4307 56.7153 44.09C56.566 43.7913 56.4843 43.4903 56.4703 43.187C56.461 42.879 56.4563 42.5313 56.4563 42.144V35.34H57.7023V42.088C57.7023 42.396 57.7047 42.6597 57.7093 42.879C57.7187 43.0983 57.7677 43.2873 57.8563 43.446C58.0243 43.7447 58.2903 43.9197 58.6543 43.971C59.023 44.0223 59.4663 44.0083 59.9843 43.929V45ZM54.9233 38.469V37.44H59.9843V38.469H54.9233ZM64.4247 45.21C63.6734 45.21 63.0247 45.0397 62.4787 44.699C61.9374 44.3583 61.5197 43.887 61.2257 43.285C60.9317 42.683 60.7847 41.9923 60.7847 41.213C60.7847 40.4197 60.9341 39.7243 61.2327 39.127C61.5314 38.5297 61.9537 38.0653 62.4997 37.734C63.0457 37.398 63.6874 37.23 64.4247 37.23C65.1807 37.23 65.8317 37.4003 66.3777 37.741C66.9237 38.077 67.3414 38.546 67.6307 39.148C67.9247 39.7453 68.0717 40.4337 68.0717 41.213C68.0717 42.0017 67.9247 42.697 67.6307 43.299C67.3367 43.8963 66.9167 44.3653 66.3707 44.706C65.8247 45.042 65.1761 45.21 64.4247 45.21ZM64.4247 44.027C65.2087 44.027 65.7921 43.7657 66.1747 43.243C66.5574 42.7203 66.7487 42.0437 66.7487 41.213C66.7487 40.359 66.5551 39.68 66.1677 39.176C65.7804 38.6673 65.1994 38.413 64.4247 38.413C63.8974 38.413 63.4634 38.532 63.1227 38.77C62.7821 39.008 62.5277 39.337 62.3597 39.757C62.1917 40.177 62.1077 40.6623 62.1077 41.213C62.1077 42.0623 62.3037 42.7437 62.6957 43.257C63.0877 43.7703 63.6641 44.027 64.4247 44.027ZM72.3596 45.203C71.4589 45.203 70.7192 45.007 70.1406 44.615C69.5666 44.2183 69.2142 43.6653 69.0836 42.956L70.3576 42.753C70.4602 43.1637 70.6959 43.4903 71.0646 43.733C71.4332 43.971 71.8906 44.09 72.4366 44.09C72.9546 44.09 73.3606 43.9827 73.6546 43.768C73.9486 43.5533 74.0956 43.2617 74.0956 42.893C74.0956 42.6783 74.0466 42.5057 73.9486 42.375C73.8506 42.2397 73.6476 42.1137 73.3396 41.997C73.0362 41.8803 72.5766 41.7403 71.9606 41.577C71.2932 41.3997 70.7682 41.2153 70.3856 41.024C70.0029 40.8327 69.7299 40.611 69.5666 40.359C69.4032 40.107 69.3216 39.8013 69.3216 39.442C69.3216 38.9987 69.4429 38.6113 69.6856 38.28C69.9282 37.944 70.2666 37.685 70.7006 37.503C71.1392 37.321 71.6479 37.23 72.2266 37.23C72.8006 37.23 73.3139 37.321 73.7666 37.503C74.2192 37.685 74.5832 37.944 74.8586 38.28C75.1386 38.6113 75.3042 38.9987 75.3556 39.442L74.0816 39.673C74.0209 39.267 73.8249 38.9473 73.4936 38.714C73.1669 38.4807 72.7446 38.357 72.2266 38.343C71.7366 38.3243 71.3376 38.4107 71.0296 38.602C70.7262 38.7933 70.5746 39.0523 70.5746 39.379C70.5746 39.561 70.6306 39.7173 70.7426 39.848C70.8592 39.9787 71.0716 40.1023 71.3796 40.219C71.6922 40.3357 72.1449 40.4687 72.7376 40.618C73.4049 40.786 73.9299 40.9703 74.3126 41.171C74.6999 41.367 74.9752 41.6003 75.1386 41.871C75.3019 42.137 75.3836 42.466 75.3836 42.858C75.3836 43.586 75.1152 44.16 74.5786 44.58C74.0419 44.9953 73.3022 45.203 72.3596 45.203Z" />
    </svg>
  );
}

export function FillLike(svgProps) {
  return (
    <svg
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <g id="SVGRepo_bgCarrier" strokeWidth={0} />
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <g id="SVGRepo_iconCarrier">
        <path d="M2 9.1371C2 14 6.01943 16.5914 8.96173 18.9109C10 19.7294 11 20.5 12 20.5C13 20.5 14 19.7294 15.0383 18.9109C17.9806 16.5914 22 14 22 9.1371C22 4.27416 16.4998 0.825464 12 5.50063C7.50016 0.825464 2 4.27416 2 9.1371Z" />
      </g>
    </svg>
  );
}

export function Like(svgProps) {
  return (
    <svg
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <g id="SVGRepo_bgCarrier" strokeWidth={0} />
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <g id="SVGRepo_iconCarrier">
        <path
          d="M15.7 4C18.87 4 21 6.98 21 9.76C21 15.39 12.16 20 12 20C11.84 20 3 15.39 3 9.76C3 6.98 5.13 4 8.3 4C10.12 4 11.31 4.91 12 5.71C12.69 4.91 13.88 4 15.7 4Z"
          // stroke="#00aae3"
          strokeWidth={2}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </svg>
  );
}

export function FillComment(svgProps) {
  return (
    <svg
      width="24"
      height="24"
      xmlns="http://www.w3.org/2000/svg"
      fillRule="evenodd"
      clipRule="evenodd"
      {...svgProps}
    >
      <path d="M20 15c0 .552-.448 1-1 1s-1-.448-1-1 .448-1 1-1 1 .448 1 1m-3 0c0 .552-.448 1-1 1s-1-.448-1-1 .448-1 1-1 1 .448 1 1m-3 0c0 .552-.448 1-1 1s-1-.448-1-1 .448-1 1-1 1 .448 1 1m5.415 4.946c-1 .256-1.989.482-3.324.482-3.465 0-7.091-2.065-7.091-5.423 0-3.128 3.14-5.672 7-5.672 3.844 0 7 2.542 7 5.672 0 1.591-.646 2.527-1.481 3.527l.839 2.686-2.943-1.272zm-13.373-3.375l-4.389 1.896 1.256-4.012c-1.121-1.341-1.909-2.665-1.909-4.699 0-4.277 4.262-7.756 9.5-7.756 5.018 0 9.128 3.194 9.467 7.222-1.19-.566-2.551-.889-3.967-.889-4.199 0-8 2.797-8 6.672 0 .712.147 1.4.411 2.049-.953-.126-1.546-.272-2.369-.483m17.958-1.566c0-2.172-1.199-4.015-3.002-5.21l.002-.039c0-5.086-4.988-8.756-10.5-8.756-5.546 0-10.5 3.698-10.5 8.756 0 1.794.646 3.556 1.791 4.922l-1.744 5.572 6.078-2.625c.982.253 1.932.407 2.85.489 1.317 1.953 3.876 3.314 7.116 3.314 1.019 0 2.105-.135 3.242-.428l4.631 2-1.328-4.245c.871-1.042 1.364-2.384 1.364-3.75" />
    </svg>
  );
}

export function Comment(svgProps) {
  return (
    <svg
      width="24"
      height="24"
      xmlns="http://www.w3.org/2000/svg"
      fillRule="evenodd"
      clipRule="evenodd"
      {...svgProps}
    >
      <path d="M20 15c0 .552-.448 1-1 1s-1-.448-1-1 .448-1 1-1 1 .448 1 1m-3 0c0 .552-.448 1-1 1s-1-.448-1-1 .448-1 1-1 1 .448 1 1m-3 0c0 .552-.448 1-1 1s-1-.448-1-1 .448-1 1-1 1 .448 1 1m5.415 4.946c-1 .256-1.989.482-3.324.482-3.465 0-7.091-2.065-7.091-5.423 0-3.128 3.14-5.672 7-5.672 3.844 0 7 2.542 7 5.672 0 1.591-.646 2.527-1.481 3.527l.839 2.686-2.943-1.272zm-13.373-3.375l-4.389 1.896 1.256-4.012c-1.121-1.341-1.909-2.665-1.909-4.699 0-4.277 4.262-7.756 9.5-7.756 5.018 0 9.128 3.194 9.467 7.222-1.19-.566-2.551-.889-3.967-.889-4.199 0-8 2.797-8 6.672 0 .712.147 1.4.411 2.049-.953-.126-1.546-.272-2.369-.483m17.958-1.566c0-2.172-1.199-4.015-3.002-5.21l.002-.039c0-5.086-4.988-8.756-10.5-8.756-5.546 0-10.5 3.698-10.5 8.756 0 1.794.646 3.556 1.791 4.922l-1.744 5.572 6.078-2.625c.982.253 1.932.407 2.85.489 1.317 1.953 3.876 3.314 7.116 3.314 1.019 0 2.105-.135 3.242-.428l4.631 2-1.328-4.245c.871-1.042 1.364-2.384 1.364-3.75" />
    </svg>
  );
}

export function BookMark(svgProps) {
  return (
    <svg
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <g id="SVGRepo_bgCarrier" strokeWidth={0} />
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <g id="SVGRepo_iconCarrier">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M3.5 3C3.5 1.89543 4.39543 1 5.5 1H18.5C19.6046 1 20.5 1.89543 20.5 3V22C20.5 22.3612 20.3052 22.6944 19.9904 22.8715C19.6756 23.0486 19.2897 23.0422 18.981 22.8548L12 18.6157L5.01903 22.8548C4.71028 23.0422 4.32441 23.0486 4.00961 22.8715C3.6948 22.6944 3.5 22.3612 3.5 22V3ZM18.5 3L5.5 3V20.2228L11.481 16.591C11.7999 16.3974 12.2001 16.3974 12.519 16.591L18.5 20.2228V3Z"
        />
      </g>
    </svg>
  );
}

export function FillBookMark(svgProps) {
  return (
    <svg
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <g id="SVGRepo_bgCarrier" strokeWidth={0} />
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <g id="SVGRepo_iconCarrier">
        <path d="M5.5 1C4.39543 1 3.5 1.89543 3.5 3V22C3.5 22.3612 3.6948 22.6944 4.00961 22.8715C4.32441 23.0486 4.71028 23.0422 5.01903 22.8548L12 18.6157L18.981 22.8548C19.2897 23.0422 19.6756 23.0486 19.9904 22.8715C20.3052 22.6944 20.5 22.3612 20.5 22V3C20.5 1.89543 19.6046 1 18.5 1H5.5Z" />
      </g>
    </svg>
  );
}

export function ListIconAbout(svgProps) {
  return (
    <svg
      version="1.1"
      id="Capa_1"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      viewBox="0 0 480 480"
      xmlSpace="preserve"
      {...svgProps}
    >
      <g id="SVGRepo_bgCarrier" strokeWidth={0} />
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <g id="SVGRepo_iconCarrier">
        <g>
          <g>
            <path d="M487.2,69.7c0,12.9-10.5,23.4-23.4,23.4h-322c-12.9,0-23.4-10.5-23.4-23.4s10.5-23.4,23.4-23.4h322.1 C476.8,46.4,487.2,56.8,487.2,69.7z M463.9,162.3H141.8c-12.9,0-23.4,10.5-23.4,23.4s10.5,23.4,23.4,23.4h322.1 c12.9,0,23.4-10.5,23.4-23.4C487.2,172.8,476.8,162.3,463.9,162.3z M463.9,278.3H141.8c-12.9,0-23.4,10.5-23.4,23.4 s10.5,23.4,23.4,23.4h322.1c12.9,0,23.4-10.5,23.4-23.4C487.2,288.8,476.8,278.3,463.9,278.3z M463.9,394.3H141.8 c-12.9,0-23.4,10.5-23.4,23.4s10.5,23.4,23.4,23.4h322.1c12.9,0,23.4-10.5,23.4-23.4C487.2,404.8,476.8,394.3,463.9,394.3z M38.9,30.8C17.4,30.8,0,48.2,0,69.7s17.4,39,38.9,39s38.9-17.5,38.9-39S60.4,30.8,38.9,30.8z M38.9,146.8 C17.4,146.8,0,164.2,0,185.7s17.4,38.9,38.9,38.9s38.9-17.4,38.9-38.9S60.4,146.8,38.9,146.8z M38.9,262.8 C17.4,262.8,0,280.2,0,301.7s17.4,38.9,38.9,38.9s38.9-17.4,38.9-38.9S60.4,262.8,38.9,262.8z M38.9,378.7 C17.4,378.7,0,396.1,0,417.6s17.4,38.9,38.9,38.9s38.9-17.4,38.9-38.9C77.8,396.2,60.4,378.7,38.9,378.7z" />
          </g>
        </g>
      </g>
    </svg>
  );
}

export function UmbrellaBeach(svgProps) {
  return (
    <svg
      viewBox="-2 -2 30 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <g id="SVGRepo_bgCarrier" strokeWidth={0} />
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <g id="SVGRepo_iconCarrier">
        <path d="M8.53259 18.1345C8.20862 18.3926 8.15523 18.8644 8.41332 19.1884C8.67142 19.5123 9.14328 19.5657 9.46725 19.3076L8.53259 18.1345ZM13.7289 17.0001V16.2501C13.7225 16.2501 13.7162 16.2501 13.7099 16.2503L13.7289 17.0001ZM23.1912 17.7501C23.6054 17.7501 23.9412 17.4143 23.9412 17.0001C23.9412 16.5859 23.6054 16.2501 23.1912 16.2501V17.7501ZM9.46725 19.3076C10.6865 18.3363 12.1895 17.7893 13.7479 17.7498L13.7099 16.2503C11.825 16.2981 10.0073 16.9596 8.53259 18.1345L9.46725 19.3076ZM13.7289 17.7501H23.1912V16.2501H13.7289V17.7501Z" />
        <path d="M22.0375 7.26015L22.284 7.96848L22.285 7.96813L22.0375 7.26015ZM22.4795 5.72516L23.0656 5.25721L23.0651 5.25651L22.4795 5.72516ZM5.05367 11.7891L4.30369 11.7852L4.30369 11.7859L5.05367 11.7891ZM6.35366 12.7181L6.59958 13.4266L6.60016 13.4264L6.35366 12.7181ZM22.285 7.96813C22.5458 7.87697 22.7809 7.72464 22.9707 7.52391L21.8809 6.49333C21.8557 6.51992 21.8246 6.54009 21.79 6.55217L22.285 7.96813ZM22.9707 7.52391C23.1606 7.32318 23.2995 7.07989 23.3759 6.81442L21.9345 6.39936C21.9244 6.43452 21.906 6.46675 21.8809 6.49333L22.9707 7.52391ZM23.3759 6.81442C23.4524 6.54894 23.4641 6.26901 23.4101 5.99808L21.939 6.29124C21.9462 6.32713 21.9446 6.36421 21.9345 6.39936L23.3759 6.81442ZM23.4101 5.99808C23.3561 5.72715 23.238 5.4731 23.0656 5.25721L21.8934 6.19312C21.9163 6.22171 21.9319 6.25536 21.939 6.29124L23.4101 5.99808ZM23.0651 5.25651C21.7039 3.55587 19.849 2.31893 17.7559 1.71596L17.3407 3.15734C19.1358 3.67446 20.7266 4.7353 21.894 6.19382L23.0651 5.25651ZM17.7559 1.71596C15.6627 1.113 13.4341 1.17364 11.3768 1.88955L11.8698 3.30621C13.6342 2.69223 15.5455 2.64022 17.3407 3.15734L17.7559 1.71596ZM11.3768 1.88955C9.31948 2.60546 7.53466 3.94143 6.26799 5.71358L7.4883 6.58582C8.57464 5.06597 10.1054 3.9202 11.8698 3.30621L11.3768 1.88955ZM6.26799 5.71358C5.00131 7.48573 4.31506 9.60693 4.30369 11.7852L5.80366 11.793C5.81341 9.92487 6.40196 8.10567 7.4883 6.58582L6.26799 5.71358ZM4.30369 11.7859C4.30252 12.0623 4.36763 12.335 4.49355 12.581L5.82881 11.8976C5.81213 11.865 5.80351 11.8289 5.80366 11.7923L4.30369 11.7859ZM4.49355 12.581C4.61947 12.827 4.80254 13.0393 5.0274 13.1999L5.89951 11.9795C5.86973 11.9583 5.84549 11.9302 5.82881 11.8976L4.49355 12.581ZM5.0274 13.1999C5.25226 13.3606 5.51237 13.4651 5.78592 13.5045L5.99998 12.0199C5.96375 12.0147 5.9293 12.0008 5.89951 11.9795L5.0274 13.1999ZM5.78592 13.5045C6.05946 13.544 6.33848 13.5173 6.59958 13.4266L6.10774 12.0096C6.07317 12.0216 6.03621 12.0251 5.99998 12.0199L5.78592 13.5045ZM6.60016 13.4264L22.284 7.96848L21.791 6.55182L6.10717 12.0098L6.60016 13.4264Z" />
        <path d="M14.8925 9.71063C14.7387 9.32604 14.3022 9.13896 13.9177 9.29277C13.5331 9.44659 13.346 9.88305 13.4998 10.2676L14.8925 9.71063ZM16.3038 17.2786C16.4576 17.6632 16.894 17.8502 17.2786 17.6964C17.6632 17.5426 17.8503 17.1061 17.6965 16.7216L16.3038 17.2786ZM13.4998 10.2676L16.3038 17.2786L17.6965 16.7216L14.8925 9.71063L13.4998 10.2676Z" />
        <path d="M0.842114 21.5057C0.431041 21.4548 0.056556 21.7468 0.00567908 22.1579C-0.0451979 22.569 0.246799 22.9435 0.657872 22.9943L0.842114 21.5057ZM5.24995 20.75L5.80663 20.2474C5.67053 20.0967 5.4791 20.0076 5.27611 20.0005C5.07313 19.9934 4.87595 20.069 4.72967 20.2099L5.24995 20.75ZM11.9999 20.75L12.5566 20.2474C12.4144 20.0899 12.2121 20 11.9999 20C11.7877 20 11.5854 20.0899 11.4432 20.2474L11.9999 20.75ZM18.7498 20.75L19.2701 20.2099C19.1238 20.069 18.9267 19.9934 18.7237 20.0005C18.5207 20.0076 18.3293 20.0967 18.1932 20.2474L18.7498 20.75ZM20.8302 22.0089L21.0673 21.2974L20.8302 22.0089ZM23.287 23.0009C23.6985 22.9536 23.9938 22.5818 23.9465 22.1702C23.8993 21.7587 23.5274 21.4634 23.1159 21.5107L23.287 23.0009ZM0.657872 22.9943C1.58314 23.1088 2.5223 23.0153 3.40678 22.7204L2.93244 21.2974C2.25987 21.5216 1.54571 21.5928 0.842114 21.5057L0.657872 22.9943ZM3.40678 22.7204C4.29126 22.4256 5.09874 21.937 5.77024 21.2902L4.72967 20.2099C4.21904 20.7017 3.60502 21.0732 2.93244 21.2974L3.40678 22.7204ZM4.69327 21.2526C5.18991 21.8027 5.79646 22.2425 6.4737 22.5434L7.08288 21.1727C6.59742 20.957 6.16264 20.6418 5.80663 20.2474L4.69327 21.2526ZM6.4737 22.5434C7.15094 22.8444 7.88381 22.9999 8.62492 22.9999V21.5C8.09368 21.5 7.56834 21.3885 7.08288 21.1727L6.4737 22.5434ZM8.62492 22.9999C9.36604 22.9999 10.0989 22.8444 10.7761 22.5434L10.167 21.1727C9.6815 21.3885 9.15617 21.5 8.62492 21.5V22.9999ZM10.7761 22.5434C11.4534 22.2425 12.0599 21.8027 12.5566 21.2526L11.4432 20.2474C11.0872 20.6418 10.6524 20.957 10.167 21.1727L10.7761 22.5434ZM11.4432 21.2526C11.9399 21.8027 12.5464 22.2425 13.2236 22.5434L13.8328 21.1727C13.3474 20.957 12.9126 20.6418 12.5566 20.2474L11.4432 21.2526ZM13.2236 22.5434C13.9009 22.8444 14.6337 22.9999 15.3749 22.9999V21.5C14.8436 21.5 14.3183 21.3885 13.8328 21.1727L13.2236 22.5434ZM15.3749 22.9999C16.116 22.9999 16.8488 22.8444 17.5261 22.5434L16.9169 21.1727C16.4314 21.3885 15.9061 21.5 15.3749 21.5V22.9999ZM17.5261 22.5434C18.2033 22.2425 18.8099 21.8027 19.3065 21.2526L18.1932 20.2474C17.8372 20.6418 17.4024 20.957 16.9169 21.1727L17.5261 22.5434ZM18.2295 21.2902C18.9011 21.937 19.7085 22.4256 20.593 22.7204L21.0673 21.2974C20.3948 21.0732 19.7807 20.7017 19.2701 20.2099L18.2295 21.2902ZM20.593 22.7204C21.46 23.0094 22.3795 23.1051 23.287 23.0009L23.1159 21.5107C22.4258 21.5899 21.7266 21.5172 21.0673 21.2974L20.593 22.7204Z" />
        <path d="M10.9146 2.84426C11.0507 3.23548 11.4782 3.44232 11.8694 3.30625C12.2606 3.17018 12.4675 2.74272 12.3314 2.3515L10.9146 2.84426ZM11.6887 0.503828C11.5527 0.112606 11.1252 -0.094234 10.734 0.0418375C10.3428 0.177909 10.1359 0.605364 10.272 0.996586L11.6887 0.503828ZM12.3314 2.3515L11.6887 0.503828L10.272 0.996586L10.9146 2.84426L12.3314 2.3515Z" />
      </g>
    </svg>
  );
}

export function CameraIcon(svgProps) {
  return (
    <svg
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <g id="SVGRepo_bgCarrier" strokeWidth={0} />
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <g id="SVGRepo_iconCarrier">
        <path
          d="M12 16C13.6569 16 15 14.6569 15 13C15 11.3431 13.6569 10 12 10C10.3431 10 9 11.3431 9 13C9 14.6569 10.3431 16 12 16Z"
          strokeWidth={2}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M3 16.8V9.2C3 8.0799 3 7.51984 3.21799 7.09202C3.40973 6.71569 3.71569 6.40973 4.09202 6.21799C4.51984 6 5.0799 6 6.2 6H7.25464C7.37758 6 7.43905 6 7.49576 5.9935C7.79166 5.95961 8.05705 5.79559 8.21969 5.54609C8.25086 5.49827 8.27836 5.44328 8.33333 5.33333C8.44329 5.11342 8.49827 5.00346 8.56062 4.90782C8.8859 4.40882 9.41668 4.08078 10.0085 4.01299C10.1219 4 10.2448 4 10.4907 4H13.5093C13.7552 4 13.8781 4 13.9915 4.01299C14.5833 4.08078 15.1141 4.40882 15.4394 4.90782C15.5017 5.00345 15.5567 5.11345 15.6667 5.33333C15.7216 5.44329 15.7491 5.49827 15.7803 5.54609C15.943 5.79559 16.2083 5.95961 16.5042 5.9935C16.561 6 16.6224 6 16.7454 6H17.8C18.9201 6 19.4802 6 19.908 6.21799C20.2843 6.40973 20.5903 6.71569 20.782 7.09202C21 7.51984 21 8.0799 21 9.2V16.8C21 17.9201 21 18.4802 20.782 18.908C20.5903 19.2843 20.2843 19.5903 19.908 19.782C19.4802 20 18.9201 20 17.8 20H6.2C5.0799 20 4.51984 20 4.09202 19.782C3.71569 19.5903 3.40973 19.2843 3.21799 18.908C3 18.4802 3 17.9201 3 16.8Z"
          strokeWidth={2}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </svg>
  );
}

export function HourGlass(svgProps) {
  return (
    <svg
      viewBox="-1.5 -1.5 24 24"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <g id="SVGRepo_bgCarrier" strokeWidth={0} />
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <g id="SVGRepo_iconCarrier">
        <path d="M15.6 4.576V2.228C15.6 1.439 13.092 0 10 0 6.907 0 4.4 1.439 4.4 2.228v2.348C4.4 6.717 8.277 8.484 8.277 10c0 1.514-3.877 3.281-3.877 5.422v2.35C4.4 18.56 6.907 20 10 20c3.092 0 5.6-1.44 5.6-2.229v-2.35c0-2.141-3.877-3.908-3.877-5.422 0-1.515 3.877-3.282 3.877-5.423zM5.941 2.328c.696-.439 2-1.082 4.114-1.082 2.113 0 4.006 1.082 4.006 1.082.142.086.698.383.317.609-.838.497-2.478 1.02-4.378 1.02s-3.484-.576-4.324-1.074c-.381-.225.265-.555.265-.555zM10.501 10c0 1.193.996 1.961 2.051 2.986.771.748 1.826 1.773 1.826 2.435v1.328c-.97-.483-3.872-.955-3.872-2.504 0-.783-1.013-.783-1.013 0 0 1.549-2.902 2.021-3.872 2.504v-1.328c0-.662 1.056-1.688 1.826-2.435C8.502 11.961 9.498 11.193 9.498 10s-.996-1.961-2.051-2.986c-.771-.75-1.826-1.775-1.826-2.438l-.046-.998C6.601 4.131 8.227 4.656 10 4.656c1.772 0 3.406-.525 4.433-1.078l-.055.998c0 .662-1.056 1.688-1.826 2.438-1.054 1.025-2.051 1.793-2.051 2.986z" />
      </g>
    </svg>
  );
}

export function BasicDeatilsPhone(svgProps) {
  return (
    <svg
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        d="M18.3282 22.5001C17.4132 22.5001 16.1278 22.1691 14.2032 21.0938C11.8627 19.7813 10.0524 18.5696 7.72457 16.2479C5.48019 14.0049 4.388 12.5527 2.85941 9.77115C1.13254 6.63052 1.42691 4.98427 1.75597 4.28068C2.14785 3.43974 2.72629 2.93677 3.47394 2.43755C3.89861 2.15932 4.34801 1.92081 4.81644 1.72505C4.86332 1.7049 4.90691 1.68568 4.94582 1.66833C5.17785 1.5638 5.52941 1.40583 5.97472 1.57458C6.27191 1.68615 6.53722 1.91443 6.95254 2.32458C7.80425 3.16458 8.96816 5.03537 9.39754 5.95412C9.68582 6.57333 9.8766 6.98208 9.87707 7.44052C9.87707 7.97724 9.60707 8.39115 9.27941 8.83787C9.218 8.92177 9.15707 9.00193 9.098 9.07974C8.74129 9.54849 8.663 9.68396 8.71457 9.92583C8.8191 10.4119 9.59863 11.859 10.8797 13.1372C12.1608 14.4155 13.5661 15.1458 14.0541 15.2499C14.3063 15.3038 14.4446 15.2222 14.9283 14.8529C14.9977 14.7999 15.0689 14.7451 15.1435 14.6902C15.6432 14.3185 16.0378 14.0555 16.5619 14.0555H16.5647C17.0208 14.0555 17.4113 14.2533 18.0582 14.5796C18.9019 15.0052 20.8289 16.1541 21.6741 17.0068C22.0852 17.4211 22.3144 17.6855 22.4264 17.9822C22.5952 18.429 22.4363 18.7791 22.3327 19.0135C22.3153 19.0524 22.2961 19.0951 22.276 19.1424C22.0787 19.61 21.8387 20.0585 21.5593 20.4821C21.061 21.2274 20.5561 21.8044 19.7133 22.1968C19.2805 22.4015 18.8069 22.5052 18.3282 22.5001Z"
        fill="black"
      />
    </svg>
  );
}

export function BasicDetailsDistance(svgProps) {
  return (
    <svg
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        d="M16.78 7.22C16.922 7.36 17 7.551 17 7.75V10.25C17 10.4489 16.921 10.6397 16.7803 10.7803C16.6397 10.921 16.4489 11 16.25 11C16.0511 11 15.8603 10.921 15.7197 10.7803C15.579 10.6397 15.5 10.4489 15.5 10.25V9.56L13.78 11.28C13.7108 11.3516 13.628 11.4087 13.5365 11.448C13.445 11.4872 13.3465 11.5079 13.2469 11.5087C13.1474 11.5095 13.0486 11.4905 12.9565 11.4527C12.8643 11.415 12.7806 11.3592 12.7102 11.2888C12.6398 11.2183 12.5842 11.1346 12.5465 11.0424C12.5088 10.9502 12.4899 10.8514 12.4908 10.7518C12.4917 10.6523 12.5125 10.5539 12.5518 10.4624C12.5912 10.3709 12.6483 10.2882 12.72 10.219L14.44 8.5H13.75C13.5511 8.5 13.3603 8.42098 13.2197 8.28033C13.079 8.13968 13 7.94891 13 7.75C13 7.55109 13.079 7.36032 13.2197 7.21967C13.3603 7.07902 13.5511 7 13.75 7H16.25C16.4488 7.00017 16.6395 7.07931 16.78 7.22ZM7 16.25C7 16.4489 7.07902 16.6397 7.21967 16.7803C7.36032 16.921 7.55109 17 7.75 17H10.251C10.4499 17 10.6407 16.921 10.7813 16.7803C10.922 16.6397 11.001 16.4489 11.001 16.25C11.001 16.0511 10.922 15.8603 10.7813 15.7197C10.6407 15.579 10.4499 15.5 10.251 15.5H9.561L11.281 13.78C11.4176 13.6385 11.4931 13.449 11.4913 13.2523C11.4895 13.0557 11.4105 12.8676 11.2714 12.7286C11.1322 12.5896 10.9441 12.5109 10.7475 12.5092C10.5508 12.5076 10.3614 12.5833 10.22 12.72L8.5 14.438V13.748C8.5 13.5491 8.42098 13.3583 8.28033 13.2177C8.13968 13.077 7.94891 12.998 7.75 12.998C7.55109 12.998 7.36032 13.077 7.21967 13.2177C7.07902 13.3583 7 13.5491 7 13.748V16.25ZM2 6.75C2 6.02065 2.28973 5.32118 2.80546 4.80546C3.32118 4.28973 4.02065 4 4.75 4H19.25C19.9793 4 20.6788 4.28973 21.1945 4.80546C21.7103 5.32118 22 6.02065 22 6.75V17.25C22 17.6111 21.9289 17.9687 21.7907 18.3024C21.6525 18.636 21.4499 18.9392 21.1945 19.1945C20.9392 19.4499 20.636 19.6525 20.3024 19.7907C19.9687 19.9289 19.6111 20 19.25 20H4.75C4.38886 20 4.03127 19.9289 3.69762 19.7907C3.36398 19.6525 3.06082 19.4499 2.80546 19.1945C2.5501 18.9392 2.34753 18.636 2.20933 18.3024C2.07113 17.9687 2 17.6111 2 17.25V6.75ZM4.75 5.5C4.06 5.5 3.5 6.06 3.5 6.75V17.25C3.5 17.94 4.06 18.5 4.75 18.5H19.25C19.94 18.5 20.5 17.94 20.5 17.25V6.75C20.5 6.06 19.94 5.5 19.25 5.5H4.75Z"
        fill="black"
      />
    </svg>
  );
}
export function BasicDetailsFree(svgProps) {
  return (
    <svg
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        d="M16.78 7.22C16.922 7.36 17 7.551 17 7.75V10.25C17 10.4489 16.921 10.6397 16.7803 10.7803C16.6397 10.921 16.4489 11 16.25 11C16.0511 11 15.8603 10.921 15.7197 10.7803C15.579 10.6397 15.5 10.4489 15.5 10.25V9.56L13.78 11.28C13.7108 11.3516 13.628 11.4087 13.5365 11.448C13.445 11.4872 13.3465 11.5079 13.2469 11.5087C13.1474 11.5095 13.0486 11.4905 12.9565 11.4527C12.8643 11.415 12.7806 11.3592 12.7102 11.2888C12.6398 11.2183 12.5842 11.1346 12.5465 11.0424C12.5088 10.9502 12.4899 10.8514 12.4908 10.7518C12.4917 10.6523 12.5125 10.5539 12.5518 10.4624C12.5912 10.3709 12.6483 10.2882 12.72 10.219L14.44 8.5H13.75C13.5511 8.5 13.3603 8.42098 13.2197 8.28033C13.079 8.13968 13 7.94891 13 7.75C13 7.55109 13.079 7.36032 13.2197 7.21967C13.3603 7.07902 13.5511 7 13.75 7H16.25C16.4488 7.00017 16.6395 7.07931 16.78 7.22ZM7 16.25C7 16.4489 7.07902 16.6397 7.21967 16.7803C7.36032 16.921 7.55109 17 7.75 17H10.251C10.4499 17 10.6407 16.921 10.7813 16.7803C10.922 16.6397 11.001 16.4489 11.001 16.25C11.001 16.0511 10.922 15.8603 10.7813 15.7197C10.6407 15.579 10.4499 15.5 10.251 15.5H9.561L11.281 13.78C11.4176 13.6385 11.4931 13.449 11.4913 13.2523C11.4895 13.0557 11.4105 12.8676 11.2714 12.7286C11.1322 12.5896 10.9441 12.5109 10.7475 12.5092C10.5508 12.5076 10.3614 12.5833 10.22 12.72L8.5 14.438V13.748C8.5 13.5491 8.42098 13.3583 8.28033 13.2177C8.13968 13.077 7.94891 12.998 7.75 12.998C7.55109 12.998 7.36032 13.077 7.21967 13.2177C7.07902 13.3583 7 13.5491 7 13.748V16.25ZM2 6.75C2 6.02065 2.28973 5.32118 2.80546 4.80546C3.32118 4.28973 4.02065 4 4.75 4H19.25C19.9793 4 20.6788 4.28973 21.1945 4.80546C21.7103 5.32118 22 6.02065 22 6.75V17.25C22 17.6111 21.9289 17.9687 21.7907 18.3024C21.6525 18.636 21.4499 18.9392 21.1945 19.1945C20.9392 19.4499 20.636 19.6525 20.3024 19.7907C19.9687 19.9289 19.6111 20 19.25 20H4.75C4.38886 20 4.03127 19.9289 3.69762 19.7907C3.36398 19.6525 3.06082 19.4499 2.80546 19.1945C2.5501 18.9392 2.34753 18.636 2.20933 18.3024C2.07113 17.9687 2 17.6111 2 17.25V6.75ZM4.75 5.5C4.06 5.5 3.5 6.06 3.5 6.75V17.25C3.5 17.94 4.06 18.5 4.75 18.5H19.25C19.94 18.5 20.5 17.94 20.5 17.25V6.75C20.5 6.06 19.94 5.5 19.25 5.5H4.75Z"
        fill="black"
      />
    </svg>
  );
}

export function WeatherHumidity(svgProps) {
  return (
    <svg
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <g id="SVGRepo_bgCarrier" strokeWidth={0} />
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <g id="SVGRepo_iconCarrier">
        <path d="M12 21.5C16.1012 21.5 19.5 18.4372 19.5 14.5714C19.5 12.1555 18.2672 9.71249 16.8732 7.70906C15.4698 5.69214 13.8515 4.04821 12.9778 3.21778C12.4263 2.69364 11.5737 2.69364 11.0222 3.21779C10.1485 4.04821 8.53016 5.69214 7.1268 7.70906C5.73282 9.71249 4.5 12.1555 4.5 14.5714C4.5 18.4372 7.8988 21.5 12 21.5Z" />
        <path
          d="M12 18C11.4747 18 10.9546 17.8965 10.4693 17.6955C9.98396 17.4945 9.54301 17.1999 9.17157 16.8284C8.80014 16.457 8.5055 16.016 8.30448 15.5307C8.10346 15.0454 8 14.5253 8 14"
          strokeLinecap="round"
        />
      </g>
    </svg>
  );
}

export function WeatherSpeed(svgProps) {
  return (
    <svg
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <g id="SVGRepo_bgCarrier" strokeWidth={0} />
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <g id="SVGRepo_iconCarrier">
        <path
          d="M8 12H14M5 9H16.5C17.8807 9 19 7.88071 19 6.5C19 5.11929 17.8807 4 16.5 4M4 15H17C18.1046 15 19 15.8954 19 17C19 18.1046 18.1046 19 17 19"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </svg>
  );
}

export function WeatherVisibilty(svgProps) {
  return (
    <svg
      viewBox="0 0 20 20"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      fill="#000000"
      {...svgProps}
    >
      <g id="SVGRepo_bgCarrier" strokeWidth={0} />
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <g id="SVGRepo_iconCarrier">
        <g
          id="Free-Icons"
          stroke="none"
          strokeWidth={1}
          fill="none"
          fillRule="evenodd"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <g
            transform="translate(-451.000000, -526.000000)"
            id="Group"
            stroke="#000000"
            strokeWidth={2}
          >
            <g transform="translate(449.000000, 524.000000)" id="Shape">
              <path d="M12,15.5 C10.0670034,15.5 8.5,13.9329966 8.5,12 C8.5,10.0670034 10.0670034,8.5 12,8.5 C13.9329966,8.5 15.5,10.0670034 15.5,12 C15.5,13.9329966 13.9329966,15.5 12,15.5 Z"></path>
              <line x1={12} y1={19} x2={12} y2={21}></line>
              <line x1={5} y1={12} x2={3} y2={12}></line>
              <line x1={21} y1={12} x2={19} y2={12}></line>
              <line x1={12} y1={3} x2={12} y2={5}></line>
              <line
                x1="7.3016885"
                y1="17.0538957"
                x2="5.88747494"
                y2="18.4681092"
              ></line>
              <line
                x1="7.3016885"
                y1="7.15440072"
                x2="5.88747494"
                y2="5.74018716"
              ></line>
              <line
                x1="18.615397"
                y1="18.4681092"
                x2="17.2011834"
                y2="17.0538957"
              ></line>
              <line
                x1="18.615397"
                y1="5.74018716"
                x2="17.2011834"
                y2="7.15440072"
              ></line>
            </g>
          </g>
        </g>
      </g>
    </svg>
  );
}

export function FlagIcon(svgProps) {
  return (
    <svg
      version="1.1"
      id="Capa_1"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      viewBox="0 0 65.456 65.456"
      xmlSpace="preserve"
      {...svgProps}
    >
      <g id="SVGRepo_bgCarrier" strokeWidth={0} />
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <g id="SVGRepo_iconCarrier">
        <g>
          <path d="M57.427,5.031C53.76,1.646,49.895,0,45.611,0c-5.052,0-9.663,2.314-14.123,4.553c-4.012,2.014-7.801,3.916-11.432,3.916 c-2.742,0-5.203-1.092-7.745-3.438c-0.875-0.808-2.146-1.021-3.238-0.543c-1.023,0.448-1.698,1.425-1.78,2.526 c-0.147,0.354-0.23,0.742-0.23,1.149v54.293c0,1.657,1.343,3,3,3s3-1.343,3-3V44.807c2.222,1.1,4.536,1.66,6.992,1.66 c0,0,0.001,0,0.002,0c5.051-0.001,9.662-2.314,14.122-4.553c4.013-2.014,7.803-3.915,11.434-3.915c2.742,0,5.203,1.092,7.744,3.438 c0.875,0.81,2.146,1.023,3.238,0.544c1.092-0.478,1.797-1.557,1.797-2.748V7.235C58.392,6.397,58.042,5.599,57.427,5.031z M52.392,33.534C50.236,32.506,47.989,32,45.613,32c-5.052,0-9.664,2.314-14.125,4.553c-4.012,2.013-7.801,3.914-11.431,3.915 h-0.001c-2.393,0-4.572-0.833-6.778-2.605V12.934c2.156,1.029,4.403,1.535,6.779,1.535c5.052,0,9.664-2.314,14.123-4.553 C38.192,7.902,41.982,6,45.612,6c2.395,0,4.574,0.833,6.78,2.605V33.534z" />
        </g>
      </g>
    </svg>
  );
}

export const LoaderIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      style={{ margin: "auto", background: "#fff", display: "block" }}
      width="50px"
      height="50px"
      viewBox="0 0 100 100"
      preserveAspectRatio="xMidYMid"
    >
      <circle
        cx={50}
        cy={50}
        fill="none"
        stroke="#00aae3"
        strokeWidth={10}
        r={35}
        strokeDasharray="164.93361431346415 56.97787143782138"
      >
        <animateTransform
          attributeName="transform"
          type="rotate"
          repeatCount="indefinite"
          dur="1s"
          values="0 50 50;360 50 50"
          keyTimes="0;1"
        />
      </circle>
    </svg>
  );
};
export const CheckRoundIcon = () => {
  return (
    <svg
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      width="30px"
      height="30px"
    >
      <g id="SVGRepo_bgCarrier" strokeWidth={0} />
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <g id="SVGRepo_iconCarrier">
        <path
          d="M16.0303 10.0303C16.3232 9.73744 16.3232 9.26256 16.0303 8.96967C15.7374 8.67678 15.2626 8.67678 14.9697 8.96967L10.5 13.4393L9.03033 11.9697C8.73744 11.6768 8.26256 11.6768 7.96967 11.9697C7.67678 12.2626 7.67678 12.7374 7.96967 13.0303L9.96967 15.0303C10.2626 15.3232 10.7374 15.3232 11.0303 15.0303L16.0303 10.0303Z"
          fill="#00aae3"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M12 1.25C6.06294 1.25 1.25 6.06294 1.25 12C1.25 17.9371 6.06294 22.75 12 22.75C17.9371 22.75 22.75 17.9371 22.75 12C22.75 6.06294 17.9371 1.25 12 1.25ZM2.75 12C2.75 6.89137 6.89137 2.75 12 2.75C17.1086 2.75 21.25 6.89137 21.25 12C21.25 17.1086 17.1086 21.25 12 21.25C6.89137 21.25 2.75 17.1086 2.75 12Z"
          fill="#00aae3"
        />
      </g>
    </svg>
  );
};
export const HalfStarIcon = ({
  width = "20",
  height = "20",
  color = "#FF9635",
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="half-star-icon"
    >
      <path
        d="M12.73 2.51001L14.49 6.03001C14.73 6.52002 15.37 6.99001 15.91 7.08001L19.1 7.61001C21.14 7.95001 21.62 9.43001 20.15 10.89L17.67 13.37C17.25 13.79 17.02 14.6 17.15 15.18L17.86 18.25C18.42 20.68 17.13 21.62 14.98 20.35L11.99 18.58C11.45 18.26 10.56 18.26 10.01 18.58L7.02 20.35C4.88 21.62 3.58 20.67 4.14 18.25L4.85 15.18C4.98 14.6 4.75 13.79 4.33 13.37L1.85 10.89C0.390001 9.43001 0.860001 7.95001 2.9 7.61001L6.09 7.08001C6.62 6.99001 7.26 6.52002 7.5 6.03001L9.26 2.51001C10.22 0.600015 11.78 0.600015 12.73 2.51001Z"
        fill="url(#paint0_linear_5067_58)"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <defs>
        <linearGradient
          id="paint0_linear_5067_58"
          x1={1}
          y1={11}
          x2={11}
          y2={11}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset="0.9999" stopColor={color} />
          <stop offset={1} stopColor="#7D8998" stopOpacity={0} />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const FillStarIcon = ({
  width = "21",
  height = "21",
  color = "#FF9635",
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="fill-star-icon"
    >
      <path
        d="M13.73 3.51001L15.49 7.03001C15.73 7.52002 16.37 7.99001 16.91 8.08001L20.1 8.61001C22.14 8.95001 22.62 10.43 21.15 11.89L18.67 14.37C18.25 14.79 18.02 15.6 18.15 16.18L18.86 19.25C19.42 21.68 18.13 22.62 15.98 21.35L12.99 19.58C12.45 19.26 11.56 19.26 11.01 19.58L8.02 21.35C5.88 22.62 4.58 21.67 5.14 19.25L5.85 16.18C5.98 15.6 5.75 14.79 5.33 14.37L2.85 11.89C1.39 10.43 1.86 8.95001 3.9 8.61001L7.09 8.08001C7.62 7.99001 8.26 7.52002 8.5 7.03001L10.26 3.51001C11.22 1.60001 12.78 1.60001 13.73 3.51001Z"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
        fill={color}
      />
    </svg>
  );
};

export const OutlineStar = ({
  width = "21",
  height = "21",
  color = "#292D32",
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="outline-star"
    >
      <path
        d="M12.73 2.51001L14.49 6.03001C14.73 6.52002 15.37 6.99001 15.91 7.08001L19.1 7.61001C21.14 7.95001 21.62 9.43001 20.15 10.89L17.67 13.37C17.25 13.79 17.02 14.6 17.15 15.18L17.86 18.25C18.42 20.68 17.13 21.62 14.98 20.35L11.99 18.58C11.45 18.26 10.56 18.26 10.01 18.58L7.02 20.35C4.88 21.62 3.58 20.67 4.14 18.25L4.85 15.18C4.98 14.6 4.75 13.79 4.33 13.37L1.85 10.89C0.390001 9.43001 0.860001 7.95001 2.9 7.61001L6.09 7.08001C6.62 6.99001 7.26 6.52002 7.5 6.03001L9.26 2.51001C10.22 0.600015 11.78 0.600015 12.73 2.51001Z"
        // fill={color}
        strokeWidth="1.5"
        stroke={color}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const LocationDirectionIcon = (svgProps) => {
  return (
    <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" {...svgProps}>
      <g id="SVGRepo_bgCarrier" strokeWidth={0} />
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <g id="SVGRepo_iconCarrier">
        {" "}
        <path
          fillRule="evenodd"
          d="M13.9439774,2.73116957 L14.0855382,2.86385819 L21.1361418,9.91446183 C22.2879527,11.0662728 22.2879527,12.9337272 21.1361418,14.0855382 L14.0855382,21.1361418 C12.9337272,22.2879527 11.0662728,22.2879527 9.91446183,21.1361418 L2.86385819,14.0855382 C1.71204727,12.9337272 1.71204727,11.0662728 2.86385819,9.91446183 L9.91446183,2.86385819 C11.0202003,1.75811971 12.7854759,1.71389017 13.9439774,2.73116957 Z M11.3286754,4.27807176 L4.27807176,11.3286754 C3.90730941,11.6994377 3.90730941,12.3005623 4.27807176,12.6713246 L11.3286754,19.7219282 C11.6994377,20.0926906 12.3005623,20.0926906 12.6713246,19.7219282 L19.7219282,12.6713246 C20.0926906,12.3005623 20.0926906,11.6994377 19.7219282,11.3286754 L12.6864633,4.29321048 L12.6243321,4.23401232 C12.2514817,3.9066126 11.6827715,3.92397563 11.3286754,4.27807176 Z M13.6128994,9.20970461 L13.7071068,9.29289322 L15.7071068,11.2928932 C16.0675907,11.6533772 16.0953203,12.2206082 15.7902954,12.6128994 L15.7071068,12.7071068 L13.7071068,14.7071068 C13.3165825,15.0976311 12.6834175,15.0976311 12.2928932,14.7071068 C11.9324093,14.3466228 11.9046797,13.7793918 12.2097046,13.3871006 L12.2928932,13.2928932 L12.584,13 L10,13 L10,14 C10,14.5522847 9.55228475,15 9,15 C8.48716416,15 8.06449284,14.6139598 8.00672773,14.1166211 L8,14 L8,12 C8,11.4871642 8.38604019,11.0644928 8.88337887,11.0067277 L9,11 L12.585,11 L12.2928932,10.7071068 C11.9324093,10.3466228 11.9046797,9.77939176 12.2097046,9.38710056 L12.2928932,9.29289322 C12.6533772,8.93240926 13.2206082,8.90467972 13.6128994,9.20970461 Z"
        />{" "}
      </g>
    </svg>
  );
};

export const EyeIcon = (svgProps) => {
  return (
    <svg
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <g id="SVGRepo_bgCarrier" strokeWidth={0} />
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <g id="SVGRepo_iconCarrier">
        {" "}
        <path
          d="M15.0007 12C15.0007 13.6569 13.6576 15 12.0007 15C10.3439 15 9.00073 13.6569 9.00073 12C9.00073 10.3431 10.3439 9 12.0007 9C13.6576 9 15.0007 10.3431 15.0007 12Z"
          stroke={svgProps?.stroke}
          strokeWidth={2}
          strokeLinecap="round"
          strokeLinejoin="round"
        />{" "}
        <path
          d="M12.0012 5C7.52354 5 3.73326 7.94288 2.45898 12C3.73324 16.0571 7.52354 19 12.0012 19C16.4788 19 20.2691 16.0571 21.5434 12C20.2691 7.94291 16.4788 5 12.0012 5Z"
          stroke={svgProps?.stroke}
          strokeWidth={2}
          strokeLinecap="round"
          strokeLinejoin="round"
        />{" "}
      </g>
    </svg>
  );
};
export const ReviewWriteIcon = (svgProps) => {
  return (
    <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" {...svgProps}>
      <g id="SVGRepo_bgCarrier" strokeWidth={0} />
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <g id="SVGRepo_iconCarrier">
        <path d="M2,21h8a1,1,0,0,0,0-2H3.071A7.011,7.011,0,0,1,10,13a5.044,5.044,0,1,0-3.377-1.337A9.01,9.01,0,0,0,1,20,1,1,0,0,0,2,21ZM10,5A3,3,0,1,1,7,8,3,3,0,0,1,10,5ZM20,15l3,.438L20.5,17.5l.781,3.5L18.5,19l-2.781,2,.781-3.5L14,15.438,17,15l1.5-3Z" />
      </g>
    </svg>
  );
};

// basic-detail-icons

export const ClockIcon = (svgProps) => {
  return (
    <svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" {...svgProps}>
      <path
        d="M1 10C1 11.1819 1.23279 12.3522 1.68508 13.4442C2.13738 14.5361 2.80031 15.5282 3.63604 16.364C4.47177 17.1997 5.46392 17.8626 6.55585 18.3149C7.64778 18.7672 8.8181 19 10 19C11.1819 19 12.3522 18.7672 13.4442 18.3149C14.5361 17.8626 15.5282 17.1997 16.364 16.364C17.1997 15.5282 17.8626 14.5361 18.3149 13.4442C18.7672 12.3522 19 11.1819 19 10C19 7.61305 18.0518 5.32387 16.364 3.63604C14.6761 1.94821 12.3869 1 10 1C7.61305 1 5.32387 1.94821 3.63604 3.63604C1.94821 5.32387 1 7.61305 1 10Z"
        stroke="black"
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10 5V10L13 13"
        stroke="black"
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const OfferIcon = (svgProps) => {
  return (
    <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" {...svgProps}>
      <path
        d="M8.27313 9.00146C8.12394 9.00146 7.98087 9.06073 7.87538 9.16622C7.76989 9.27171 7.71063 9.41478 7.71063 9.56396V14.484C7.71063 14.6331 7.76989 14.7762 7.87538 14.8817C7.98087 14.9872 8.12394 15.0465 8.27313 15.0465C8.42231 15.0465 8.56539 14.9872 8.67088 14.8817C8.77637 14.7762 8.83563 14.6331 8.83563 14.484V13.0102C8.83563 12.9277 8.93388 12.8857 8.99313 12.9427L10.9851 14.8125C11.039 14.863 11.1023 14.9025 11.1714 14.9286C11.2406 14.9547 11.3142 14.967 11.388 14.9646C11.4619 14.9623 11.5346 14.9454 11.6019 14.915C11.6692 14.8846 11.7299 14.8412 11.7805 14.7873C11.8311 14.7335 11.8705 14.6702 11.8967 14.601C11.9228 14.5319 11.935 14.4583 11.9327 14.3844C11.9303 14.3106 11.9135 14.2379 11.8831 14.1706C11.8526 14.1032 11.8093 14.0425 11.7554 13.992L10.4159 12.7342C10.3581 12.6802 10.3896 12.5805 10.4669 12.5617C10.8951 12.4569 11.2703 12.1991 11.5216 11.8369C11.7729 11.4746 11.883 11.033 11.8312 10.5951C11.7793 10.1573 11.5691 9.75354 11.2402 9.45998C10.9112 9.16642 10.4863 9.00334 10.0454 9.00146H8.27313ZM8.92938 11.4862C8.90451 11.4862 8.88067 11.4763 8.86309 11.4588C8.84551 11.4412 8.83563 11.4173 8.83563 11.3925V10.2202C8.83563 10.1685 8.87763 10.1265 8.92938 10.1265H10.0454C10.1366 10.1234 10.2274 10.1388 10.3126 10.1716C10.3977 10.2044 10.4753 10.254 10.5409 10.3174C10.6065 10.3808 10.6586 10.4568 10.6942 10.5408C10.7299 10.6248 10.7482 10.7151 10.7482 10.8063C10.7482 10.8976 10.7299 10.9879 10.6942 11.0719C10.6586 11.1559 10.6065 11.2318 10.5409 11.2953C10.4753 11.3587 10.3977 11.4083 10.3126 11.4411C10.2274 11.4739 10.1366 11.4892 10.0454 11.4862H8.92938ZM3.08838 9.62771C3.08838 9.47853 3.14764 9.33546 3.25313 9.22997C3.35862 9.12448 3.50169 9.06521 3.65088 9.06521H6.37563C6.52481 9.06521 6.66789 9.12448 6.77338 9.22997C6.87887 9.33546 6.93813 9.47853 6.93813 9.62771C6.93813 9.7769 6.87887 9.91997 6.77338 10.0255C6.66789 10.131 6.52481 10.1902 6.37563 10.1902H4.30713C4.28226 10.1902 4.25842 10.2001 4.24084 10.2177C4.22326 10.2353 4.21338 10.2591 4.21338 10.284V11.4667C4.21338 11.5192 4.25538 11.5605 4.30713 11.5605H6.33888C6.48806 11.5605 6.63114 11.6197 6.73663 11.7252C6.84212 11.8307 6.90138 11.9738 6.90138 12.123C6.90138 12.2721 6.84212 12.4152 6.73663 12.5207C6.63114 12.6262 6.48806 12.6855 6.33888 12.6855H4.30713C4.28226 12.6855 4.25842 12.6953 4.24084 12.7129C4.22326 12.7305 4.21338 12.7544 4.21338 12.7792V14.4495C4.21338 14.5986 4.15412 14.7417 4.04863 14.8472C3.94314 14.9527 3.80006 15.012 3.65088 15.012C3.50169 15.012 3.35862 14.9527 3.25313 14.8472C3.14764 14.7417 3.08838 14.5986 3.08838 14.4495V9.62771ZM13.1759 9.06521C13.0267 9.06521 12.8836 9.12448 12.7781 9.22997C12.6726 9.33546 12.6134 9.47853 12.6134 9.62771V14.4502C12.6134 14.7607 12.8646 15.0127 13.1759 15.0127H15.8999C16.0491 15.0127 16.1921 14.9535 16.2976 14.848C16.4031 14.7425 16.4624 14.5994 16.4624 14.4502C16.4624 14.301 16.4031 14.158 16.2976 14.0525C16.1921 13.947 16.0491 13.8877 15.8999 13.8877H13.8321C13.8198 13.8877 13.8075 13.8853 13.7961 13.8805C13.7847 13.8757 13.7743 13.8688 13.7656 13.86C13.7569 13.8512 13.75 13.8408 13.7453 13.8293C13.7406 13.8179 13.7383 13.8056 13.7384 13.7932V12.78C13.7384 12.7282 13.7804 12.6862 13.8321 12.6862H15.8631C16.0123 12.6862 16.1554 12.627 16.2609 12.5215C16.3664 12.416 16.4256 12.2729 16.4256 12.1237C16.4256 11.9745 16.3664 11.8315 16.2609 11.726C16.1554 11.6205 16.0123 11.5612 15.8631 11.5612H13.8321C13.8073 11.5612 13.7834 11.5513 13.7658 11.5338C13.7483 11.5162 13.7384 11.4923 13.7384 11.4675V10.2847C13.7384 10.2322 13.7804 10.191 13.8321 10.191H15.8999C16.0491 10.191 16.1921 10.1317 16.2976 10.0262C16.4031 9.92072 16.4624 9.77765 16.4624 9.62847C16.4624 9.47928 16.4031 9.33621 16.2976 9.23072C16.1921 9.12523 16.0491 9.06597 15.8999 9.06597L13.1759 9.06521ZM17.8101 9.06521C17.6609 9.06521 17.5179 9.12448 17.4124 9.22997C17.3069 9.33546 17.2476 9.47853 17.2476 9.62771V14.4502C17.2476 14.7607 17.4996 15.0127 17.8101 15.0127H20.5349C20.6841 15.0127 20.8271 14.9535 20.9326 14.848C21.0381 14.7425 21.0974 14.5994 21.0974 14.4502C21.0974 14.301 21.0381 14.158 20.9326 14.0525C20.8271 13.947 20.6841 13.8877 20.5349 13.8877H18.4664C18.454 13.8877 18.4418 13.8853 18.4303 13.8805C18.4189 13.8757 18.4085 13.8688 18.3998 13.86C18.3911 13.8512 18.3842 13.8408 18.3795 13.8293C18.3749 13.8179 18.3725 13.8056 18.3726 13.7932V12.78C18.3726 12.7282 18.4146 12.6862 18.4664 12.6862H20.4981C20.6473 12.6862 20.7904 12.627 20.8959 12.5215C21.0014 12.416 21.0606 12.2729 21.0606 12.1237C21.0606 11.9745 21.0014 11.8315 20.8959 11.726C20.7904 11.6205 20.6473 11.5612 20.4981 11.5612H18.4664C18.4415 11.5612 18.4177 11.5513 18.4001 11.5338C18.3825 11.5162 18.3726 11.4923 18.3726 11.4675V10.2847C18.3726 10.2322 18.4146 10.191 18.4664 10.191H20.5349C20.6841 10.191 20.8271 10.1317 20.9326 10.0262C21.0381 9.92072 21.0974 9.77765 21.0974 9.62847C21.0974 9.47928 21.0381 9.33621 20.9326 9.23072C20.8271 9.12523 20.6841 9.06597 20.5349 9.06597L17.8101 9.06521Z"
        fill="black"
      />
      <path
        d="M4.5 0.75C4.00754 0.75 3.51991 0.846997 3.06494 1.03545C2.60997 1.22391 2.19657 1.50013 1.84835 1.84835C1.14509 2.55161 0.75 3.50544 0.75 4.5V19.5C0.75 20.4946 1.14509 21.4484 1.84835 22.1517C2.19657 22.4999 2.60997 22.7761 3.06494 22.9645C3.51991 23.153 4.00754 23.25 4.5 23.25H19.5C20.4946 23.25 21.4484 22.8549 22.1517 22.1517C22.8549 21.4484 23.25 20.4946 23.25 19.5V4.5C23.25 4.00754 23.153 3.51991 22.9645 3.06494C22.7761 2.60997 22.4999 2.19657 22.1517 1.84835C21.8034 1.50013 21.39 1.22391 20.9351 1.03545C20.4801 0.846997 19.9925 0.75 19.5 0.75H4.5ZM2.25 4.5C2.25 3.90326 2.48705 3.33097 2.90901 2.90901C3.33097 2.48705 3.90326 2.25 4.5 2.25H19.5C20.0967 2.25 20.669 2.48705 21.091 2.90901C21.5129 3.33097 21.75 3.90326 21.75 4.5V19.5C21.75 20.0967 21.5129 20.669 21.091 21.091C20.669 21.5129 20.0967 21.75 19.5 21.75H4.5C3.90326 21.75 3.33097 21.5129 2.90901 21.091C2.48705 20.669 2.25 20.0967 2.25 19.5V4.5Z"
        fill="black"
      />
    </svg>
  );
};

export const ParkingIcon = (svgProps) => {
  return (
    <svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" {...svgProps}>
      <path
        d="M9.99992 18.3332C9.99992 18.3332 16.6666 13.3332 16.6666 7.9165C16.6666 4.46484 13.682 1.6665 9.99992 1.6665C6.31784 1.6665 3.33325 4.46484 3.33325 7.9165C3.33325 13.3332 9.99992 18.3332 9.99992 18.3332Z"
        stroke="black"
        strokeWidth={2}
        strokeLinejoin="round"
      />
      <path
        d="M8.75 5.83301V12.4997"
        stroke="black"
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.75 5.83301H11.25C11.692 5.83301 12.116 6.0086 12.4285 6.32116C12.7411 6.63372 12.9167 7.05765 12.9167 7.49967C12.9167 7.9417 12.7411 8.36562 12.4285 8.67819C12.116 8.99075 11.692 9.16634 11.25 9.16634H8.75V5.83301Z"
        stroke="black"
        strokeWidth={2}
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const PhoneIcon = (svgProps) => {
  return (
    <svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" {...svgProps}>
      <path
        d="M9.99992 18.3332C9.99992 18.3332 16.6666 13.3332 16.6666 7.9165C16.6666 4.46484 13.682 1.6665 9.99992 1.6665C6.31784 1.6665 3.33325 4.46484 3.33325 7.9165C3.33325 13.3332 9.99992 18.3332 9.99992 18.3332Z"
        stroke="black"
        strokeWidth={2}
        strokeLinejoin="round"
      />
      <path
        d="M8.75 5.83301V12.4997"
        stroke="black"
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.75 5.83301H11.25C11.692 5.83301 12.116 6.0086 12.4285 6.32116C12.7411 6.63372 12.9167 7.05765 12.9167 7.49967C12.9167 7.9417 12.7411 8.36562 12.4285 8.67819C12.116 8.99075 11.692 9.16634 11.25 9.16634H8.75V5.83301Z"
        stroke="black"
        strokeWidth={2}
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const PinIcon = (svgProps) => {
  return (
    <svg viewBox="0 0 18 18" xmlns="http://www.w3.org/2000/svg" {...svgProps}>
      <g clipPath="url(#clip0_1242_12678)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M9 1.5C10.7902 1.5 12.5071 2.21116 13.773 3.47703C15.0388 4.7429 15.75 6.45979 15.75 8.25C15.75 10.5555 14.493 12.4425 13.1685 13.7963C12.5066 14.4652 11.7846 15.0717 11.0115 15.6083L10.692 15.8258L10.542 15.9255L10.2592 16.1055L10.0073 16.2593L9.69525 16.4408C9.48338 16.5613 9.24379 16.6248 9 16.6248C8.75621 16.6248 8.51662 16.5613 8.30475 16.4408L7.99275 16.2593L7.60275 16.0193L7.45875 15.9255L7.15125 15.7208C6.31723 15.1562 5.5403 14.5117 4.8315 13.7963C3.507 12.4418 2.25 10.5555 2.25 8.25C2.25 6.45979 2.96116 4.7429 4.22703 3.47703C5.4929 2.21116 7.20979 1.5 9 1.5ZM9 3C7.60761 3 6.27226 3.55312 5.28769 4.53769C4.30312 5.52226 3.75 6.85761 3.75 8.25C3.75 9.9915 4.704 11.52 5.90325 12.747C6.41897 13.2689 6.97631 13.748 7.56975 14.1795L7.91325 14.424C8.02425 14.5013 8.13075 14.5733 8.2335 14.64L8.526 14.8275L8.78325 14.9843L9 15.111L9.34125 14.9093L9.6165 14.7368C9.76275 14.6438 9.92025 14.5395 10.0868 14.424L10.4303 14.1795C11.0237 13.748 11.581 13.2689 12.0968 12.747C13.296 11.5208 14.25 9.9915 14.25 8.25C14.25 6.85761 13.6969 5.52226 12.7123 4.53769C11.7277 3.55312 10.3924 3 9 3ZM9 5.25C9.79565 5.25 10.5587 5.56607 11.1213 6.12868C11.6839 6.69129 12 7.45435 12 8.25C12 9.04565 11.6839 9.80871 11.1213 10.3713C10.5587 10.9339 9.79565 11.25 9 11.25C8.20435 11.25 7.44129 10.9339 6.87868 10.3713C6.31607 9.80871 6 9.04565 6 8.25C6 7.45435 6.31607 6.69129 6.87868 6.12868C7.44129 5.56607 8.20435 5.25 9 5.25ZM9 6.75C8.60218 6.75 8.22064 6.90804 7.93934 7.18934C7.65804 7.47064 7.5 7.85218 7.5 8.25C7.5 8.64783 7.65804 9.02936 7.93934 9.31066C8.22064 9.59196 8.60218 9.75 9 9.75C9.39782 9.75 9.77936 9.59196 10.0607 9.31066C10.342 9.02936 10.5 8.64783 10.5 8.25C10.5 7.85218 10.342 7.47064 10.0607 7.18934C9.77936 6.90804 9.39782 6.75 9 6.75Z"
          fill="#1A1A1A"
        />
      </g>
      <defs>
        <clipPath id="clip0_1242_12678">
          <rect width={18} height={18} fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export const RulerIcon = (svgProps) => {
  return (
    <svg viewBox="0 0 20 16" xmlns="http://www.w3.org/2000/svg" {...svgProps}>
      <path
        d="M14.78 3.22C14.922 3.36 15 3.551 15 3.75V6.25C15 6.44891 14.921 6.63968 14.7803 6.78033C14.6397 6.92098 14.4489 7 14.25 7C14.0511 7 13.8603 6.92098 13.7197 6.78033C13.579 6.63968 13.5 6.44891 13.5 6.25V5.56L11.78 7.28C11.7108 7.3516 11.628 7.4087 11.5365 7.44796C11.445 7.48722 11.3465 7.50787 11.2469 7.50868C11.1474 7.5095 11.0486 7.49048 10.9565 7.45272C10.8643 7.41497 10.7806 7.35924 10.7102 7.28879C10.6398 7.21834 10.5842 7.13457 10.5465 7.04238C10.5088 6.95019 10.4899 6.85142 10.4908 6.75184C10.4917 6.65226 10.5125 6.55385 10.5518 6.46237C10.5912 6.37088 10.6483 6.28815 10.72 6.219L12.44 4.5H11.75C11.5511 4.5 11.3603 4.42098 11.2197 4.28033C11.079 4.13968 11 3.94891 11 3.75C11 3.55109 11.079 3.36032 11.2197 3.21967C11.3603 3.07902 11.5511 3 11.75 3H14.25C14.4488 3.00018 14.6395 3.07931 14.78 3.22ZM5 12.25C5 12.4489 5.07902 12.6397 5.21967 12.7803C5.36032 12.921 5.55109 13 5.75 13H8.251C8.44991 13 8.64068 12.921 8.78133 12.7803C8.92198 12.6397 9.001 12.4489 9.001 12.25C9.001 12.0511 8.92198 11.8603 8.78133 11.7197C8.64068 11.579 8.44991 11.5 8.251 11.5H7.561L9.281 9.78C9.41755 9.63848 9.49306 9.449 9.49126 9.25235C9.48946 9.0557 9.41049 8.86763 9.27137 8.72864C9.13225 8.58965 8.9441 8.51086 8.74745 8.50924C8.5508 8.50763 8.36139 8.58331 8.22 8.72L6.5 10.438V9.748C6.5 9.54909 6.42098 9.35832 6.28033 9.21767C6.13968 9.07702 5.94891 8.998 5.75 8.998C5.55109 8.998 5.36032 9.07702 5.21967 9.21767C5.07902 9.35832 5 9.54909 5 9.748V12.25ZM0 2.75C0 2.02065 0.289731 1.32118 0.805456 0.805456C1.32118 0.289731 2.02065 0 2.75 0H17.25C17.9793 0 18.6788 0.289731 19.1945 0.805456C19.7103 1.32118 20 2.02065 20 2.75V13.25C20 13.6111 19.9289 13.9687 19.7907 14.3024C19.6525 14.636 19.4499 14.9392 19.1945 15.1945C18.9392 15.4499 18.636 15.6525 18.3024 15.7907C17.9687 15.9289 17.6111 16 17.25 16H2.75C2.38886 16 2.03127 15.9289 1.69762 15.7907C1.36398 15.6525 1.06082 15.4499 0.805456 15.1945C0.550095 14.9392 0.347532 14.636 0.209331 14.3024C0.0711308 13.9687 0 13.6111 0 13.25V2.75ZM2.75 1.5C2.06 1.5 1.5 2.06 1.5 2.75V13.25C1.5 13.94 2.06 14.5 2.75 14.5H17.25C17.94 14.5 18.5 13.94 18.5 13.25V2.75C18.5 2.06 17.94 1.5 17.25 1.5H2.75Z"
        fill="black"
      />
    </svg>
  );
};

export const SandIcon = (svgProps) => {
  return (
    <svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" {...svgProps}>
      <path
        d="M10.4633 4.36938e-05V2.92363L14.092 1.46184L10.4633 4.36938e-05ZM9.48655 2.55972C8.29282 2.58342 7.12031 2.69346 6.13636 2.88197C6.25465 2.94464 6.33124 3.0263 6.35868 3.11901C6.37943 3.18964 6.3712 3.26507 6.33445 3.34098C6.2977 3.4169 6.23315 3.49181 6.14449 3.56145C6.05583 3.63108 5.9448 3.69408 5.81773 3.74683C5.69066 3.79958 5.55005 3.84106 5.40392 3.8689C5.2578 3.89674 5.10903 3.91039 4.9661 3.90907C4.82317 3.90776 4.68889 3.8915 4.57091 3.86124C4.45293 3.83098 4.35357 3.78729 4.2785 3.73269C4.20344 3.67808 4.15414 3.61362 4.13341 3.54298C4.13207 3.53756 4.13089 3.53211 4.12989 3.52663C4.06287 3.56423 3.99944 3.60218 3.94398 3.64047C3.63685 3.85259 3.51764 4.04119 3.51764 4.25978C3.51764 4.47837 3.63685 4.66697 3.94398 4.87909C4.25111 5.09121 4.74164 5.29581 5.35422 5.46032C6.57931 5.78934 8.2792 5.96478 9.97491 5.96478C11.6706 5.96478 13.3705 5.78934 14.5956 5.46027C15.2082 5.29581 15.6987 5.09121 16.0058 4.87909C16.313 4.66697 16.4322 4.47837 16.4322 4.25978C16.4322 4.04119 16.313 3.85259 16.0058 3.64047C15.6987 3.42835 15.2082 3.22376 14.5956 3.05924C14.1408 2.9371 13.6198 2.83659 13.0586 2.75803L10.4633 3.80341V4.25978H9.48655V2.55972ZM3.21306 5.40729L1.9686 9.91884C1.99007 9.91815 2.0117 9.91779 2.03344 9.91775C2.13442 9.91859 2.23721 9.92638 2.339 9.94092C2.48684 9.96201 2.63014 9.99697 2.76072 10.0438C2.8913 10.0906 3.0066 10.1484 3.10004 10.2139C3.19347 10.2793 3.26321 10.3511 3.30527 10.4252C3.34734 10.4993 3.3609 10.5743 3.34519 10.6457C3.31345 10.7901 3.16441 10.9115 2.93087 10.9835C2.69733 11.0554 2.39842 11.0719 2.09986 11.0293C1.95635 11.0088 1.81711 10.9751 1.68963 10.9303L0.118184 16.6276C0.146395 16.6268 0.175034 16.6265 0.204028 16.627C0.371953 16.6303 0.548667 16.6559 0.719742 16.7018C0.862209 16.7399 0.997679 16.7913 1.11842 16.8529C1.23915 16.9146 1.34279 16.9853 1.42342 17.061C1.50404 17.1367 1.56008 17.2159 1.58832 17.2941C1.61655 17.3724 1.61645 17.4481 1.588 17.517C1.55958 17.5858 1.50337 17.6465 1.42258 17.6955C1.34179 17.7446 1.23801 17.781 1.11715 17.8027C0.996303 17.8244 0.860749 17.8309 0.718236 17.822C0.575722 17.8131 0.429038 17.7889 0.286562 17.7507C0.186902 17.7239 0.0904739 17.6906 0 17.6518C0.0660921 17.8406 0.197462 18.0177 0.393079 18.199C0.805422 18.5813 1.53846 18.9446 2.49674 19.2367C3.69204 19.6011 5.22486 19.8581 6.88307 20C6.88806 19.278 6.92632 18.2329 7.22216 17.2797C7.3958 16.7201 7.65626 16.1858 8.09585 15.7661C8.53537 15.3463 9.1881 15.0581 9.97491 15.0581C10.7617 15.0581 11.4144 15.3464 11.854 15.7661C12.0207 15.9253 12.1615 16.101 12.2817 16.2883C12.5848 16.2884 12.8753 16.3467 13.0894 16.4503C13.3036 16.554 13.4239 16.6945 13.4239 16.8409C13.4237 16.9476 13.3596 17.0519 13.2395 17.1414C13.1193 17.2308 12.9482 17.3016 12.7468 17.3451C13.0245 18.2805 13.0619 19.2944 13.0668 20C14.725 19.8581 16.2578 19.6011 17.4531 19.2367C17.5345 19.2119 17.6135 19.1865 17.6916 19.1607C17.5709 19.1129 17.4856 19.0434 17.4433 18.9585C17.4097 18.8911 17.404 18.8155 17.4264 18.7361C17.4487 18.6566 17.4988 18.5748 17.5736 18.4953C17.6485 18.4159 17.7467 18.3403 17.8626 18.2729C17.9785 18.2056 18.11 18.1477 18.2493 18.1027C18.4632 18.0337 18.6876 17.9975 18.8931 17.9989C19.0406 17.9999 19.1741 18.0203 19.2844 18.0586C19.3947 18.0968 19.4793 18.1521 19.5323 18.2206C19.5403 18.2134 19.549 18.2062 19.5567 18.199C19.9691 17.8168 20.0995 17.4541 19.9238 16.9586L19.9198 16.9474L17.6832 8.83844C17.5786 8.87015 17.4678 8.89451 17.3545 8.9107C17.056 8.95329 16.757 8.93681 16.5235 8.86487C16.2899 8.79294 16.1409 8.67145 16.1092 8.52712C16.0934 8.45565 16.107 8.38073 16.149 8.30663C16.1911 8.23252 16.2608 8.16069 16.3543 8.09524C16.4477 8.02979 16.563 7.97199 16.6936 7.92515C16.8242 7.87831 16.9675 7.84335 17.1154 7.82226C17.2091 7.80912 17.3036 7.80169 17.3968 7.80014L16.7368 5.40729C16.7057 5.43101 16.6739 5.45412 16.6415 5.47662C16.1889 5.78912 15.5942 6.0217 14.9045 6.20693C13.5249 6.5774 11.752 6.7517 9.97491 6.7517C9.66588 6.7517 9.35708 6.74602 9.05027 6.73539C8.98977 6.85562 8.84756 6.96256 8.6464 7.03909C8.44525 7.11561 8.19679 7.15729 7.94082 7.15744C7.6376 7.15744 7.34681 7.09921 7.1324 6.99556C6.918 6.89191 6.79755 6.75134 6.79755 6.60476C6.79756 6.58648 6.79944 6.56822 6.8032 6.55003C6.17348 6.46412 5.57968 6.35041 5.04535 6.20693C4.35562 6.0217 3.76085 5.78912 3.30835 5.47662C3.27591 5.45412 3.24414 5.43101 3.21306 5.40729ZM15.0096 12.3015C15.1597 12.3015 15.3084 12.3158 15.4471 12.3435C15.5858 12.3713 15.7119 12.412 15.8181 12.4634C15.9242 12.5147 16.0084 12.5756 16.0659 12.6427C16.1233 12.7097 16.1529 12.7816 16.1529 12.8542C16.1529 13.0008 16.0324 13.1413 15.818 13.245C15.6036 13.3486 15.3128 13.4069 15.0096 13.4069C14.7063 13.4069 14.4155 13.3486 14.2011 13.245C13.9867 13.1413 13.8663 13.0008 13.8663 12.8542C13.8662 12.7816 13.8958 12.7097 13.9533 12.6427C14.0107 12.5756 14.0949 12.5147 14.2011 12.4634C14.3073 12.412 14.4333 12.3713 14.572 12.3435C14.7107 12.3158 14.8594 12.3015 15.0096 12.3015Z"
        fill="black"
      />
    </svg>
  );
};

export const ToiletIcon = (svgProps) => {
  return (
    <svg viewBox="0 0 19 18" xmlns="http://www.w3.org/2000/svg" {...svgProps}>
      <path
        d="M3.75 0C2.1 0 0.75 1.35 0.75 3C0.75 3.83475 1.10775 4.58775 1.66425 5.133C1.15397 5.47773 0.735627 5.94189 0.445583 6.48512C0.155539 7.02834 0.00257878 7.6342 0 8.25V12.4688L1.5 13.2188V18H6V13.2188L7.5 12.4688V8.25C7.49742 7.6342 7.34446 7.02834 7.05442 6.48512C6.76437 5.94189 6.34603 5.47773 5.83575 5.133C6.39225 4.58775 6.75 3.8355 6.75 3C6.75 1.35 5.4 0 3.75 0ZM13.5 0C11.85 0 10.5 1.35 10.5 3C10.5 3.864 10.8922 4.632 11.4847 5.1795C10.7775 5.70892 10.2994 6.48861 10.1483 7.359V7.383L9.02325 13.3597L8.83575 14.25H11.25V18H15.75V14.25H18.1642L17.9767 13.359L16.8517 7.383V7.35975C16.7008 6.48909 16.2226 5.7091 15.5153 5.1795C16.1078 4.632 16.5 3.864 16.5 3C16.5 1.35 15.15 0 13.5 0ZM3.75 1.5C4.58775 1.5 5.25 2.16225 5.25 3C5.25 3.83775 4.58775 4.5 3.75 4.5C2.91225 4.5 2.25 3.83775 2.25 3C2.25 2.16225 2.91225 1.5 3.75 1.5ZM13.5 1.5C14.3377 1.5 15 2.16225 15 3C15 3.83775 14.3377 4.5 13.5 4.5C12.6623 4.5 12 3.83775 12 3C12 2.16225 12.6623 1.5 13.5 1.5ZM3.75 6C4.34674 6 4.91903 6.23705 5.34099 6.65901C5.76295 7.08097 6 7.65326 6 8.25V11.5312L4.5 12.2812V16.5H3V12.2812L1.5 11.5312V8.25C1.5 7.65326 1.73705 7.08097 2.15901 6.65901C2.58097 6.23705 3.15326 6 3.75 6ZM13.5 6C14.4225 6 15.2077 6.612 15.375 7.617V7.64025H15.3983L16.3597 12.75H14.25V16.5H12.75V12.75H10.6403L11.6017 7.64025H11.625V7.61775C11.7923 6.612 12.5775 6 13.5 6Z"
        fill="black"
      />
    </svg>
  );
};

export const HomeIcon = (svgProps) => {
  return (
    <svg
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <g id="SVGRepo_bgCarrier" strokeWidth={0} />
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <g id="SVGRepo_iconCarrier">
        <path
          d="M22 22L2 22"
          stroke={svgProps?.stroke}
          strokeWidth="1.5"
          strokeLinecap="round"
        />{" "}
        <path
          d="M2 11L6.06296 7.74968M22 11L13.8741 4.49931C12.7784 3.62279 11.2216 3.62279 10.1259 4.49931L9.34398 5.12486"
          stroke={svgProps?.stroke}
          strokeWidth="1.5"
          strokeLinecap="round"
        />{" "}
        <path
          d="M15.5 5.5V3.5C15.5 3.22386 15.7239 3 16 3H18.5C18.7761 3 19 3.22386 19 3.5V8.5"
          stroke={svgProps?.stroke}
          strokeWidth="1.5"
          strokeLinecap="round"
        />{" "}
        <path
          d="M4 22V9.5"
          stroke={svgProps?.stroke}
          strokeWidth="1.5"
          strokeLinecap="round"
        />{" "}
        <path
          d="M20 9.5V13.5M20 22V17.5"
          stroke={svgProps?.stroke}
          strokeWidth="1.5"
          strokeLinecap="round"
        />{" "}
        <path
          d="M15 22V17C15 15.5858 15 14.8787 14.5607 14.4393C14.1213 14 13.4142 14 12 14C10.5858 14 9.87868 14 9.43934 14.4393M9 22V17"
          stroke={svgProps?.stroke}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />{" "}
        <path
          d="M14 9.5C14 10.6046 13.1046 11.5 12 11.5C10.8954 11.5 10 10.6046 10 9.5C10 8.39543 10.8954 7.5 12 7.5C13.1046 7.5 14 8.39543 14 9.5Z"
          stroke={svgProps?.stroke}
          strokeWidth="1.5"
        />{" "}
      </g>
    </svg>
  );
};

export const MapIcon = (svgProps) => {
  return (
    <svg
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <g id="SVGRepo_bgCarrier" strokeWidth={0} />
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <g id="SVGRepo_iconCarrier">
        <path
          d="M5.7 15C4.03377 15.6353 3 16.5205 3 17.4997C3 19.4329 7.02944 21 12 21C16.9706 21 21 19.4329 21 17.4997C21 16.5205 19.9662 15.6353 18.3 15M12 9H12.01M18 9C18 13.0637 13.5 15 12 18C10.5 15 6 13.0637 6 9C6 5.68629 8.68629 3 12 3C15.3137 3 18 5.68629 18 9ZM13 9C13 9.55228 12.5523 10 12 10C11.4477 10 11 9.55228 11 9C11 8.44772 11.4477 8 12 8C12.5523 8 13 8.44772 13 9Z"
          stroke={svgProps?.stroke}
          strokeWidth={2}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </svg>
  );
};

export const LeftSquareArrowIcon = (svgProps) => {
  return (
    <svg
      width={24}
      height={24}
      viewBox="0 0 25 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.5 17.7143V6.28571C4.5 5.67951 4.74082 5.09812 5.16947 4.66947C5.59812 4.24082 6.17951 4 6.78571 4H18.2143C18.8205 4 19.4019 4.24082 19.8305 4.66947C20.2592 5.09812 20.5 5.67951 20.5 6.28571V17.7143C20.5 18.3205 20.2592 18.9019 19.8305 19.3305C19.4019 19.7592 18.8205 20 18.2143 20H6.78571C6.17951 20 5.59812 19.7592 5.16947 19.3305C4.74082 18.9019 4.5 18.3205 4.5 17.7143Z"
        stroke="#545454"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.78571 17.7143V6.28571C6.78571 5.67951 7.02653 5.09812 7.45518 4.66947C7.88384 4.24082 8.46522 4 9.07143 4H6.78571C5.64286 4 4.5 5.02286 4.5 6.28571V17.7143C4.5 18.9771 5.64286 20 6.78571 20H9.07143C8.46522 20 7.88384 19.7592 7.45518 19.3305C7.02653 18.9019 6.78571 18.3205 6.78571 17.7143Z"
        fill="#545454"
        stroke="#545454"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.5001 15.4286L9.07153 12M9.07153 12L12.5001 8.57141M9.07153 12H18.2144"
        stroke="#545454"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
export const RightSquareArrowIcon = (svgProps) => {
  return (
    <svg
      width={24}
      height={24}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4 17.7143V6.28571C4 5.67951 4.24082 5.09812 4.66947 4.66947C5.09812 4.24082 5.67951 4 6.28571 4H17.7143C18.3205 4 18.9019 4.24082 19.3305 4.66947C19.7592 5.09812 20 5.67951 20 6.28571V17.7143C20 18.3205 19.7592 18.9019 19.3305 19.3305C18.9019 19.7592 18.3205 20 17.7143 20H6.28571C5.67951 20 5.09812 19.7592 4.66947 19.3305C4.24082 18.9019 4 18.3205 4 17.7143Z"
        stroke="#545454"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M17.7143 17.7143V6.28571C17.7143 5.67951 17.4735 5.09812 17.0448 4.66947C16.6162 4.24082 16.0348 4 15.4286 4H17.7143C18.8572 4 20 5.02286 20 6.28571V17.7143C20 18.9771 18.8572 20 17.7143 20H15.4286C16.0348 20 16.6162 19.7592 17.0448 19.3305C17.4735 18.9019 17.7143 18.3205 17.7143 17.7143Z"
        fill="#545454"
        stroke="#545454"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.0001 15.4286L15.4286 12M15.4286 12L12.0001 8.57141M15.4286 12H6.28577"
        stroke="#545454"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const SharkIcon = (props) => {
  return (
    <svg
      stroke="currentColor"
      fill="currentColor"
      strokeWidth={0}
      viewBox="0 0 512 512"
      height="25px"
      width="30px"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path d="M243.5 52.06C203.4 97.42 156.6 131.5 99.8 161.6c11.9 6.1 25.1 9.4 37 11 9.3 1.2 17.8 1.3 23.9 1.1 3.1-.1 5.6-.2 7.3-.4.9-.1 1.6-.2 2-.2.4-.1 1.4-.5-.5.2l5.6 17c-2.1.8-1.9.5-2.6.6-.8.1-1.6.2-2.7.3-2.2.2-5 .4-8.5.5-2.5.1-5.2.1-8.2.1.4 1.7.6 3.4.6 5.3 0 14.2-12.9 24.5-27 24.5s-26.99-10.3-26.99-24.5c0-5.2 1.79-9.9 4.69-13.8-8.05-3-16.06-6.9-23.56-12.1-19.96 9.9-41.1 19.4-63.59 28.8v149.7c11.13 28.4 29.91 50.9 74.31 62.6l-4.6 17.4c-33.24-8.8-55.03-24.7-69.71-43.9V413c9.54 15.5 20.05 27.7 39.76 33.6l-5.1 17.2c-14.8-4.3-25.86-12-34.66-21.2v49.1H236.2c14.1-4.2 21.8-9.7 28.6-16.2 4.2-4 8-8.5 12.4-13.1-44.8-19.7-80.7-31.4-110.1-50.8-32.2-21.2-55.8-52.6-72.94-111.6l-1.3-4.5 2.9-3.6C133.8 244.1 221.1 201.8 287.5 170c-2.3-42.7-14.8-82.1-44-117.94zm-13.3 34.75l18 .68c-.6 17.11-5.3 36.21-23.9 52.01l-11.6-13.8c14.6-12.4 16.9-23.9 17.5-38.89zm267.6 21.69c-7.2 3.7-16.7 8.9-26.7 15.2-20.8 12.9-43.1 30.9-49.8 44.8-7.8 16.3-7.7 49-4.4 76.7 3.4 27.6 9 50.4 9 50.4l1.5 6.1-5.1 3.5c-25.4 17.5-41 35.4-58.1 51.6l-2.2 2-21.2 2.6c1.1 19.8-6.7 37-13 52.8l11.9 2.4c12.9-11.1 22.5-22.7 26.7-34.8l1.4-4 4-1.5c33-12.3 61.4-32.4 85.7-58.4 11-20.9 4.1-45.8 3-73.8l-.1-3.7 37.4-40.7v-91.2zm-371.1 82.1c-5.7 0-9 3.6-9 6.5s3.3 6.5 9 6.5 9-3.6 9-6.5-3.3-6.5-9-6.5zm155.1 3.6c-9.8 4.4-19.6 8.8-29.4 13.1-4.8 2.4-9.5 4.9-14.3 7.4l3.1.5c15.6 2.7 25.6 9.6 33.9 16.3 4.3-13.1 7.3-25.5 6.7-37.3zm114.6 15.7c-9.5 23.4-17.1 47-23 62l-1.4 3.6-3.6 1.4c-24.2 10-52 19-99.1 6.8-6.6 17.4-20.8 29.1-32.5 40.2l7.7 6.5c5.2-2.2 11-4.8 16.7-7.9 7.4-4 13.8-9.1 15.6-11.8l3.3-5.1 5.9 1.2c50 9.4 80.7 3 109.9-14.5 3.2-1.9 6.3-3.9 9.5-6.1-1.8-8.7-4.3-22-6.3-38.9-1.5-12-2.6-24.8-2.7-37.4zm-168.8 10.3c-11.5 6.2-23 12.5-34.1 19l2.1.5c15.2 3.6 25.7 9.7 34.2 16.1 1.3-3.4 2.6-6.7 3.4-9.6 1.6-6.4 1.9-11.3-1.5-18l-4.1-8zm-44.1 25c-11.2 6.8-21.8 13.7-31.6 20.6l6.7.9c10.8 1.3 18.8 6 24.4 11.2.7.7 1.2 1.4 1.9 2 1.9-9.9 2.2-19.7-.1-29.3l-1.3-5.4zm-35.8 23.7c-10.1 7.3-19.1 14.7-26.6 22.1 9.1 4 17.8 9 25.8 15.3 2.8-10 2.8-19.5 1.7-29.7l-.9-7.7zm37.2 90.2c-10.3 4.9-22.3 9.6-35.6 13.5 8.3 9.6 17.5 17.2 27.7 24 1.5 1 3 1.9 4.5 2.8l2-10.1c.8-4.4 1.9-13 1.9-21.1.1-3.2-.2-6.3-.5-9.1zm47.1 21c-11.3 8.3-23.3 15-36.9 19.5l-8.6 2.8c9.7 5.5 20.4 10.5 32.2 15.6l4.1-10.8c3.5-9.2 6.8-16.6 8.4-22.8.4-1.5.6-2.9.8-4.3zm41.4 20.7c-13.5 8.4-27.3 14.3-39.5 18.2l-7.4 2.4c9.7 4 20.2 8.3 31.3 12.9l3.6-8.7c3.6-8.5 9.2-15.8 12-24.8zm36.8 23.7c-14.4 6.8-29 11.9-39.1 13.5l-3.2.5c8 3.4 16.3 7 25.1 11 2.7-.2 4.5-1.1 6.5-2.8 2.4-2.1 4.8-5.8 6.8-10.6 1.5-3.5 2.7-7.5 3.9-11.6z" />
    </svg>
  )
}

export const LineDotFilterIcon = (svgProps) => {
  return (
    <svg
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        d="M21.25 12H8.895M4.534 12H2.75M4.534 12C4.534 11.4218 4.76368 10.8673 5.17251 10.4585C5.58134 10.0497 6.13583 9.82001 6.714 9.82001C7.29217 9.82001 7.84666 10.0497 8.25549 10.4585C8.66432 10.8673 8.894 11.4218 8.894 12C8.894 12.5782 8.66432 13.1327 8.25549 13.5415C7.84666 13.9503 7.29217 14.18 6.714 14.18C6.13583 14.18 5.58134 13.9503 5.17251 13.5415C4.76368 13.1327 4.534 12.5782 4.534 12ZM21.25 18.607H15.502M15.502 18.607C15.502 19.1853 15.2718 19.7404 14.8628 20.1494C14.4539 20.5583 13.8993 20.788 13.321 20.788C12.7428 20.788 12.1883 20.5573 11.7795 20.1485C11.3707 19.7397 11.141 19.1852 11.141 18.607M15.502 18.607C15.502 18.0287 15.2718 17.4746 14.8628 17.0657C14.4539 16.6567 13.8993 16.427 13.321 16.427C12.7428 16.427 12.1883 16.6567 11.7795 17.0655C11.3707 17.4743 11.141 18.0288 11.141 18.607M11.141 18.607H2.75M21.25 5.39301H18.145M13.784 5.39301H2.75M13.784 5.39301C13.784 4.81484 14.0137 4.26035 14.4225 3.85152C14.8313 3.44269 15.3858 3.21301 15.964 3.21301C16.2503 3.21301 16.5338 3.2694 16.7983 3.37896C17.0627 3.48851 17.3031 3.64909 17.5055 3.85152C17.7079 4.05395 17.8685 4.29427 17.9781 4.55876C18.0876 4.82325 18.144 5.10673 18.144 5.39301C18.144 5.67929 18.0876 5.96277 17.9781 6.22726C17.8685 6.49175 17.7079 6.73207 17.5055 6.93451C17.3031 7.13694 17.0627 7.29751 16.7983 7.40707C16.5338 7.51663 16.2503 7.57301 15.964 7.57301C15.3858 7.57301 14.8313 7.34333 14.4225 6.93451C14.0137 6.52568 13.784 5.97118 13.784 5.39301Z"
        strokeWidth="1.5"
        fillRule="evenodd"
        clipRule="evenodd"
        strokeMiterlimit={10}
        strokeLinecap="round"
      />
    </svg>
  );
};

export const DownArrowIcon = (svgProps) => {
  return (
    <svg
      width={17}
      height={16}
      viewBox="0 0 17 16"
      xmlns="http://www.w3.org/2000/svg"
      fill="#1A1A1A"
      {...svgProps}
    >
      <g>
        <path d="M14.5669 5.91655L8.96693 11.5332C8.90026 11.5999 8.82804 11.647 8.75026 11.6746C8.67248 11.7026 8.58915 11.7166 8.50026 11.7166C8.41137 11.7166 8.32804 11.7026 8.25026 11.6746C8.17248 11.647 8.10026 11.5999 8.03359 11.5332L2.41693 5.91655C2.26137 5.761 2.18359 5.56655 2.18359 5.33322C2.18359 5.09989 2.26693 4.89989 2.43359 4.73322C2.60026 4.56655 2.7947 4.48322 3.01693 4.48322C3.23915 4.48322 3.43359 4.56655 3.60026 4.73322L8.50026 9.63322L13.4003 4.73322C13.5558 4.57766 13.7474 4.49989 13.9749 4.49989C14.2029 4.49989 14.4003 4.58322 14.5669 4.74989C14.7336 4.91655 14.8169 5.111 14.8169 5.33322C14.8169 5.55544 14.7336 5.74989 14.5669 5.91655Z" />
      </g>
    </svg>
  );
};

export const UPArrowIcon = (svgProps) => {
  return (
    <svg
      width="13"
      height="13"
      viewBox="0 0 16 10"
      xmlns="http://www.w3.org/2000/svg"
      fill="#1A1A1A"
      {...svgProps}
    >
      <g>
        <path d="M0.314778 8.1297L7.31478 1.10887C7.39811 1.02553 7.48839 0.966644 7.58561 0.932199C7.68283 0.897199 7.787 0.8797 7.89811 0.8797C8.00922 0.8797 8.11339 0.897199 8.21061 0.932199C8.30783 0.966644 8.39811 1.02553 8.48145 1.10887L15.5023 8.1297C15.6967 8.32414 15.7939 8.5672 15.7939 8.85887C15.7939 9.15053 15.6898 9.40053 15.4814 9.60886C15.2731 9.8172 15.0301 9.92136 14.7523 9.92136C14.4745 9.92136 14.2314 9.8172 14.0231 9.60886L7.89811 3.48387L1.77311 9.60886C1.57867 9.80331 1.33922 9.90053 1.05478 9.90053C0.769779 9.90053 0.523111 9.79637 0.314778 9.58803C0.106445 9.3797 0.00227833 9.13664 0.00227833 8.85887C0.00227833 8.58109 0.106445 8.33803 0.314778 8.1297Z" />
      </g>
    </svg>
  );
};

export const CloseCircleIcon = (svgProps) => {
  return (
    <svg
      width={26}
      height={25}
      viewBox="0 0 26 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <rect x="0.799805" width={25} height={25} rx="12.5" fill="#FEF1F1" />
      <path
        d="M17.4665 8.33337L9.13315 16.6667"
        stroke="#EF4343"
        strokeWidth="2.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.13315 8.33337L17.4665 16.6667"
        stroke="#EF4343"
        strokeWidth="2.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const CheckCircleIcon = (svgProps) => {
  return (
    <svg
      width={25}
      height={25}
      viewBox="0 0 25 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <rect width={25} height={25} rx="12.5" fill="#F2FDF5" />
      <path
        d="M18.75 8.75L10.1562 16.25L6.25 12.8409"
        stroke="#16A249"
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const PauseCircleIcon = (svgProps) => {
  return (
    <svg
      width={26}
      height={25}
      viewBox="0 0 26 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <rect x="0.549988" width={25} height={25} rx="12.5" fill="white" />
      <path
        d="M9.9252 17.7083V7.29163"
        stroke="#667384"
        strokeWidth="2.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.1752 17.7083V7.29163"
        stroke="#667384"
        strokeWidth="2.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const CategoryIcon = (svgProps) => {
  return (
    <svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" {...svgProps}>
      <path
        d="M2.16667 6.75C1.70833 6.75 1.31611 6.58694 0.99 6.26083C0.663889 5.93472 0.500556 5.54222 0.5 5.08333V2.16667C0.5 1.70833 0.663333 1.31611 0.99 0.99C1.31667 0.663889 1.70889 0.500556 2.16667 0.5H5.08333C5.54167 0.5 5.93417 0.663333 6.26083 0.99C6.5875 1.31667 6.75056 1.70889 6.75 2.16667V5.08333C6.75 5.54167 6.58694 5.93417 6.26083 6.26083C5.93472 6.5875 5.54222 6.75056 5.08333 6.75H2.16667ZM2.16667 15.5C1.70833 15.5 1.31611 15.3369 0.99 15.0108C0.663889 14.6847 0.500556 14.2922 0.5 13.8333V10.9167C0.5 10.4583 0.663333 10.0661 0.99 9.74C1.31667 9.41389 1.70889 9.25056 2.16667 9.25H5.08333C5.54167 9.25 5.93417 9.41333 6.26083 9.74C6.5875 10.0667 6.75056 10.4589 6.75 10.9167V13.8333C6.75 14.2917 6.58694 14.6842 6.26083 15.0108C5.93472 15.3375 5.54222 15.5006 5.08333 15.5H2.16667ZM10.9167 6.75C10.4583 6.75 10.0661 6.58694 9.74 6.26083C9.41389 5.93472 9.25056 5.54222 9.25 5.08333V2.16667C9.25 1.70833 9.41333 1.31611 9.74 0.99C10.0667 0.663889 10.4589 0.500556 10.9167 0.5H13.8333C14.2917 0.5 14.6842 0.663333 15.0108 0.99C15.3375 1.31667 15.5006 1.70889 15.5 2.16667V5.08333C15.5 5.54167 15.3369 5.93417 15.0108 6.26083C14.6847 6.5875 14.2922 6.75056 13.8333 6.75H10.9167ZM10.9167 15.5C10.4583 15.5 10.0661 15.3369 9.74 15.0108C9.41389 14.6847 9.25056 14.2922 9.25 13.8333V10.9167C9.25 10.4583 9.41333 10.0661 9.74 9.74C10.0667 9.41389 10.4589 9.25056 10.9167 9.25H13.8333C14.2917 9.25 14.6842 9.41333 15.0108 9.74C15.3375 10.0667 15.5006 10.4589 15.5 10.9167V13.8333C15.5 14.2917 15.3369 14.6842 15.0108 15.0108C14.6847 15.3375 14.2922 15.5006 13.8333 15.5H10.9167Z"
        fillRule="evenodd"
        clipRule="evenodd"
      />
    </svg>
  );
};

export const BeachIcon = (svgProps) => {
  return (
    <svg viewBox="0 0 17 17" xmlns="http://www.w3.org/2000/svg" {...svgProps}>
      <path
        d="M13.3925 0.945017C12.1957 0.257418 10.7755 0.0712896 9.44194 0.427277C8.10839 0.783264 6.96987 1.65245 6.275 2.84502L6.085 3.17377C6.04377 3.2449 6.01698 3.32347 6.00616 3.40497C5.99534 3.48647 6.00071 3.56931 6.02196 3.64873C6.04321 3.72816 6.07992 3.80261 6.12999 3.86782C6.18006 3.93303 6.24251 3.98773 6.31375 4.02877L9.62375 5.93127L7.5875 9.49002C5.775 9.72627 4.2375 10.9025 3.2625 12.4825C3.72678 12.378 4.21341 12.454 4.62375 12.695C5.5325 11.4775 6.84875 10.6975 8.26 10.6975C9.67125 10.6975 10.9875 11.4775 11.895 12.6963C12.3057 12.4549 12.7928 12.379 13.2575 12.4838C12.2987 10.9275 10.795 9.76502 9.02 9.50252L10.7063 6.55252L14.2562 8.59252C14.3994 8.67471 14.5693 8.69691 14.7288 8.65429C14.8883 8.61167 15.0244 8.50767 15.1075 8.36502L15.2987 8.03502C15.6413 7.4448 15.8636 6.79266 15.9531 6.11616C16.0426 5.43965 15.9974 4.75212 15.8201 4.09316C15.6428 3.43419 15.3369 2.8168 14.9201 2.27651C14.5032 1.73622 13.9849 1.28371 13.3925 0.945017ZM7.4875 3.26252C7.94386 2.58152 8.59961 2.05827 9.36492 1.76445C10.1302 1.47064 10.9677 1.42063 11.7625 1.62127C11.3304 1.84079 10.9173 2.09584 10.5275 2.38377C9.89875 2.85877 9.4125 3.39627 8.90875 4.08002L7.4875 3.26252ZM11.28 3.38127C11.6275 3.11877 12.0338 2.87627 12.5575 2.62127C12.625 3.12127 12.64 3.55002 12.5875 3.96877C12.5175 4.51877 12.3275 5.09252 11.9275 5.81377L10 4.70502C10.425 4.14252 10.8088 3.73627 11.28 3.38002M14.3337 7.19502L13.0113 6.43502C13.4738 5.60627 13.7325 4.87627 13.8275 4.12377C13.8775 3.72877 13.8788 3.33752 13.8488 2.93627C14.3337 3.5227 14.6378 4.23748 14.7239 4.99359C14.81 5.74971 14.6745 6.51455 14.3337 7.19502ZM4.26875 14.1163C4.22603 13.9877 4.14267 13.8764 4.03123 13.7993C3.91979 13.7222 3.7863 13.6834 3.65089 13.6888C3.51547 13.6941 3.38547 13.7433 3.28047 13.829C3.17547 13.9147 3.10117 14.0322 3.06875 14.1638C2.91125 14.8038 2.62 15.1388 2.30125 15.3325C1.96 15.5388 1.51625 15.6238 1 15.6238C0.83424 15.6238 0.675268 15.6896 0.558058 15.8068C0.440848 15.924 0.375 16.083 0.375 16.2488C0.375 16.4145 0.440848 16.5735 0.558058 16.6907C0.675268 16.8079 0.83424 16.8738 1 16.8738C1.63 16.8738 2.3325 16.7738 2.9475 16.4025C3.2525 16.2175 3.52125 15.9763 3.7425 15.6713C4.94375 17.0213 7.0925 17.0138 8.26125 15.6475C9.45 17.0388 11.6563 17.0225 12.8438 15.5975C13.1694 15.9933 13.578 16.3128 14.0406 16.5333C14.5033 16.7538 15.0087 16.8701 15.5212 16.8738C15.687 16.8738 15.846 16.8079 15.9632 16.6907C16.0804 16.5735 16.1462 16.4145 16.1462 16.2488C16.1462 16.083 16.0804 15.924 15.9632 15.8068C15.846 15.6896 15.687 15.6238 15.5212 15.6238C14.6262 15.6238 13.735 15.0388 13.4412 14.1225C13.4008 13.9973 13.3219 13.888 13.2156 13.8102C13.1094 13.7325 12.9814 13.6902 12.8498 13.6895C12.7181 13.6888 12.5897 13.7297 12.4826 13.8063C12.3756 13.883 12.2955 13.9914 12.2537 14.1163C11.6725 15.8675 9.41625 15.8375 8.85125 14.1913C8.80875 14.0685 8.72902 13.962 8.62316 13.8866C8.5173 13.8112 8.39058 13.7707 8.26062 13.7707C8.13067 13.7707 8.00395 13.8112 7.89809 13.8866C7.79223 13.962 7.7125 14.0685 7.67 14.1913C7.10375 15.8375 4.84875 15.8675 4.26875 14.1163Z"
        fillRule="evenodd"
        clipRule="evenodd"
      />
    </svg>
  );
};

export const ReadBookIcon = (svgProps) => {
  return (
    <svg viewBox="0 0 20 21" xmlns="http://www.w3.org/2000/svg" {...svgProps}>
      <path
        d="M10 9.61328V19.1966M10 9.61328C9.21417 9.19661 6.61417 8.36328 2.5 8.36328V17.5299C4.42875 17.5299 8.62875 17.5299 10 19.1966M10 9.61328C10.7858 9.19661 13.3858 8.36328 17.5 8.36328V17.5299C15.5712 17.5299 11.3712 17.5299 10 19.1966"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.0003 9.19663C11.8413 9.19663 13.3337 7.70425 13.3337 5.8633C13.3337 4.02235 11.8413 2.52997 10.0003 2.52997C8.15938 2.52997 6.66699 4.02235 6.66699 5.8633C6.66699 7.70425 8.15938 9.19663 10.0003 9.19663Z"
        strokeWidth="1.5"
      />
    </svg>
  );
};
/* beach aminity */

export const SurfingIcon = (svgProps) => {
  return (
    <svg
      viewBox="0 0 60 60"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <g clipPath="url(#clip0_1323_2391)">
        <path
          d="M1.17949 52.5085C3.80291 52.5085 5.23599 53.4908 6.28231 54.2079C7.05334 54.7364 7.4053 54.9776 8.38428 54.9776C9.36337 54.9776 9.71533 54.7364 10.4864 54.2079C11.5328 53.4908 12.9658 52.5085 15.5893 52.5085C18.1363 52.5085 19.5611 53.4343 20.5995 54.1445C22.7155 53.6117 24.9125 52.9601 27.1483 52.19C40.5369 47.5782 50.2342 40.4823 48.8076 36.3409C47.8074 33.4371 41.6036 32.7571 33.37 34.1995L30.9152 36.2217L32.8771 43.0371C33.5257 45.2906 32.2247 47.6435 29.971 48.2921C27.7173 48.9409 25.3648 47.6398 24.716 45.3862L22.3239 37.0763C20.7477 37.6118 19.2184 38.1807 17.6709 38.8128L12.8136 49.4011C11.8375 51.5291 9.31832 52.4695 7.18368 51.4901C5.05222 50.5123 4.11689 47.9918 5.09468 45.8603L5.22962 45.5663C1.76334 48.1777 -0.137291 50.7219 0.229883 52.6842C0.524874 52.571 0.844751 52.5085 1.17949 52.5085Z"
          fill="black"
        />
        <path
          d="M45.6857 15.598L37.2439 11.1811C36.9667 11.0362 36.6628 10.9494 36.3508 10.9261C32.4341 10.6711 30.0825 10.5466 28.677 10.4869C27.4009 10.4327 26.1253 10.5823 24.8958 10.9282C24.8748 10.9341 24.8536 10.9402 24.8322 10.9461C22.33 11.6503 20.1213 13.1423 18.5335 15.2006C17.5818 16.4343 16.3136 18.0747 14.607 20.264L7.42724 23.2353C6.25707 23.7196 5.70094 25.0608 6.18524 26.2311C6.66859 27.3993 8.00861 27.9582 9.18102 27.4732L16.924 24.269C17.293 24.1162 17.6157 23.8698 17.8602 23.5541L21.3257 19.078C22.0967 21.6463 22.6471 23.48 23.4933 26.299L14.093 31.1726C13.5431 31.4577 13.1041 31.9183 12.8459 32.4814L6.42786 46.4719C5.78775 47.8672 6.40002 49.5172 7.79536 50.1573C8.17103 50.3297 8.56474 50.4112 8.95267 50.4112C10.0057 50.4112 11.0131 49.8096 11.4809 48.7898L17.5003 35.668L25.6955 31.4192L24.2964 32.5717C23.4413 33.2763 23.0861 34.4213 23.3927 35.486L26.1083 44.9206C26.5394 46.4182 28.1281 47.327 29.6172 46.8676C31.0607 46.4222 31.8876 44.9009 31.4678 43.443L29.2396 35.7022L33.336 32.3329C35.2271 30.7775 36.0272 28.2561 35.3786 25.895C34.2007 21.6069 32.6664 16.0344 32.429 15.2337L35.5379 15.4647L43.5596 19.6617C44.6853 20.2505 46.0693 19.8116 46.6544 18.6928C47.2417 17.5708 46.808 16.1851 45.6857 15.598Z"
          fill="black"
        />
        <path
          d="M22.9352 10.1855C25.3657 9.45587 26.7446 6.89414 26.015 4.46356C25.4177 2.47365 23.592 1.18848 21.6159 1.18848C18.5479 1.18848 16.3262 4.15088 17.2132 7.10562C17.9406 9.52899 20.498 10.9171 22.9352 10.1855Z"
          fill="black"
        />
        <path
          d="M58.8205 53.9829C56.6538 53.9829 55.4879 54.782 54.551 55.424C53.713 55.9984 53.0509 56.4521 51.6149 56.4521C50.1789 56.4521 49.5171 55.9984 48.6789 55.424C47.742 54.782 46.5761 53.9829 44.4093 53.9829C42.2426 53.9829 41.0766 54.782 40.1399 55.424C39.302 55.9984 38.6399 56.4521 37.204 56.4521C35.7681 56.4521 35.1063 55.9984 34.2683 55.4241C33.3315 54.782 32.1656 53.9829 29.999 53.9829C27.8324 53.9829 26.6665 54.782 25.7298 55.4241C24.8919 55.9984 24.23 56.4521 22.7942 56.4521C21.3583 56.4521 20.6965 55.9984 19.8585 55.4241C18.9217 54.782 17.7558 53.9829 15.5892 53.9829C13.4226 53.9829 12.2567 54.782 11.3199 55.4241C10.482 55.9984 9.82007 56.4521 8.38416 56.4521C6.94837 56.4521 6.28656 55.9984 5.44853 55.4241C4.51178 54.782 3.34597 53.9829 1.17937 53.9829C0.528057 53.9829 -0.000118256 54.511 -0.000118256 55.1624C-0.000118256 55.8138 0.528057 56.3419 1.17937 56.3419C2.61516 56.3419 3.27697 56.7955 4.115 57.3698C5.05175 58.0119 6.21756 58.811 8.38416 58.811C10.5508 58.811 11.7167 58.0119 12.6534 57.3698C13.4913 56.7955 14.1533 56.3419 15.5892 56.3419C17.0251 56.3419 17.6869 56.7955 18.5249 57.3698C19.4617 58.0119 20.6276 58.811 22.7942 58.811C24.9608 58.811 26.1267 58.0119 27.0634 57.3698C27.9013 56.7955 28.5632 56.3419 29.999 56.3419C31.4349 56.3419 32.0967 56.7955 32.9347 57.3698C33.8715 58.0119 35.0374 58.811 37.204 58.811C39.3707 58.811 40.5367 58.0119 41.4734 57.3699C42.3113 56.7955 42.9734 56.3419 44.4093 56.3419C45.8453 56.3419 46.5072 56.7955 47.3454 57.3699C48.2823 58.012 49.4482 58.811 51.6149 58.811C53.7817 58.811 54.9477 58.0119 55.8845 57.3699C56.7226 56.7955 57.3845 56.3419 58.8205 56.3419C59.4718 56.3419 60 55.8138 60 55.1624C60 54.511 59.4719 53.9829 58.8205 53.9829Z"
          fill="black"
        />
        <path
          d="M56.0405 27.4167C57.9565 26.0501 57.0518 22.3654 55.4788 22.9672C53.3796 23.7704 51.8826 28.1353 51.1208 30.9405C51.0101 31.3482 51.5531 31.6028 51.7929 31.2549C53.2868 29.0889 54.5247 28.4979 56.0405 27.4167Z"
          fill="black"
        />
        <path
          d="M56.1953 35.8583C57.5922 36.1501 58.862 34.2318 57.947 33.7784C56.7991 33.2096 54.4672 34.1724 52.8932 34.9661C52.61 35.1089 52.7378 35.5381 53.0532 35.505C54.4256 35.3609 55.1662 35.6433 56.1953 35.8583Z"
          fill="black"
        />
      </g>
      <defs>
        <clipPath id="clip0_1323_2391">
          <rect width={60} height={60} fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export const DisabilityIcon = (svgProps) => {
  return (
    <svg
      viewBox="0 0 60 60"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <g clipPath="url(#clip0_1323_2529)">
        <path
          d="M52.7075 55.1337L48.7948 38.1784C48.3586 36.4335 46.7971 35.2151 44.9986 35.2151H37.3959C36.3204 33.4492 34.9181 31.9008 33.2603 30.6553V19.5639C33.2603 16.615 30.1019 14.8116 27.5974 16.0637L19.7719 19.9765C18.4377 20.6433 17.6222 21.9845 17.6222 23.4767V28.4812C17.5979 28.4895 17.5733 28.4922 17.5493 28.5015C11.2478 30.9628 7.17574 36.9257 7.17574 43.6928C7.17574 52.6826 14.4893 59.9962 23.4791 59.9962C32.5295 59.9962 40.1306 52.502 39.7535 43.0407H41.9439L45.1153 57.0328C45.6378 59.119 47.7381 60.4135 49.8612 59.8796C51.953 59.3567 53.2298 57.2277 52.7075 55.1337ZM23.4789 56.0834C16.6467 56.0834 11.0884 50.5249 11.0884 43.6929C11.0884 39.056 13.6176 34.9358 17.6221 32.7948V37.8237C17.6221 39.9814 19.3642 41.7365 21.5218 41.7365C23.6795 41.7365 25.4346 39.9814 25.4346 37.8237V31.4198C25.5314 31.454 25.6289 31.487 25.7333 31.5062C31.6063 32.5849 35.8694 37.7104 35.8694 43.693C35.8693 50.5249 30.311 56.0834 23.4789 56.0834Z"
          fill="black"
        />
        <path
          d="M30.0002 0C26.7639 0 24.131 2.63273 24.131 5.86922C24.131 9.1057 26.7637 11.7384 30.0002 11.7384C33.2367 11.7384 35.8694 9.1057 35.8694 5.86922C35.8694 2.63273 33.2366 0 30.0002 0Z"
          fill="black"
        />
      </g>
      <defs>
        <clipPath id="clip0_1323_2529">
          <rect width={60} height={60} fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export const NudeIcon = (svgProps) => {
  return (
    <svg
      viewBox="0 0 60 60"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        d="M15 18C13.355 18 12 19.355 12 21V24C12 24.9333 12.3303 25.7611 13.0254 26.4219C15.8461 29.1033 18.5899 30.9789 20.8184 32.9824C23.0468 34.986 24.7686 37.0745 25.7754 40.332C26.081 41.321 26.922 42 28 42H32C33.0777 42 33.9171 41.321 34.2227 40.332C35.2293 37.0744 36.9513 34.986 39.1797 32.9824C41.4081 30.9788 44.1519 29.1032 46.9727 26.4219C47.6678 25.7611 48 24.9335 48 24V21C48 19.355 46.645 18 45 18H15Z"
        fill="black"
      />
      <circle cx={30} cy={30} r="28.5" stroke="black" strokeWidth={3} />
    </svg>
  );
};

export const FamilyIcon = (svgProps) => {
  return (
    <svg
      viewBox="0 0 60 60"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <g clipPath="url(#clip0_1323_2418)">
        <path
          d="M41.2727 44.6368C41.2727 44.9095 41.0909 45.1822 40.9091 45.364L42.2727 55.7277C42.4545 56.9095 41.8182 58.0913 40.9091 58.7277C41.1818 59.0004 43.5455 58.6368 43.8182 56.4549L45.3636 43.7277C44 43.6368 42.7273 43.4549 41.4545 43.0913L41.2727 44.6368Z"
          fill="black"
        />
        <path
          d="M52.8182 42.6367C51.1818 43.1822 49.4546 43.5458 47.7273 43.6367L49.2727 56.364C49.7273 59.8185 54.8182 59.0913 54.4546 55.7276L52.8182 42.6367Z"
          fill="black"
        />
        <path
          d="M13.6364 10.8184C16.3978 10.8184 18.6364 8.57978 18.6364 5.81836C18.6364 3.05694 16.3978 0.818359 13.6364 0.818359C10.8749 0.818359 8.63637 3.05694 8.63637 5.81836C8.63637 8.57978 10.8749 10.8184 13.6364 10.8184Z"
          fill="black"
        />
        <path
          d="M19.6364 47.8186C18.8182 47.4549 18.2727 47.0913 18 46.9095L19.2727 40.5458C17.5455 41.9095 15.0909 39.9095 16.0909 37.8185C16.0909 37.7276 19.8182 30.2731 19.5455 30.7276C19.8182 30.2731 20.1818 29.8185 20.7273 29.6367C20.8182 28.2731 21.2727 22.2731 21.2727 22.7276C21.3636 23.1822 21.5455 23.5458 21.6364 24.0004C22.2727 22.9095 23.4546 22.1822 24.7273 22.1822C24.0909 19.9095 23.2727 17.7276 22.2727 15.4549C19.8182 10.0913 6.36364 11.2731 4.54546 15.5458C2.36364 20.6367 1.18182 25.5458 0.909096 30.2731C0.818187 32.5458 4.27273 32.7276 4.36364 30.4549C4.45455 27.9095 5.00001 25.2731 5.72728 22.5458L7.00001 39.2731C7.00001 39.5458 7.27273 39.7276 7.54546 39.8186L5.63637 55.8186C5.27273 59.1822 10.3636 59.9095 10.8182 56.4549L12.7273 39.8186H14.1818L16.1818 56.364C16.4546 58.4549 19 59.364 20.4546 58.0004C19.9091 57.5458 19.5455 56.8186 19.5455 56.0913V47.8186H19.6364Z"
          fill="black"
        />
        <path
          d="M55.6364 26.9093C55.3636 24.5456 54.2727 22.0002 53.1818 19.9093V23.4547L56.4545 40.0002C51.9091 42.7275 46.6364 43.5456 41.4545 42.0911C41.5454 41.0002 41.1818 46.0911 42.2727 31.3638C44.2727 30.2729 46.4545 28.182 46.9091 23.9093C47 22.4547 46 21.182 44.5455 21.0911C43.0909 21.0002 41.8182 22.0002 41.7273 23.4547C41.6364 24.9093 41.1818 25.7275 40.6364 26.2729C40.2727 26.0911 39.8182 25.9093 39.4545 25.7275L39.8182 23.8184C41.2727 21.4547 40.3636 18.3638 37.9091 17.182C40.1818 12.7275 39.6364 13.2729 40.5454 12.7275C44.1818 10.9093 47.8182 10.6365 51.6364 12.2729C55.0909 13.7275 58.6364 22.6365 59.1818 26.3638C59.3636 28.7275 56 29.2729 55.6364 26.9093Z"
          fill="black"
        />
        <path
          d="M46.3636 10.8184C49.0909 10.8184 51.3636 8.63654 51.3636 5.81836C51.3636 3.09109 49.0909 0.818359 46.3636 0.818359C43.6364 0.818359 41.3636 3.09109 41.3636 5.81836C41.4546 8.63654 43.6364 10.8184 46.3636 10.8184Z"
          fill="black"
        />
        <path
          d="M35.8182 25.2731C38 25.2731 39.5455 23.4549 39.5455 21.4549C39.5455 20.0004 38.6364 18.6367 37.3636 18.0004C36.9091 17.8185 36.3636 17.6367 35.8182 17.6367C33.7273 17.6367 32.0909 19.2731 32.0909 21.364C32.0909 23.5458 33.8182 25.2731 35.8182 25.2731Z"
          fill="black"
        />
        <path
          d="M41.4546 55.8185L40.0909 45.7276L40 44.9095C40.1818 44.9095 40.3636 44.7276 40.3636 44.5458C41.1818 34.9095 41.4546 30.8185 41.4546 30.9095C44.3636 29.6367 45.7273 27.0913 46 23.9095C46.0909 23.0004 45.3636 22.0913 44.4546 22.0004C43.5455 21.9095 42.6364 22.6367 42.5455 23.5458C42.4546 25.0913 41.9091 26.5458 40.6364 27.364C39.5455 26.7276 38.0909 26.1822 36.8182 26.0004C36.7273 25.9095 36 26.364 34.6364 26.0004C34.4546 26.0004 33.1818 26.0913 31.7273 26.9095C30.6364 27.4549 29 28.6367 29.1818 29.7276C29.6364 29.9095 30 30.2731 30.1818 30.7276C30.1818 30.8185 33.9091 38.2731 33.6364 37.8185C34.4546 39.6367 32.7273 41.5458 30.9091 40.7276C30.9091 40.8185 31.1818 44.7276 31.1818 44.4549C31.1818 44.6367 31.3636 44.8185 31.5455 44.8185C29.9091 56.7276 30.0909 55.7276 30.0909 55.7276C29.7273 58.5458 34 59.1822 34.3636 56.2731L35.2727 49.5458L35.7273 52.8185L36.1818 56.4549C36.4546 58.6367 39 59.7276 40.7273 58.6367C40.2727 58.5458 39.9091 58.4549 39.5455 58.1822C40.9091 58.0004 41.6364 56.9095 41.4546 55.8185Z"
          fill="black"
        />
        <path
          d="M20.3636 48.1821L20.4546 48.7276V56.0912C20.4546 58.3639 23.9091 58.3639 23.9091 56.0912V49.0003C22.7273 48.9094 21.5455 48.6367 20.3636 48.1821C20.4546 48.1821 20.4546 48.1821 20.3636 48.1821Z"
          fill="black"
        />
        <path
          d="M29.6364 57.4548C30.2727 56.4548 29.9091 55.7275 30 50.7275L29.3636 55.7275C29.2727 56.273 29.4546 56.9094 29.6364 57.4548Z"
          fill="black"
        />
        <path
          d="M25.7273 56.0912C25.7273 58.3639 29.1818 58.3639 29.1818 56.0912V48.1821C28.0909 48.6367 26.9091 48.9094 25.7273 49.0003V56.0912Z"
          fill="black"
        />
        <path
          d="M21.8182 26C21.8182 29.9091 27.8182 29.9091 27.8182 26C27.8182 24.3636 26.4546 23 24.8182 23C23.1818 23 21.8182 24.3636 21.8182 26Z"
          fill="black"
        />
        <path
          d="M30.7273 46.5454C30.7273 46.4545 28.6364 36 28.8182 36.6363V35.5454C28.8182 35.6363 30.7273 39.4545 30.7273 39.3636C31.4546 40.909 33.8182 39.8181 33.0909 38.1818C29.3637 30.6363 29.9091 30.6363 27.3636 29.8181C27 29.7272 26.7273 29.6363 26.3636 29.5454C25.6364 29.909 24.4546 29.909 23.5455 29.5454C22.8182 29.7272 21.8182 30 21 30.4545C20.8182 30.5454 20.5455 30.7272 20.4546 31C14.4546 43.1818 20.8182 30.2727 17 38.0909C16.7273 38.6363 16.9091 39.2727 17.3636 39.6363C17.9091 40.1818 19 39.909 19.3636 39.1818C19.7273 38.3636 21.1818 35.4545 21.0909 35.6363V36.4545C19.4546 44.7272 19.0909 46.4545 19.1818 46.3636C22.9091 48.8181 27.0909 48.7272 30.7273 46.5454Z"
          fill="black"
        />
      </g>
      <defs>
        <clipPath id="clip0_1323_2418">
          <rect width={60} height={60} fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export const GayIcon = (svgProps) => {
  return (
    <svg
      viewBox="0 0 60 60"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <g clipPath="url(#clip0_1323_2453)">
        <path
          d="M59.8828 19.3359C59.8828 8.65699 51.2258 0 40.5469 0C29.8679 0 21.2109 8.65699 21.2109 19.3359C21.2109 22.2108 21.8393 24.9386 22.9648 27.3907C26.0678 26.0326 28.2422 22.9342 28.2422 19.3359C28.2422 12.5402 33.7512 7.03125 40.5469 7.03125C47.3426 7.03125 52.8516 12.5402 52.8516 19.3359C52.8516 26.1316 47.3426 31.6406 40.5469 31.6406C39.9516 31.6406 39.3664 31.5975 38.7938 31.5157C37.3886 33.7474 35.6053 35.7259 33.5224 37.3542C34.6451 37.7921 35.8181 38.1292 37.0314 38.3521V48.2812H33.4058C32.7701 48.2812 32.1911 48.6359 31.9179 49.1925C31.6447 49.7491 31.7261 50.4084 32.1268 50.8869L39.2679 59.4096C39.5808 59.7832 40.0506 60 40.5469 60C41.0432 60 41.513 59.7832 41.8259 59.4097L48.967 50.887C49.3679 50.4086 49.4493 49.7493 49.176 49.1926C48.9028 48.636 48.3238 48.2814 47.6879 48.2814H44.0625V38.3522C53.0627 36.6987 59.8828 28.8142 59.8828 19.3359Z"
          fill="black"
        />
        <path
          d="M26.5942 48.2812H22.9688V38.3521C31.969 36.6987 38.7891 28.8142 38.7891 19.3359C38.7891 16.4611 38.1608 13.7333 37.0355 11.2811C33.9323 12.6391 31.7578 15.7376 31.7578 19.3359C31.7578 26.1316 26.2488 31.6406 19.4531 31.6406C12.6574 31.6406 7.14844 26.1316 7.14844 19.3359C7.14844 12.5402 12.6574 7.03125 19.4531 7.03125C20.0529 7.03125 20.6423 7.07508 21.2193 7.15793C22.6327 4.9227 24.4222 2.94809 26.4993 1.32457C24.3159 0.469688 21.9394 0 19.4531 0C8.77418 0 0.117188 8.65699 0.117188 19.3359C0.117188 28.8142 6.93727 36.6987 15.9375 38.3521V48.2812H12.312C11.6762 48.2812 11.0972 48.6359 10.824 49.1925C10.5509 49.7491 10.6322 50.4084 11.033 50.8869L18.1741 59.4096C18.487 59.7832 18.9568 60 19.4531 60C19.9494 60 20.4192 59.7832 20.7321 59.4097L27.8733 50.887C28.2742 50.4086 28.3555 49.7493 28.0822 49.1926C27.8091 48.6359 27.2299 48.2812 26.5942 48.2812Z"
          fill="black"
        />
      </g>
      <defs>
        <clipPath id="clip0_1323_2453">
          <rect width={60} height={60} fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export const SharkIconNew = (props) => {
  return <svg
    width={432}
    height={232}
    viewBox="0 0 432 232"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M179.589 82.1391C155.815 110.188 57.4952 131.562 53.2954 132.387C51.9855 132.664 50.6192 132.43 49.4761 131.733C48.3329 131.036 47.4993 129.928 47.1457 128.637C40.0203 102.145 23.2482 79.2738 0.123047 64.515C9.79757 90.3138 26.2968 145.511 7.77267 183.534C24.9058 177.552 39.1182 165.271 47.5207 149.186C47.8594 148.52 48.3364 147.934 48.9198 147.466C49.5032 146.999 50.1796 146.662 50.9038 146.478C51.6279 146.293 52.3831 146.265 53.1189 146.396C53.8547 146.527 54.5541 146.813 55.1704 147.236C69.8696 157.285 134.291 177.609 166.315 186.984C167.397 187.307 168.347 187.97 169.022 188.875C169.698 189.78 170.063 190.879 170.065 192.009C170.065 195.234 170.065 201.758 124.242 231.682C164.14 224.707 202.088 201.308 202.463 201.008C203.429 200.417 204.56 200.154 205.688 200.258C304.683 208.583 354.706 192.009 379.38 175.885L372.93 167.785C372.009 166.648 371.417 165.28 371.218 163.83C371.018 162.381 371.219 160.904 371.798 159.56C372.378 158.217 373.313 157.057 374.504 156.206C375.695 155.356 377.096 154.847 378.555 154.736C388.529 153.986 397.229 151.736 406.678 147.611C416.936 143.572 425.724 136.51 431.877 127.362C370.98 79.8143 236.886 77.9393 235.537 77.9393C234.397 77.9333 233.291 77.5569 232.385 76.8669C231.478 76.177 230.821 75.2109 230.512 74.1145C221.512 41.3411 201.788 14.6424 189.414 0.318146C190.164 30.4667 181.164 77.4144 180.714 79.7393C180.551 80.6223 180.163 81.4486 179.589 82.1391ZM159.49 126.012C158.343 126.011 157.227 125.634 156.314 124.939C155.401 124.244 154.741 123.269 154.435 122.163C154.129 121.057 154.193 119.882 154.619 118.816C155.044 117.75 155.807 116.854 156.79 116.262C158.59 115.138 200.063 91.0637 238.386 97.0634C239.769 97.2822 241.008 98.0412 241.83 99.1734C242.653 100.306 242.992 101.718 242.774 103.101C242.555 104.483 241.796 105.722 240.664 106.545C239.532 107.367 238.119 107.707 236.736 107.488C202.088 102.013 162.565 125.037 162.19 125.262C161.375 125.752 160.442 126.011 159.49 126.012ZM330.332 153.611C329.378 153.614 328.441 153.358 327.622 152.869C326.802 152.38 326.132 151.677 325.682 150.836C322.796 145.047 321.294 138.667 321.294 132.199C321.294 125.731 322.796 119.351 325.682 113.563C326.339 112.329 327.458 111.407 328.794 111C330.13 110.592 331.574 110.731 332.807 111.388C334.04 112.044 334.962 113.163 335.37 114.5C335.778 115.836 335.638 117.279 334.982 118.512C332.902 122.762 331.821 127.43 331.821 132.162C331.821 136.893 332.902 141.562 334.982 145.811C335.428 146.615 335.656 147.522 335.642 148.441C335.628 149.36 335.373 150.26 334.902 151.049C334.431 151.839 333.762 152.491 332.959 152.941C332.157 153.39 331.251 153.621 330.332 153.611ZM356.881 157.585C355.927 157.589 354.99 157.333 354.17 156.844C353.351 156.355 352.681 155.652 352.231 154.811C340.231 132.462 351.706 110.488 352.231 109.513C352.561 108.902 353.008 108.363 353.546 107.925C354.085 107.487 354.704 107.159 355.369 106.961C356.034 106.762 356.732 106.697 357.422 106.768C358.113 106.839 358.782 107.045 359.393 107.375C360.004 107.705 360.543 108.152 360.981 108.691C361.419 109.229 361.747 109.849 361.945 110.514C362.143 111.179 362.209 111.877 362.138 112.567C362.067 113.257 361.86 113.927 361.53 114.538C361.38 114.763 352.231 132.537 361.53 149.786C361.977 150.59 362.205 151.496 362.191 152.416C362.177 153.335 361.922 154.235 361.451 155.024C360.98 155.814 360.31 156.466 359.508 156.916C358.706 157.365 357.8 157.596 356.881 157.585Z"
      fill="black"
      {...props}
    />
  </svg>
}

export const WindIcon = (props) => {
  return <svg
    stroke="currentColor"
    fill="none"
    strokeWidth={2.1}
    viewBox="0 0 24 24"
    strokeLinecap="round"
    strokeLinejoin="round"
    height="24px"
    width="24px"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M12.8 19.6A2 2 0 1 0 14 16H2" />
    <path d="M17.5 8a2.5 2.5 0 1 1 2 4H2" />
    <path d="M9.8 4.4A2 2 0 1 1 11 8H2" />
  </svg>
}

export const SharkIconShadow = (svgProps) => {
  return (<svg
    width={30}
    height={30}
    viewBox="0 0 30 30"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...svgProps}
  >
    <rect x={3} y={3} width={24} height={24} rx={12} fill="#FF1616" />
    <rect
      x={1.5}
      y={1.5}
      width={27}
      height={27}
      rx={13.5}
      stroke="#FF1616"
      strokeOpacity={0.2}
      strokeWidth={3}
    />
    <path
      d="M17.67 12.8175C16.995 11.385 15.405 10.5 14.25 10.5C14.6025 11.55 14.46 12.4275 14.2125 13.0725C12.3375 13.5 10.5 14.25 10.5 14.25C10.5 14.25 8.25 11.25 6.75 11.25L8.25 15.75L7.5 18.75C9 18.75 10.5 16.5 10.5 16.5C10.5 16.5 14.25 18 16.5 18V19.5C16.9875 19.5 17.9325 18.8775 18.5475 17.8275C19.4775 17.67 20.25 17.4075 20.9025 17.1075C20.79 16.9725 20.6475 16.83 20.46 16.725C19.9725 16.4325 19.365 16.2525 18.75 16.125C19.365 16.005 20.0025 15.915 20.6925 16.0125C21.0375 16.0575 21.405 16.155 21.75 16.395C21.825 16.44 21.8775 16.5 21.93 16.5375C22.8 15.945 23.25 15.33 23.25 15C23.25 14.3475 20.3925 13.125 17.67 12.8175ZM19.5 15C19.0875 15 18.75 14.6625 18.75 14.25C18.75 14.07 18.825 13.9125 18.9225 13.785C19.38 13.875 19.8225 14.0025 20.2275 14.13C20.25 14.1675 20.25 14.205 20.25 14.25C20.25 14.6625 19.9125 15 19.5 15Z"
      fill="white"
    />
  </svg>)
}

export const DogIcon = (svgProps) => {
  return (
    <svg
      viewBox="0 0 60 60"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        d="M52.5 10.9687C52.5 11.175 52.4625 11.3906 52.3875 11.5875C52.0313 12.5062 50.7 15 46.875 15C46.875 15 44.0625 15 44.0625 16.875C44.0625 18.75 47.8125 24.375 47.8125 30.9375C47.8125 30.9375 47.8125 36.5625 44.0625 39.375C40.3125 42.1875 42.1875 55.3125 42.1875 55.3125C42.1875 55.3125 45.9375 55.3125 45.9375 57.1875V58.125H39.375C39.375 58.125 36.5625 55.3125 36.5625 49.6875C36.5625 46.0219 36.9656 43.95 37.2375 42.9469C37.35 42.6375 37.425 42.4125 37.4625 42.2906C37.4062 42.4031 37.3313 42.6094 37.2375 42.9469C36.5156 44.9438 34.2281 50.325 30.3937 51.3844C30.6937 50.3719 30.9375 49.1531 30.9375 47.8125C30.9375 42.1875 26.25 41.25 26.25 41.25C26.25 41.25 30 43.125 30 46.875C30 48.75 29.5312 50.3906 29.0625 51.5625C28.5938 52.7344 28.125 53.4375 28.125 53.4375C28.125 53.4375 29.0625 55.3125 31.875 55.3125C34.6875 55.3125 34.6875 57.1875 34.6875 57.1875V58.125H19.6875C15.9375 58.125 12.1875 57.1875 12.1875 51.5625C12.1875 45.9375 12.1875 43.125 15 39.375C17.8125 35.625 21.5625 36.5625 27.1875 29.0625C30.8344 24.2063 32.5125 19.3406 33.2437 16.5188C33.5437 16.7906 33.9563 16.875 34.6875 16.875C36.5625 16.875 42.1875 16.875 39.375 9.375C40.3125 15 37.5 15.9375 35.625 15.9375C34.5094 15.9375 34.0594 15.2719 33.675 14.5406C33.4219 14.0344 33.1875 13.5 32.8125 13.125C31.875 12.1875 30.9375 13.125 30.9375 10.3125C30.9375 7.5 34.6875 2.8125 40.3125 2.8125C45.9375 2.8125 46.875 7.5 46.875 7.5C47.9906 8.61563 49.7719 9.06562 51.0281 9.25312C51.8719 9.375 52.5 10.1062 52.5 10.9687Z"
        fill="black"
      />
      <path
        d="M37.4625 42.2906V42.2812C37.4812 42.2531 37.4906 42.2344 37.4906 42.2156C37.4906 42.1969 37.5 42.1875 37.5 42.1875"
        fill="black"
      />
      <path
        d="M10.3125 39.375C9.01875 43.2657 10.4063 45.3563 11.3906 46.2844C11.25 47.8219 11.25 49.5282 11.25 51.5625C11.25 51.9844 11.2688 52.3875 11.3156 52.7625C5.55 48.0657 4.81875 41.0344 7.5 36.5625C10.3125 31.875 16.875 30.9375 17.8125 31.875C17.8125 31.875 12.1875 33.75 10.3125 39.375Z"
        fill="black"
      />
    </svg>
  );
};

export const CampingIcon = (svgProps) => {
  return (
    <svg
      viewBox="0 0 60 60"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M24.145 4.68918L30.6512 26.1857H22.3015L19.4093 16.6299L23.0234 4.68918C23.0606 4.56956 23.1351 4.46499 23.236 4.39073C23.3369 4.31646 23.4589 4.27641 23.5842 4.27641C23.7094 4.27641 23.8314 4.31646 23.9323 4.39073C24.0332 4.46499 24.1077 4.56956 24.145 4.68918ZM40.7331 39.7923L38.3214 34.9899H29.6292L32.0409 39.7923H40.7331ZM56.3722 56.8654L41.9513 28.1493C41.9037 28.0513 41.8291 27.9689 41.7364 27.9117C41.6437 27.8545 41.5366 27.8249 41.4277 27.8263H19.0339L34.043 57.7142H55.8488C55.9488 57.7145 56.0473 57.6891 56.1348 57.6405C56.2222 57.5919 56.2957 57.5216 56.3483 57.4364C56.4008 57.3513 56.4306 57.254 56.4348 57.154C56.439 57.0541 56.4174 56.9547 56.3722 56.8654ZM42.0586 41.4329H31.5389C31.3736 41.4329 31.2121 41.383 31.0756 41.2897C30.9392 41.1964 30.8341 41.064 30.7741 40.91L27.5727 34.535C27.5114 34.4101 27.4827 34.2718 27.4894 34.1328C27.496 33.9939 27.5378 33.859 27.6108 33.7405C27.6837 33.6221 27.7855 33.5241 27.9066 33.4557C28.0277 33.3873 28.1642 33.3507 28.3032 33.3494H38.8234C38.9887 33.3494 39.1502 33.3993 39.2866 33.4926C39.4231 33.5859 39.5281 33.7183 39.588 33.8724L42.7896 40.2474C42.8517 40.3722 42.8809 40.5107 42.8745 40.65C42.8682 40.7892 42.8264 40.9245 42.7533 41.0431C42.6801 41.1617 42.578 41.2598 42.4564 41.328C42.3349 41.3962 42.198 41.4323 42.0586 41.4329ZM16.8878 57.7142V30.4595L3.62684 56.8654C3.58166 56.9547 3.56014 57.0541 3.56436 57.1541C3.56858 57.254 3.59838 57.3513 3.65092 57.4364C3.70345 57.5216 3.77697 57.5919 3.86443 57.6405C3.95189 57.6891 4.05036 57.7145 4.15043 57.7142H16.8878ZM18.5284 30.4595L32.2148 57.7142H18.5273V30.4595H18.5284ZM50.5195 2.28613C51.7292 2.31134 52.9163 2.61885 53.9861 3.18412C55.0558 3.7494 55.9787 4.55681 56.6812 5.54197C57.3836 6.52714 57.8461 7.6628 58.0319 8.8584C58.2177 10.054 58.1215 11.2765 57.7511 12.4283C57.3806 13.5802 56.7462 14.6295 55.8984 15.4927C55.0505 16.3559 54.0127 17.0091 52.8677 17.4002C51.7227 17.7912 50.5022 17.9093 49.3034 17.7451C48.1047 17.5808 46.9609 17.1387 45.9633 16.4541C45.9243 16.4273 45.8944 16.3893 45.8774 16.3451C45.8605 16.3009 45.8573 16.2526 45.8683 16.2066C45.8793 16.1606 45.9039 16.119 45.939 16.0873C45.9741 16.0555 46.018 16.0352 46.0649 16.0288C52.5713 15.152 55.0287 7.1132 50.3539 2.69102C50.3195 2.65851 50.2957 2.61634 50.2857 2.57006C50.2757 2.52377 50.28 2.47554 50.2979 2.43172C50.3159 2.3879 50.3467 2.35055 50.3863 2.32458C50.4259 2.29862 50.4724 2.28525 50.5198 2.28625L50.5195 2.28613ZM13.4793 2.70074C13.442 2.58113 13.3675 2.47655 13.2666 2.40229C13.1657 2.32803 13.0437 2.28797 12.9185 2.28797C12.7932 2.28797 12.6712 2.32803 12.5703 2.40229C12.4694 2.47655 12.3949 2.58113 12.3577 2.70074L1.90125 37.2476C1.87422 37.3352 1.86818 37.428 1.88363 37.5183C1.89908 37.6087 1.93558 37.6942 1.99019 37.7678C2.0448 37.8415 2.11597 37.9012 2.19795 37.9423C2.27994 37.9833 2.37043 38.0045 2.46211 38.0041H11.2711L16.9774 26.6412C17.0451 26.5051 17.1492 26.3905 17.2782 26.3102C17.4072 26.2298 17.556 26.1869 17.708 26.1862H20.5874L13.4793 2.70074Z"
        fill="black"
      />
    </svg>
  );
};

export const CloseIcon = (svgProps) => {
  return <svg
    stroke="currentColor"
    fill="currentColor"
    strokeWidth={0}
    viewBox="0 0 512 512"
    height="24px"
    width="24px"
    xmlns="http://www.w3.org/2000/svg"
    {...svgProps}
  >
    <path d="m289.94 256 95-95A24 24 0 0 0 351 127l-95 95-95-95a24 24 0 0 0-34 34l95 95-95 95a24 24 0 1 0 34 34l95-95 95 95a24 24 0 0 0 34-34z" />
  </svg>
}

export const ZoomInIcon = ({ svgProps }) => {
  return (
    <svg
      width={28}
      height={28}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <g id="SVGRepo_bgCarrier" strokeWidth={0} />
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <g id="SVGRepo_iconCarrier">
        <path
          d="M4 15V18C4 19.1046 4.89543 20 6 20H9M15.2173 20H18C19.1046 20 20 19.1046 20 18V15M20 9V6C20 4.89543 19.1046 4 18 4H15M4 9V6C4 4.89543 4.89543 4 6 4H9"
          stroke="#000000"
          strokeWidth={2}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </svg>
  );
};

export const InfoIcon = ({ svgProps }) => {
  return (
    <svg
      viewBox="-0.5 0 25 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      width={20}
      height={20}
      {...svgProps}
    >
      <g id="SVGRepo_bgCarrier" strokeWidth={0} />
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <g id="SVGRepo_iconCarrier">
        <path
          d="M12 21.5C17.1086 21.5 21.25 17.3586 21.25 12.25C21.25 7.14137 17.1086 3 12 3C6.89137 3 2.75 7.14137 2.75 12.25C2.75 17.3586 6.89137 21.5 12 21.5Z"
          stroke="#fff"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M12.9309 8.15005C12.9256 8.39231 12.825 8.62272 12.6509 8.79123C12.4767 8.95974 12.2431 9.05271 12.0008 9.05002C11.8242 9.04413 11.6533 8.98641 11.5093 8.884C11.3652 8.7816 11.2546 8.63903 11.1911 8.47415C11.1275 8.30927 11.1139 8.12932 11.152 7.95675C11.19 7.78419 11.278 7.6267 11.405 7.50381C11.532 7.38093 11.6923 7.29814 11.866 7.26578C12.0397 7.23341 12.2192 7.25289 12.3819 7.32181C12.5446 7.39072 12.6834 7.506 12.781 7.65329C12.8787 7.80057 12.9308 7.97335 12.9309 8.15005ZM11.2909 16.5301V11.1501C11.2882 11.0556 11.3046 10.9615 11.3392 10.8736C11.3738 10.7857 11.4258 10.7057 11.4922 10.6385C11.5585 10.5712 11.6378 10.518 11.7252 10.4822C11.8126 10.4464 11.9064 10.4286 12.0008 10.43C12.094 10.4299 12.1863 10.4487 12.272 10.4853C12.3577 10.5218 12.4352 10.5753 12.4997 10.6426C12.5642 10.7099 12.6143 10.7895 12.6472 10.8767C12.6801 10.9639 12.6949 11.0569 12.6908 11.1501V16.5301C12.6908 16.622 12.6727 16.713 12.6376 16.7979C12.6024 16.8828 12.5508 16.96 12.4858 17.025C12.4208 17.09 12.3437 17.1415 12.2588 17.1767C12.1738 17.2119 12.0828 17.23 11.9909 17.23C11.899 17.23 11.8079 17.2119 11.723 17.1767C11.6381 17.1415 11.5609 17.09 11.4959 17.025C11.4309 16.96 11.3793 16.8828 11.3442 16.7979C11.309 16.713 11.2909 16.622 11.2909 16.5301Z"
          fill="#fff"
        />
      </g>
    </svg>
  );
};

export const FullViewIcon = (svgProps) => {
  return (
    <svg viewBox="0 0 18 18" xmlns="http://www.w3.org/2000/svg" {...svgProps}>
      <path
        d="M12 18L6 15.9L1.35 17.7C1.01667 17.8333 0.708333 17.796 0.425 17.588C0.141667 17.38 0 17.1007 0 16.75V2.75C0 2.53333 0.0626666 2.34167 0.188 2.175C0.313333 2.00833 0.484 1.88333 0.7 1.8L6 0L12 2.1L16.65 0.3C16.9833 0.166667 17.2917 0.204333 17.575 0.413C17.8583 0.621667 18 0.900667 18 1.25V15.25C18 15.4667 17.9377 15.6583 17.813 15.825C17.6883 15.9917 17.5173 16.1167 17.3 16.2L12 18ZM11 15.55V3.85L7 2.45V14.15L11 15.55ZM13 15.55L16 14.55V2.7L13 3.85V15.55ZM2 15.3L5 14.15V2.45L2 3.45V15.3Z"
        fillRule="evenodd"
        clipRule="evenodd"
      />
    </svg>
  );
};

export const SharkButtonIcon = (props) => (
  <svg
    stroke="currentColor"
    fill="none"
    strokeWidth={2}
    viewBox="0 0 24 24"
    strokeLinecap="round"
    strokeLinejoin="round"
    height="200px"
    width="200px"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M8 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h5.697" />
    <path d="M18 14v4h4" />
    <path d="M18 11v-4a2 2 0 0 0 -2 -2h-2" />
    <path d="M8 3m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" />
    <path d="M18 18m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0" />
    <path d="M8 11h4" />
    <path d="M8 15h3" />
  </svg>
);


export const ShareIcon = (svgProps) => {
  return (
    <svg
      viewBox="0 0 18 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        d="M12.75 8H14.625C15.1223 8 15.5992 8.19754 15.9508 8.54917C16.3025 8.90081 16.5 9.37772 16.5 9.875V18.875C16.5 19.3723 16.3025 19.8492 15.9508 20.2008C15.5992 20.5525 15.1223 20.75 14.625 20.75H3.375C2.87772 20.75 2.40081 20.5525 2.04917 20.2008C1.69754 19.8492 1.5 19.3723 1.5 18.875V9.875C1.5 9.37772 1.69754 8.90081 2.04917 8.54917C2.40081 8.19754 2.87772 8 3.375 8H5.25M12.75 5L9 1.25M9 1.25L5.25 5M9 1.25V14.0469"
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const NextArrowIcon = (svgProps) => {
  return <svg
    width="6" height="12" viewBox="0 0 6 12"
    fill="none" xmlns="http://www.w3.org/2000/svg"
    {...svgProps}><path d="M1.11005 10.6355L5.09385 6.65167C5.56433 6.18119 5.56433 5.41132 5.09385 4.94084L1.11005 0.957031" stroke="#FFFFFF" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path></svg>
}

export const ShareIcon1 = (svgProps) => {
  return <svg
    width="20" height="20" viewBox="0 0 21 21"
    fill="none" xmlns="http://www.w3.org/2000/svg" class="share-icon w-7 h-7">
    <path d="M15.3304 7.46094C16.7111 7.46094 17.8304 6.34165 17.8304 4.96094C17.8304 3.58023 16.7111 2.46094 15.3304 2.46094C13.9497 2.46094 12.8304 3.58023 12.8304 4.96094C12.8304 6.34165 13.9497 7.46094 15.3304 7.46094Z" stroke="#110F2A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
    </path>
    <path d="M5.33038 13.2969C6.7111 13.2969 7.83038 12.1776 7.83038 10.7969C7.83038 9.41616 6.7111 8.29688 5.33038 8.29688C3.94967 8.29688 2.83038 9.41616 2.83038 10.7969C2.83038 12.1776 3.94967 13.2969 5.33038 13.2969Z" stroke="#110F2A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
    <path d="M15.3304 19.1328C16.7111 19.1328 17.8304 18.0135 17.8304 16.6328C17.8304 15.2521 16.7111 14.1328 15.3304 14.1328C13.9497 14.1328 12.8304 15.2521 12.8304 16.6328C12.8304 18.0135 13.9497 19.1328 15.3304 19.1328Z" stroke="#110F2A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
    <path d="M7.48859 12.0547L13.1803 15.3714" stroke="#110F2A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M13.1719 6.22266L7.48859 9.53932" stroke="#110F2A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
    </path></svg>
}

export const MenuIcon = (svgProps) => {
  return <svg stroke="currentColor" fill="none" stroke-width="1.5" viewBox="0 0 24 24" stroke-linecap="round" stroke-linejoin="round" height="30px" width="30px" xmlns="http://www.w3.org/2000/svg" {...svgProps}>
    <line x1="4" x2="20" y1="12" y2="12"></line>
    <line x1="4" x2="20" y1="6" y2="6"></line>
    <line x1="4" x2="20" y1="18" y2="18"></line>
  </svg>
}

export const UserIcon = (props) => (
  <svg
    stroke="currentColor"
    fill="none"
    strokeWidth={2}
    viewBox="0 0 24 24"
    strokeLinecap="round"
    strokeLinejoin="round"
    height="24px"
    width="24px"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
    <circle cx={12} cy={7} r={4} />
  </svg>
);

export const StarIcon = (props) => (
  <svg
    width={29}
    height={28}
    viewBox="0 0 29 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M13.7799 0.927052C14.0793 0.0057416 15.3827 0.00574017 15.682 0.927051L18.3476 9.13097C18.4815 9.54299 18.8655 9.82195 19.2987 9.82195H27.9248C28.8935 9.82195 29.2963 11.0616 28.5126 11.631L21.5339 16.7013C21.1834 16.9559 21.0368 17.4073 21.1706 17.8193L23.8363 26.0232C24.1356 26.9445 23.0811 27.7107 22.2974 27.1413L15.3187 22.071C14.9683 21.8163 14.4937 21.8163 14.1432 22.071L7.16451 27.1413C6.38079 27.7107 5.32631 26.9445 5.62566 26.0232L8.29128 17.8193C8.42515 17.4073 8.27849 16.9559 7.928 16.7013L0.949338 11.631C0.165624 11.0616 0.568399 9.82195 1.53712 9.82195H10.1632C10.5965 9.82195 10.9804 9.54299 11.1143 9.13097L13.7799 0.927052Z"
      fill="#E49D3E"
      {...props}
    />
  </svg>
);

export const ApproveIcon = (props) => (
  <svg
    width={27}
    height={27}
    viewBox="0 0 27 27"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <rect
      x={0.96875}
      y={0.717773}
      width={26}
      height={26}
      rx={13}
      fill="#00C853"
      fillOpacity={0.2}
    />
    <g clipPath="url(#clip0_920_9117)">
      <path
        d="M13.9688 5.71777C18.387 5.71777 21.9688 9.2995 21.9688 13.7178C21.9688 18.1361 18.387 21.7178 13.9688 21.7178C9.55047 21.7178 5.96875 18.1361 5.96875 13.7178C5.96875 9.2995 9.55047 5.71777 13.9688 5.71777ZM17.4223 11.1642C17.2401 10.982 16.9521 10.9698 16.7558 11.1278L16.7152 11.1642L12.6688 15.2107L11.2223 13.7642C11.027 13.569 10.7105 13.569 10.5152 13.7642C10.333 13.9465 10.3208 14.2344 10.4787 14.4307L10.5152 14.4713L12.3152 16.2713C12.4974 16.4536 12.7854 16.4657 12.9817 16.3078L13.0223 16.2713L17.4223 11.8713C17.6176 11.6761 17.6176 11.3595 17.4223 11.1642Z"
        fill="#00C853"
      />
    </g>
    <defs>
      <clipPath id="clip0_920_9117">
        <rect
          width={16}
          height={16}
          fill="white"
          transform="translate(5.96875 5.71777)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const FaPaperPlane = (props) => (
  <svg
    stroke="currentColor"
    fill="currentColor"
    strokeWidth={0}
    viewBox="0 0 512 512"
    height="20px"
    width="20px"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M476 3.2L12.5 270.6c-18.1 10.4-15.8 35.6 2.2 43.2L121 358.4l287.3-253.2c5.5-4.9 13.3 2.6 8.6 8.3L176 407v80.5c0 23.6 28.5 32.9 42.5 15.8L282 426l124.6 52.2c14.2 6 30.4-2.9 33-18.2l72-432C515 7.8 493.3-6.8 476 3.2z" />
  </svg>
);

export const BsRobot = (props) => (
  <svg
    stroke="currentColor"
    fill="currentColor"
    strokeWidth={0}
    viewBox="0 0 16 16"
    height="20px"
    width="20px"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M6 12.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5M3 8.062C3 6.76 4.235 5.765 5.53 5.886a26.6 26.6 0 0 0 4.94 0C11.765 5.765 13 6.76 13 8.062v1.157a.93.93 0 0 1-.765.935c-.845.147-2.34.346-4.235.346s-3.39-.2-4.235-.346A.93.93 0 0 1 3 9.219zm4.542-.827a.25.25 0 0 0-.217.068l-.92.9a25 25 0 0 1-1.871-.183.25.25 0 0 0-.068.495c.55.076 1.232.149 2.02.193a.25.25 0 0 0 .189-.071l.754-.736.847 1.71a.25.25 0 0 0 .404.062l.932-.97a25 25 0 0 0 1.922-.188.25.25 0 0 0-.068-.495c-.538.074-1.207.145-1.98.189a.25.25 0 0 0-.166.076l-.754.785-.842-1.7a.25.25 0 0 0-.182-.135" />
    <path d="M8.5 1.866a1 1 0 1 0-1 0V3h-2A4.5 4.5 0 0 0 1 7.5V8a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1v1a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-1a1 1 0 0 0 1-1V9a1 1 0 0 0-1-1v-.5A4.5 4.5 0 0 0 10.5 3h-2zM14 7.5V13a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V7.5A3.5 3.5 0 0 1 5.5 4h5A3.5 3.5 0 0 1 14 7.5" />
  </svg>
);

export const FaPlus = (props) => (
  <svg
    stroke="currentColor"
    fill="currentColor"
    strokeWidth={0}
    viewBox="0 0 448 512"
    height="20px"
    width="20px"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M416 208H272V64c0-17.67-14.33-32-32-32h-32c-17.67 0-32 14.33-32 32v144H32c-17.67 0-32 14.33-32 32v32c0 17.67 14.33 32 32 32h144v144c0 17.67 14.33 32 32 32h32c17.67 0 32-14.33 32-32V304h144c17.67 0 32-14.33 32-32v-32c0-17.67-14.33-32-32-32z" />
  </svg>
);

export const MultiStar = (props) => (
  <svg
    viewBox="0 0 24 24"
    fill="currentColor"
    // className="size-3/5"
    width={40}
    height={40}
    aria-hidden="true"
    {...props}
  >
    <path
      fill="currentColor"
      d="M17.991 5.27c1.466-.272 2.314-1.13 2.555-2.575.152 1.392 1.214 2.397 2.533 2.565-1.45.267-2.303 1.12-2.544 2.58a2.862 2.862 0 0 0-.806-1.732 2.887 2.887 0 0 0-1.738-.838ZM1.318 12.877C7.662 11.699 11.332 7.983 12.374 1.73c.657 6.026 5.256 10.375 10.964 11.1C17.063 13.987 13.37 17.68 12.328 24c-.317-2.877-1.45-5.414-3.488-7.499-2.085-2.084-4.6-3.262-7.522-3.624ZM.66 3.434C2.614 3.07 3.745 1.926 4.066 0c.202 1.857 1.62 3.197 3.378 3.42-1.933.356-3.07 1.494-3.392 3.44-.098-.886-.447-1.667-1.075-2.31C2.335 3.909 1.56 3.547.66 3.435Z"
    />
  </svg>
);

export const LessThenIcon = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth={1.5}
    width={24}
    height={24}
    strokeLinecap="round"
    strokeLinejoin="round"
    className=""
    {...props}
  >
    <path d="M16.052 20.357 8.145 12.45a.617.617 0 0 1 0-.9l7.907-7.907" />
  </svg>
);

export const GreaterThenIcon = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth={1.5}
    width={24}
    height={24}
    strokeLinecap="round"
    strokeLinejoin="round"
    className=""
    {...props}
  >
    <path d="M7.948 20.357 15.855 12.45a.617.617 0 0 0 0-.9L7.948 3.643" />
  </svg>
)

export const TextMesaageIcon = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    // color="#00aae3"
    width={24}
    height={24}
    strokeWidth={1.5}
    strokeLinecap="round"
    strokeLinejoin="round"
    // className="shrink-0 transform-cpu size-[1em] text-[26px] ml-0 group-hover/button:scale-105 transition-transform"
    {...props}
  >
    <path d="M12.429 5a7.428 7.428 0 0 0-6.183 11.543L5 19.857l4.171-.754A7.429 7.429 0 1 0 12.43 5Z" />
  </svg>
  // <svg
  //   xmlns="http://www.w3.org/2000/svg"
  //   viewBox="0 0 24 24"
  //   // fill="currentColor"
  //   // stroke="currentColor"
  //   width={24}
  //   height={24}
  //   strokeWidth={1.5}
  //   strokeLinecap="round"
  //   strokeLinejoin="round"
  //   {...props}
  // >
  //   <path d="M12.429 5a7.428 7.428 0 0 0-6.183 11.543L5 19.857l4.171-.754A7.429 7.429 0 1 0 12.43 5Z" />
  // </svg>
);

export const PlusIcon = (props) => (<svg
  xmlns="http://www.w3.org/2000/svg"
  viewBox="0 0 24 24"
  fill="none"
  stroke="currentColor"
  strokeWidth="1.5"
  width={24}
  height={24}
  strokeLinecap="round"
  strokeLinejoin="round"
  // className="shrink-0 transform-cpu size-[1em] stroke-2 text-[1.25em]"
  {...props}
>
  <path d="M12 3.694V20.41M3.643 12h16.714"></path>
</svg>);

export const AudioIcon = (props) => (<svg
  xmlns="http://www.w3.org/2000/svg"
  viewBox="0 0 24 24"
  fill="none"
  stroke="currentColor"
  strokeWidth="1.5"
  width={24}
  height={24}
  strokeLinecap="round"
  strokeLinejoin="round"
  // className="shrink-0 transform-cpu size-[1em] text-[2em]"
  {...props}
>
  <path className="origin-center ![animation-delay:.1s]" d="M4.286 10.22v3.56"></path>
  <path className="origin-center ![animation-delay:.2s]" d="M7.857 7.256v9.49"></path>
  <path className="origin-center ![animation-delay:.3s]" d="M12 4.287v15.424"></path>
  <path className="origin-center ![animation-delay:.4s]" d="M15.857 7.244v9.49"></path>
  <path className="origin-center ![animation-delay:.5s]" d="M19.714 10.22v3.56"></path>
</svg>);

export const PlaneIcon = (props) => (<svg
  xmlns="http://www.w3.org/2000/svg"
  viewBox="0 0 24 24"
  fill="none"
  stroke="currentColor"
  strokeWidth="1.5"
  strokeLinecap="round"
  width={30}
  height={30}
  strokeLinejoin="round"
  // className="shrink-0 transform-cpu size-[1em] text-[2em] ml-[-.0625em] mt-[.0625em]"
  {...props}
>
  <path d="m10.483 17.143 2.803 2.79a1.415 1.415 0 0 0 1.35.386 1.441 1.441 0 0 0 1.041-.952L20.28 5.571a1.441 1.441 0 0 0-1.852-1.851L4.633 8.323a1.44 1.44 0 0 0-.952 1.106 1.414 1.414 0 0 0 .386 1.285l3.523 3.523-.116 4.462 3.009-1.556ZM19.868 4.003 7.59 14.237"></path>
</svg>);

export const InfoIcon2 = (props) => (<svg
  xmlns="http://www.w3.org/2000/svg"
  viewBox="0 0 24 24"
  fill="none"
  stroke="currentColor"
  strokeWidth="1.5"
  strokeLinecap="round"
  width={24}
  height={24}
  strokeLinejoin="round"
  {...props}
// className="shrink-0 transform-cpu size-[1em] -mt-0.5 inline-block text-[1.25em]"
>
  <path d="M12 20.357a8.357 8.357 0 1 0 0-16.714 8.357 8.357 0 0 0 0 16.714ZM12 12v4.5"></path>
  <path d="M12 9.429a.643.643 0 1 0 0-1.286.643.643 0 0 0 0 1.286Z"></path>
</svg>);

export const CollapseLeftArrow = (props) => <svg
  xmlns="http://www.w3.org/2000/svg"
  viewBox="0 0 24 24"
  fill="none"
  stroke="currentColor"
  strokeWidth={1.5}
  strokeLinecap="round"
  strokeLinejoin="round"
  width={24}
  height={24}
  // className="shrink-0 transform-cpu size-[1em] text-[1.7em]"
  {...props}
>
  <path d="M3.573 6.017v13.204m8.762-1.489L7.223 12.62m0 0 5.112-5.114M7.223 12.62h13.204" />
</svg>;

export const CollapseRightArrow = (props) => <svg
  xmlns="http://www.w3.org/2000/svg"
  viewBox="0 0 24 24"
  fill="none"
  stroke="currentColor"
  strokeWidth={1.5}
  strokeLinecap="round"
  strokeLinejoin="round"
  width={24}
  height={24}
  {...props}
>
  <path d="M20.427 6.017v13.204m-8.762-1.489 5.112-5.112m0 0-5.112-5.114m5.112 5.114H3.573" />
</svg>;

export const ChatHistoryIcon = (props) => <svg
  stroke="currentColor"
  fill="currentColor"
  strokeWidth={0}
  viewBox="0 0 26 26"
  height="24px"
  width="24px"
  xmlns="http://www.w3.org/2000/svg"
  {...props}
>
  <path fill="none" d="M0 0h24v24H0z" />
  <path d="M13 3a9 9 0 0 0-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42A8.954 8.954 0 0 0 13 21a9 9 0 0 0 0-18zm-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8H12z" />
</svg>
{/* <svg
  stroke="currentColor"
  fill="currentColor"
  // strokeWidth={0}
  viewBox="0 0 24 24"
  width={24}
  height={24}
  strokeWidth={1.5}
  // strokeLinecap="round"
  // strokeLinejoin="round"
  xmlns="http://www.w3.org/2000/svg"
  {...props}
>
  <path d="M12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22C10.298 22 8.69525 21.5748 7.29229 20.8248L2 22L3.17629 16.7097C2.42562 15.3063 2 13.7028 2 12C2 6.47715 6.47715 2 12 2ZM13 7H11V14H17V12H13V7Z" />
</svg> */}

export const MapBeachIcon = (props) => <svg
  width={15}
  height={15}
  viewBox="0 0 10 12"
  fill="none"
  xmlns="http://www.w3.org/2000/svg"
  {...props}
>
  <path
    d="M8.39439 1.31845C8.02997 1.09797 7.62759 0.954843 7.21032 0.897268C6.79305 0.839685 6.36907 0.868777 5.96259 0.982871C5.55604 1.09697 5.17493 1.29383 4.84103 1.56223C4.50713 1.83063 4.22698 2.16531 4.01656 2.54714L3.89582 2.7661C3.8545 2.84123 3.84335 2.9305 3.86481 3.01425C3.88628 3.098 3.93861 3.16939 4.01031 3.21274L6.28397 4.58797L4.92334 7.08286L4.89503 7.08243C3.58024 7.08243 2.44154 7.88146 1.76331 9.01117C1.86773 8.97251 1.97846 8.95606 2.08902 8.96274C2.19958 8.96951 2.30774 8.99926 2.40719 9.05034C2.50663 9.10134 2.59536 9.17266 2.66817 9.26009C2.74098 9.34751 2.79642 9.44926 2.83123 9.5594C3.07937 10.3437 3.98366 10.3009 4.21185 9.60611C4.2683 9.43434 4.3745 9.28537 4.51564 9.17994C4.65677 9.07451 4.82581 9.01777 4.9991 9.01777C5.17247 9.01777 5.34151 9.07451 5.48265 9.17994C5.62378 9.28537 5.72998 9.43434 5.78644 9.60611C6.01455 10.3009 6.91888 10.3437 7.16704 9.55897C7.22423 9.3782 7.3364 9.22203 7.48629 9.11429C7.63618 9.00654 7.81561 8.95323 7.99725 8.96231C7.4426 8.06463 6.59496 7.38337 5.6003 7.16051L6.82479 4.91506L9.02014 6.2432C9.05573 6.26471 9.09492 6.27869 9.13558 6.28426C9.17633 6.28991 9.21765 6.28709 9.25725 6.27594C9.29693 6.2648 9.33407 6.2456 9.36656 6.21937C9.39912 6.19314 9.42645 6.16057 9.4469 6.12329L9.56767 5.9048C9.7781 5.52294 9.91466 5.1014 9.96964 4.66426C10.0245 4.22711 9.99672 3.78291 9.88782 3.35702C9.77884 2.93113 9.5909 2.5319 9.33465 2.18212C9.07839 1.83234 8.75889 1.53887 8.39439 1.31845ZM2.33745 9.7304C2.31607 9.66311 2.27438 9.60491 2.21867 9.56454C2.16297 9.52426 2.09626 9.50394 2.0286 9.50686C1.96095 9.50969 1.89601 9.53549 1.84358 9.5804C1.79114 9.62531 1.75406 9.68677 1.73792 9.75569C1.63175 10.2101 1.43066 10.4597 1.20167 10.6045C0.961857 10.7562 0.656258 10.8147 0.31528 10.8147C0.232464 10.8147 0.153035 10.8491 0.0944777 10.9105C0.0359205 10.9719 0.00302124 11.0551 0.00302124 11.1419C0.00302124 11.2286 0.0359205 11.3118 0.0944777 11.3731C0.153035 11.4345 0.232464 11.4689 0.31528 11.4689C0.713718 11.4689 1.14796 11.4027 1.52433 11.1645C1.74894 11.0225 1.93827 10.8269 2.07682 10.5935C2.82166 11.5889 4.27591 11.5836 4.9991 10.5831C5.73399 11.5994 7.22243 11.5889 7.95642 10.5456C8.15565 10.8298 8.41558 11.0612 8.71528 11.2215C9.01507 11.3818 9.34643 11.4665 9.68295 11.4689C9.76583 11.4689 9.84519 11.4345 9.90377 11.3731C9.96236 11.3118 9.99525 11.2286 9.99525 11.1419C9.99525 11.0551 9.96236 10.9719 9.90377 10.9105C9.84519 10.8491 9.76583 10.8147 9.68295 10.8147C9.0689 10.8147 8.45682 10.396 8.25489 9.73391C8.23493 9.668 8.19557 9.6104 8.14239 9.56943C8.08921 9.52846 8.02506 9.50617 7.95904 9.50583C7.89309 9.5054 7.8287 9.52691 7.77511 9.56729C7.72152 9.60766 7.68151 9.66474 7.66081 9.7304C7.2599 10.9992 5.68817 10.983 5.2943 9.7832C5.27311 9.71883 5.23326 9.66303 5.18041 9.62351C5.12747 9.584 5.06415 9.56274 4.9991 9.56274C4.93414 9.56274 4.87081 9.584 4.81787 9.62351C4.76494 9.66303 4.72509 9.71883 4.7039 9.7832C4.31011 10.983 2.73881 10.9988 2.33745 9.7304Z"
    fill="#00aae3"
    {...props}
  />
</svg>


export const ThreeDotMenuIcon = (props) => <svg
  xmlns="http://www.w3.org/2000/svg"
  viewBox="0 0 24 24"
  fill="none"
  stroke="currentColor"
  strokeWidth={1.5}
  strokeLinecap="round"
  strokeLinejoin="round"
  className="shrink-0 transform-cpu size-[1em] text-[1.7em]"
  {...props}
>
  <path
    d="M18.429 13.929a1.929 1.929 0 1 0 0-3.858 1.929 1.929 0 0 0 0 3.858ZM12 13.929a1.929 1.929 0 1 0 0-3.858 1.929 1.929 0 0 0 0 3.858ZM5.571 13.929a1.929 1.929 0 1 0 0-3.858 1.929 1.929 0 0 0 0 3.858Z"
    fill="currentColor"
    stroke="none"
  />
</svg>;

export const EarthSideBarIcon = (props) => (
  <svg
    width={22}
    height={22}
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M19.48 7.301C19.9893 8.4676 20.2515 9.72706 20.25 11C20.25 16.105 16.105 20.25 11 20.25C5.895 20.25 1.75 16.105 1.75 11C1.75 5.895 5.895 1.75 11 1.75C11.1989 1.75 11.3897 1.67098 11.5303 1.53033C11.671 1.38968 11.75 1.19891 11.75 1C11.75 0.801088 11.671 0.610322 11.5303 0.46967C11.3897 0.329018 11.1989 0.25 11 0.25C5.067 0.25 0.25 5.067 0.25 11C0.25 16.933 5.067 21.75 11 21.75C16.933 21.75 21.75 16.933 21.75 11C21.75 9.471 21.43 8.017 20.854 6.699C20.7742 6.5168 20.6252 6.37377 20.4399 6.30138C20.2547 6.22899 20.0482 6.23317 19.866 6.313C19.6838 6.39283 19.5408 6.54177 19.4684 6.72706C19.396 6.91234 19.4002 7.1188 19.48 7.301Z"
      fill="black"
      {...props}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M16 0.25C15.0872 0.250265 14.2119 0.612988 13.5664 1.25843C12.921 1.90387 12.5583 2.77921 12.558 3.692C12.558 4.286 12.827 5.009 13.243 5.715C14.078 7.136 15.47 8.53 15.47 8.53C15.5396 8.59966 15.6222 8.65493 15.7131 8.69264C15.8041 8.73035 15.9016 8.74975 16 8.74975C16.0985 8.74975 16.1959 8.73035 16.2869 8.69264C16.3778 8.65493 16.4604 8.59966 16.53 8.53C16.53 8.53 17.922 7.136 18.757 5.715C19.173 5.009 19.442 4.286 19.442 3.692C19.442 1.792 17.9 0.25 16 0.25ZM16 1.75C17.072 1.75 17.942 2.62 17.942 3.692C17.942 4.22 17.549 4.869 17.127 5.481C16.7797 5.97615 16.4034 6.45032 16 6.901C15.675 6.535 15.25 6.027 14.873 5.481C14.451 4.869 14.058 4.22 14.058 3.692C14.058 2.62 14.928 1.75 16 1.75ZM0.603014 11.636L3.87301 13.68C4.46901 14.052 5.15801 14.25 5.86001 14.25H6.62001C7.27701 14.25 7.90101 14.537 8.32901 15.036L9.38001 16.263C9.62219 16.5453 9.7906 16.8832 9.87018 17.2466C9.94975 17.6099 9.93802 17.9873 9.83601 18.345L9.27901 20.294C9.22885 20.4838 9.25511 20.6857 9.35216 20.8564C9.44921 21.027 9.6093 21.1529 9.79808 21.2068C9.98685 21.2607 10.1892 21.2385 10.3618 21.1449C10.5344 21.0512 10.6633 20.8937 10.721 20.706L11.278 18.757C11.4483 18.161 11.4681 17.532 11.3357 16.9264C11.2032 16.3208 10.9226 15.7575 10.519 15.287L9.46701 14.06C9.11521 13.649 8.67856 13.3191 8.18709 13.093C7.69563 12.8668 7.16101 12.7498 6.62001 12.75H5.86001C5.43901 12.75 5.02601 12.632 4.66801 12.408L1.39701 10.364C1.31348 10.3087 1.21972 10.2707 1.12127 10.2523C1.02282 10.2339 0.921671 10.2353 0.823794 10.2566C0.725917 10.2779 0.633294 10.3186 0.551393 10.3762C0.469491 10.4339 0.39997 10.5074 0.346931 10.5923C0.293892 10.6773 0.258411 10.772 0.24258 10.8709C0.22675 10.9698 0.230892 11.0709 0.254761 11.1682C0.27863 11.2655 0.321743 11.357 0.381556 11.4373C0.441369 11.5177 0.516669 11.5852 0.603014 11.636Z"
      fill="black"
      {...props}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M4.32904 3.335L5.92504 6.527C6.09955 6.87636 6.34652 7.18451 6.64948 7.43092C6.95245 7.67732 7.30446 7.85633 7.68204 7.956L9.44004 8.421C9.88304 8.538 10.226 8.888 10.334 9.333L11.087 12.42C11.2503 13.0901 11.6593 13.6743 12.233 14.057L12.699 14.367C13.0233 14.5839 13.39 14.7293 13.7748 14.7934C14.1596 14.8575 14.5537 14.8389 14.9307 14.7388C15.3077 14.6387 15.6591 14.4594 15.9615 14.2129C16.2638 13.9663 16.5101 13.6582 16.684 13.309L17.259 12.158C17.3398 11.9968 17.4544 11.855 17.5951 11.7423C17.7358 11.6296 17.8991 11.5486 18.074 11.505L20.865 10.807C21.0555 10.7564 21.2183 10.6329 21.3184 10.4632C21.4185 10.2935 21.4478 10.0913 21.4 9.90014C21.3522 9.70901 21.2311 9.54437 21.0629 9.4418C20.8947 9.33924 20.6928 9.30698 20.501 9.352L17.71 10.049C17.3254 10.1454 16.9661 10.3238 16.6568 10.572C16.3475 10.8202 16.0955 11.1323 15.918 11.487L15.342 12.638C15.2633 12.7969 15.1514 12.9372 15.0141 13.0494C14.8767 13.1617 14.717 13.2433 14.5455 13.2888C14.3741 13.3344 14.1949 13.3428 14.0199 13.3135C13.845 13.2842 13.6783 13.2179 13.531 13.119L13.065 12.809C12.8042 12.6351 12.6183 12.3696 12.544 12.065L11.792 8.978C11.6748 8.49726 11.43 8.05707 11.0834 7.70384C10.7369 7.35062 10.3014 7.09744 9.82304 6.971L8.06504 6.506C7.89343 6.46069 7.73347 6.37926 7.59585 6.26717C7.45824 6.15507 7.34613 6.01489 7.26704 5.856L5.67104 2.665C5.62752 2.5761 5.56683 2.49669 5.49247 2.43136C5.41811 2.36603 5.33156 2.31606 5.23779 2.28434C5.14403 2.25262 5.04492 2.23977 4.94617 2.24654C4.84742 2.2533 4.75099 2.27955 4.66243 2.32376C4.57387 2.36797 4.49494 2.42928 4.43018 2.50414C4.36543 2.57901 4.31614 2.66595 4.28515 2.75995C4.25416 2.85396 4.24208 2.95317 4.24961 3.05186C4.25715 3.15056 4.28414 3.24679 4.32904 3.335Z"
      fill="black"
      {...props}
    />
  </svg>
);

export const BlogSideBarIcon = (props) => (
  <svg
    width={22}
    height={20}
    viewBox="0 0 22 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"

  >
    <path
      d="M19.25 4.5H17.75V2.25C17.75 1.65326 17.5129 1.08097 17.091 0.65901C16.669 0.237053 16.0967 0 15.5 0H2.75C2.15326 0 1.58097 0.237053 1.15901 0.65901C0.737053 1.08097 0.5 1.65326 0.5 2.25V17.25C0.5 17.8467 0.737053 18.419 1.15901 18.841C1.58097 19.2629 2.15326 19.5 2.75 19.5H18.875C19.5712 19.5 20.2389 19.2234 20.7312 18.7312C21.2234 18.2389 21.5 17.5712 21.5 16.875V6.75C21.5 6.15326 21.2629 5.58097 20.841 5.15901C20.419 4.73705 19.8467 4.5 19.25 4.5ZM2 17.25V2.25C2 2.05109 2.07902 1.86032 2.21967 1.71967C2.36032 1.57902 2.55109 1.5 2.75 1.5H15.5C15.6989 1.5 15.8897 1.57902 16.0303 1.71967C16.171 1.86032 16.25 2.05109 16.25 2.25V16.875C16.2516 17.265 16.3413 17.6496 16.5125 18H2.75C2.55109 18 2.36032 17.921 2.21967 17.7803C2.07902 17.6397 2 17.4489 2 17.25ZM20 16.875C20 17.1734 19.8815 17.4595 19.6705 17.6705C19.4595 17.8815 19.1734 18 18.875 18C18.5766 18 18.2905 17.8815 18.0795 17.6705C17.8685 17.4595 17.75 17.1734 17.75 16.875V6H19.25C19.4489 6 19.6397 6.07902 19.7803 6.21967C19.921 6.36032 20 6.55109 20 6.75V16.875Z"
      fill="black"
      {...props}
    />
    <path
      d="M14 5.25H6.5C6.30109 5.25 6.11032 5.32902 5.96967 5.46967C5.82902 5.61032 5.75 5.80109 5.75 6C5.75 6.19891 5.82902 6.38968 5.96967 6.53033C6.11032 6.67098 6.30109 6.75 6.5 6.75H14C14.1989 6.75 14.3897 6.67098 14.5303 6.53033C14.671 6.38968 14.75 6.19891 14.75 6C14.75 5.80109 14.671 5.61032 14.5303 5.46967C14.3897 5.32902 14.1989 5.25 14 5.25ZM14 9H4.25C4.05109 9 3.86032 9.07902 3.71967 9.21967C3.57902 9.36032 3.5 9.55109 3.5 9.75C3.5 9.94891 3.57902 10.1397 3.71967 10.2803C3.86032 10.421 4.05109 10.5 4.25 10.5H14C14.1989 10.5 14.3897 10.421 14.5303 10.2803C14.671 10.1397 14.75 9.94891 14.75 9.75C14.75 9.55109 14.671 9.36032 14.5303 9.21967C14.3897 9.07902 14.1989 9 14 9ZM14 12.75H4.25C4.05109 12.75 3.86032 12.829 3.71967 12.9697C3.57902 13.1103 3.5 13.3011 3.5 13.5C3.5 13.6989 3.57902 13.8897 3.71967 14.0303C3.86032 14.171 4.05109 14.25 4.25 14.25H14C14.1989 14.25 14.3897 14.171 14.5303 14.0303C14.671 13.8897 14.75 13.6989 14.75 13.5C14.75 13.3011 14.671 13.1103 14.5303 12.9697C14.3897 12.829 14.1989 12.75 14 12.75Z"
      fill="black"
      {...props}
    />
  </svg>
);

export const MapSideBarIcon = (props) => (
  <svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"

  >
    <path
      d="M23.9424 23.0631L19.7073 13.1823C19.6528 13.0554 19.5623 12.9472 19.447 12.8712C19.3317 12.7952 19.1967 12.7546 19.0585 12.7546H16.3689C17.4575 10.8529 18.3527 8.74804 18.3527 6.96716C18.3527 -2.20388 5.64738 -2.31573 5.64738 6.96716C5.64738 8.74804 6.54255 10.8528 7.63113 12.7546H4.94148C4.80336 12.7546 4.66826 12.7951 4.55295 12.8711C4.43763 12.9472 4.34715 13.0554 4.29274 13.1823L0.0576724 23.0631C-0.141546 23.5278 0.199797 24.0469 0.706422 24.0469H23.2936C23.7992 24.0469 24.1419 23.5289 23.9424 23.0631ZM18.5929 14.1661L20.6827 19.0415C20.6173 19.0755 13.8157 22.6074 13.767 22.6353H12.2895C12.2522 22.5936 12.5696 22.9127 7.96933 18.3129L10.5245 17.0051C11.0455 17.67 11.4104 18.0924 11.4674 18.158C11.7486 18.4817 12.2516 18.4812 12.5325 18.158C12.6506 18.0221 14.0894 16.3549 15.5068 14.1661H18.5929ZM12 1.46202C14.3797 1.46202 16.9409 3.18487 16.9409 6.96712C16.9409 10.1473 13.3761 14.88 12 16.588C10.624 14.8799 7.05911 10.1473 7.05911 6.96712C7.05911 3.16443 9.54048 1.46202 12 1.46202ZM5.40695 14.1661C8.82625 14.1661 8.4377 14.1666 8.49245 14.165C8.88995 14.7789 9.28914 15.3519 9.66049 15.8616L3.2807 19.1268L5.40695 14.1661ZM2.41005 21.1582L6.64895 18.9888L10.296 22.6353H1.77686L2.41005 21.1582ZM16.8263 22.6353L21.2404 20.3425L22.2232 22.6353H16.8263Z"
      fill="black"
      {...props}
    />
    <path
      d="M14.1175 6.40265C14.1175 5.23519 13.1675 4.28522 12.0001 4.28522C10.8324 4.28522 9.88245 5.23519 9.88245 6.40265C9.88245 7.57012 10.8324 8.5199 12.0001 8.5199C13.1675 8.5199 14.1175 7.57012 14.1175 6.40265ZM11.2942 6.40265C11.2942 6.01336 11.6108 5.69676 12.0001 5.69676C12.3891 5.69676 12.706 6.01336 12.706 6.40265C12.706 6.79172 12.3892 7.10836 12.0001 7.10836C11.6108 7.10836 11.2942 6.79176 11.2942 6.40265H11.2942Z"
      fill="black"
      {...props}
    />
  </svg>
);

export const NudeBeachSideBarIcon = (props) => (
  <svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"

  >
    <path
      d="M19.7334 17.1402H14.0338V9.60864H21.5331C21.9214 9.60864 22.2362 9.29383 22.2362 8.90552C22.2362 3.99503 18.2412 0 13.3306 0C8.4201 0 4.42507 3.99503 4.42507 8.90552C4.42507 9.29383 4.73988 9.60864 5.12819 9.60864H12.6275V17.1402H8.10824L6.04416 15.0761C4.56466 13.5966 2.15741 13.5966 0.677957 15.0761C0.546113 15.208 0.472046 15.3868 0.472046 15.5733C0.472046 15.7598 0.546113 15.9386 0.677957 16.0705L5.75823 21.1507C5.74729 21.1776 5.73799 21.2051 5.73039 21.233L5.23262 23.1171C5.13344 23.4925 5.3574 23.8773 5.73282 23.9765C5.79301 23.9924 5.85343 24 5.91287 24C6.22426 24 6.50893 23.7915 6.59222 23.4762L7.07794 21.6377H20.6599V23.2967C20.6599 23.685 20.9747 23.9998 21.363 23.9998C21.7513 23.9998 22.0661 23.685 22.0661 23.2967V21.6378H22.8247C23.213 21.6378 23.5278 21.3229 23.5278 20.9346C23.5279 18.8424 21.8257 17.1402 19.7334 17.1402ZM20.7973 8.2024H17.8439C17.7669 6.17395 17.3576 4.27989 16.6717 2.80845C16.5345 2.51412 16.3881 2.24164 16.2337 1.99109C18.7255 3.04118 20.5356 5.40029 20.7973 8.2024ZM10.4276 1.99109C10.2732 2.24164 10.1268 2.51412 9.9896 2.80845C9.30364 4.27994 8.89442 6.17395 8.81736 8.2024H5.86398C6.12568 5.40029 7.9357 3.04118 10.4276 1.99109ZM13.3306 1.40624C14.7269 1.40624 16.2742 4.16298 16.4371 8.2024H10.2241C10.387 4.16298 11.9342 1.40624 13.3306 1.40624ZM5.04981 16.0705L7.31981 18.3405C7.45166 18.4724 7.63049 18.5464 7.81696 18.5465H19.7334C20.8057 18.5465 21.7152 19.2567 22.016 20.2316H6.82772L2.24413 15.648C3.14628 15.1714 4.29162 15.3123 5.04981 16.0705Z"
      fill="black"
      {...props}
    />
  </svg>
);

export const SharkSideBarIcon = (props) => (
  <svg
    width={22}
    height={24}
    viewBox="0 0 22 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"

  >
    <path
      d="M14.0469 8.95331C14.3058 8.95331 14.5156 8.74344 14.5156 8.48456C14.5156 8.22567 14.3058 8.01581 14.0469 8.01581C13.788 8.01581 13.5781 8.22567 13.5781 8.48456C13.5781 8.74344 13.788 8.95331 14.0469 8.95331Z"
      fill="black"
      {...props}
    />
    <path
      d="M7.95312 8.95331C8.21201 8.95331 8.42188 8.74344 8.42188 8.48456C8.42188 8.22567 8.21201 8.01581 7.95312 8.01581C7.69424 8.01581 7.48438 8.22567 7.48438 8.48456C7.48438 8.74344 7.69424 8.95331 7.95312 8.95331Z"
      fill="black"
      {...props}
    />
    <path
      d="M21.5469 14.5352C21.5469 13.0924 21.0967 11.6076 20.4624 10.3389L20.4614 10.3369L20.4614 10.3368C18.9027 7.20463 16.102 4.82085 13.11 4.06265L11.4275 0.323427C11.3904 0.241083 11.3303 0.171196 11.2545 0.122168C11.1787 0.07314 11.0903 0.0470581 11 0.0470581C10.9097 0.0470581 10.8213 0.07314 10.7455 0.122168C10.6697 0.171196 10.6096 0.241083 10.5725 0.323427L8.88997 4.06269C5.89817 4.82085 3.09758 7.20444 1.53875 10.3366C0.899422 11.6152 0.453125 13.0984 0.453125 14.5352C0.453125 16.1077 1.00058 17.5566 2.03975 18.7486C1.13858 20.2412 0.931062 21.8821 0.921875 23.0296C0.919886 23.2245 0.980343 23.4149 1.09439 23.5729C1.20844 23.731 1.37009 23.8483 1.55567 23.9078C1.93466 24.0307 2.34866 23.8973 2.58266 23.5688C3.09847 22.8459 3.99191 21.8353 5.35733 21.039C8.7027 22.4281 13.304 22.4253 16.6427 21.039C18.008 21.8352 18.9015 22.8459 19.4171 23.5686C19.5926 23.8149 19.8704 23.9529 20.1602 23.9529C20.6636 23.9529 21.082 23.5474 21.0781 23.0294C21.0689 21.882 20.8614 20.2412 19.9603 18.7485C20.9995 17.5566 21.5469 16.1076 21.5469 14.5352ZM14.0469 7.0783C14.8223 7.0783 15.4531 7.70915 15.4531 8.48455C15.4531 9.25996 14.8223 9.8908 14.0469 9.8908C13.2715 9.8908 12.6406 9.25996 12.6406 8.48455C12.6406 7.70915 13.2715 7.0783 14.0469 7.0783ZM7.95312 7.0783C8.72853 7.0783 9.35938 7.70915 9.35938 8.48455C9.35938 9.25996 8.72853 9.8908 7.95312 9.8908C7.17772 9.8908 6.54688 9.25996 6.54688 8.48455C6.54688 7.70915 7.17772 7.0783 7.95312 7.0783ZM1.86017 22.9677C1.87522 21.9983 2.04828 20.665 2.72534 19.4362C3.20267 19.8568 3.74722 20.2333 4.35364 20.5623C3.17417 21.3554 2.36483 22.2718 1.86017 22.9677ZM11 21.1408C6.34995 21.1408 1.39062 19.0701 1.39062 14.5352C1.39062 13.445 1.68537 12.24 2.24436 11.0336C3.84369 11.1513 4.8005 11.4769 5.80981 11.8205C7.08467 12.2545 8.40294 12.7033 11 12.7033C13.5971 12.7033 14.9153 12.2545 16.1902 11.8205C17.1995 11.4769 18.1564 11.1513 19.7556 11.0336C20.3146 12.2401 20.6094 13.4451 20.6094 14.5352C20.6094 19.0605 15.6737 21.1408 11 21.1408ZM20.1397 22.9677C19.6363 22.2731 18.8267 21.3561 17.6464 20.5623C18.2528 20.2333 18.7974 19.8568 19.2747 19.4362C19.9513 20.6641 20.1246 21.9967 20.1397 22.9677Z"
      fill="black"
      {...props}
    />
    <path
      d="M19.3899 13.378C19.4312 13.2936 19.4462 13.1987 19.4329 13.1056C19.4196 13.0125 19.3787 12.9256 19.3154 12.8561C19.252 12.7867 19.1693 12.7378 19.0778 12.716C18.9864 12.6942 18.8905 12.7003 18.8026 12.7337C16.4217 13.6364 13.6248 14.1945 10.9957 14.2267L10.994 14.2268C8.2051 14.2592 5.83731 13.7339 3.19735 12.7337C3.10948 12.7004 3.01362 12.6942 2.92223 12.7161C2.83083 12.738 2.74811 12.7868 2.68481 12.8562C2.62151 12.9257 2.58054 13.0126 2.56723 13.1056C2.55391 13.1986 2.56887 13.2935 2.61015 13.3779C3.82665 15.8662 6.31721 18.4848 8.97671 19.3684C10.3595 19.8279 11.7471 19.8407 12.9974 19.5012C14.1496 19.1894 15.2515 18.5667 16.2723 17.6506C17.4684 16.5777 18.5687 15.0743 19.3706 13.4176L19.3899 13.378ZM5.94273 16.3279C5.13231 15.528 4.50967 14.6888 4.09014 14.0489C4.82242 14.2932 5.66364 14.5379 6.59898 14.7347L5.94273 16.3279ZM6.68124 16.9964L7.25068 15.614L8.25582 18.0475C7.72074 17.7714 7.19439 17.42 6.68124 16.9964ZM8.00265 14.9789C8.75785 15.0831 9.51842 15.1437 10.2806 15.1604L9.13993 17.7324L8.00265 14.9789ZM9.77115 18.6216L10.9934 15.8657L12.2204 18.7132C11.4164 18.8521 10.5992 18.8242 9.77115 18.6216ZM12.857 17.8216L11.7029 15.143C12.4614 15.1092 13.2324 15.0359 14.0087 14.9232L12.857 17.8216ZM13.6932 18.2561L14.7548 15.5842L15.3984 17.1666C14.8495 17.6248 14.2794 17.9895 13.6932 18.2561ZM16.1331 16.4845L15.3995 14.6809C16.3979 14.4774 17.2745 14.2361 17.9946 14.0068C17.4506 14.9366 16.8143 15.7841 16.1331 16.4845Z"
      fill="black"
      {...props}
    />
  </svg>
);

export const NewsIcon = (props) => (
  <svg
    width={24}
    height={22}
    viewBox="0 0 24 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"

  >
    <path
      d="M22.9004 19.9047C20.8026 17.3247 17.1719 15.7842 13.1879 15.7842C12.8414 15.7842 12.5016 15.8105 12.1611 15.8337L10.7331 10.4427L17.6354 8.6165C17.8067 8.57066 17.9666 8.48972 18.105 8.37882C18.2434 8.26792 18.3573 8.12947 18.4394 7.97225C18.6066 7.6505 18.6299 7.2695 18.5039 6.92675C17.0631 3.02075 12.8076 0.871997 8.42538 1.7255L8.12238 0.579497C8.07441 0.408931 7.96129 0.264066 7.80745 0.176167C7.6536 0.0882685 7.47137 0.0643849 7.30007 0.109671C7.12877 0.154957 6.98214 0.265781 6.89184 0.418225C6.80153 0.570669 6.77479 0.752508 6.81738 0.924497L7.12038 2.0705C2.88513 3.4955 0.248128 7.46975 0.927628 11.5775C0.986878 11.9375 1.19538 12.257 1.50063 12.4542C1.64973 12.5504 1.81723 12.6145 1.99245 12.6424C2.16767 12.6703 2.34678 12.6614 2.51838 12.6162L9.42738 10.7877L10.8044 15.9882C7.78863 16.4727 5.14263 17.8512 3.47388 19.904C3.32673 20.0839 3.23414 20.3022 3.20703 20.533C3.17992 20.7639 3.21942 20.9976 3.32088 21.2067C3.53088 21.6485 3.96363 21.923 4.44963 21.923H21.9224C22.4084 21.923 22.8411 21.6485 23.0511 21.2067C23.2596 20.7732 23.2011 20.2745 22.9004 19.9047ZM17.2341 7.32575L13.5614 8.29775C12.8616 6.125 11.7951 4.109 10.5734 2.91425C13.5516 3.0245 16.2396 4.7075 17.2341 7.32575ZM2.24988 11.291C1.81938 8.50625 3.31638 5.71775 5.84463 4.15625C5.36838 5.7995 5.43663 8.084 5.90388 10.3242L2.24988 11.291ZM8.11038 3.17525L8.11188 3.1745H8.11263C9.18588 2.88725 11.0871 5.0495 12.2549 8.64275L7.20963 9.97775C6.44763 6.27875 7.03188 3.4625 8.11038 3.17525ZM4.67613 20.573C6.52263 18.446 9.75663 17.1342 13.1879 17.1342C16.6191 17.1342 19.8531 18.4467 21.6996 20.573H4.67613Z"
      fill="black"
      {...props}
    />
  </svg>
);

export const LoginIcon = (props) => (
  <svg
    stroke="currentColor"
    fill="currentColor"
    strokeWidth={0}
    viewBox="0 0 512 512"
    height="24px"
    width="24px"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M256 48c-42.9 0-84.2 13-119.2 37.5-34.2 24-60.2 57.2-75.1 96.1L58 192h45.7l1.9-5c8.2-17.8 19.4-33.9 33.5-48 31.2-31.2 72.7-48.4 116.9-48.4s85.7 17.2 116.9 48.4c31.2 31.2 48.4 72.7 48.4 116.9 0 44.1-17.2 85.7-48.4 116.9-31.2 31.2-72.7 48.4-116.9 48.4-44.1 0-85.6-17.2-116.9-48.4-14-14-25.3-30.1-33.5-47.9l-1.9-5H58l3.6 10.4c14.9 38.9 40.9 72.1 75.1 96.1C171.8 451.1 213 464 256 464c114.7 0 208-93.3 208-208S370.7 48 256 48z" />
    <path d="M48 277.4h189.7l-43.6 44.7L224 352l96-96-96-96-31 29.9 44.7 44.7H48v42.8z" />
  </svg>
);

export const ExternalLinkIcon = (props) => (
  <svg
    stroke="currentColor"
    fill="none"
    strokeWidth={2}
    viewBox="0 0 24 24"
    strokeLinecap="round"
    strokeLinejoin="round"
    height="24px"
    width="24px"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M12 6h-6a2 2 0 0 0 -2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-6" />
    <path d="M11 13l9 -9" />
    <path d="M15 4h5v5" />
  </svg>
)