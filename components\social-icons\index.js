import {
  Mail,
  Github,
  Facebook,
  Youtube,
  Linkedin,
  Twitter,
  Mastodon,
  Threads,
  Instagram,
} from "./icons";

const components = {
  mail: Mail,
  github: Github,
  facebook: Facebook,
  youtube: Youtube,
  linkedin: Linkedin,
  twitter: Twitter,
  mastodon: <PERSON><PERSON><PERSON>,
  threads: Threads,
  instagram: Instagram,
  // pintrest: Pintrest,
};

const SocialIcon = ({ kind, href, size = 8 }) => {
  if (
    !href ||
    (kind === "mail" &&
      !/^mailto:\w+([.-]?\w+)@\w+([.-]?\w+)(.\w{2,3})+$/.test(href))
  )
    return null;

  const SocialSvg = components[kind];

  return (
    <a
      className="text-sandee-sm  rounded-full p-[6px] w-8 h-8 flex items-center justify-center bg-[#F4F4F4] bg-opacity-20"
      target="_blank"
      rel="noopener noreferrer"
      href={href}
    >
      <span className="sr-only">{kind}</span>
      <SocialSvg
        className={`fill-current text-white hover:text-primary-500   h-${size} w-${size}`}
      />
    </a>
  );
};

export default SocialIcon;
