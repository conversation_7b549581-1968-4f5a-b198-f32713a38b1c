{"ListicleData": {"data": [{"id": 3333, "name": "Most Popular Beaches in the World", "nameSlug": "most-popular-beaches-in-the-world", "cityName": null, "stateName": null, "countryName": null, "tag": "World", "image": "https://jumpinvestors.s3.amazonaws.com/1687357745724.jpg", "description": "", "worldwide": true, "rank": 100, "createdAt": "2023-06-21T14:29:05.996Z", "updatedAt": "2023-08-24T07:46:04.118Z", "imageId": 73224, "countryId": null, "stateId": null, "cityId": null, "listiclesBeaches": [{"id": 15816, "rank": 100, "AllBeachId": 36964, "AllBeach": {"id": 36964, "name": "Spiaggia Dei Conigli", "nameSlug": "spiaggia-dei-conigli", "rating100": 96, "city": {"id": 39084, "name": "Lampedusa e Linosa", "slug": "lampedusa-e-linosa", "state": {"id": 4296, "name": "Sicily", "slug": "sicily", "country": {"id": 703, "name": "Italy", "slug": "italy"}}}, "images": [{"license": {"SandeeUploaderId": "5ce2d4cc165a9c523f9b2eae", "SandeeUploaderName": "<PERSON><PERSON>", "SandeeUploaderEmail": "<EMAIL>", "relativePath": null, "name": "5ce2d4cc165a9c523f9b2eae/image/jpeg/598df95041eaf50f622fca51/1564875615403.jpeg", "type": "image/jpeg", "photographerName": "Figiu", "photographerLink": "https://commons.wikimedia.org/wiki/User:Figiu", "photoName": "Spiaggia Isola dei Coniglio Lampedusa", "photoSource": "Wikimedia Commons", "photoLink": "https://commons.wikimedia.org/wiki/File:Spiagg<PERSON>_Isola_dei_Coniglio_Lampedusa.JPG", "photoModified": "No", "licenseName": "CC by 3.0", "licenseLink": "https://creativecommons.org/licenses/by/3.0/deed.en", "acl": "public-read", "key": "5ce2d4cc165a9c523f9b2eae/image/jpeg/598df95041eaf50f622fca51/1564875615403.jpeg", "success_action_status": "201", "content-type": "image/jpeg", "bucket": "sandee-v2", "X-Amz-Algorithm": "AWS4-HMAC-SHA256", "X-Amz-Credential": "AKIAICOSRWABPZPXD6IQ/20190803/us-west-2/s3/aws4_request", "X-Amz-Date": "20190803T234049Z", "Policy": "eyJleHBpcmF0aW9uIjoiMjAxOS0wOC0wM1QyMzo0NTo0OVoiLCJjb25kaXRpb25zIjpbeyJhY2wiOiJwdWJsaWMtcmVhZCJ9LHsia2V5IjoiNWNlMmQ0Y2MxNjVhOWM1MjNmOWIyZWFlL2ltYWdlL2pwZWcvNTk4ZGY5NTA0MWVhZjUwZjYyMmZjYTUxLzE1NjQ4NzU2MTU0MDMuanBlZyJ9LHsic3VjY2Vzc19hY3Rpb25fc3RhdHVzIjoiMjAxIn0seyJjb250ZW50LXR5cGUiOiJpbWFnZS9qcGVnIn0seyJidWNrZXQiOiJzYW5kZWUtdjIifSx7IlgtQW16LUFsZ29yaXRobSI6IkFXUzQtSE1BQy1TSEEyNTYifSx7IlgtQW16LUNyZWRlbnRpYWwiOiJBS0lBSUNPU1JXQUJQWlBYRDZJUS8yMDE5MDgwMy91cy13ZXN0LTIvczMvYXdzNF9yZXF1ZXN0In0seyJYLUFtei1EYXRlIjoiMjAxOTA4MDNUMjM0MDQ5WiJ9XX0=", "X-Amz-Signature": "6c75e618307b38ea7f3a3f30a26ff41244a4c409f584ec23a3de09305415909f"}, "id": 45549, "imageUrl": "https://sandee-resized.s3-us-west-2.amazonaws.com/original/5ce2d4cc165a9c523f9b2eae/image/jpeg/598df95041eaf50f622fca51/1564875615403.webp", "fileNameProcessed": "5ce2d4cc165a9c523f9b2eae/image/jpeg/598df95041eaf50f622fca51/1564875615403.webp", "createdByDisplayName": "<PERSON><PERSON>", "imageProcessed": "169090776276445029"}]}}, {"id": 15810, "rank": 100, "AllBeachId": 8184, "AllBeach": {"id": 8184, "name": "Quarta Praia", "nameSlug": "quarta-praia", "rating100": 83, "city": {"id": 46135, "name": "Morro de Sao Paulo", "slug": "morro-de-sao-paulo", "state": {"id": 5645, "name": "Bahia", "slug": "bahia", "country": {"id": 855, "name": "Brazil", "slug": "brazil"}}}, "images": [{"license": {"licenseLink": "https://creativecommons.org/licenses/by/2.0", "licenseName": "CC BY 2.0", "photoModified": "No", "photoLink": null, "photoName": null, "photographerName": "<PERSON>", "photoSource": "Google", "photographerLink": "https://www.google.com/maps/contrib/118232232567123167210"}, "id": 75579, "imageUrl": "https://lh5.googleusercontent.com/p/AF1QipMqB2Nun67GCcenjZTU2CFcszg0I-Pr1FtavvZq=w900", "fileNameProcessed": null, "createdByDisplayName": "<PERSON>", "imageProcessed": null}]}}, {"id": 15809, "rank": 100, "AllBeachId": 15887, "AllBeach": {"id": 15887, "name": "Varadero Beach", "nameSlug": "varadero-beach", "rating100": 96, "city": {"id": 47262, "name": "Varadero", "slug": "var<PERSON><PERSON>", "state": {"id": 5338, "name": "Matanzas", "slug": "<PERSON><PERSON><PERSON>", "country": {"id": 780, "name": "Cuba", "slug": "cuba"}}}, "images": [{"license": {"SandeeUploaderId": "5ce2761d916f87299dfcb11b", "SandeeUploaderName": "Meiduo Pan", "SandeeUploaderEmail": "<EMAIL>", "relativePath": "/<PERSON><PERSON><PERSON>(Cuba)(Momo).jpg", "name": "5ce2761d916f87299dfcb11b/image/jpeg/57996124666f1f0cec6f6ed0/1562023577900.jpg", "type": "image/jpeg", "photographerName": "<PERSON><PERSON>", "photographerLink": "https://www.flickr.com/photos/kudumomo/", "photoName": "Varadero", "photoSource": "flickr.com", "photoLink": "https://www.flickr.com/photos/kudumomo/**********/", "photoModified": "No", "licenseName": "CC BY 2.0", "licenseLink": "https://creativecommons.org/licenses/by/2.0/", "acl": "public-read", "key": "5ce2761d916f87299dfcb11b/image/jpeg/57996124666f1f0cec6f6ed0/1562023577900.jpg", "success_action_status": "201", "content-type": "image/jpeg", "bucket": "sandee-v2", "X-Amz-Algorithm": "AWS4-HMAC-SHA256", "X-Amz-Credential": "AKIAICOSRWABPZPXD6IQ/20190701/us-west-2/s3/aws4_request", "X-Amz-Date": "20190701T232708Z", "Policy": "eyJleHBpcmF0aW9uIjoiMjAxOS0wNy0wMVQyMzozMjowOFoiLCJjb25kaXRpb25zIjpbeyJhY2wiOiJwdWJsaWMtcmVhZCJ9LHsia2V5IjoiNWNlMjc2MWQ5MTZmODcyOTlkZmNiMTFiL2ltYWdlL2pwZWcvNTc5OTYxMjQ2NjZmMWYwY2VjNmY2ZWQwLzE1NjIwMjM1Nzc5MDAuanBnIn0seyJzdWNjZXNzX2FjdGlvbl9zdGF0dXMiOiIyMDEifSx7ImNvbnRlbnQtdHlwZSI6ImltYWdlL2pwZWcifSx7ImJ1Y2tldCI6InNhbmRlZS12MiJ9LHsiWC1BbXotQWxnb3JpdGhtIjoiQVdTNC1ITUFDLVNIQTI1NiJ9LHsiWC1BbXotQ3JlZGVudGlhbCI6IkFLSUFJQ09TUldBQlBaUFhENklRLzIwMTkwNzAxL3VzLXdlc3QtMi9zMy9hd3M0X3JlcXVlc3QifSx7IlgtQW16LURhdGUiOiIyMDE5MDcwMVQyMzI3MDhaIn1dfQ==", "X-Amz-Signature": "733e3b6b971898bb60d85630b00defb0b890ef6c825373854b90862252246170"}, "id": 12829, "imageUrl": "https://s3.us-west-2.amazonaws.com/sandee-v2/5ce2761d916f87299dfcb11b%2Fimage%2Fjpeg%2F57996124666f1f0cec6f6ed0%2F1562023577900.jpg", "fileNameProcessed": "5ce2761d916f87299dfcb11b/image/jpeg/57996124666f1f0cec6f6ed0/1562023577900.jpeg", "createdByDisplayName": "Meiduo Pan", "imageProcessed": "169089948823312595"}]}}, {"id": 15814, "rank": 100, "AllBeachId": 85575, "AllBeach": {"id": 85575, "name": "Trunk Bay", "nameSlug": "trunk-bay", "rating100": 98, "city": {"id": 39849, "name": "Cruz Bay", "slug": "cruz-bay", "state": {"id": 4929, "name": "Saint <PERSON>", "slug": "saint-john", "country": {"id": 682, "name": "United States Virgin Islands", "slug": "united-states-virgin-islands"}}}, "images": [{"license": {"licenseName": "CC BY-SA 2.0", "licenseLink": "https://creativecommons.org/licenses/by-sa/2.0/", "photoModified": "No", "photographerName": "Traveller-<PERSON><PERSON>", "photographerLink": "https://www.flickr.com/photos/*********@N03", "photoLink": "https://live.staticflickr.com/7501/15869475451_f59677485a_b.jpg", "photoName": "US Virgin Islands: St. John's", "photoSource": "flickr.com"}, "id": 9926, "imageUrl": "https://sandee-resized.s3-us-west-2.amazonaws.com/original/5cd994a908883e0d23444e50/jpeg/57995fec666f1f0cec6f577a/1558053360877.webp", "fileNameProcessed": "5cd994a908883e0d23444e50/jpeg/57995fec666f1f0cec6f577a/1558053360877.webp", "createdByDisplayName": "<PERSON><PERSON><PERSON>", "imageProcessed": "16908987658609695"}]}}, {"id": 15811, "rank": 100, "AllBeachId": 1826, "AllBeach": {"id": 1826, "name": "Eagle Beach", "nameSlug": "eagle-beach", "rating100": 96, "city": {"id": 52016, "name": "Punta Brabo", "slug": "punta-brabo", "state": {"id": 4936, "name": "Oranjestad", "slug": "oranjestad", "country": {"id": 777, "name": "Aruba", "slug": "aruba"}}}, "images": [{"license": {"SandeeUploaderId": "5ce2d4cc165a9c523f9b2eae", "SandeeUploaderName": "<PERSON><PERSON>", "SandeeUploaderEmail": "<EMAIL>", "relativePath": null, "name": "5ce2d4cc165a9c523f9b2eae/image/jpeg/57995e56666f1f0cec6f4972/1564880166267.jpg", "type": "image/jpeg", "photographerName": "Rumblebee", "photographerLink": "https://en.wikipedia.org/wiki/User:Rumblebee", "photoName": "Eagle Beach Aruba", "photoSource": "Wikimedia Commons", "photoLink": "https://commons.wikimedia.org/wiki/File:Eagle_Beach_Aruba.jpg", "photoModified": "NO", "licenseName": "CC by 3.0", "licenseLink": "https://creativecommons.org/licenses/by/3.0/deed.en", "acl": "public-read", "key": "5ce2d4cc165a9c523f9b2eae/image/jpeg/57995e56666f1f0cec6f4972/1564880166267.jpg", "success_action_status": "201", "content-type": "image/jpeg", "bucket": "sandee-v2", "X-Amz-Algorithm": "AWS4-HMAC-SHA256", "X-Amz-Credential": "AKIAICOSRWABPZPXD6IQ/20190804/us-west-2/s3/aws4_request", "X-Amz-Date": "20190804T005641Z", "Policy": "eyJleHBpcmF0aW9uIjoiMjAxOS0wOC0wNFQwMTowMTo0MVoiLCJjb25kaXRpb25zIjpbeyJhY2wiOiJwdWJsaWMtcmVhZCJ9LHsia2V5IjoiNWNlMmQ0Y2MxNjVhOWM1MjNmOWIyZWFlL2ltYWdlL2pwZWcvNTc5OTVlNTY2NjZmMWYwY2VjNmY0OTcyLzE1NjQ4ODAxNjYyNjcuanBnIn0seyJzdWNjZXNzX2FjdGlvbl9zdGF0dXMiOiIyMDEifSx7ImNvbnRlbnQtdHlwZSI6ImltYWdlL2pwZWcifSx7ImJ1Y2tldCI6InNhbmRlZS12MiJ9LHsiWC1BbXotQWxnb3JpdGhtIjoiQVdTNC1ITUFDLVNIQTI1NiJ9LHsiWC1BbXotQ3JlZGVudGlhbCI6IkFLSUFJQ09TUldBQlBaUFhENklRLzIwMTkwODA0L3VzLXdlc3QtMi9zMy9hd3M0X3JlcXVlc3QifSx7IlgtQW16LURhdGUiOiIyMDE5MDgwNFQwMDU2NDFaIn1dfQ==", "X-Amz-Signature": "554ab8bc29ad9b2d74c36f43d371ad1d412e5c77179fd4a05c4198785f62896b"}, "id": 8884, "imageUrl": "https://sandee-resized.s3-us-west-2.amazonaws.com/original/5ce2d4cc165a9c523f9b2eaeimagejpeg57995e56666f1f0cec6f49721564880166267.webp", "fileNameProcessed": "5ce2d4cc165a9c523f9b2eae/image/jpeg/57995e56666f1f0cec6f4972/1564880166267.webp", "createdByDisplayName": "<PERSON><PERSON>", "imageProcessed": "16908984823808658"}]}}, {"id": 15812, "rank": 100, "AllBeachId": 26570, "AllBeach": {"id": 26570, "name": "Radhanagar Beach", "nameSlug": "radhanagar-beach", "rating100": 96, "city": {"id": 33868, "name": "Havelock Island", "slug": "havelock-island", "state": {"id": 5291, "name": "Andaman and Nicobar Islands", "slug": "andaman-and-nicobar-islands", "country": {"id": 671, "name": "India", "slug": "india"}}}, "images": [{"license": {"licenseLink": "https://creativecommons.org/licenses/by/2.0/", "licenseName": "CC BY 2.0", "photoModified": "No", "photoLink": "https://www.flickr.com/photos/55249578@N05/16697781236", "photoName": "Radhanagar Beach", "photographerName": "voxpepoli", "photoSource": "voxpepoli", "photographerLink": "https://www.flickr.com/photos/55249578@N05"}, "id": 59384, "imageUrl": "https://live.staticflickr.com/8657/16697781236_db9d0bb8fe.jpg", "fileNameProcessed": "1688673964089.webp", "createdByDisplayName": "User uploaded photo", "imageProcessed": "169091128734558731"}]}}, {"id": 15815, "rank": 100, "AllBeachId": 6735, "AllBeach": {"id": 6735, "name": "Baia Dos Porcos", "nameSlug": "baia-dos-porcos", "rating100": 96, "city": {"id": 33806, "name": "<PERSON><PERSON> <PERSON>", "slug": "ilha-de-fernando-de-noronha", "state": {"id": 4301, "name": "Pernambuco", "slug": "pernambuco", "country": {"id": 855, "name": "Brazil", "slug": "brazil"}}}, "images": [{"license": {"licenseLink": "https://creativecommons.org/licenses/by/2.0/", "licenseName": "CC BY 2.0", "photoModified": "No", "photoLink": "https://www.flickr.com/photos/af2009/**********/", "photoName": "<PERSON><PERSON>, Baía dos Porcos", "photographerName": "<PERSON><PERSON>", "photoSource": "flickr.com", "photographerLink": "https://www.flickr.com/photos/af2009"}, "id": 8624, "imageUrl": "https://sandee-resized.s3-us-west-2.amazonaws.com/original/5649513565383f6ccbb6c539/5b75ad13e9d7ec174dece327.webp", "fileNameProcessed": "5649513565383f6ccbb6c539/5b75ad13e9d7ec174dece327.jpeg", "createdByDisplayName": "Sandee Research", "imageProcessed": "16908984109708398"}]}}]}, {"id": 3336, "name": "Most Famous Beaches in the World", "nameSlug": "most-famous-beaches-in-the-world", "cityName": null, "stateName": null, "countryName": null, "tag": "Famous", "image": "https://jumpinvestors.s3.amazonaws.com/1688772721855.jpg", "description": "", "worldwide": true, "rank": 100, "createdAt": "2023-06-21T15:14:58.916Z", "updatedAt": "2023-08-24T07:51:09.982Z", "imageId": 73253, "countryId": null, "stateId": null, "cityId": null, "listiclesBeaches": [{"id": 20478, "rank": 1, "AllBeachId": 76099, "AllBeach": {"id": 76099, "name": "Miami Beach - South Beach", "nameSlug": "miami-beach---south-beach", "rating100": 98, "city": {"id": 55915, "name": "Miami Beach", "slug": "miami-beach", "state": {"id": 5634, "name": "Florida", "slug": "florida", "country": {"id": 661, "name": "United States", "slug": "united-states"}}}, "images": [{"license": {"SandeeUploaderId": "5bbef4d9beb4313b6c211efd", "SandeeUploaderName": "<PERSON>", "SandeeUploaderEmail": "<EMAIL>", "relativePath": null, "name": "5bbef4d9beb4313b6c211efd/image/jpeg/582f1446a71091055f1bb24d/1568336625963.jpg", "type": "image/jpeg", "photographerName": "<PERSON>", "photographerLink": "https://www.flickr.com/photos/50144889@N08/", "photoName": "Miami Beach. Nikon D3100. DSC_0207", "photoSource": "flickr.com", "photoLink": "https://www.flickr.com/photos/50144889@N08/34620341132/", "photoModified": "No", "licenseName": "CC BY 2.0", "licenseLink": "https://creativecommons.org/licenses/by/2.0/", "acl": "public-read", "key": "5bbef4d9beb4313b6c211efd/image/jpeg/582f1446a71091055f1bb24d/1568336625963.jpg", "success_action_status": "201", "content-type": "image/jpeg", "bucket": "sandee-v2", "X-Amz-Algorithm": "AWS4-HMAC-SHA256", "X-Amz-Credential": "AKIAICOSRWABPZPXD6IQ/20190913/us-west-2/s3/aws4_request", "X-Amz-Date": "20190913T010448Z", "Policy": "eyJleHBpcmF0aW9uIjoiMjAxOS0wOS0xM1QwMTowOTo0OFoiLCJjb25kaXRpb25zIjpbeyJhY2wiOiJwdWJsaWMtcmVhZCJ9LHsia2V5IjoiNWJiZWY0ZDliZWI0MzEzYjZjMjExZWZkL2ltYWdlL2pwZWcvNTgyZjE0NDZhNzEwOTEwNTVmMWJiMjRkLzE1NjgzMzY2MjU5NjMuanBnIn0seyJzdWNjZXNzX2FjdGlvbl9zdGF0dXMiOiIyMDEifSx7ImNvbnRlbnQtdHlwZSI6ImltYWdlL2pwZWcifSx7ImJ1Y2tldCI6InNhbmRlZS12MiJ9LHsiWC1BbXotQWxnb3JpdGhtIjoiQVdTNC1ITUFDLVNIQTI1NiJ9LHsiWC1BbXotQ3JlZGVudGlhbCI6IkFLSUFJQ09TUldBQlBaUFhENklRLzIwMTkwOTEzL3VzLXdlc3QtMi9zMy9hd3M0X3JlcXVlc3QifSx7IlgtQW16LURhdGUiOiIyMDE5MDkxM1QwMTA0NDhaIn1dfQ==", "X-Amz-Signature": "6ffa27298db5adc6113ecc41cd7a6afa6107bd77fc9e38401616a9d67ba6cc81"}, "id": 29089, "imageUrl": "https://sandee-resized.s3-us-west-2.amazonaws.com/original/5bbef4d9beb4313b6c211efd/image/jpeg/582f1446a71091055f1bb24d/1568336625963.webp", "fileNameProcessed": "5bbef4d9beb4313b6c211efd/image/jpeg/582f1446a71091055f1bb24d/1568336625963.webp", "createdByDisplayName": "<PERSON>", "imageProcessed": "169090356502028710"}]}}, {"id": 15832, "rank": 2, "AllBeachId": 75311, "AllBeach": {"id": 75311, "name": "Santa Monica Beach", "nameSlug": "santa-monica-beach", "rating100": 98, "city": {"id": 47238, "name": "Santa Monica", "slug": "santa-monica", "state": {"id": 5228, "name": "California", "slug": "california", "country": {"id": 661, "name": "United States", "slug": "united-states"}}}, "images": [{"license": {"licenseName": "", "licenseLink": "", "photoName": "", "photoLink": "", "photoSource": "", "photoModified": "", "photographerName": "", "photographerLink": ""}, "id": 2926, "imageUrl": "https://sandee-resized.s3-us-west-2.amazonaws.com/original/589deb9045b10b39dd6bc13d/image/jpeg/57995dac666f1f0cec6f34b3/1578125362573.webp", "fileNameProcessed": "589deb9045b10b39dd6bc13d/image/jpeg/57995dac666f1f0cec6f34b3/1578125362573.webp", "createdByDisplayName": "<PERSON>", "imageProcessed": "16908969149332808"}]}}, {"id": 15834, "rank": 3, "AllBeachId": 76672, "AllBeach": {"id": 76672, "name": "Waikiki Beach", "nameSlug": "waikiki-beach", "rating100": 97, "city": {"id": 53666, "name": "Honolulu", "slug": "honolulu", "state": {"id": 4722, "name": "Hawaii", "slug": "hawaii", "country": {"id": 661, "name": "United States", "slug": "united-states"}}}, "images": [{"license": {"licenseName": "CC BY 2.0", "licenseLink": "https://creativecommons.org/licenses/by/2.0/", "photoModified": "No", "photographerName": "<PERSON>", "photographerLink": "https://www.flickr.com/photos/40315625@N08", "photoLink": "https://live.staticflickr.com/7540/16178632002_fbf2e4ee82_b.jpg", "photoName": "Rainbow Tower Waikiki, Hawaii", "photoSource": "flickr.com"}, "id": 33145, "imageUrl": "https://sandee-resized.s3-us-west-2.amazonaws.com/original/5cd9a7b84a2cda0d4ecd4243jpeg585a0c7fa71091055f1bc60d1559151201609.webp", "fileNameProcessed": "5cd9a7b84a2cda0d4ecd4243jpeg585a0c7fa71091055f1bc60d1559151201609.webp", "createdByDisplayName": "<PERSON>", "imageProcessed": "169090460633032760"}]}}, {"id": 15836, "rank": 4, "AllBeachId": 2461, "AllBeach": {"id": 2461, "name": "Bondi Beach", "nameSlug": "bondi-beach", "rating100": 98, "city": {"id": 32237, "name": "Sydney", "slug": "sydney", "state": {"id": 6022, "name": "New South Wales", "slug": "new-south-wales", "country": {"id": 685, "name": "Australia", "slug": "australia"}}}, "images": [{"license": [], "id": 88448, "imageUrl": "https://jumpinvestors.s3.amazonaws.com/1703521352802.jpg", "fileNameProcessed": null, "createdByDisplayName": "<PERSON>", "imageProcessed": "1703521353003"}]}}, {"id": 15839, "rank": 5, "AllBeachId": 24209, "AllBeach": {"id": 24209, "name": "Navagio Shipwreck Beach", "nameSlug": "navagio-shipwreck-beach", "rating100": 96, "city": {"id": 44089, "name": "Elation", "slug": "elation", "state": {"id": 4552, "name": "Ionian Islands", "slug": "ionian-islands", "country": {"id": 863, "name": "Greece", "slug": "greece"}}}, "images": [{"license": {"licenseName": "CC BY 2.0", "licenseLink": "https://creativecommons.org/licenses/by/2.0/", "photoModified": "No", "photographerName": "dronepicr", "photographerLink": "https://www.flickr.com/photos/*********@N02", "photoLink": "https://live.staticflickr.com/4860/45746239274_07a623e5c0_b.jpg", "photoName": "Zakynthos island in Greece", "photoSource": "flickr.com"}, "id": 44067, "imageUrl": "https://sandee-resized.s3-us-west-2.amazonaws.com/original/5cd994a908883e0d23444e50jpeg598df1c341eaf50f622eda6d1558412754568.webp", "fileNameProcessed": "5cd994a908883e0d23444e50/jpeg/598df1c341eaf50f622eda6d/1558412754568.jpeg", "createdByDisplayName": "<PERSON><PERSON><PERSON>", "imageProcessed": "169090739275043593"}]}}, {"id": 15840, "rank": 100, "AllBeachId": 8742, "AllBeach": {"id": 8742, "name": "The Baths", "nameSlug": "the-baths", "rating100": 96, "city": {"id": 43056, "name": "Spanish Town", "slug": "spanish-town", "state": {"id": 5198, "name": "Virgin Gorda", "slug": "virgin-gorda", "country": {"id": 750, "name": "British Virgin Islands", "slug": "british-virgin-islands"}}}, "images": [{"license": {"SandeeUploaderId": "5cda23554bf5cd0d2a9bd4f9", "SandeeUploaderName": "<PERSON>", "SandeeUploaderEmail": "<EMAIL>", "relativePath": null, "name": "5cda23554bf5cd0d2a9bd4f9/image/jpeg/5b44d1c03b9ea9277057c71c/1558469947808.jpg", "type": "image/jpeg", "acl": "public-read", "key": "5cda23554bf5cd0d2a9bd4f9/image/jpeg/5b44d1c03b9ea9277057c71c/1558469947808.jpg", "success_action_status": "201", "content-type": "image/jpeg", "bucket": "sandee-v2", "X-Amz-Algorithm": "AWS4-HMAC-SHA256", "X-Amz-Credential": "AKIAICOSRWABPZPXD6IQ/20190521/us-west-2/s3/aws4_request", "X-Amz-Date": "20190521T201909Z", "Policy": "eyJleHBpcmF0aW9uIjoiMjAxOS0wNS0yMVQyMDoyNDowOVoiLCJjb25kaXRpb25zIjpbeyJhY2wiOiJwdWJsaWMtcmVhZCJ9LHsia2V5IjoiNWNkYTIzNTU0YmY1Y2QwZDJhOWJkNGY5L2ltYWdlL2pwZWcvNWI0NGQxYzAzYjllYTkyNzcwNTdjNzFjLzE1NTg0Njk5NDc4MDguanBnIn0seyJzdWNjZXNzX2FjdGlvbl9zdGF0dXMiOiIyMDEifSx7ImNvbnRlbnQtdHlwZSI6ImltYWdlL2pwZWcifSx7ImJ1Y2tldCI6InNhbmRlZS12MiJ9LHsiWC1BbXotQWxnb3JpdGhtIjoiQVdTNC1ITUFDLVNIQTI1NiJ9LHsiWC1BbXotQ3JlZGVudGlhbCI6IkFLSUFJQ09TUldBQlBaUFhENklRLzIwMTkwNTIxL3VzLXdlc3QtMi9zMy9hd3M0X3JlcXVlc3QifSx7IlgtQW16LURhdGUiOiIyMDE5MDUyMVQyMDE5MDlaIn1dfQ==", "X-Amz-Signature": "a6a3797bc551f2cad884966ecf521e084f5a91d0be17d82b2c3065b3f4deae8c"}, "id": 55598, "imageUrl": "https://s3.us-west-2.amazonaws.com/sandee-v2/5cda23554bf5cd0d2a9bd4f9%2Fimage%2Fjpeg%2F5b44d1c03b9ea9277057c71c%2F1558469947808.jpg", "fileNameProcessed": null, "createdByDisplayName": "<PERSON>", "imageProcessed": "169091034068955057"}]}}, {"id": 15845, "rank": 100, "AllBeachId": 44582, "AllBeach": {"id": 44582, "name": "Playa Del Carmen", "nameSlug": "playa-del-carmen", "rating100": 98, "city": {"id": 30025, "name": "Playa del Carmen", "slug": "playa-del-carmen", "state": {"id": 5260, "name": "Quintana Roo", "slug": "quintana-roo", "country": {"id": 769, "name": "Mexico", "slug": "mexico"}}}, "images": [{"license": {"photographerName": "", "photoName": "", "photoLink": "", "photoModified": "No", "licenseName": "CC BY 2.0", "licenseLink": "https://creativecommons.org/licenses/by/2.0/legalcode"}, "id": 29489, "imageUrl": "http://sandee-media.s3.amazonaws.com/5649513565383f6ccbb6c956/5b9c03890e2d6f37bd44dd4a.jpg", "fileNameProcessed": "5649513565383f6ccbb6c956/5b9c03890e2d6f37bd44dd4a.jpeg", "createdByDisplayName": null, "imageProcessed": "169090366866429110"}]}}]}]}, "BlogData": {"data": [{"id": 3381, "name": "Best 15 Best Beaches in the Philippines", "nameSlug": "best-15-best-beaches-in-the-philippines", "cityName": null, "stateName": null, "countryName": "Philippines", "tag": "Country", "image": "https://jumpinvestors.s3.amazonaws.com/1688658679464.jpg", "description": "", "worldwide": false, "rank": 100, "createdAt": "2023-06-22T16:20:18.221Z", "updatedAt": "2024-01-01T12:13:13.152Z", "imageId": 69467, "countryId": 658, "stateId": null, "cityId": null, "listicleImage": {"license": [], "imageUrl": "https://jumpinvestors.s3.amazonaws.com/1688658679464.jpg", "imageProcessed": "1692800655181214", "createdByDisplayName": "User uploaded photo", "id": 69467}}, {"id": 3389, "name": "Best 17 Nude Beaches in the World", "nameSlug": "best-17-nude-beaches-in-the-world", "cityName": null, "stateName": null, "countryName": null, "tag": "<PERSON><PERSON>", "image": "https://jumpinvestors.s3.amazonaws.com/1688658866631.jpg", "description": "", "worldwide": true, "rank": 100, "createdAt": "2023-06-22T17:04:59.576Z", "updatedAt": "2023-08-24T07:30:26.349Z", "imageId": 73141, "countryId": null, "stateId": null, "cityId": null, "listicleImage": {"license": [], "imageUrl": "https://jumpinvestors.s3.amazonaws.com/1688658866631.jpg", "imageProcessed": "1692862201455918", "createdByDisplayName": "User uploaded photo", "id": 73141}}, {"id": 3374, "name": "Most Dangerous Beaches in the World", "nameSlug": "most-dangerous-beaches-in-the-world", "cityName": null, "stateName": null, "countryName": null, "tag": "Dangerous", "image": "https://jumpinvestors.s3.amazonaws.com/1688658915378.jpg", "description": "", "worldwide": true, "rank": 100, "createdAt": "2023-06-22T16:07:41.322Z", "updatedAt": "2023-08-24T12:42:13.455Z", "imageId": 74051, "countryId": null, "stateId": null, "cityId": null, "listicleImage": {"license": [], "imageUrl": "https://jumpinvestors.s3.amazonaws.com/1688658915378.jpg", "imageProcessed": "16928809214260", "createdByDisplayName": "User uploaded photo", "id": 74051}}, {"id": 3336, "name": "Most Famous Beaches in the World", "nameSlug": "most-famous-beaches-in-the-world", "cityName": null, "stateName": null, "countryName": null, "tag": "Famous", "image": "https://jumpinvestors.s3.amazonaws.com/1688772721855.jpg", "description": "", "worldwide": true, "rank": 100, "createdAt": "2023-06-21T15:14:58.916Z", "updatedAt": "2023-08-24T07:51:09.982Z", "imageId": 73253, "countryId": null, "stateId": null, "cityId": null, "listicleImage": {"license": [], "imageUrl": "https://jumpinvestors.s3.amazonaws.com/1688772721855.jpg", "imageProcessed": "16928634487891030", "createdByDisplayName": "User uploaded photo", "id": 73253}}]}}