export const ALPHABET = [
  "A",
  "B",
  "C",
  "D",
  "E",
  "F",
  "G",
  "H",
  "I",
  "J",
  "K",
  "L",
  "M",
  "N",
  "O",
  "P",
  "Q",
  "R",
  "S",
  "T",
  "U",
  "V",
  "W",
  "X",
  "Y",
  "Z",
];
export const FILTERS = [
  {
    name: "Nude ",
    id: "nude",
  },
  // {
  //   name: "Family ",
  //   id: "Family-Friendly",
  // },
  {
    name: "Surfing ",
    id: "surfing",
  },
  {
    name: "Camping ",
    id: "camping",
  },
  {
    name: "<PERSON> ",
    id: "dogs",
  },

  {
    name: "Disability ",
    id: "disability",
  },
  {
    name: "LGBTQ+",
    id: "lgbtqplus",
  },
];

export const BeachDetailsFilter = [
  {
    title: "Amenities",
    items: [
      {
        id: "ATM",
        value: "ATM",
        label: "ATM",
      },
      {
        id: "barbecue",
        value: "barbecue",
        label: "Barbecue",
      },
      {
        id: "boatRamps",
        value: "boatRamps",
        label: "Boat Ramp",
      },
      {
        id: "dogs",
        value: "dogs",
        label: "Dogs",
      },
      {
        id: "entryFee",
        value: "entryFee",
        label: "Entry Fee",
      }, //new
      {
        id: "firePits",
        value: "firePits",
        label: "Fire Pits",
      },
      {
        id: "hidden",
        value: "hidden",
        label: "Hidden",
      },
      {
        id: "lifeguard",
        value: "lifeguard",
        label: "Lifeguard",
      },
      { id: "marineLife", label: "Marine Life", value: "marineLife" },
      {
        id: "pier",
        value: "pier",
        label: "Pier",
      }, //new
      {
        id: "playground",
        value: "playground",
        label: "Playground",
      },
      {
        id: "restRooms",
        value: "restRooms",
        label: "Restrooms",
      },
      {
        id: "shops",
        value: "shops",
        label: "Shops",
      }, //new
      {
        id: "showers",
        value: "showers",
        label: "Showers",
      },
      {
        id: "wifi",
        value: "wifi",
        label: "WiFi",
      },
    ],
  },
  {
    title: "Activities",
    items: [
      {
        id: "bikePaths",
        value: "bikePaths",
        label: "Bike Paths",
      },
      {
        id: "camping",
        value: "camping",
        label: "Camping",
      },
      {
        id: "clubsPartyScene",
        value: "clubsPartyScene",
        label: "Clubs Party Scene",
      }, //new
      {
        id: "fishing",
        value: "fishing",
        label: "Fishing",
      },
      {
        id: "hiking",
        value: "hiking",
        label: "Hiking",
      },
      {
        id: "nude",
        value: "nude",
        label: "Nude",
      },
      {
        id: "paddleBoards",
        value: "paddleBoards",
        label: "Paddleboarding",
      },
      {
        id: "scubaDiving",
        value: "scubaDiving",
        label: "Scuba Diving",
      },
      {
        id: "snorkeling",
        value: "snorkeling",
        label: "Snorkeling",
      },
      {
        id: "surfing",
        value: "surfing",
        label: "Surfing",
      },
      {
        id: "tours",
        value: "tours",
        label: "Tours",
      }, //new
      {
        id: "volleyball",
        value: "volleyball",
        label: "Volleyball",
      },
    ],
  },
  {
    title: "Rentals",
    items: [
      {
        id: "ATV",
        value: "ATV",
        label: "ATV",
      },
      {
        id: "bikeAccess",
        value: "bikeAccess",
        label: "Bikes",
      },
      {
        id: "boatAccess",
        value: "boatAccess",
        label: "Boat",
      }, //new
      {
        id: "fishingBoats",
        value: "fishingBoats",
        label: "Fishing Boats",
      },
      {
        id: "jetSkiing",
        value: "jetSkiing",
        label: "Jet Skiing",
      },
      {
        id: "kayakingCanoeing",
        value: "kayakingCanoeing",
        label: "Kayaking Canoeing",
      },
      {
        id: "beachMatRental",
        value: "beachMatRental",
        label: "Chair Rentals",
      },
      {
        id: "sailing",
        value: "sailing",
        label: "Sailing",
      }, //new
      {
        id: "speedBoats",
        value: "speedBoats",
        label: "Speed Boats",
      },
      {
        id: "umbrellaRental",
        value: "umbrellaRental",
        label: "Umbrella Rental",
      },
      {
        id: "waterSkiing",
        value: "waterSkiing",
        label: "Waterskiing",
      },
      {
        id: "windsurfing",
        value: "windsurfing",
        label: "Windsurfing",
      },
      {
        id: "yachts",
        value: "yachts",
        label: "Yachts",
      },
    ],
  },
  {
    title: "Food",
    items: [
      {
        id: "bars",
        value: "bars",
        label: "Bars",
      },
      {
        id: "foodSnacks",
        value: "foodSnacks",
        label: "Food Snacks",
      }, //new
      {
        id: "beachVendors",
        value: "beachVendors",
        label: "Local Beach Vendor",
      },
      {
        id: "lounge",
        value: "lounge",
        label: "Lounge",
      }, //new
      {
        id: "restaurant",
        value: "restaurant",
        label: "Restaurants",
      },
    ],
  },
  {
    title: "Access",
    items: [
      {
        id: "bikeRacks",
        value: "bikeRacks",
        label: "Bike Racks",
      },
      {
        id: "carAccess",
        value: "carAccess",
        label: "Car Access",
      },
      {
        id: "disability",
        value: "disability",
        label: "Disabled Access",
      },
      {
        id: "footAccess",
        value: "footAccess",
        label: "Foot Access",
      },
      {
        id: "freeParking",
        value: "freeParking",
        label: "Parking - Free",
      },
      {
        id: "paidParking",
        value: "paidParking",
        label: "Parking - Paid",
      },
      {
        id: "rvParking",
        value: "rvParking",
        label: "Parking - RV Parking",
      },
      {
        id: "streetParking",
        value: "streetParking",
        label: "Parking - Street",
      },
      {
        id: "busStop",
        value: "busStop",
        label: "Public Bus Stop",
      },
      {
        id: "stairsToBeach",
        value: "stairsToBeach",
        label: "Stairs to Beach",
      },
    ],
  },
  {
    title: "Features",
    items: [
      {
        id: "blueFlag",
        value: "blueFlag",
        label: "Blue Flag",
      },
      {
        id: "jellyfish",
        value: "jellyfish",
        label: "Jellyfish",
      }, //new
      {
        id: "pebbles",
        value: "pebbles",
        label: "Pebbles",
      }, //new
      {
        id: "riptide",
        value: "riptide",
        label: "Riptide",
      }, //new
      {
        id: "rocky",
        value: "rocky",
        label: "Rocky",
      }, //new
      {
        id: "sharks",
        value: "sharks",
        label: "Sharks",
      }, //new
      {
        id: "lgbtqplus",
        value: "lgbtqplus",
        label: "LGBTQ+",
      }, //new
    ],
  },
];
