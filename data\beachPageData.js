export const BeachPageData = {
  Summary: [
    { tabLabel: "Overview", label: ":BeachName Overview", id: "summary" },
    { tabLabel: "Overview", label: ":BeachName Overview", id: "introduction" },
    {
      tabLabel: "Restaurants",
      label: "Restaurants Near :BeachName ",
      id: "restaurant",
    },
    { tabLabel: "Hotels", label: "Hotels Near :BeachName ", id: "hotel" },
    {
      tabLabel: "Amenities",
      label: "Amenities at :BeachName ",
      id: "amenities",
    },
    {
      tabLabel: "Activities",
      label: "Activities at :BeachName ",
      id: "activities",
    },
    {
      tabLabel: "Disability Access",
      label: "Disability Access at :BeachName ",
      id: "disabilityAccess",
    },
    { tabLabel: "Parking", label: "Parking at :BeachName ", id: "parking" },
    {
      tabLabel: "Fun Fact",
      label: "Fun Fact About :BeachName ",
      id: "funFact",
    },
    {
      tabLabel: "Attractions",
      label: "Attractions at :BeachName ",
      id: "attraction",
    },

  ],
  beachInformation: [
    //       { id: "allTerrainVehicles", label: "All-Terrain Vehicles" },
    //       { id: "panicTable", label: "Panic Table" },
    //       { id: "partyScene", label: "Party Scene" },
    //       { id: "swimming", label: "Swimming" },
    //       { id: "loungeChairRental", label: "Lounge Chair Rental" },
    //       { id: "boats", label: "Boats" },
    {
      title: "Amenities",
      items: [
        { id: "ATM", label: "ATM", icon: "ATM" },
        { id: "barbecue", label: "Barbecue" },
        { id: "boatRamps", label: "Boat Ramp" },
        { id: "dogs", label: "Dogs" },
        { id: "entryfee", label: "Entry Fee" }, //new
        { id: "firePits", label: "Fire Pits" },
        { id: "hidden", label: "Hidden" },
        { id: "lifeguard", label: "Lifeguard", icon: "lifeguard" },
        { id: "marineLife", label: "Marine Life" },
        { id: "pier", label: "Pier" }, //new
        { id: "playground", label: "Playground", icon: "playground" },
        { id: "restRooms", label: "Restrooms", icon: "restRooms" },
        { id: "shops", label: "Shops" }, //new
        { id: "showers", label: "Showers", icon: "showers" },
        { id: "wifi", label: "WiFi" },
        { id: "picnicTable", label: "Picnic Table", icon: "picnicTable" }, //new
      ],
    },
    {
      title: "Activities",
      items: [
        { id: "bikePaths", label: "Bike Paths", icon: "bikePaths" },
        { id: "camping", label: "Camping" },
        { id: "clubspartyscene", label: "Clubs Party Scene" }, //new
        { id: "fishing", label: "Fishing", icon: "fishing" },
        { id: "hiking", label: "Hiking" },
        { id: "nude", label: "Nude" },
        { id: "paddleBoards", label: "Paddle boarding" },
        { id: "scubaDiving", label: "Scuba Diving" },
        { id: "snorkeling", label: "Snorkeling" },
        { id: "surfing", label: "Surfing", icon: "surfing" },
        { id: "tours", label: "Tours" }, //new
        { id: "volleyball", label: "Volleyball", icon: "volleyball" },
        { id: "cave", label: "Cave" },
      ],
    },
    {
      title: "Rentals",
      items: [
        { id: "ATV", label: "ATV" },
        { id: "bikeAccess", label: "Bikes", icon: "bikeAccess" },
        { id: "boats", label: "Boat" }, //new
        { id: "fishingBoats", label: "Fishing Boats" },

        { id: "jetSkiing", label: "Jet Skiing" },
        { id: "kayakingCanoeing", label: "Kayaking Canoeing" },
        { id: "beachMatRental", label: "Chair Rentals" },
        { id: "sailing", label: "Sailing" }, //new
        { id: "speedBoats", label: "Speed Boats" },
        { id: "umbrellaRental", label: "Umbrella Rental" },
        { id: "waterSkiing", label: "Waterskiing" },
        { id: "windsurfing", label: "Windsurfing" },
        { id: "yachts", label: "Yachts" },
      ],
    },
    {
      title: "Food",
      items: [
        { id: "bars", label: "Bars", icon: "bars" },
        { id: "foodsnacks", label: "Food Snacks" }, //new
        {
          id: "beachVendors",
          label: "Local Beach Vendor",
          icon: "beachVendors",
        },
        { id: "lounge", label: "Lounge" }, //new
        { id: "restaurant", label: "Restaurants", icon: "restaurant" },
      ],
    },
    {
      title: "Access",
      items: [
        { id: "bikeRacks", label: "Bike Racks" },
        { id: "carAccess", label: "Car Access", icon: "carAccess" },
        { id: "disability", label: "Disabled Access", icon: "disability" },
        { id: "footAccess", label: "Foot Access", icon: "footAccess" },
        { id: "freeParking", label: "Parking - Free", icon: "freeParking" },
        { id: "paidParking", label: "Parking - Paid", icon: "paidParking" },
        { id: "rvParking", label: "Parking - RV Parking", icon: "rvParking" },
        { id: "streetParking", label: "Parking - Street" },
        { id: "busStop", label: "Public Bus Stop" },
        {
          id: "stairsToBeach",
          label: "Stairs to Beach",
          icon: "stairsToBeach",
        },
        { id: "boatAccess", label: "Boat Access", },
      ],
    },
    {
      title: "Features",
      items: [
        { id: "blueFlag", label: "Blue Flag" },
        { id: "jellyfish", label: "Jellyfish" }, //new
        { id: "pebbles", label: "Pebbles" }, //new
        { id: "riptide", label: "Riptide" }, //new
        { id: "rocky", label: "Rocky" }, //new
        { id: "sharks", label: "Sharks" }, //new
        { id: "lgbtqplus", label: "LGBTQ+" }, //new
      ],
    },
  ],
};

export const Activities = [
  { id: "bikePaths", label: "Bike Paths" },
  { id: "camping", label: "Camping" },
  { id: "clubspartyscene", label: "Clubs Party Scene" }, //new
  { id: "fishing", label: "Fishing" },
  { id: "hiking", label: "Hiking" },
  { id: "nude", label: "Nude" },
  { id: "paddleBoards", label: "Paddleboarding" },
  { id: "scubaDiving", label: "Scuba Diving" },
  { id: "snorkeling", label: "Snorkeling" },
  { id: "surfing", label: "Surfing" },
  { id: "tours", label: "Tours" }, //new
  { id: "volleyball", label: "Volleyball" },
];
