import { WEBSITE_URL } from "@/helper/functions";

export const siteMetadata = {
  title: "Sandee: Best Beaches Near You, Beach Info, and World Map",
  ogTitle: "Sandee",
  author: "Sandee Team",
  headerTitle:
    "Sandee: Best Beaches Near You, Beach Info, and World Map",
  description:
    "Search over 150,000 worldwide beaches near you with <PERSON><PERSON>’s map, info, detailed reviews, and photos about the best beaches in the world",
  language: "en-us",
  theme: "system", // system, dark or light
  siteUrl: WEBSITE_URL,
  siteRepo: "https://github.com/Akash9179/sandee-user-v2",
  siteLogo: "/static/images/logo.png",
  socialBanner: "/static/images/twitter-card.png",
  mastodon: "https://mastodon.social/@mastodonuser",
  email: "<EMAIL>",
  github: "https://github.com",
  twitter: "https://twitter.com/sandee?lang=en",
  facebook: "https://www.facebook.com/sandeebeachescompany/",
  youtube: "https://www.youtube.com/@sandee",
  linkedin: "https://www.linkedin.com",
  threads: "https://www.threads.net",
  instagram: "https://www.instagram.com/sandee/",
  locale: "en-US",
  // analytics: {
  //   // If you want to use an analytics provider you have to add it to the
  //   // content security policy in the `next.config.js` file.
  //   // supports Plausible, Simple Analytics, Umami, Posthog or Google Analytics.
  //   umamiAnalytics: {
  //     // We use an env variable for this site to avoid other users cloning our analytics ID
  //     umamiWebsiteId: process.env.NEXT_UMAMI_ID, // e.g. 123e4567-e89b-12d3-a456-426614174000
  //   },
  //   // plausibleAnalytics: {
  //   //   plausibleDataDomain: '', // e.g. tailwind-nextjs-starter-blog.vercel.app
  //   // },
  //   // simpleAnalytics: {},
  //   // posthogAnalytics: {
  //   //   posthogProjectApiKey: '', // e.g. 123e4567-e89b-12d3-a456-426614174000
  //   // },
  //   // googleAnalytics: {
  //   //   googleAnalyticsId: '', // e.g. G-XXXXXXX
  //   // },
  // },
  // newsletter: {
  //   // supports mailchimp, buttondown, convertkit, klaviyo, revue, emailoctopus
  //   // Please add your .env file and modify it according to your selection
  //   provider: "buttondown",
  // },
  // comments: {
  //   // If you want to use an analytics provider you have to add it to the
  //   // content security policy in the `next.config.js` file.
  //   // Select a provider and use the environment variables associated to it
  //   // https://vercel.com/docs/environment-variables
  //   provider: "giscus", // supported providers: giscus, utterances, disqus
  //   giscusConfig: {
  //     // Visit the link below, and follow the steps in the 'configuration' section
  //     // https://giscus.app/
  //     repo: process.env.NEXT_PUBLIC_GISCUS_REPO,
  //     repositoryId: process.env.NEXT_PUBLIC_GISCUS_REPOSITORY_ID,
  //     category: process.env.NEXT_PUBLIC_GISCUS_CATEGORY,
  //     categoryId: process.env.NEXT_PUBLIC_GISCUS_CATEGORY_ID,
  //     mapping: "pathname", // supported options: pathname, url, title
  //     reactions: "1", // Emoji reactions: 1 = enable / 0 = disable
  //     // Send discussion metadata periodically to the parent window: 1 = enable / 0 = disable
  //     metadata: "0",
  //     // theme example: light, dark, dark_dimmed, dark_high_contrast
  //     // transparent_dark, preferred_color_scheme, custom
  //     theme: "light",
  //     // theme when dark mode
  //     darkTheme: "transparent_dark",
  //     // If the theme option above is set to 'custom`
  //     // please provide a link below to your custom theme css file.
  //     // example: https://giscus.app/themes/custom_example.css
  //     themeURL: "",
  //     // This corresponds to the `data-lang="en"` in giscus's configurations
  //     lang: "en",
  //   },
  // },
  // search: {
  //   // provider: "algolia", // kbar or algolia
  //   // kbarConfig: {
  //   //   searchDocumentsPath: "search.json", // path to load documents to search
  //   // },
  //   provider: "algolia",
  //   algoliaConfig: {
  //     // The application ID provided by Algolia
  //     appId: "R2IYF7ETH7",
  //     // Public API key: it is safe to commit it
  //     apiKey: "********************************",
  //     indexName: "docsearch",
  //   },
  // },
};

export const SiteDataPageWise = {
  blogs: {
    title: `Blogs | Sandee the Ultimate beach guide`,
    description:
      "Explore top beaches with Sandee, your ultimate guide! Dive into our travel blogs for tips, hidden gems, and beachfront bliss. Uncover paradise with expert insights. Your sun, sea, and sand journey begins here!",
    alternates: {
      canonical: `${WEBSITE_URL}/blog`,
    },
  },
  shark: {
    title: `International Shark Attacks Worldwide | Insights by Sandee`,
    description:
      "Explore global shark attack data with Sandee. Learn about hotspots, safety tips, and facts surrounding international shark encounters worldwide.",
    alternates: {
      canonical: `${WEBSITE_URL}/shark`,
    },
  },
  allShark: {
    title: `International All Shark Attacks Worldwide | Insights by Sandee`,
    description:
      "Explore global all shark attacks data with Sandee. Learn about hotspots, safety tips, and facts surrounding international shark encounters worldwide.",
    alternates: {
      canonical: `${WEBSITE_URL}/shark`,
    },
  },
  nudeBeach: {
    title: `Best Nude Beaches Worldwide | Sandee`,
    description:
      "Explore the top nude beaches worldwide with Sandee. Find the perfect spot to enjoy sun, sea, and freedom on stunning clothing-optional beaches.",
    alternates: {
      canonical: `${WEBSITE_URL}/nude-beaches`,
    },
  },
  sharkMap: {
    title: `Shark Attack Beach Maps 2025 - Plan Your Beach Visit Safely with Sandee`,
    description:
      "Explore detailed maps of shark attack beaches with Sandee. Stay informed, plan your trip wisely, and enjoy your beach experience with safety in mind.",
    alternates: {
      canonical: `${WEBSITE_URL}/maps/shark-attacks`,
    },
  },
  nudeMap: {
    title: ` Top Nude Beaches Maps 2025 | Discover with Sandee`,
    description:
      "Explore detailed maps of the best nude beaches worldwide with Sandee. Find your perfect naturist getaway, beach rules, and amenities for a seamless experience.",
    alternates: {
      canonical: `${WEBSITE_URL}/maps/shark-attacks`,
    },
  },
  news: {
    title: `Stay Updated with the Latest Beach News | Sandee`,
    description:
      "Explore the latest beach news, updates, and trends from around the globe at Sandee. Discover insights, travel tips, and must-visit beach destinations all in one place.",
    alternates: {
      canonical: `${WEBSITE_URL}/news`,
    },
  },
  podcast: {
    title: `The Beaches Podcast | Sandee`,
    description:
      "Tune into The Beaches Podcast for coastal insights, travel tips, and stories from the world's most stunning beaches. Explore seaside wonders from famous to hidden gems!",
    alternates: {
      canonical: `${WEBSITE_URL}/podcast`,
    },
  },
  aboutUS: {
    title: `About Us | Sandee the Ultimate beach guide`,
    description:
      "Sandee: Your go-to global beach guide! Explore 100,000+ beaches worldwide with activities, photos, maps, and reviews. 🏖️",
    alternates: {
      canonical: `${WEBSITE_URL}/about-us`,
    },
  },
  contactUS: {
    title: `Contact Us | Sandee the Ultimate beach guide`,
    description:
      "Contact Sandee, your go-to beach expert! Questions, suggestions, or just a hello - our page opens the door to sandy shores and seaside fun.",
    alternates: {
      canonical: `${WEBSITE_URL}/contact-us`,
    },
  },
  cookiePolicy: {
    title: `Cookie-policy | Sandee the Ultimate beach guide `,
    description:
      "Explore Sandee, the Ultimate Beach Guide's Cookie Policy. Discover how we prioritize your online experience. Your privacy is our priority.",
    alternates: {
      canonical: `${WEBSITE_URL}/cookie-policy`,
    },
  },
  press: {
    title: `Press Release | Sandee the Ultimate beach guide`,
    description:
      "Discover the latest updates and exclusive insights on beach life with Sandee, your ultimate beach guide. Press releases and newsletters for beach enthusiasts! 🏖️ #SandeeGuide",
    alternates: {
      canonical: `${WEBSITE_URL}/press`,
    },
  },
  privacyPolicy: {
    title: `Privacy Policy | Sandee the Ultimate beach guide`,
    description:
      "Explore worry-free beach bliss with Sandee, your Ultimate Beach Guide. Our Privacy Policy ensures your tranquility is as protected as your favorite sun-soaked spot. 🏖️🔒 #SandeePrivacy",
    alternates: {
      canonical: `${WEBSITE_URL}/privacy-policy`,
    },
  },
  mrBeach: {
    title: `Mr. Beach |Randall Kaplan - World's Foremost Beach Expert`,
    description:
      "Discover the world of beaches with Mr. Beach, Randall Kaplan, the ultimate expert. Your go-to beach guide with Sandee - your passport to coastal bliss!",
    alternates: {
      canonical: `${WEBSITE_URL}/mr-beach`,
    },
  },
  terms: {
    title: `Terms & Conditions |  Sandee the Ultimate beach guide`,
    description:
      "Explore the beach with confidence using Sandee, the Ultimate Beach Guide. Check out our Terms & Conditions for a seamless seaside experience. 🏖️🌊 #SandeeGuide",
    alternates: {
      canonical: `${WEBSITE_URL}/terms`,
    },
  },
  saveOurOcean: {
    title: `Save Our Oceans |  Sandee the Ultimate beach guide`,
    description:
      "As the leading beach information company, we believe in protecting the beautiful oceans and beaches we visit. That's why we proudly support nonprofits dedicated to marine conservation and sustainability. We are fortunate to work with the best ocean conservancy groups on the planet. Read on to learn about some of the amazing groups working to protect beaches and marine life worldwide.",
    alternates: {
      canonical: `${WEBSITE_URL}/save-our-ocean`,
    },
  },
  countries: {
    title:
      "Sandee: Explore the latest updates on the most beautiful beach countries",
    description:
      "Discover the allure of Sandee - your gateway to the Most Popular Beach Countries and a comprehensive list of All Beach Countries. Plan your perfect beach escape with Sandee.",
    alternates: {
      canonical: `${WEBSITE_URL}/countries`,
    },
  },
  map: {
    title: `Explore the World's Best Beaches with Sandee's Map`,
    description: `Discover top beach locations worldwide with Sandee's beach map. Get detailed overviews and find the perfect beach destinations on our comprehensive map.`,
    alternates: {
      canonical: `${WEBSITE_URL}/map`,
    },
  },
  sitemaps: {
    title: `Sitemap | Comprehensive Guide to Sandee.com’s Pages`,
    description:
      "Explore the complete sitemap of Sandee.com. Easily navigate through all the pages and discover detailed content, services, and resources available on our website.",
    alternates: {
      canonical: `${WEBSITE_URL}/sitemap`,
    },
  },
};
