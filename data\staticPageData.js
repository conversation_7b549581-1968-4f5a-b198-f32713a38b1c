import {
  CameraIcon,
  HourGlass,
  ListIconAbout,
  UmbrellaBeach,
} from "@/components/social-icons/icons";
import { CDNImageUrl } from "@/helper/functions";

const staticPageData = {
  MRBEACH: [
    {
      link: "https://www.entrepreneur.com/leadership/this-serial-entrepreneur-co-founded-a-3-billion-company/378679",
      imgSrc: CDNImageUrl("Icons/entrepreneur.svg"),
      title: "Entrepreneur",
      description:
        "This Serial Entrepreneur Co-Founded a $3 Billion Company. Here's Where He Says Big Ideas Come From.",
    },
    {
      link: "https://www.thezoereport.com/living/best-beachfront-hotels-in-america",
      imgSrc: CDNImageUrl("Icons/thezoereport.svg"),
      title: "TZR",
      description:
        "This Beachfront Hotel Is A Favorite Of Travel Pros — And It’s Easy To See Why",
    },
    {
      link: "https://marketscale.com/industries/hospitality/travel-boom-promises-a-summer-of-sandy-beaches/",
      imgSrc: CDNImageUrl("Icons/marketscale.svg"),
      title: "marketscale",
      description: "Travel Boom Promises a Summer of Sandy Beaches",
    },
    {
      link: "https://www.buzzfeed.com/fabianabuontempo/summer-2021-post-pandemic-air-travel",
      imgSrc: CDNImageUrl("Icons/buzzfeed.svg"),
      title: "BuzzFeed",
      description:
        "Booked Your First Big Trip In Forever? Here’s What Experts Recommend You Do Before Traveling",
    },
    {
      link: "https://www.tripsavvy.com/best-beach-hacks-expert-tips-5191922",
      imgSrc: CDNImageUrl("Icons/tripsavvy.svg"),
      title: "tripsavvy",
      description: "The Best Beach Hacks Recommended to Us by Experts",
    },
    {
      link: "https://www.benzinga.com/general/entrepreneurship/21/03/20091230/exclusive-vc-and-business-founder-randall-kaplan-talks-life-relationships-and-innovation",
      imgSrc: CDNImageUrl("Icons/benzinga.svg"),

      title: "benzinga",
      description:
        "Exclusive: VC And Business Founder Randall Kaplan Talks Life, Relationships And Innovation",
    },
    {
      link: "https://www.thejewishnews.com/culture/arts/a-feast-for-the-eyes-visit-beautiful-beaches-of-the-world-in-a-new-coffee/article_04cecac6-b3b2-5046-b8d7-8786f7f00eb4.html",
      imgSrc: CDNImageUrl("Icons/jewishnews.svg"),
      title: "thejewishnews",
      description:
        "A Feast for the Eyes: Visit Beautiful Beaches of the World in a New Coffee Table Book",
    },
    {
      link: "https://medium.com/authority-magazine/randall-kaplan-of-akamai-technologies-the-future-of-travel-in-the-post-covid-world-c599af97f610",

      imgSrc: CDNImageUrl("Icons/medium.svg"),
      title: "medium",
      description:
        "Randall Kaplan of Akamai Technologies: The Future of Travel in The Post Covid World",
    },
    {
      link: "https://www.gonomad.com/186077-bliss-beaches",
      imgSrc:
        "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSESQbVLT9N9dC_cSaWVQ3SO6xWVEaVvWbzvFZm0l7zz2wzPWUB-vHhCQGM3A9ZaEWgGpk&usqp=CAU",

      title: "GO N0MAD",
      description:
        "A beautiful and inspiring photographic tour capturing the quintessential beach experience, escape with Bliss: Beaches: Translucent water; azure skies",
    },
    {
      link: "https://www.beverlyhillsmagazine.com/perfect-gifts-for-your-loved-ones/",

      imgSrc: CDNImageUrl("Icons/beverlyhillsmagazine.svg"),
      title: "beverlyhillsmagazine",
      description:
        "Gifts that come at the appropriate time are perfect gifts. You can get gifts for your loved ones at any time",
    },
    {
      link: "https://www.huffpost.com/entry/questions-before-international-travel_l_6091fc2ae4b09cce6c243285",

      imgSrc: CDNImageUrl("Icons/huffpost.svg"),
      title: "HuffPost",
      description:
        "6 Questions To Ask Yourself Before Booking An International Trip",
    },
    {
      link: "https://www.frommers.com/blogs/passportable/blog_posts/new-photo-book-captures-the-world-s-beautiful-beaches-from-above",

      imgSrc: CDNImageUrl("Icons/frommers.svg"),
      title: "Frommers",
      description:
        "New Photo Book Captures the World's Beautiful Beaches from Above",
    },
  ],
  PRESS: [
    {
      link: "https://www.bloomberg.com/news/articles/2019-08-06/sandee-wants-to-be-the-yelp-of-beaches",

      imgSrc: CDNImageUrl("Icons/bloomberg.svg"),
      title: "bloomberg",
      description:
        "Sandee wants to be the Yelp for Beaches. The new website has over 51,000 beaches!",
    },
    {
      link: "https://www.kickstarter.com/projects/sandee/sandee-choose-your-beach",

      imgSrc: CDNImageUrl("Icons/kickstarter.svg"),
      title: "kickstarter",
      description:
        "How many times have you picked a beach to visit on vacation or planned a day trip to a local beach and were disappointed because it wasnt what you wanted?",
    },
    {
      link: "https://www.maxim.com/style/sandee-internet-beach-database-2019-8/",

      imgSrc: CDNImageUrl("Icons/maxim.svg"),
      title: "maxim",
      description:
        "'Sandee' Is the Internet Beach Database You Never Knew You Needed. It's always beach day somewhere.",
    },
    {
      link: "https://www.insidehook.com/article/travel/sandee-beach-vacation-research",

      imgSrc: CDNImageUrl("Icons/insidehook.svg"),
      title: "insidehook",
      description:
        "A new database called Sandee has information on 51,000 beaches across 178 countries and territories. Consult it before planning your next",
    },
    {
      link: "https://skift.com/2019/08/06/want-a-pristine-private-beach-all-to-yourself-sandees-got-you-covered/",

      imgSrc: CDNImageUrl("Icons/skift.svg"),
      title: "skift",
      description:
        "Want a Pristine, Private Beach All to Yourself? Sandee’s Got You Covered",
    },
    {
      link: "https://coveteur.com/2019/08/10/new-site-makes-it-easy-find-beaches-around-world/",

      imgSrc: CDNImageUrl("Icons/coveteur.svg"),
      title: "coveteur",
      description: "Is This The Yelp Of Beaches?",
    },
  ],
  ABOUTUS: [
    {
      title: "LISTICLES",
      count: `${5000?.toLocaleString()} +`,
      icon: <ListIconAbout className="p-2 fill-black w-20 h-20 stroke-black" />,
    },
    {
      title: "BEACHES",
      count: `${100000?.toLocaleString()} +`,
      icon: <UmbrellaBeach className=" fill-black w-20 h-20 stroke-black" />,
    },
    {
      title: "PHOTOS",
      count: `${65000?.toLocaleString()} +`,
      icon: <CameraIcon className="p-1  w-20 h-20 stroke-black" />,
    },
    {
      title: "HOURS OF RESEARCH",
      count: `${300000?.toLocaleString()} +`,
      icon: <HourGlass className="p-1 fill-black w-20 h-20 stroke-black" />,
    },
  ],
};

export default staticPageData;
