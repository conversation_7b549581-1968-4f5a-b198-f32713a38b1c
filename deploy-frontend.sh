#!/bin/bash

# Stop script execution on any error
set -e

# Define variables
AWS_REGION="us-east-1"
AWS_ACCOUNT_ID="************"
ECR_REPOSITORY="sandee-frontend-staging"
IMAGE_TAG="latest"
IMAGE_URI="$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_REPOSITORY:$IMAGE_TAG"

# Free Docker build cache
echo "Cleaning up Docker build cache..."
docker builder prune -f

# Authenticate Docker with AWS ECR
echo "Authenticating with AWS ECR..."
aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com

# Build Docker image
echo "Building Docker image..."
DOCKER_BUILDKIT=1 docker build -t $ECR_REPOSITORY .

# Tag the Docker image
echo "Tagging Docker image..."
docker tag $ECR_REPOSITORY:$IMAGE_TAG $IMAGE_URI

# Push the image to AWS ECR
echo "Pushing Docker image to AWS ECR..."
docker push $IMAGE_URI

# Deploy with Docker Compose
echo "Deploying with Docker Compose..."
docker-compose down
docker-compose up -d

echo "Frontend service deployed successfully!"
