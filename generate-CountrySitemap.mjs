import axios from "axios";
import dotenv from "dotenv";
dotenv.config();
import fs from "fs-extra";
import cron from "node-cron";

const generateSiteMap = (data) => {
  // const currentDate = new Date().toISOString().split("T")[0];
  // Use the received data to dynamically generate the sitemap content
  // This is just a placeholder; modify it based on your API response structure
  const urlElements = data.map((item) => {
    return `<url>
      <loc>${item?.url}</loc>
      <lastmod>${item?.lastModified}</lastmod>
      <changefreq>${item?.changeFrequency}</changefreq>
      <priority>${item?.priority}</priority>
    </url>`;
  });

  return `<?xml version="1.0" encoding="UTF-8"?>\n<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n${urlElements.join(
    "\n"
  )}\n</urlset>`;
};

async function generateCountrySitemap() {
  const API_URL =
    process.env.NEXT_PUBLIC_ENV == "production"
      ? process.env.NEXT_PUBLIC_BASE_URL
      : process.env.NEXT_PUBLIC_DEV_BASE_URL;
  const URL =
    process.env.NEXT_PUBLIC_ENV == "production"
      ? process.env.NEXT_PUBLIC_WEB_URL
      : process.env.NEXT_PUBLIC_DEV_WEB_URL;

  const country = await axios.get(`${API_URL}/sitemap/getAllCountry`);
  return country?.data?.data?.map((el) => ({
    url: `${URL}/${el?.countrySlug}`.replace(/&/g, "&amp;"),
    lastModified: el?.updatedAt,
    changeFrequency: "weekly",
    priority: 1,
  }));
}
const writeSitemapToFile = async () => {
  try {
    const apiData = await generateCountrySitemap();
    const sitemap = generateSiteMap(apiData);
    const filePath = "public/Sitemap-Country.xml";

    await fs.outputFile(filePath, sitemap);
    // logController("Country sitemap written to file successfully");
  } catch (error) {
    // console.error("Error writing sitemap to file:", error);
  }
};

// Initial write
writeSitemapToFile();

// Schedule automatic updates every 10 seconds
const CronJob = process.env.NEXT_PUBLIC_CRONJOB_SITEMAP || "0 0 * * * * *";
cron.schedule(CronJob, async () => {
  // logController("Updating sitemap index...");
  await writeSitemapToFile();
});
