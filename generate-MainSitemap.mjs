import dotenv from "dotenv";
dotenv.config();
import fs from "fs-extra";
import cron from "node-cron";
// import { logController } from "./helper/functions";

const generateSiteMap = (data) => {
  // const currentDate = new Date().toISOString().split("T")[0];
  // Use the received data to dynamically generate the sitemap content
  // This is just a placeholder; modify it based on your API response structure
  const urlElements = data.map((item) => {
    return `<url>
      <loc>${item?.url}</loc>
      <lastmod>${item?.lastModified}</lastmod>
      <changefreq>${item?.changeFrequency}</changefreq>
      <priority>${item?.priority}</priority>
    </url>`;
  });

  return `<?xml version="1.0" encoding="UTF-8"?>\n<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n${urlElements.join(
    "\n"
  )}\n</urlset>`;
};

function generateMainSitemap() {
  const URL =
    process.env.NEXT_PUBLIC_ENV == "production"
      ? process.env.NEXT_PUBLIC_WEB_URL
      : process.env.NEXT_PUBLIC_DEV_WEB_URL;

  return [
    {
      url: `${URL}/countries`,
      lastModified: new Date().toISOString().split("T")[0],
      changeFrequency: "weekly",
      priority: 1,
    },
    {
      url: `${URL}/sitemap`,
      lastModified: new Date().toISOString().split("T")[0],
      changeFrequency: "weekly",
      priority: 1,
    },
    {
      url: `${URL}/about-us`,
      lastModified: new Date().toISOString().split("T")[0],
      changeFrequency: "weekly",
      priority: 1,
    },
    {
      url: `${URL}/press`,
      lastModified: new Date().toISOString().split("T")[0],
      changeFrequency: "weekly",
      priority: 1,
    },
    {
      url: `${URL}/mr-beach`,
      lastModified: new Date().toISOString().split("T")[0],
      changeFrequency: "weekly",
      priority: 1,
    },
    {
      url: `${URL}/privacy-policy`,
      lastModified: new Date().toISOString().split("T")[0],
      changeFrequency: "weekly",
      priority: 1,
    },
    {
      url: `${URL}/cookie-policy`,
      lastModified: new Date().toISOString().split("T")[0],
      changeFrequency: "weekly",
      priority: 1,
    },
    {
      url: `${URL}/terms`,
      lastModified: new Date().toISOString().split("T")[0],
      changeFrequency: "weekly",
      priority: 1,
    },
    {
      url: `${URL}/contact-us`,
      lastModified: new Date().toISOString().split("T")[0],
      changeFrequency: "weekly",
      priority: 1,
    },
    {
      url: `${URL}/save-our-ocean`,
      lastModified: new Date().toISOString().split("T")[0],
      changeFrequency: "weekly",
      priority: 1,
    },
    {
      url: `${URL}/blog`,
      lastModified: new Date().toISOString().split("T")[0],
      changeFrequency: "weekly",
      priority: 1,
    },
    {
      url: `${URL}/list`,
      lastModified: new Date().toISOString().split("T")[0],
      changeFrequency: "weekly",
      priority: 1,
    },
    {
      url: `${URL}/filter/Nude`,
      lastModified: new Date().toISOString().split("T")[0],
      changeFrequency: "weekly",
      priority: 1,
    },
    {
      url: `${URL}/filter/Dog`,
      lastModified: new Date().toISOString().split("T")[0],
      changeFrequency: "weekly",
      priority: 1,
    },
    {
      url: `${URL}/filter/Surfing`,
      lastModified: new Date().toISOString().split("T")[0],
      changeFrequency: "weekly",
      priority: 1,
    },
    {
      url: `${URL}/filter/Disability`,
      lastModified: new Date().toISOString().split("T")[0],
      changeFrequency: "weekly",
      priority: 1,
    },
    {
      url: `${URL}/filter/Camping`,
      lastModified: new Date().toISOString().split("T")[0],
      changeFrequency: "weekly",
      priority: 1,
    },
    {
      url: `${URL}/filter/Family-Friendly`,
      lastModified: new Date().toISOString().split("T")[0],
      changeFrequency: "weekly",
      priority: 1,
    },
    {
      url: `${URL}/map`,
      lastModified: new Date().toISOString().split("T")[0],
      changeFrequency: "weekly",
      priority: 1,
    },
    {
      url: `${URL}/podcast`,
      lastModified: new Date().toISOString().split("T")[0],
      changeFrequency: "weekly",
      priority: 1,
    },
    {
      url: `${URL}/shark`,
      lastModified: new Date().toISOString().split("T")[0],
      changeFrequency: "weekly",
      priority: 1,
    },
    {
      url: `${URL}/news`,
      lastModified: new Date().toISOString().split("T")[0],
      changeFrequency: "weekly",
      priority: 1,
    },
    {
      url: `${URL}/nude-beaches`,
      lastModified: new Date().toISOString().split("T")[0],
      changeFrequency: "weekly",
      priority: 1,
    },
    {
      url: `${URL}/maps/nude-beaches`,
      lastModified: new Date().toISOString().split("T")[0],
      changeFrequency: "weekly",
      priority: 1,
    },
    {
      url: `${URL}/maps/shark-attacks`,
      lastModified: new Date().toISOString().split("T")[0],
      changeFrequency: "weekly",
      priority: 1,
    },
  ];
}
const writeSitemapToFile = async () => {
  try {
    const apiData = generateMainSitemap();
    const sitemap = generateSiteMap(apiData);
    const filePath = "public/Sitemap-Main.xml";

    await fs.outputFile(filePath, sitemap);
    // logController("Main sitemap written to file successfully ");
  } catch (error) {
    // logController("Error : Main sitemap written to file successfully ");
    // console.error("Error writing sitemap to file:", error);
  }
};

// Initial write
writeSitemapToFile();

// Schedule automatic updates every 10 seconds
const CronJob = process.env.NEXT_PUBLIC_CRONJOB_SITEMAP || "0 0 * * * * *";
cron.schedule(CronJob, async () => {
  // logController("Updating sitemap index...");
  await writeSitemapToFile();
});
