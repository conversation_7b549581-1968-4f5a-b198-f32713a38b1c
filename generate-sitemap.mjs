import dotenv from "dotenv";
dotenv.config();
import fs from "fs-extra";
import cron from "node-cron";

const generateSitemapIndex = () => {
  //   logController( process.env.NEXT_PUBLIC_ENV == "production",
  //   process.env.NEXT_PUBLIC_ENV
  //  )
  const CONST_ENV_WEB_URL =
    process.env.NEXT_PUBLIC_ENV == "production"
      ? process.env.NEXT_PUBLIC_WEB_URL
      : process.env.NEXT_PUBLIC_DEV_WEB_URL;
  const sitemaps = [
    {
      loc: `${CONST_ENV_WEB_URL}/Sitemap-Main.xml`,
      lastmod: new Date().toISOString().split("T")[0],
    },
    {
      loc: `${CONST_ENV_WEB_URL}/Sitemap-Beaches1.xml`,
      lastmod: new Date().toISOString().split("T")[0],
    },
    {
      loc: `${CONST_ENV_WEB_URL}/Sitemap-Beaches2.xml`,
      lastmod: new Date().toISOString().split("T")[0],
    },
    {
      loc: `${CONST_ENV_WEB_URL}/Sitemap-Beaches3.xml`,
      lastmod: new Date().toISOString().split("T")[0],
    },
    {
      loc: `${CONST_ENV_WEB_URL}/Sitemap-Beaches4.xml`,
      lastmod: new Date().toISOString().split("T")[0],
    },
    {
      loc: `${CONST_ENV_WEB_URL}/Sitemap-Beaches5.xml`,
      lastmod: new Date().toISOString().split("T")[0],
    },
    {
      loc: `${CONST_ENV_WEB_URL}/Sitemap-City.xml`,
      lastmod: new Date().toISOString().split("T")[0],
    },
    {
      loc: `${CONST_ENV_WEB_URL}/Sitemap-State.xml`,
      lastmod: new Date().toISOString().split("T")[0],
    },
    {
      loc: `${CONST_ENV_WEB_URL}/Sitemap-Country.xml`,
      lastmod: new Date().toISOString().split("T")[0],
    },
    {
      loc: `${CONST_ENV_WEB_URL}/Sitemap-Blog.xml`,
      lastmod: new Date().toISOString().split("T")[0],
    },
    {
      loc: `${CONST_ENV_WEB_URL}/Sitemap-List.xml`,
      lastmod: new Date().toISOString().split("T")[0],
    },
    {
      loc: `${CONST_ENV_WEB_URL}/Sitemap-Island.xml`,
      lastmod: new Date().toISOString().split("T")[0],
    },
    {
      loc: `${CONST_ENV_WEB_URL}/Sitemap-MapBeaches1.xml`,
      lastmod: new Date().toISOString().split("T")[0],
    },
    {
      loc: `${CONST_ENV_WEB_URL}/Sitemap-MapBeaches2.xml`,
      lastmod: new Date().toISOString().split("T")[0],
    },
    {
      loc: `${CONST_ENV_WEB_URL}/Sitemap-MapBeaches3.xml`,
      lastmod: new Date().toISOString().split("T")[0],
    },
    {
      loc: `${CONST_ENV_WEB_URL}/Sitemap-MapBeaches4.xml`,
      lastmod: new Date().toISOString().split("T")[0],
    },
    {
      loc: `${CONST_ENV_WEB_URL}/Sitemap-MapBeaches5.xml`,
      lastmod: new Date().toISOString().split("T")[0],
    },
    {
      loc: `${CONST_ENV_WEB_URL}/Sitemap-SharkSpecies.xml`,
      lastmod: new Date().toISOString().split("T")[0],
    },
    {
      loc: `${CONST_ENV_WEB_URL}/Sitemap-NudeBeaches1.xml`,
      lastmod: new Date().toISOString().split("T")[0],
    },
    {
      loc: `${CONST_ENV_WEB_URL}/Sitemap-News.xml`,
      lastmod: new Date().toISOString().split("T")[0],
    },
    // {
    //   loc: `${CONST_ENV_WEB_URL}/Sitemap-NudeBeaches2.xml`,
    //   lastmod: new Date().toISOString().split("T")[0],
    // },
    // {
    //   loc: `${CONST_ENV_WEB_URL}/Sitemap-NudeBeaches3.xml`,
    //   lastmod: new Date().toISOString().split("T")[0],
    // },
    // {
    //   loc: `${CONST_ENV_WEB_URL}/Sitemap-NudeBeaches4.xml`,
    //   lastmod: new Date().toISOString().split("T")[0],
    // },
    // {
    //   loc: `${CONST_ENV_WEB_URL}/Sitemap-NudeBeaches5.xml`,
    //   lastmod: new Date().toISOString().split("T")[0],
    // },
  ];

  const root = `<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n`;

  const sitemapElements = sitemaps.map(({ loc, lastmod }) => {
    return `<sitemap>\n<loc>${loc}</loc>\n<lastmod>${lastmod}</lastmod>\n</sitemap>\n`;
  });

  const closingTag = `</sitemapindex>`;

  return root + sitemapElements.join("") + closingTag;
};

const writeSitemapIndexToFile = async () => {
  try {
    const sitemapIndex = generateSitemapIndex();
    const filePath = "public/sitemap.xml";

    await fs.outputFile(filePath, sitemapIndex);
    console.log("Succefully sitemap index...")
    // logController("Main Sitemap Milan");
  } catch (error) {
    // console.error("Error writing sitemap index to file:", error);
  }
};

// Initial write
writeSitemapIndexToFile();

// Schedule automatic updates every day at 12 o'clock
// cron.schedule('0 12 * * *', async () => {
//   logController('Updating sitemap index...');
//   await writeSitemapIndexToFile();
// });
const CronJob = "30 8 * * *";
// const CronJob = process.env.NEXT_PUBLIC_CRONJOB_SITEMAP || "0 0 * * *";
cron.schedule(CronJob, async () => {
  // logController("Updating sitemap index...");
  console.log("Updating sitemap index...")
  await writeSitemapIndexToFile();
});
