'use client'
import React, { createContext, useState} from "react";
export const valContext = createContext();

const ValProvider = ({ children }) => {
    const [activityStatus, setActivityStatus] = useState({});
    const [mapLoaded, setMapLoaded] = useState(false);
    const [mapLocation, setMapLocation] = useState(null);
    const [listicleLocation, setListicleLocation] = useState(null);
    const [locationFetched, setLocationFetched] = useState(false);
    const [map, setMap] = useState(null);
  return (
    <valContext.Provider value={{listicleLocation, setListicleLocation, map, setMap, locationFetched, setLocationFetched, mapLocation, setMapLocation, mapLoaded, setMapLoaded, activityStatus, setActivityStatus}}>
      {children}
    </valContext.Provider>
  );
};

export default ValProvider;