// import parse from "node-html-parser";

import ActiveLinks from "@/components/ActiveLinks";
import { redirect } from "next/navigation";
import dayjs from "dayjs";
import utc from 'dayjs/plugin/utc'; // For handling UTC times
import timezone from 'dayjs/plugin/timezone'; // For handling UTC times

dayjs.extend(utc);
dayjs.extend(timezone);

export const convertUTCtoLocal = (utcTime, format = "YYYY-MM-DD HH:mm:ss",) => {
  return dayjs.utc(utcTime).local().format(format);
}


export function slugConverter(name, reverse = false) {
  if (reverse) {
    // Revert slug to name
    return name
      .replace(/-/g, " ")
      .replace(/(?:^|\s)\S/g, (c) => c.toUpperCase())
      .replace(/\s/g, " ");
  } else {
    // Convert name to slug
    const firstLetter = name.charAt(0).toLowerCase();
    const restOfString = name.slice(1);
    return firstLetter + restOfString.replace(/\s+/g, "-").toLowerCase();
  }
}

export const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: "smooth",
  });
};
export function scrollToSection(sectionId, offset = 0) {
  const section = document.getElementById(sectionId);
  if (section) {
    // Scroll to the section using scrollIntoView
    section.scrollIntoView({ behavior: "smooth", block: "start" });

    // Use requestAnimationFrame to ensure the scrollIntoView has completed
    requestAnimationFrame(() => {
      // Calculate the position of the section
      const sectionTop =
        section.getBoundingClientRect().top + window.pageYOffset;
      // Adjust the scroll position with the offset
      window.scrollTo({
        top: sectionTop - offset,
        behavior: "smooth",
      });
    });
  }
}

// export function scrollToSection(sectionId,top=0) {
//   var section = document.getElementById(sectionId);
//   const sectionTop = section.getBoundingClientRect().top + window?.pageYOffset;
//   section.scrollIntoView({ behavior: "smooth" ,top:sectionTop-top});
// }

const shimmer = (w, h) => `
    <svg width="${w}" height="${h}" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
      <defs>
        <linearGradient id="g">
          <stop stop-color="#CFCFCF" offset="20%" />
          <stop stop-color="#CFCFCF" offset="50%" />
          <stop stop-color="#CFCFCF" offset="70%" />
        </linearGradient>
      </defs>
      <rect width="${w}" height="${h}" fill="#CFCFCF" />
      <rect id="r" width="${w}" height="${h}" fill="url(#g)" />
      <animate xlink:href="#r" attributeName="x" from="-${w}" to="${w}" dur="1s" repeatCount="indefinite"  />
    </svg>
`;
const toBase64 = (str) =>
  typeof window === "undefined"
    ? Buffer.from(str).toString("base64")
    : window.btoa(str);

export const blurDataURL = (width, height) =>
  `data:image/svg+xml;base64,${toBase64(shimmer(width, height))}`;

export const openLinksInNewTab = () => {
  const links = document.querySelectorAll(".BeachDescriptionLinker a"); // Select all links on the page

  links.forEach((link) => {
    link.setAttribute("target", "_blank"); // Set target attribute to open in new tab
    link.setAttribute("rel", "noopener noreferrer"); // Add recommended security attributes
  });
};

export const CDNImageUrl = (path) => `https://images.sandee.com/${path}`;
export const buildQueryString = (params) => {
  const queryParts = [];

  for (const key in params) {
    if (params.hasOwnProperty(key)) {
      const value = params[key];

      if (key.startsWith("autogenerate-mul-array-") && Array.isArray(value)) {
        // Handle autogenerate-mul-array- with array values
        const arrayKey = key.slice("autogenerate-mul-array-".length);
        value.forEach((item) => {
          queryParts.push(
            `${encodeURIComponent(arrayKey)}=${encodeURIComponent(item)}`
          );
        });
      } else {
        // Handle other cases
        queryParts.push(
          `${encodeURIComponent(key)}=${encodeURIComponent(value)}`
        );
      }
    }
  }

  return queryParts.length > 0 ? `?${queryParts.join("&")}` : "";
};

export const ProceedImageURL = (keyPrefix, type = 1) => {
  // const HWGEnerator = {
  //   1: { width: 350, height: 250 },
  //   2: { width: 900, height: 500 },
  //   3: { width: 1300, height: 700 },
  // };

  const HWGEnerator = {
    1: { width: 390, height: 260 },
    2: { width: 1050, height: 700 },
    3: { width: 1650, height: 1100 },
    4: { width: 1462, height: 731 },
    5: { width: 355, height: 276 }
  };
  if (keyPrefix) {
    // cdn.sandee.com/100_1050_700.avif
    // return `https://new-resize-avif-photos.sandee.com/${keyPrefix}_${HWGEnerator[type].width}_${HWGEnerator[type].height}.avif`;
    return `https://cdn.sandee.com/${keyPrefix}_${HWGEnerator[type].width}_${HWGEnerator[type].height}.avif`;

    // return `https://images.sandee.com/${keyPrefix}_${HWGEnerator[type].width}_${HWGEnerator[type].height}.webp`;
    // return `https://new-resize-photos.s3-accelerate.amazonaws.com/${keyPrefix}_${HWGEnerator[type].width}_${HWGEnerator[type].height}.webp`;
    // return `https://ddsfbbro1curb.cloudfront.net/${keyPrefix}_${HWGEnerator[type].width}_${HWGEnerator[type].height}.webp`;
    // return `https://new-resize-photos.s3.amazonaws.com/${keyPrefix}_${HWGEnerator[type].width}_${HWGEnerator[type].height}.webp`;
  }
  return null;
};
export function LinkGenerator(data, size = 1200, alternateIdLink) {
  // Regular expression to match the specific image URL pattern
  const googleImageRegex = /https:\/\/lh[0-9]+\.googleusercontent\.com/;
  if (data?.imageUrl && googleImageRegex.test(data.imageUrl)) {
    // Replace the existing size parameter (if any) with the new size
    let updatedUrl = data.imageUrl;
    if (/=s[0-9]+/.test(data.imageUrl)) {
      updatedUrl = data.imageUrl.replace(/=s[0-9]+/, `=s${size}`);
    } else if (/=w[0-9]+/.test(data.imageUrl)) {
      updatedUrl = data.imageUrl.replace(/=w[0-9]+/, `=w${size}`);
    } else {
      updatedUrl = `${updatedUrl}=s${size}-k-no`;
    }
    return updatedUrl;
  } else if (data?.imageProcessed) {
    return alternateIdLink;
  } else if (data?.imageUrl) {
    return data?.imageUrl;
  }
  return alternateIdLink;
}

export const API_BASE_URL =
  process.env.NEXT_PUBLIC_ENV == "production"
    ? process.env.NEXT_PUBLIC_BASE_URL
    : process.env.NEXT_PUBLIC_DEV_BASE_URL;
export const WEBSITE_URL =
  process.env.NEXT_PUBLIC_ENV == "production"
    ? process.env.NEXT_PUBLIC_WEB_URL
    : process.env.NEXT_PUBLIC_DEV_WEB_URL;
export const getToken = () => {
  if (typeof window !== "undefined") {
    const ProfileData = localStorage.getItem("profile");
    const Token = localStorage.getItem("token");
    if (
      // localStorage.getItem("ItemToken") &&
      Token &&
      ProfileData
    ) {
      return { token: Token, profile: ProfileData };
    }
  }
  return null;
};
export function extractLocationProperties(obj) {
  return {
    city: {
      name: obj?.name,
      cityId: obj?.id,
      slug: obj?.slug,
    },
    state: {
      name: obj?.state?.name,
      stateId: obj?.state?.id,
      slug: obj?.state?.slug,
    },
    country: {
      name: obj?.state?.country?.name,
      countryId: obj?.state?.country?.id,
      slug: obj?.state?.country?.slug,
    },
  };
}
export const IsUserAuthenticated = () => {
  if (typeof window !== "undefined") {
    const ProfileData = localStorage.getItem("profile");
    const Token = localStorage.getItem("token");

    if (
      typeof window !== "undefined" &&
      // localStorage.getItem("ItemToken") &&
      !!Token &&
      !!ProfileData &&
      validValue(Token) &&
      validValue(ProfileData)
    ) {
      return true;
    }
  }
  return false;
};

export function isJsonString(str) {
  try {
    JSON.parse(str);
  } catch (e) {
    return false;
  }
  return true;
}

export const validValue = (val) =>
  val !== null &&
  val !== undefined &&
  val !== "undefined" &&
  val !== "" &&
  !!val;

export const apiGenerator = (apiObject, exchangePair = {}, join = null) => {
  // const apiObj = { ...apiObject };
  if (Object.keys(exchangePair).length) {
    Object.keys(exchangePair).forEach((el) => {
      apiObject = apiObject.replace(`:${el}`, exchangePair[el]);
    });
  }

  if (join) {
    apiObject = `${apiObject}${join}`;
  }
  return apiObject;
};

export const withErrorHandling =
  (fn) =>
    async (...args) => {
      try {
        return await fn(...args);
      } catch (error) {
        return { data: null, error: true };
      }
    };


export function FinalImageGenerator(data, imageSize = 1600, type = 1, defaultImage) {
  // Regular expression to match the specific image URL pattern
  const googleImageRegex = /https:\/\/lh[0-9]+\.googleusercontent\.com/;

  if (data?.imageUrl && googleImageRegex.test(data.imageUrl)) {
    // If it's a Google image, replace the size parameter
    const updatedUrl = data.imageUrl?.replace(/=[^=]*$/, "");
    // const updatedUrl = data.imageUrl.replace(/=s[0-9]+/, `=w${1600}-h${1200}`);
    return `${updatedUrl}=s${imageSize || 2500}-k-no`;
  } else if (data?.imageProcessed && !googleImageRegex.test(data.imageUrl)) {
    // If image is processed, use alternateIdLink
    const keyPrefix = data?.id; // Assuming 'id' is the appropriate property for keyPrefix
    return ProceedImageURL(keyPrefix, type);
    // return "https://new-resize-avif-photos.s3.us-east-1.amazonaws.com/country-images/187515_3840_1740.avif"
  } else if (data?.imageUrl) {
    // If imageUrl exists, generate a new URL using ProceedImageURL
    return data?.imageUrl;
    // return "https://new-resize-avif-photos.s3.us-east-1.amazonaws.com/country-images/187515_3840_1740.avif"

  } else if (data && data.lat && data.lon) {
    // If no image but lat/lon exists, return a static Google Map image
    return `https://maps.googleapis.com/maps/api/staticmap?center=${data.lat},${data.lon}&zoom=15&size=600x400&maptype=satellite&key=AIzaSyDLfO8bHqEoNNmnyh3scit7hHUQr7Jx36Q`;
  }
  return defaultImage || "https://images.sandee.com/images/header/Default-Header.avif";
}

export const addAltToImages = (htmlString, data) => {
  const images = htmlString?.querySelectorAll("img");
  images?.forEach((img, index) => {
    img.setAttribute("alt", data?.alt ? data?.alt : `Sandee Blog Content`);
    img.setAttribute(
      "title",
      data?.title ? data?.title : `Sandee Blog Content`
    );
  });
  const link = htmlString?.querySelectorAll("a");

  link?.forEach((link, index) => {
    link.setAttribute(
      "title",
      data?.title ? data?.title : `Sandee Blog Content`
    );
    link.setAttribute("target", "_blank");
  });
  const spanElements = htmlString?.querySelectorAll("span");
  spanElements?.forEach((spanElement) => {
    spanElement?.style?.removeProperty("width");
    spanElement?.style?.removeProperty("height");
  });

  return htmlString?.body.innerHTML;
};

export const DocTocandHTMLBlog = (dataOfBlog) => {
  let doc = "";
  let toc = [];
  let DataForBlog = {};
  try {
    // doc = parse(dataOfBlog?.data?.overview, "text/html");
    const parser = new DOMParser();
    // let doc = parser.parseFromString(DATA?.overview, "text/html");
    doc = parser.parseFromString(dataOfBlog?.data?.overview, "text/html");

    let headings = Array?.from(doc?.querySelectorAll("h2"));
    headings.forEach((heading, index) => {
      heading.id = `heading-${index + 1}`;
    });
    toc = headings.map((heading, index) => (
      <li
        key={index}
        className="text-sandee-blue   text-sandee-sm font-semibold list-disc leading-9"
      >
        {/* <p

        // onClick={() => {
        //   scrollToSection(`heading-${index + 1}`);
        // }}
        > */}
        <ActiveLinks
          href={`#heading-${index + 1}`}
          className={""}
          ActiveClassName="active-link text-sandee-18 font-bold  text-yellow-200 list-disc "
        >
          {heading.textContent}
        </ActiveLinks>
        {/* </p> */}
      </li>
    ));

    // setTableOfContents(toc);
    const modifiedHtml = addAltToImages(doc, {
      alt: "sandee Blog Image",
      title: "Sandee link",
    });
    DataForBlog = {
      ...dataOfBlog?.data,
      Tag: dataOfBlog?.data?.blogCategories?.map((tag) => tag?.category?.name),
      overview: modifiedHtml,
    };
  } catch (error) {
    // console.log(error);
  }
  return {
    doc,
    toc,
    DataForBlog,
  };
};

export const EnvTrueFalse = {
  true: true,
  false: false,
  1: true,
  0: false,
};

export function generateBreakpoints(minWidth, maxWidth, step, widthDivisor) {
  const breakpoints = {};
  for (let width = minWidth; width <= maxWidth; width += step) {
    breakpoints[width] = {
      slidesPerView: width / widthDivisor < 1 ? 1.3 : width / widthDivisor,
    };
  }
  return breakpoints;
}

export const sanitizeHtml = (html) => {
  if (!html || html === "") return "";
  const sanitizedHtml = html
    ?.replace(
      /color:\s*(#000000|rgb\((\s*0\s*,){2}\s*0\s*\)|rgba\((\s*0\s*,){3}\s*0\s*,\s*0\s*\));/gi,
      ""
    )
    ?.replace(/font-family[^;]+;?/g, "")
    ?.replace(
      /<(p|h1|h2|h3|h4|h5|h6|div|span)><br><\/\1>/g,
      '<div class="spacing_overview"></div>'
    )
    ?.replace(
      /<p><span style="background-color: transparent;">(?:&nbsp;|&#xFEFF;)<\/span><\/p>/g,
      ""
    )
    ?.replace(/<p class="ql-indent-1"><br><\/p>/g, "")
    ?.replace(/<p class="ql-indent-2"><br><\/p>/g, "")
    ?.replace(/<span class="ql-cursor">﻿<\/span>/g, "")
    ?.replace(/&lt;u&gt;/g, "<u>")
    ?.replace(/&lt;\/u&gt;/g, "</u>")
    ?.replace(/&lt;hr&gt;/g, "<hr>")
    ?.replace(/&lt;\/hr&gt;/g, "</hr>");

  return sanitizedHtml;
};

export const RedirectionFunction = (ResponseOfRedirect) => {
  if (!!ResponseOfRedirect?.[0]?.originalUrl) {
    const Redirector = ResponseOfRedirect?.[0]?.type?.split("-");
    if (Redirector[0] === "Temporary") {
      return redirect(`${ResponseOfRedirect?.[0]?.originalUrl ?? "/"}`);
    } else if (Redirector[0] === "Permanent") {
      return redirect(`${ResponseOfRedirect?.[0]?.originalUrl ?? "/"}`);
      // return permanentRedirect(
      //   `${ResponseOfRedirect?.[0]?.originalUrl ?? "/"}`
      // );
    } else {
      return redirect(`${ResponseOfRedirect?.[0]?.originalUrl ?? "/"}`);
    }
  }
};

// export const getEmbedUrl = (url) => {
//   if (url.includes("youtube.com/watch?v=")) {
//     const videoId = url.split("v=")[1];
//     return `https://www.youtube.com/embed/${videoId}`;
//   } else if (url.includes("youtube.com/embed/")) {
//     return url;
//   } else if (url.includes("youtube.com/live/")) {
//     const videoId = url.split("youtube.com/live/")[1].split("?")[0];
//     return `https://www.youtube.com/embed/live_stream?channel=${videoId}`;
//   } else {
//     // Fallback for non-YouTube links
//     return url; // Use the external link as the fallback URL
//   }
// };
export const getEmbedUrl = (url) => {
  let embedUrl = url;
  let videoId = null;
  if (url.includes("youtube.com/watch?v=")) {
    videoId = url.split("v=")[1];
    embedUrl = `https://www.youtube.com/embed/${videoId}`;
  } else if (url.includes("youtube.com/embed/")) {
    videoId = url.split("/embed/")[1].split("?")[0];
    embedUrl = url;
  } else if (url.includes("youtube.com/live/")) {
    videoId = url.split("youtube.com/live/")[1].split("?")[0];
    embedUrl = `https://www.youtube.com/embed/live_stream?channel=${videoId}`;
  }

  if (!embedUrl.includes("autoplay=")) {
    embedUrl += embedUrl.includes("?") ? "&autoplay=1" : "?autoplay=1";
  }

  if (!embedUrl.includes("mute=")) {
    embedUrl += "&mute=1";
  }

  if (!embedUrl.includes("loop=")) {
    embedUrl += "&loop=1";
  }
  if (!embedUrl.includes("playlist=") && videoId) {
    embedUrl += `&playlist=${videoId}`;
  }
  return embedUrl;
};

export function WeatherIconLink(iconId) {
  const IconsLocal = {
    "01d": "/static/icons/Weather/01d.svg",
    "01n": "/static/icons/Weather/01n.svg",
    "02d": "/static/icons/Weather/02d.svg",
    "02n": "/static/icons/Weather/02n.svg",
    "03d": "/static/icons/Weather/03d.svg",
    "03n": "/static/icons/Weather/03n.svg",
    "04d": "/static/icons/Weather/04d.svg",
    "04n": "/static/icons/Weather/04n.svg",
    "09d": "/static/icons/Weather/09d.svg",
    "09n": "/static/icons/Weather/09n.svg",
    "10d": "/static/icons/Weather/10d.svg",
    "10n": "/static/icons/Weather/10n.svg",
    "11d": "/static/icons/Weather/11d.svg",
    "11n": "/static/icons/Weather/11n.svg",
    "13d": "/static/icons/Weather/13d.svg",
    "13n": "/static/icons/Weather/13n.svg",
    "50d": "/static/icons/Weather/50d.svg",
    "50n": "/static/icons/Weather/50n.svg",
    // "10d": "/static/icons/Weather/10d.svg",
    // "10n": "/static/icons/Weather/10n.svg",
  };
  if (IconsLocal?.[iconId]) {
    return IconsLocal?.[iconId];
  }
  if (iconId) {
    return `http://openweathermap.org/img/wn/${iconId || "01n"}@4x.png`;
  }
  return "/static/icons/Weather/01n.svg";
}

export function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

export function logController(...args) {
  return process.env.NEXT_PUBLIC_ENV == "production"
    ? null
    : console.log(...args);
}
export const isEmptyObject = (obj) => {
  return obj === undefined || obj === null || Object.keys(obj).length === 0;
};

export function extractMainRoute(url) {
  if (!url) return;
  // Remove protocol and hostname
  let path = url?.split("/").slice(3).join("/"); // Output: "map/zuma-beach/@34.016392,-118.82376"

  // Extract main route
  let mainRoute = path?.split("/")[0]; // Output: "map"

  return mainRoute;
}

export function convertUnixTimeTo12HourFormat(unixTime, offset = 0) {
  // Apply offset to the Unix timestamp
  const adjustedTime = unixTime + offset;

  // Convert Unix timestamp to milliseconds and create a new Date object
  const dateObj = new Date(adjustedTime * 1000);

  // Extract hours and minutes
  let hours = dateObj.getUTCHours();
  const minutes = dateObj.getUTCMinutes();

  // Determine AM/PM
  const period = hours >= 12 ? "PM" : "AM";

  // Convert hours from 24-hour format to 12-hour format
  hours = hours % 12 || 12; // `12` for `0` hours, `1-11` otherwise

  // Format minutes to always have two digits
  const formattedMinutes = minutes.toString().padStart(2, "0");

  // Construct the time string in 12-hour format
  return `${hours}:${formattedMinutes} ${period}`;
}

export const defaultImage =
  "https://images.sandee.com/images/header/Default-Header.avif";

export const isHTML = (str) => {
  const htmlRegex = /<\/?[a-z][\s\S]*>/i;
  return htmlRegex.test(str);
};
export async function getCoordinatesFromNominatim(
  country = "",
  state = "",
  city = ""
) {
  // Build query based on available parameters
  let query = "";

  if (city) query += `&city=${encodeURIComponent(city)}`;
  if (state) query += `&state=${encodeURIComponent(state)}`;
  if (country) query += `&country=${encodeURIComponent(country)}`;

  // Check if we have any part of the query (at least one of country, state, or city)
  if (!query) {
    console.error("At least one of country, state, or city must be provided!");
    return null;
  }

  const url = `https://nominatim.openstreetmap.org/search?format=json${query}`;

  try {
    const response = await fetch(url);
    const data = await response.json();

    if (data && data.length > 0) {
      const { lat, lon } = data[0];
      // console.log(`Latitude: ${lat}, Longitude: ${lon}`);
      return { lat, lon };
    } else {
      console.error("Location not found!");
    }
  } catch (error) {
    console.error("Error fetching coordinates from Nominatim:", error);
  }
}

export function isMobileView(breakpoint = 768) {
  if (typeof window !== "undefined") {
    return window.innerWidth <= breakpoint;
  }
}
// export const isMobileView = () => {
//   if (typeof window !== "undefined") {
//     return window?.innerWidth <= 768;
//   }
// };

export const prependTheToCountry = (countryName) => {
  // List of countries that should have "The" before their name
  const countriesWithThe = [
    "Bahamas",
    "Gambia",
    "Maldives",
    "Netherlands",
    "Philippines",
    "United Arab Emirates",
    "United Kingdom",
    "United States of America"
  ];

  // Check if the country is in the list and return the updated name
  if (countriesWithThe.includes(countryName)) {
    return `The ${countryName}`;
  }
  return countryName; // Return the name unchanged if not in the list
}


export const convertSlugToSentence = (slug) => {
  if (!slug) return "";

  // Split the slug by hyphens
  let words = slug.split("-");

  // Capitalize the first word and keep the rest lowercase
  let sentence = words
    .map((word, index) =>
      index === 0 ? word.charAt(0).toUpperCase() + word.slice(1) : word
    )
    .join(" ");

  // Return the formatted sentence
  return sentence;
}

export const EditorContent = ({ ref = null, id = "content", value = "", className = "", dataTestid = "" }) => {
  return <div data-testid={dataTestid} ref={ref} id={id ?? ""} className={`ck-content ${className}`} dangerouslySetInnerHTML={{ __html: value }} />
}

export const processContent = (content) => {
  if (!content) return "";
  return content
    .replaceAll('<p><br></p>', '<div class="spacing_overview"></div>')
    .replaceAll('<p>&nbsp;</p>', '<div class="spacing_overview"></div>')
    .replaceAll('<p><br data-cke-filler="true"></p>', '<div class="spacing_overview"></div>')
    .replace(/<p>(.*?)(<br\s*\/?>|&nbsp;)+<\/p>/g, (match, content) => {
      // Clean and return processed content
      const cleanContent = content.trim();
      return cleanContent
        ? `<p>${cleanContent}</p><div class="spacing_overview"></div>`
        : '<div class="spacing_overview"></div>';
    });
};

export const altText = (data, extraText = "") => {
  const altText = data?.alterText || data?.image?.alterText || data?.images?.[0]?.alterText || null;
  if (extraText != "") {
    return altText ? altText : data?.name ? `${extraText} ${data?.name}` : data?.title ? `${extraText} ${data?.title}` : `${extraText}`;
  } else {
    return altText ? altText : data?.name ? `Sandee ${data?.name}` : data?.title ? `Sandee ${data?.title}` : `Sandee`;
  }
};

export const processContentOnePTag = (html) => {
  // Step 1: Split content by <span class="spacing_overview"></span>
  // Step 1: Replace invalid </br> with <br>
  const cleanedHtmlString = html?.replaceAll(/<\/br>/g, "<br>");

  // Step 2: Replace double <br><br> with a single <br>
  const finalHtmlString = cleanedHtmlString?.replaceAll(/<br>\s*<br>/g, "<br>");
  const parts = finalHtmlString
    ?.replace(/<br\s*\/?>/g, '<span class="spacing_overview"></span>')?.split(/<span class="spacing_overview"><\/span>/g);

  // Step 2: Wrap each part in <p> tags
  const paragraphs = parts?.map((part) => `<p>${part?.trim()}</p><p><br></p>`)?.join("");

  // Step 3: Handle <br> tags by moving content before <br> into a new <div>
  const finalContent = processContent(paragraphs);
  // paragraphs.replace(
  //   /(<p>.*?)(<br>)(.*?<\/p>)/g,
  //   (match, beforeBr, brTag, afterBr) => {
  //     return `<div>${beforeBr}</div><p>${afterBr}</p>`;
  //   }
  // );

  return finalContent;
};

// export const convertMarkdownToHTML = (inputString) => {
//   // Regular expression to match Markdown-style links
//   const markdownLinkRegex = /\[([^\]]+)\]\((https?:\/\/[^\s)]+)\)/g;
//   const def = `India is home to some incredibly beautiful beaches across its vast coastline. Here are some notable ones: - <a href="https://sandee.com/india/goa/candolim/goa-beach">Goa Beach</a>: Known for its vibrant nightlife, water sports, and bustling flea markets. - <a href="https://sandee.com/india/kerala/kovalam/kovalam-beach">Kovalam Beach</a>: Located in Kerala, it offers a calm and serene environment, ideal for relaxation and sunbathing. - <a href="https://sandee.com/india/andaman-and-nicobar-islands/port-blair/andaman-beach">Andaman Beach</a>: In the Andaman and Nicobar Islands, known for its crystal-clear turquoise waters and coral reefs. Each destination provides a unique flavor of India's coastal beauty, from tranquil retreats to active holiday spots. If you need more details on any specific beach or have another beach in mind, let me know!`
//   // Replace Markdown links with HTML <a> tags
//   const htmlString = def?.replace(markdownLinkRegex, (match, text, url) => {
//     return `<a class="text-sandee-blue font-semibold" href="${url}">${text}</a>`;
//   });
//   console.log(htmlString)
//   return `<p>${htmlString}</p>`;
// }
// export const convertMarkdownToHTML = (inputString) => {
//   // Regular expression to match existing HTML <a> tags
//   const htmlLinkRegex = /<a\s+(?:[^>]*?\s+)?href=(["'])(https?:\/\/[^\s)]+)\1([^>]*)>/g;

//   const def = `India offers a stunning variety of beaches across its vast coastline. Here are a few exquisite choices: - Explore the tranquil environment of [Pulicat Lagoon](https://sandee.com/india/tamil-nadu/karimanal/pulicat-lagoon), perfect for bird watching and enjoying a quiet day by the water in Tamil Nadu. - Experience the beautiful sands of [Pasir Iii Holtekamp Beach](https://sandee.com/india/kerala/thrikkunnapuzha/pasir-iii-holtekamp-beach) in Kerala, a hidden gem that offers serene views and a relaxing atmosphere. - Visit [Pantai Wanga](https://sandee.com/india/karnataka/shasihithlu/pantai-wanga) in Karnataka for its stunning coastline and inviting waters, ideal for a day of fun and sun. - Discover the unspoiled beauty of [Pulau Lihaga](https://sandee.com/india/kerala/kozhikode/pulau-lihaga) in Kerala, perfect for snorkeling and exploring marine life. - And don't miss [Pulau Menjangan Beach](https://sandee.com/india/kerala/vakkad/pulau-menjangan-beach), known for its crystal-clear waters and vibrant coral reefs in Kerala. Each of these beaches offers a unique slice of India's coastal cha`;

//   // Replace existing HTML <a> tags with the same tags but with the added class
//   const htmlString = def?.replace(htmlLinkRegex, (match, quote, url, rest) => {
//     return `<a class="text-sandee-blue font-semibold" href="${url}"${rest}>`;
//   });

//   console.log(htmlString);
//   return `<p>${htmlString}</p>`;
// };

export const convertMarkdownToHTML = (inputString) => {
  if (!inputString) return "";

  // Regular expression to match Markdown-style links: [text](url)
  const markdownLinkRegex = /\[([^\]]+)\]\((https?:\/\/[^\s)]+)\)/g;
  // const def = `India offers a stunning variety of beaches across its vast coastline. Here are a few exquisite choices: - Explore the tranquil environment of [Pulicat Lagoon](https://sandee.com/india/tamil-nadu/karimanal/pulicat-lagoon), perfect for bird watching and enjoying a quiet day by the water in Tamil Nadu. - Experience the beautiful sands of [Pasir Iii Holtekamp Beach](https://sandee.com/india/kerala/thrikkunnapuzha/pasir-iii-holtekamp-beach) in Kerala, a hidden gem that offers serene views and a relaxing atmosphere. - Visit [Pantai Wanga](https://sandee.com/india/karnataka/shasihithlu/pantai-wanga) in Karnataka for its stunning coastline and inviting waters, ideal for a day of fun and sun. - Discover the unspoiled beauty of [Pulau Lihaga](https://sandee.com/india/kerala/kozhikode/pulau-lihaga) in Kerala, perfect for snorkeling and exploring marine life. - And don't miss [Pulau Menjangan Beach](https://sandee.com/india/kerala/vakkad/pulau-menjangan-beach), known for its crystal-clear waters and vibrant coral reefs in Kerala. Each of these beaches offers a unique slice of India's coastal cha`;
  // Replace Markdown links with HTML <a> tags
  const htmlString = inputString.replace(markdownLinkRegex, (match, text, url) => {
    return `
     <div className='flex items-center gap-1'>
     <svg
  width={15}
  height={15}
  viewBox="0 0 10 12"
  fill="none"
  xmlns="http://www.w3.org/2000/svg"
  {...props}
>
  <path
    d="M8.39439 1.31845C8.02997 1.09797 7.62759 0.954843 7.21032 0.897268C6.79305 0.839685 6.36907 0.868777 5.96259 0.982871C5.55604 1.09697 5.17493 1.29383 4.84103 1.56223C4.50713 1.83063 4.22698 2.16531 4.01656 2.54714L3.89582 2.7661C3.8545 2.84123 3.84335 2.9305 3.86481 3.01425C3.88628 3.098 3.93861 3.16939 4.01031 3.21274L6.28397 4.58797L4.92334 7.08286L4.89503 7.08243C3.58024 7.08243 2.44154 7.88146 1.76331 9.01117C1.86773 8.97251 1.97846 8.95606 2.08902 8.96274C2.19958 8.96951 2.30774 8.99926 2.40719 9.05034C2.50663 9.10134 2.59536 9.17266 2.66817 9.26009C2.74098 9.34751 2.79642 9.44926 2.83123 9.5594C3.07937 10.3437 3.98366 10.3009 4.21185 9.60611C4.2683 9.43434 4.3745 9.28537 4.51564 9.17994C4.65677 9.07451 4.82581 9.01777 4.9991 9.01777C5.17247 9.01777 5.34151 9.07451 5.48265 9.17994C5.62378 9.28537 5.72998 9.43434 5.78644 9.60611C6.01455 10.3009 6.91888 10.3437 7.16704 9.55897C7.22423 9.3782 7.3364 9.22203 7.48629 9.11429C7.63618 9.00654 7.81561 8.95323 7.99725 8.96231C7.4426 8.06463 6.59496 7.38337 5.6003 7.16051L6.82479 4.91506L9.02014 6.2432C9.05573 6.26471 9.09492 6.27869 9.13558 6.28426C9.17633 6.28991 9.21765 6.28709 9.25725 6.27594C9.29693 6.2648 9.33407 6.2456 9.36656 6.21937C9.39912 6.19314 9.42645 6.16057 9.4469 6.12329L9.56767 5.9048C9.7781 5.52294 9.91466 5.1014 9.96964 4.66426C10.0245 4.22711 9.99672 3.78291 9.88782 3.35702C9.77884 2.93113 9.5909 2.5319 9.33465 2.18212C9.07839 1.83234 8.75889 1.53887 8.39439 1.31845ZM2.33745 9.7304C2.31607 9.66311 2.27438 9.60491 2.21867 9.56454C2.16297 9.52426 2.09626 9.50394 2.0286 9.50686C1.96095 9.50969 1.89601 9.53549 1.84358 9.5804C1.79114 9.62531 1.75406 9.68677 1.73792 9.75569C1.63175 10.2101 1.43066 10.4597 1.20167 10.6045C0.961857 10.7562 0.656258 10.8147 0.31528 10.8147C0.232464 10.8147 0.153035 10.8491 0.0944777 10.9105C0.0359205 10.9719 0.00302124 11.0551 0.00302124 11.1419C0.00302124 11.2286 0.0359205 11.3118 0.0944777 11.3731C0.153035 11.4345 0.232464 11.4689 0.31528 11.4689C0.713718 11.4689 1.14796 11.4027 1.52433 11.1645C1.74894 11.0225 1.93827 10.8269 2.07682 10.5935C2.82166 11.5889 4.27591 11.5836 4.9991 10.5831C5.73399 11.5994 7.22243 11.5889 7.95642 10.5456C8.15565 10.8298 8.41558 11.0612 8.71528 11.2215C9.01507 11.3818 9.34643 11.4665 9.68295 11.4689C9.76583 11.4689 9.84519 11.4345 9.90377 11.3731C9.96236 11.3118 9.99525 11.2286 9.99525 11.1419C9.99525 11.0551 9.96236 10.9719 9.90377 10.9105C9.84519 10.8491 9.76583 10.8147 9.68295 10.8147C9.0689 10.8147 8.45682 10.396 8.25489 9.73391C8.23493 9.668 8.19557 9.6104 8.14239 9.56943C8.08921 9.52846 8.02506 9.50617 7.95904 9.50583C7.89309 9.5054 7.8287 9.52691 7.77511 9.56729C7.72152 9.60766 7.68151 9.66474 7.66081 9.7304C7.2599 10.9992 5.68817 10.983 5.2943 9.7832C5.27311 9.71883 5.23326 9.66303 5.18041 9.62351C5.12747 9.584 5.06415 9.56274 4.9991 9.56274C4.93414 9.56274 4.87081 9.584 4.81787 9.62351C4.76494 9.66303 4.72509 9.71883 4.7039 9.7832C4.31011 10.983 2.73881 10.9988 2.33745 9.7304Z"
    fill="#00aae3"
    {...props}
  />
</svg>
    <a class="text-sandee-blue font-semibold" href="${url}" target="_blank" rel="noopener noreferrer">${text}</a>
    </div>
    `;
  });
  console.log(htmlString)
  // Wrap the result in a <p> tag for consistent formatting
  return `<p>${htmlString}</p>`;
};

export const storeBeachesData = (beachesArray = {}) => {
  const storedBeaches = JSON.parse(localStorage.getItem("beachesData")) || {};
  const beachesObject = beachData?.reduce((acc, beach) => {
    acc[`${beach.name} || ${beach.citySlug} || ${beach.stateSlug} || ${beach.countrySlug}`] = beach;
    return acc;
  }, {});
  Object?.keys(beachesArray)?.map(beach => {
    // console.log(beach, "inside - beaches")
    if (!storedBeaches[beach]) {
      storedBeaches[beach] = beachesArray[beach];
    }
  })
  // console.log(storedBeaches)
  localStorage.setItem("beachesData", JSON.stringify(storedBeaches));
}


export const beachData = [
  {
    "id": 85833,
    "name": "Balneario Santa Monica",
    "countrySlug": "uruguay",
    "stateSlug": "maldonado",
    "citySlug": "santa-monica",
    "GeoLoc": {
      "crs": {
        "type": "name",
        "properties": {
          "name": "EPSG:4326"
        }
      },
      "type": "Point",
      "coordinates": [
        -54.699662,
        -34.850284
      ]
    },
    "country": {
      "id": 212,
      "name": "Uruguay"
    },
    "state": {
      "id": 1023,
      "name": "Maldonado"
    },
    "city": {
      "id": 24456,
      "name": "Santa Monica"
    },
    "beachDescription": {
      "introduction": "Balneario Santa Monica, nestled along Uruguay's tranquil shores, presents a pristine beach escape where golden sands stretch generously beneath the sun's gentle gaze. This beach, known for its serene atmosphere, spans a modest length that is perfect for leisurely strolls or simply soaking in the panoramic vistas of the Atlantic. While Balneario Santa Monica offers a peaceful retreat from the bustling city life, it is important to note that the amenities here are limited. Balneario Santa Monica has few amenities, so plan your trip accordingly, although it does not have lifeguard services, bathroom access, or allow dogs. Visitors will need to come prepared, but those seeking a more undisturbed beach experience will find this destination delightful. Activities at the beach are tailored towards enjoying the natural beauty and tranquility of the surroundings without the interruption of more bustling beach activities, making it an ideal spot for relaxation and disconnecting from the busy world outside."
    },
    "images": [
      {
        "license": {
          "licenseLink": "https://creativecommons.org/licenses/by/2.0",
          "licenseName": "CC BY 2.0",
          "photoModified": "No",
          "photoLink": null,
          "photoName": null,
          "photographerName": "Carlos",
          "photoSource": "Google",
          "photographerLink": "https://maps.google.com/maps/contrib/109158170543289316267"
        },
        "position": {},
        "id": 114762,
        "_id": null,
        "description": null,
        "isDronePhoto": false,
        "isProPhoto": false,
        "isCoverPhoto": true,
        "isFiveStarPhoto": false,
        "isPersonPhoto": false,
        "isInfluencerPhoto": false,
        "isCheckPhoto": false,
        "isCrossPhoto": false,
        "views": null,
        "likes": null,
        "score": null,
        "createdByDisplayName": null,
        "imageUrl": "https://lh5.googleusercontent.com/p/AF1QipPEUCR9lzy61T3IrhYH0vrq2gSsQPDRk2m64bLd=w1600",
        "fileNameProcessed": null,
        "fileName": null,
        "originalName": null,
        "index": 0,
        "imageProcessed": null,
        "approved": true,
        "uploadFromScript": null,
        "deletedFromScript": false,
        "alterText": null,
        "createdAt": "2024-03-09T20:35:33.197Z",
        "updatedAt": "2024-06-06T11:52:15.013Z",
        "deletedAt": null,
        "AllBeachId": 85833
      }
    ]
  },
  {
    "id": 50153,
    "name": "Santa Monica Beach Resort",
    "countrySlug": "philippines",
    "stateSlug": "calabarzon",
    "citySlug": "infanta",
    "GeoLoc": {
      "crs": {
        "type": "name",
        "properties": {
          "name": "EPSG:4326"
        }
      },
      "type": "Point",
      "coordinates": [
        121.662882,
        14.7660592
      ]
    },
    "country": {
      "id": 152,
      "name": "Philippines"
    },
    "state": {
      "id": 302,
      "name": "Calabarzon"
    },
    "city": {
      "id": 11042,
      "name": "Infanta"
    },
    "beachDescription": {
      "introduction": "The area near Santa Monica Beach Resort in Infanta, Quezon, is beautiful, though specific details about the resort itself are lacking. The region is known for its pristine beaches and offers a serene getaway from urban life. Visitors can enjoy outdoor activities, historic sites, and nearby attractions like Minasawa Island. The scenic drive to Infanta from Manila makes for a pleasant journey, offering stunning views along the way.</br>Infanta's beaches are often characterized by black sand and strong waves, making them less ideal for children but suitable for adventurous activities. The landscape is dotted with resorts and event venues, providing ample options for lodging and entertainment.</br>The local culture and cuisine are vibrant, with a mix of traditional Filipino dishes and modern amenities available in nearby restaurants."
    },
    "images": []
  },
  {
    "id": 10931,
    "name": "Praia De Santa Monica",
    "countrySlug": "cape-verde",
    "stateSlug": "boa-vista",
    "citySlug": "curral-velho",
    "GeoLoc": {
      "crs": {
        "type": "name",
        "properties": {
          "name": "EPSG:4326"
        }
      },
      "type": "Point",
      "coordinates": [
        -22.798126,
        15.970853
      ]
    },
    "country": {
      "id": 37,
      "name": "Cape Verde"
    },
    "state": {
      "id": 247,
      "name": "Boa Vista"
    },
    "city": {
      "id": 6478,
      "name": "Curral Velho"
    },
    "beachDescription": {
      "introduction": "Praia de Santa Mónica, located in Curral Velho, Cape Verde, is a breathtaking beach renowned for its beauty and tranquility. This expansive beach stretches along the coastline, offering a long stretch of pristine sandy shores."
    },
    "images": [
      {
        "license": {
          "licenseLink": "https://creativecommons.org/licenses/by/2.0",
          "licenseName": "CC BY 2.0",
          "photoModified": "No",
          "photoLink": null,
          "photoName": null,
          "photographerName": "Sander van Pelt",
          "photoSource": "Google",
          "photographerLink": "https://www.google.com/maps/contrib/118296484074686211163/photos/@-21.7632252,15.49631,7z/data=!3m1!4b1!4m3!8m2!3m1!1e1?entry=ttu"
        },
        "position": {},
        "id": 127911,
        "_id": null,
        "description": null,
        "isDronePhoto": false,
        "isProPhoto": false,
        "isCoverPhoto": true,
        "isFiveStarPhoto": false,
        "isPersonPhoto": false,
        "isInfluencerPhoto": false,
        "isCheckPhoto": false,
        "isCrossPhoto": false,
        "views": null,
        "likes": null,
        "score": null,
        "createdByDisplayName": "Saba Mehran",
        "imageUrl": "https://lh5.googleusercontent.com/p/AF1QipPzqOx8DZm3yh5rNmBZs7Imw7rfO5n9tj_PsdBa=w1200",
        "fileNameProcessed": null,
        "fileName": null,
        "originalName": null,
        "index": 0,
        "imageProcessed": null,
        "approved": true,
        "uploadFromScript": false,
        "deletedFromScript": false,
        "alterText": null,
        "createdAt": "2024-06-19T20:30:28.865Z",
        "updatedAt": "2024-06-19T20:30:35.944Z",
        "deletedAt": null,
        "AllBeachId": 10931
      }
    ]
  },
  {
    "id": 55763,
    "name": "Santa Monica Beach",
    "countrySlug": "philippines",
    "stateSlug": "mimaropa",
    "citySlug": "el-nido",
    "GeoLoc": {
      "crs": {
        "type": "name",
        "properties": {
          "name": "EPSG:4326"
        }
      },
      "type": "Point",
      "coordinates": [
        119.561869,
        11.306954
      ]
    },
    "country": {
      "id": 152,
      "name": "Philippines"
    },
    "state": {
      "id": 1108,
      "name": "MIMAROPA"
    },
    "city": {
      "id": 7779,
      "name": "El Nido"
    },
    "beachDescription": {
      "introduction": "Santa Monica Beach is a stunning stretch of white sand located on the west coast of El Nido, Philippines. It is renowned for its crystal-clear waters and tranquil surroundings. As part of the beautiful El Nido area, often referred to as the Philippines' last frontier, it offers visitors a chance to experience pristine natural beauty without the crowds. The beach maintains a serene atmosphere perfect for relaxation and exploration.</br>One of the unique aspects of this beach is its proximity to El Nido's scenic landscapes and rich biodiversity, making it an ideal spot for nature enthusiasts. The area is dotted with luxury and budget-friendly resorts that cater to various types of travelers.</br>El Nido, as a whole, offers a blend of adventure and tranquility, with opportunities for island hopping, snorkeling, and diving in its majestic lagoons and beaches."
    },
    "images": [
      {
        "license": {
          "licenseLink": "https://creativecommons.org/licenses/by/2.0",
          "licenseName": "CC BY 2.0",
          "photoModified": "No",
          "photoLink": null,
          "photoName": null,
          "photographerName": "Jun Jeon",
          "photoSource": "Google",
          "photographerLink": "https://maps.google.com/maps/contrib/114622577130698027665"
        },
        "position": {},
        "id": 78296,
        "_id": null,
        "description": null,
        "isDronePhoto": false,
        "isProPhoto": false,
        "isCoverPhoto": true,
        "isFiveStarPhoto": false,
        "isPersonPhoto": false,
        "isInfluencerPhoto": false,
        "isCheckPhoto": false,
        "isCrossPhoto": false,
        "views": null,
        "likes": null,
        "score": null,
        "createdByDisplayName": null,
        "imageUrl": "https://lh5.googleusercontent.com/p/AF1QipMFjjC3CubpbRsZ-aEtuOBTHe9ZMgnnrObOpMLI=w1600",
        "fileNameProcessed": null,
        "fileName": null,
        "originalName": null,
        "index": 0,
        "imageProcessed": null,
        "approved": true,
        "uploadFromScript": true,
        "deletedFromScript": false,
        "alterText": null,
        "createdAt": "2023-12-05T08:58:45.547Z",
        "updatedAt": "2024-06-06T10:18:00.853Z",
        "deletedAt": null,
        "AllBeachId": 55763
      }
    ]
  },
  {
    "id": 75311,
    "name": "Santa Monica Beach",
    "countrySlug": "united-states",
    "stateSlug": "california",
    "citySlug": "santa-monica",
    "GeoLoc": {
      "crs": {
        "type": "name",
        "properties": {
          "name": "EPSG:4326"
        }
      },
      "type": "Point",
      "coordinates": [
        -118.501823,
        34.013605
      ]
    },
    "country": {
      "id": 210,
      "name": "United States"
    },
    "state": {
      "id": 304,
      "name": "California"
    },
    "city": {
      "id": 24453,
      "name": "Santa Monica"
    },
    "beachDescription": {
      "introduction": "Santa Monica Beach, located in Santa Monica, California, is a 3.5-mile stretch of light brown sand along the Pacific coast. It is renowned for its vibrant beach culture, offering a mix of recreational activities like surfing, beach volleyball, and rollerblading. The beach is divided into three main sections: North Beach, Pier Beach, and South Beach. The iconic Santa Monica Pier, a historic landmark, features an amusement park and various dining options. The beach attracts millions of visitors annually and is celebrated for its accessibility and environmental stewardship.</br> Santa Monica Beach is not only a popular tourist destination but also a significant cultural and historical site, having been featured in numerous films and TV shows. Its scenic views and lively atmosphere make it a favorite among locals and visitors alike.</br> The beach's proximity to Los Angeles and its integration with the city's infrastructure make it an accessible and enjoyable destination for a wide range of activities and interests."
    },
    "images": [
      {
        "license": [],
        "position": {},
        "id": 2861,
        "_id": "59ee973afb32b35c6e54e894",
        "description": null,
        "isDronePhoto": false,
        "isProPhoto": false,
        "isCoverPhoto": true,
        "isFiveStarPhoto": false,
        "isPersonPhoto": false,
        "isInfluencerPhoto": false,
        "isCheckPhoto": false,
        "isCrossPhoto": false,
        "views": 31,
        "likes": 0,
        "score": 0,
        "createdByDisplayName": "User Uploaded Photo",
        "imageUrl": "http://sandee-media.s3.amazonaws.com/ugp/59c2f49cb2333932644e3c8b/59ee973afb32b35c6e54e894.jpg",
        "fileNameProcessed": "59c2f49cb2333932644e3c8b/59ee973afb32b35c6e54e894.jpeg",
        "fileName": "ugp/59c2f49cb2333932644e3c8b/59ee973afb32b35c6e54e894.jpg",
        "originalName": "Santa Monica State Beach-8.jpg",
        "index": 46,
        "imageProcessed": "16908969072522778",
        "approved": true,
        "uploadFromScript": null,
        "deletedFromScript": false,
        "alterText": null,
        "createdAt": "2023-03-28T06:17:04.130Z",
        "updatedAt": "2024-12-24T22:43:14.672Z",
        "deletedAt": null,
        "AllBeachId": 75311
      }
    ]
  },
  {
    "id": 79985,
    "name": "Santa Monica Beach",
    "countrySlug": "united-states",
    "stateSlug": "michigan",
    "citySlug": "toivola",
    "GeoLoc": {
      "crs": {
        "type": "name",
        "properties": {
          "name": "EPSG:4326"
        }
      },
      "type": "Point",
      "coordinates": [
        -88.962822,
        47.0053093
      ]
    },
    "country": {
      "id": 210,
      "name": "United States"
    },
    "state": {
      "id": 1100,
      "name": "Michigan"
    },
    "city": {
      "id": 27716,
      "name": "Toivola"
    },
    "beachDescription": {
      "introduction": "Santa Monica Beach in Toivola, Michigan, does not appear to be a well-known or prominent beach destination. However, the area around Toivola offers scenic landscapes and access to Lake Superior, which is renowned for its natural beauty and outdoor recreational opportunities. The region is ideal for those seeking a tranquil, natural setting with opportunities for hiking, fishing, and exploring the Upper Peninsula's wilderness. The nearby town of Toivola provides basic amenities and services for travelers.</br> </br> The Upper Peninsula of Michigan is famous for its rugged beauty, with vast forests, numerous lakes, and scenic coastlines along Lake Superior. While specific details about a beach named Santa Monica Beach in Toivola are scarce, the broader area offers a serene and picturesque environment that appeals to nature lovers and those looking for a peaceful getaway.</br> </br> Visitors to the region often enjoy activities such as hiking, fishing, and exploring the local culture, which is rich in history and natural beauty. The proximity to Lake Superior provides stunning views and opportunities for water activities during warmer months."
    },
    "images": [
      {
        "license": {
          "licenseLink": "https://creativecommons.org/licenses/by/2.0",
          "licenseName": "CC BY 2.0",
          "photoModified": "No",
          "photoLink": null,
          "photoName": null,
          "photographerName": "Dušan Janík",
          "photoSource": "Google",
          "photographerLink": null
        },
        "position": {},
        "id": 88033,
        "_id": null,
        "description": null,
        "isDronePhoto": false,
        "isProPhoto": false,
        "isCoverPhoto": true,
        "isFiveStarPhoto": false,
        "isPersonPhoto": false,
        "isInfluencerPhoto": false,
        "isCheckPhoto": false,
        "isCrossPhoto": false,
        "views": null,
        "likes": null,
        "score": null,
        "createdByDisplayName": "Dušan Janík",
        "imageUrl": "https://lh5.googleusercontent.com/p/AF1QipNLsjlC0YL9mFUy9WrhwSTHwQ8GWGMQSPVCa9lf=s1600",
        "fileNameProcessed": null,
        "fileName": null,
        "originalName": null,
        "index": 3,
        "imageProcessed": null,
        "approved": true,
        "uploadFromScript": null,
        "deletedFromScript": false,
        "alterText": null,
        "createdAt": "2023-12-15T13:45:22.503Z",
        "updatedAt": "2024-06-28T18:09:28.618Z",
        "deletedAt": null,
        "AllBeachId": 79985
      }
    ]
  },
  {
    "id": 6367,
    "name": "Praia Santa Monica",
    "countrySlug": "brazil",
    "stateSlug": "espirito-santo",
    "citySlug": "santa-monica",
    "GeoLoc": {
      "crs": {
        "type": "name",
        "properties": {
          "name": "EPSG:4326"
        }
      },
      "type": "Point",
      "coordinates": [
        -40.452353,
        -20.624191
      ]
    },
    "country": {
      "id": 28,
      "name": "Brazil"
    },
    "state": {
      "id": 587,
      "name": "Espirito Santo"
    },
    "city": {
      "id": 24455,
      "name": "Santa Monica"
    },
    "beachDescription": {
      "introduction": "Praia Santa Monica, located in Guarapari, Brazil, is a beautiful beach known for its scenic beauty and tranquil atmosphere. This beach stretches along a considerable length of shoreline, providing ample space for beachgoers to relax and enjoy the coastal ambiance. The sand on Praia Santa Monica is soft and golden, perfect for sunbathing and building sandcastles."
    },
    "images": [
      {
        "license": {
          "licenseName": "CC BY 2.0",
          "licenseLink": "https://creativecommons.org/licenses/by/2.0/legalcode",
          "photoModified": "No",
          "photographerName": "",
          "photoLink": "",
          "photoName": ""
        },
        "position": {},
        "id": 49618,
        "_id": "5a93826e5a1853080f976b5f",
        "description": null,
        "isDronePhoto": false,
        "isProPhoto": false,
        "isCoverPhoto": true,
        "isFiveStarPhoto": false,
        "isPersonPhoto": false,
        "isInfluencerPhoto": false,
        "isCheckPhoto": false,
        "isCrossPhoto": false,
        "views": 1,
        "likes": null,
        "score": 0,
        "createdByDisplayName": "Pedro Milcent",
        "imageUrl": "https://sandee-resized.s3-us-west-2.amazonaws.com/original/5a937fa15a1853080f976b5a/5a93826e5a1853080f976b5f.webp",
        "fileNameProcessed": "5a937fa15a1853080f976b5a/5a93826e5a1853080f976b5f.jpeg",
        "fileName": "ugp/5a937fa15a1853080f976b5a/5a93826e5a1853080f976b5f.JPG",
        "originalName": "IMG_1119.JPG",
        "index": 0,
        "imageProcessed": "169090880437749087",
        "approved": true,
        "uploadFromScript": null,
        "deletedFromScript": false,
        "alterText": null,
        "createdAt": "2023-03-28T17:02:15.454Z",
        "updatedAt": "2024-06-06T11:19:50.909Z",
        "deletedAt": null,
        "AllBeachId": 6367
      }
    ]
  },
  {
    "id": 75318,
    "name": "Santa Monica Pier Beach",
    "countrySlug": "united-states",
    "stateSlug": "california",
    "citySlug": "santa-monica",
    "GeoLoc": {
      "crs": {
        "type": "name",
        "properties": {
          "name": "EPSG:4326"
        }
      },
      "type": "Point",
      "coordinates": [
        -118.496892,
        34.008345
      ]
    },
    "country": {
      "id": 210,
      "name": "United States"
    },
    "state": {
      "id": 304,
      "name": "California"
    },
    "city": {
      "id": 24453,
      "name": "Santa Monica"
    },
    "beachDescription": {
      "introduction": "Santa Monica Pier Beach is a vibrant and iconic destination in California, known for its stunning coastline and lively atmosphere. The beach stretches over three miles, offering pristine sand and a picturesque backdrop for activities like biking, rollerblading, and volleyball. The Santa Monica Pier, with its amusement park, Pacific Park, is a central attraction, featuring the world's only solar-powered Ferris wheel and a variety of rides and games. The area combines urban flair with coastal tranquility, making it a must-visit for both locals and tourists. Visitors can enjoy a range of dining options, from casual eateries to upscale restaurants, and explore nearby attractions like Palisades Park and Third Street Promenade.</br> The beach is particularly popular during the late spring to early fall months when the weather is warm and ideal for outdoor activities. It also offers a unique blend of family-friendly activities and vibrant nightlife, making it suitable for all ages.</br> With its rich history and frequent appearances in movies and TV shows, Santa Monica Pier Beach has become a symbol of the classic California beach lifestyle."
    },
    "images": [
      {
        "license": [],
        "position": {},
        "id": 64061,
        "_id": null,
        "description": "Randall Kaplan",
        "isDronePhoto": false,
        "isProPhoto": true,
        "isCoverPhoto": true,
        "isFiveStarPhoto": false,
        "isPersonPhoto": false,
        "isInfluencerPhoto": false,
        "isCheckPhoto": false,
        "isCrossPhoto": false,
        "views": null,
        "likes": null,
        "score": null,
        "createdByDisplayName": "Randall Kaplan",
        "imageUrl": "https://jumpinvestors.s3.amazonaws.com/1689177217573.webp",
        "fileNameProcessed": "1689177217573.webp",
        "fileName": null,
        "originalName": null,
        "index": 1,
        "imageProcessed": "169091246515663305",
        "approved": true,
        "uploadFromScript": null,
        "deletedFromScript": false,
        "alterText": null,
        "createdAt": "2023-07-12T15:53:37.738Z",
        "updatedAt": "2024-06-06T11:00:34.464Z",
        "deletedAt": null,
        "AllBeachId": 75318
      }
    ]
  },
  {
    "id": 10885,
    "name": "Praia De Santa Monica",
    "countrySlug": "cape-verde",
    "stateSlug": "boa-vista",
    "citySlug": "povoacao-velha",
    "GeoLoc": {
      "crs": {
        "type": "name",
        "properties": {
          "name": "EPSG:4326"
        }
      },
      "type": "Point",
      "coordinates": [
        -22.95276,
        16.017652
      ]
    },
    "country": {
      "id": 37,
      "name": "Cape Verde"
    },
    "state": {
      "id": 247,
      "name": "Boa Vista"
    },
    "city": {
      "id": 21646,
      "name": "Povoacao Velha"
    },
    "beachDescription": {
      "introduction": "<p><span style=\"background-color: transparent; color: rgb(0, 0, 0);\">Located in the south-western part of the island of Boa Vista in Cabo Verde, Praia de Santa Monica – also called Santa Monica Beach, Praia do Curralinho, and Curralinho Beach – is an 18-kilometers-long beach with white sand, a few large rocks, nearby sand dunes, and nearby large rocky cliffs.&nbsp; This sandy beach is located near the village of Povoacao Velha.&nbsp; </span><em style=\"background-color: transparent; color: rgb(0, 0, 0);\">Travel+Leisure </em><span style=\"background-color: transparent; color: rgb(0, 0, 0);\">magazine listed this beach in Cape Verde as one of the “26 Most Beautiful Beaches in the World” in 2024 – this beach was listed as the 21st best beach in the world.&nbsp; In addition, TripAdvisor also listed this beach as one of the best and stunning beaches in the world in 2019 as a part of its annual </span><em style=\"background-color: transparent; color: rgb(0, 0, 0);\">Travelers’ Choice</em><span style=\"background-color: transparent; color: rgb(0, 0, 0);\"> awards.&nbsp; Praia de Santa Monica is also a favorite of Randall Kaplan, the world’s foremost beach expert who is known as Mr. Beach.</span></p>"
    },
    "images": [
      {
        "license": [],
        "position": {},
        "id": 65599,
        "_id": null,
        "description": "https://www.weather2travel.com/blog/cape-verde-best-beaches-island-by-island/",
        "isDronePhoto": false,
        "isProPhoto": false,
        "isCoverPhoto": true,
        "isFiveStarPhoto": true,
        "isPersonPhoto": false,
        "isInfluencerPhoto": false,
        "isCheckPhoto": false,
        "isCrossPhoto": false,
        "views": null,
        "likes": null,
        "score": null,
        "createdByDisplayName": "User uploaded photo",
        "imageUrl": "https://jumpinvestors.s3.amazonaws.com/1689289667928.jpg",
        "fileNameProcessed": "1689289667928.webp",
        "fileName": null,
        "originalName": null,
        "index": 2,
        "imageProcessed": "169091285939864836",
        "approved": true,
        "uploadFromScript": null,
        "deletedFromScript": false,
        "alterText": null,
        "createdAt": "2023-07-13T23:07:48.089Z",
        "updatedAt": "2024-09-04T22:59:22.794Z",
        "deletedAt": null,
        "AllBeachId": 10885
      }
    ]
  },
  {
    "id": 75315,
    "name": "Santa Monica - North Beach",
    "countrySlug": "united-states",
    "stateSlug": "california",
    "citySlug": "santa-monica",
    "GeoLoc": {
      "crs": {
        "type": "name",
        "properties": {
          "name": "EPSG:4326"
        }
      },
      "type": "Point",
      "coordinates": [
        -118.507465,
        34.0185
      ]
    },
    "country": {
      "id": 210,
      "name": "United States"
    },
    "state": {
      "id": 304,
      "name": "California"
    },
    "city": {
      "id": 24453,
      "name": "Santa Monica"
    },
    "beachDescription": {
      "introduction": "Santa Monica North Beach is part of the larger Santa Monica State Beach, stretching from the Santa Monica Pier to the city border near Will Rogers State Beach. It offers a wide sandy area with sweeping views of Santa Monica Bay, making it a popular spot for beachgoers. The beach is known for its vibrant atmosphere, with activities like surfing, beach volleyball, and rollerblading. It's also a cultural hub, hosting events and providing access to various amenities and attractions nearby, such as the Annenberg Community Beach House and the Marvin Braude Bike Trail.</br> The beach is accessible by car, bike, or public transportation, and it attracts millions of visitors annually. Its proximity to Santa Monica Pier, with its amusement park and iconic Ferris wheel, adds to its appeal. The area is also rich in history and culture, having been a symbol of beach culture for nearly 120 years.</br> Santa Monica North Beach is not only a recreational spot but also a place for social interaction, with its Chess Park and Muscle Beach areas. It has been recognized for its commitment to accessibility and environmental stewardship, making it a beloved public space in California."
    },
    "images": [
      {
        "license": [],
        "position": {},
        "id": 25494,
        "_id": "5be8eac8aa698b21b6c72c3b",
        "description": null,
        "isDronePhoto": true,
        "isProPhoto": true,
        "isCoverPhoto": true,
        "isFiveStarPhoto": false,
        "isPersonPhoto": false,
        "isInfluencerPhoto": false,
        "isCheckPhoto": false,
        "isCrossPhoto": false,
        "views": 30,
        "likes": null,
        "score": 0,
        "createdByDisplayName": "Randall Kaplan",
        "imageUrl": "https://sandee-resized.s3-us-west-2.amazonaws.com/original/589deb9045b10b39dd6bc13d/image/jpeg/1541990867685.57d882ec666f1f0cec6fa1cb.DJI_0027.webp",
        "fileNameProcessed": "589deb9045b10b39dd6bc13d/image/jpeg/1541990867685.57d882ec666f1f0cec6fa1cb.DJI_0027.jpeg",
        "fileName": "589deb9045b10b39dd6bc13d/image/jpeg/1541990867685.57d882ec666f1f0cec6fa1cb.DJI_0027.JPG",
        "originalName": "589deb9045b10b39dd6bc13d/image/jpeg/1541990867685.57d882ec666f1f0cec6fa1cb.DJI_0027.JPG",
        "index": 9,
        "imageProcessed": "169090265815725164",
        "approved": true,
        "uploadFromScript": null,
        "deletedFromScript": false,
        "alterText": null,
        "createdAt": "2023-03-28T07:08:47.076Z",
        "updatedAt": "2024-06-06T09:50:19.343Z",
        "deletedAt": null,
        "AllBeachId": 75315
      }
    ]
  }
]