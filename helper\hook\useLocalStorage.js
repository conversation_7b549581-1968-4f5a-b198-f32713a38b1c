"use client";
import { useState, useEffect } from "react";
import { isJsonString, validValue } from "../functions";
// check deploye
// Custom hook to use localStorage
const useLocalStorage = (key, initialValue = "") => {
  // Get data from localStorage or use initial value
  //   if (
  //     !typeof window &&
  //     window.localStorage &&
  //     window &&
  //     typeof window !== "undefined"
  //   ) {
  //     return [initialValue, () => {}];
  //   }

  const storedValue =
    typeof window && typeof window !== "undefined"
      ? window?.localStorage?.getItem(key)
      : "";
  const initial =
    storedValue !== null && storedValue !== undefined && validValue(storedValue)
      ? isJsonString(storedValue)
        ? JSON?.parse(storedValue)
        : `${storedValue}`
      : initialValue;

  // State to hold the current value
  const [value, setValue] = useState(initial);

  // Update localStorage and state when the value changes
  const setStoredValue = (newValue) => {
    setValue(newValue);
    typeof window &&
      typeof window !== "undefined" &&
      window?.localStorage?.setItem(key, JSON.stringify(newValue));
  };

  // Listen for changes in other tabs/windows
  useEffect(() => {
    const handleStorageChange = (event) => {
      if (event?.key === key) {
        const newValue =
          event.newValue !== null && event?.newValue !== undefined
            ? JSON?.parse(event?.newValue)
            : null;
        setValue(newValue);
      }
    };

    window.addEventListener("storage", handleStorageChange);

    return () => {
      window.removeEventListener("storage", handleStorageChange);
    };
  }, [key]);

  return [value, setStoredValue];
};

export default useLocalStorage;
