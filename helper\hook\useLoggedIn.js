"use client";
import useLocalStorage from "./useLocalStorage";
import { validValue } from "../functions";

const useLoggedIn = () => {
  const [token, SetToken] = useLocalStorage("token");
  const [profileData, setProfileData] = useLocalStorage("profile", {});

  return {
    token,
    SetToken,
    profileData,
    setProfileData,
    isLoggedIn: validValue(token) && validValue(profileData),
  };
};

export default useLoggedIn;
