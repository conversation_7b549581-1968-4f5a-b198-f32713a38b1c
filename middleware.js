import { NextResponse } from "next/server";
import { API_BASE_URL } from "./helper/functions";

let Redirections = {};
let FirstSetup = true;
export async function middleware(request) {
  const url = new URL(request.url);
  const origin = url.origin;
  const pathname = url.pathname;

  const requestHeaders = new Headers(request.headers);
  requestHeaders.set("x-url", request.url);
  requestHeaders.set("x-origin", origin);
  requestHeaders.set("x-pathname", pathname);
  if (pathname === "/api/resyncmiddleware" || FirstSetup) {
    try {
      // console.log(pathname, "API Call");
      // Make a POST API call using fetch
      const response = await fetch(`${API_BASE_URL}/beachesUrls/`);
      const data = await response.json();
      if (data?.data?.rows && Array.isArray(data?.data?.rows)) {
        data?.data?.rows?.forEach((sngleRed) => {
          Redirections[sngleRed?.url] = sngleRed?.originalUrl;
        });
      } else {
        console.error("Failed to fetch data from the API");
      }
    } catch (error) {
      console.error("Error occurred while making API call:", error);
    }
    FirstSetup = false;
  }
  // if (request.nextUrl.pathname === "/maps") {
  //   return NextResponse.redirect(new URL("/map", request.url));
  // }
  if (Redirections?.[pathname]) {
    return NextResponse.redirect(new URL(Redirections[pathname], request.url));
  }

  // Get the origin from the request headers
  const requestOrigin = request.headers.get('origin') || '*'

  // Handle preflight requests
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Max-Age': '86400',
      },
    })
  }

  // For non-OPTIONS requests, continue to the route handler
  return NextResponse.next({
    request: {
      headers: requestHeaders,
    },
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}

export const config = {
  matcher: "/((?!_next/static|static|_next/image|favicon.ico).*)",
};
