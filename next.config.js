/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: false,
  images: {
    // unoptimized: true,
    remotePatterns: [
      {
        protocol: "https",
        hostname: "sandee-resized.s3.us-west-2.amazonaws.com",
      },
      {
        protocol: "https",
        hostname: "s3.us-west-2.amazonaws.com",
      },
      {
        protocol: "https",
        hostname: "sandee-v2.s3.us-west-2.amazonaws.com",
      },
      {
        protocol: "https",
        hostname: "firebasestorage.googleapis.com",
      },
      {
        protocol: "https",
        hostname: "*",
      },
      {
        protocol: "http",
        hostname: "*",
      },
    ],
  },
  async redirects() {
    return [
      {
        source: "/welcome",
        destination: "/",
        permanent: true,
      },
      {
        source: "/erehwon-point-beach-cowes-victoria-australia",
        destination: "/australia/victoria/cowes/erehwon-point-beach",
        permanent: true,
      },
      {
        source: "/beach/honeymoon-bay-toucheng-yilan-taiwan",
        destination: "/taiwan/taiwan-province/toucheng-township/honeymoon-bay",
        permanent: true,
      },
      {
        source: "/lists",
        destination: "/list",
        permanent: true,
      },
      {
        source: "/blogs",
        destination: "/blog",
        permanent: true,
      },
      {
        source: "/beach/:countrySlug/:stateSlug/:citySlug",
        destination: "/:countrySlug/:stateSlug/:citySlug",
        permanent: true, // or false for a temporary redirect
      },
      {
        source: "/beach/:countrySlug/:stateSlug",
        destination: "/:countrySlug/:stateSlug",
        permanent: true, // or false for a temporary redirect
      },
      {
        source: "/beach/:countrySlug",
        destination: "/:countrySlug",
        permanent: true, // or false for a temporary redirect
      },
      {
        source: "/beach",
        destination: "/",
        permanent: true, // or false for a temporary redirect
      },
      {
        source: "/beach/:city/:country/:url*",
        destination: "/",
        permanent: true,
      },
      {
        source: "/top/:city/:country/:url*",
        destination: "/",
        permanent: true,
      },
      {
        source: "/top/:countrySlug/:stateSlug/:citySlug",
        destination: "/:countrySlug/:stateSlug/:citySlug",
        permanent: true, // or false for a temporary redirect
      },
      {
        source: "/top/:countrySlug/:stateSlug",
        destination: "/:countrySlug/:stateSlug",
        permanent: true, // or false for a temporary redirect
      },
      {
        source: "/top/:countrySlug",
        destination: "/:countrySlug",
        permanent: true, // or false for a temporary redirect
      },
      {
        source: "/top",
        destination: "/",
        permanent: true, // or false for a temporary redirect
      },
      {
        source: "/:country/press",
        destination: "/press",
        permanent: true,
      },
      {
        source: "/:country/:state/press",
        destination: "/press",
        permanent: true,
      },
      {
        source: "/:country/:state/:city/press",
        destination: "/press",
        permanent: true,
      },
      {
        source: "/blogs/:any*",
        destination: "/blog/:any*",
        permanent: true,
      },
      {
        source: "/:countrySlug/:stateSlug/:citySlug/aboutUs",
        destination: "/about-us",
        permanent: true,
      },
      {
        source: "/:countrySlug/:stateSlug/aboutUs",
        destination: "/about-us",
        permanent: true,
      },
      {
        source: "/:countrySlug/aboutUs",
        destination: "/about-us",
        permanent: true,
      },
      {
        source: "/:countrySlug/:stateSlug/:citySlug/contactUs",
        destination: "/contact-us",
        permanent: true,
      },
      {
        source: "/:countrySlug/:stateSlug/contactUs",
        destination: "/contact-us",
        permanent: true,
      },
      {
        source: "/:countrySlug/contactUs",
        destination: "/contact-us",
        permanent: true,
      },
      {
        source: "/filter",
        destination: "/",
        permanent: true,
      },
      {
        source: "/filter/:stateSlug/:citySlug",
        destination: "/",
        permanent: true,
      },
      {
        source: "/filter/:stateSlug/:citySlug/:nameSlug",
        destination: "/",
        permanent: true,
      },
      // undefined
      {
        source: "/undefined/:country/:state/:city",
        destination: "/",
        permanent: true,
      },
      {
        source: "/:country/undefined/:city",
        destination: "/",
        permanent: true,
      },
      {
        source: "/:country/:state/undefined",
        destination: "/",
        permanent: true,
      },
      {
        source: "/undefined/:country/undefined/:city",
        destination: "/",
        permanent: true,
      },
      {
        source: "/undefined/:country/:state/undefined",
        destination: "/",
        permanent: true,
      },
      {
        source: "/:country/undefined/undefined",
        destination: "/",
        permanent: true,
      },
      {
        source: "/undefined/:country/undefined/undefined",
        destination: "/",
        permanent: true,
      },
      {
        source: "/:countrySlug/:stateSlug/:citySlug/:nameSlug/:any/:any2*",
        destination: "/",
        permanent: true,
      },
      {
        source: "/maps",
        destination: "/map",
        permanent: true,
      },
    ];
  },
  async rewrites() {
    return [
      {
        source: '/map/:beachName/:coordinates',
        destination: '/map', // Rewrite /blog to /posts
      },
      {
        source: '/maps/nude-beaches/:beachName/:coordinates',
        destination: '/maps/nude-beaches', // Rewrite /blog to /posts
      },
      // {
      //   source: '/maps',
      //   destination: '/map', // Rewrite /blog to /posts
      // },
    ];
  },
};

module.exports = nextConfig;

// const { withContentlayer } = require("next-contentlayer");

// const withBundleAnalyzer = require("@next/bundle-analyzer")({
//   enabled: process.env.ANALYZE === "true",
// });

// // You might need to insert additional domains in script-src if you are using external services
// const ContentSecurityPolicy = `
//   default-src 'self';
//   script-src 'self' 'unsafe-eval' 'unsafe-inline' giscus.app analytics.umami.is;
//   style-src 'self' 'unsafe-inline';
//   img-src * blob: data:;
//   media-src *.s3.amazonaws.com;
//   connect-src *;
//   font-src 'self';
//   frame-src giscus.app
// `;

// const securityHeaders = [
//   // https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP
//   {
//     key: "Content-Security-Policy",
//     value: ContentSecurityPolicy.replace(/\n/g, ""),
//   },
//   // https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Referrer-Policy
//   {
//     key: "Referrer-Policy",
//     value: "strict-origin-when-cross-origin",
//   },
//   // https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Frame-Options
//   {
//     key: "X-Frame-Options",
//     value: "DENY",
//   },
//   // https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Content-Type-Options
//   {
//     key: "X-Content-Type-Options",
//     value: "nosniff",
//   },
//   // https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-DNS-Prefetch-Control
//   {
//     key: "X-DNS-Prefetch-Control",
//     value: "on",
//   },
//   // https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Strict-Transport-Security
//   {
//     key: "Strict-Transport-Security",
//     value: "max-age=31536000; includeSubDomains",
//   },
//   // https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Feature-Policy
//   {
//     key: "Permissions-Policy",
//     value: "camera=(), microphone=(), geolocation=()",
//   },
// ];

// /**
//  * @type {import('next/dist/next-server/server/config').NextConfig}
//  **/
// module.exports = () => {
//   const plugins = [withBundleAnalyzer];
//   //   const plugins = [withContentlayer, withBundleAnalyzer];
//   return plugins.reduce((acc, next) => next(acc), {
//     reactStrictMode: true,
//     pageExtensions: ["ts", "tsx", "js", "jsx", "md", "mdx"],
//     eslint: {
//       dirs: ["app", "components", "layouts", "scripts"],
//     },
//     images: {
//       remotePatterns: [
//         {
//           protocol: "https",
//           hostname: "picsum.photos",
//         },
//       ],
//     },
//     async headers() {
//       return [
//         {
//           source: "/(.*)",
//           headers: securityHeaders,
//         },
//       ];
//     },
//     webpack: (config, options) => {
//       config.module.rules.push({
//         test: /\.svg$/,
//         use: ["@svgr/webpack"],
//       });

//       return config;
//     },
//   });
// };
