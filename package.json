{"name": "sandee", "version": "0.1.0", "private": true, "scripts": {"dev": "concurrently -k 'node --experimental-modules generate-sitemap.mjs' 'node --experimental-modules generate-CountrySitemap.mjs' 'node --experimental-modules generate-StateSitemap.mjs' 'node --experimental-modules generate-IslandSitemap.mjs'  'node --experimental-modules generate-CitySitemap.mjs'  'node --experimental-modules generate-BeachSitemap.mjs'  'node --experimental-modules generate-ListSitemap.mjs' 'node --experimental-modules generate-BlogSitemap.mjs' 'node --experimental-modules generate-MainSitemap.mjs' 'node --experimental-modules generate-MapSitemap.mjs' 'node --experimental-modules generate-SharkSpeciesSitemap.mjs' 'node --experimental-modules generate-NudeBeachSitemap.mjs' 'node --experimental-modules generate-NewsSitemap.mjs' 'next dev'", "build": "next build", "start": "concurrently -k 'node --experimental-modules generate-sitemap.mjs' 'node --experimental-modules generate-CountrySitemap.mjs' 'node --experimental-modules generate-StateSitemap.mjs' 'node --experimental-modules generate-IslandSitemap.mjs'  'node --experimental-modules generate-CitySitemap.mjs'  'node --experimental-modules generate-BeachSitemap.mjs'  'node --experimental-modules generate-ListSitemap.mjs' 'node --experimental-modules generate-BlogSitemap.mjs' 'node --experimental-modules generate-MainSitemap.mjs' 'node --experimental-modules generate-MapSitemap.mjs' 'node --experimental-modules generate-SharkSpeciesSitemap.mjs' 'node --experimental-modules generate-NudeBeachSitemap.mjs' 'node --experimental-modules generate-NewsSitemap.mjs' 'next start'", "dev2": "next dev", "build2": "next build", "start2": "next start", "turbo": "next dev --turbo", "lint": "next lint"}, "dependencies": {"@mapbox/mapbox-gl-geocoder": "^5.1.0", "algoliasearch": "^5.35.0", "antd": "^5.27.0", "axios": "^1.11.0", "cheerio": "^1.1.2", "concurrently": "^9.2.0", "dayjs": "^1.11.13", "dotenv": "^17.2.1", "firebase": "^12.1.0", "fs-extra": "^11.3.1", "html-react-parser": "^5.2.6", "lodash": "^4.17.21", "mapbox-gl": "^3.14.0", "moment": "^2.30.1", "moment-timezone": "^0.6.0", "next": "15.4.6", "next-nprogress-bar": "^2.4.7", "node-cron": "^4.2.1", "react": "^19.1.1", "react-dom": "^19.1.1", "react-dropzone": "^14.3.8", "react-infinite-scroll-component": "^6.1.0", "react-use-clipboard": "^1.0.9", "sharp": "^0.34.3", "socket.io-client": "^4.8.1", "swiper": "^11.2.10"}, "devDependencies": {"@svgr/webpack": "^8.1.0", "autoprefixer": "^10.4.21", "eslint": "^9.33.0", "eslint-config-next": "15.4.6", "postcss": "^8.5.6", "tailwindcss": "^4.1.12"}}