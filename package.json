{"name": "sandee", "version": "0.1.0", "private": true, "scripts": {"dev": "concurrently -k 'node --experimental-modules generate-sitemap.mjs' 'node --experimental-modules generate-CountrySitemap.mjs' 'node --experimental-modules generate-StateSitemap.mjs' 'node --experimental-modules generate-IslandSitemap.mjs'  'node --experimental-modules generate-CitySitemap.mjs'  'node --experimental-modules generate-BeachSitemap.mjs'  'node --experimental-modules generate-ListSitemap.mjs' 'node --experimental-modules generate-BlogSitemap.mjs' 'node --experimental-modules generate-MainSitemap.mjs' 'node --experimental-modules generate-MapSitemap.mjs' 'node --experimental-modules generate-SharkSpeciesSitemap.mjs' 'node --experimental-modules generate-NudeBeachSitemap.mjs' 'node --experimental-modules generate-NewsSitemap.mjs' 'next dev'", "build": "next build", "start": "concurrently -k 'node --experimental-modules generate-sitemap.mjs' 'node --experimental-modules generate-CountrySitemap.mjs' 'node --experimental-modules generate-StateSitemap.mjs' 'node --experimental-modules generate-IslandSitemap.mjs'  'node --experimental-modules generate-CitySitemap.mjs'  'node --experimental-modules generate-BeachSitemap.mjs'  'node --experimental-modules generate-ListSitemap.mjs' 'node --experimental-modules generate-BlogSitemap.mjs' 'node --experimental-modules generate-MainSitemap.mjs' 'node --experimental-modules generate-MapSitemap.mjs' 'node --experimental-modules generate-SharkSpeciesSitemap.mjs' 'node --experimental-modules generate-NudeBeachSitemap.mjs' 'node --experimental-modules generate-NewsSitemap.mjs' 'next start'", "dev2": "next dev", "build2": "next build", "start2": "next start", "turbo": "next dev --turbo", "lint": "next lint"}, "dependencies": {"@mapbox/mapbox-gl-geocoder": "^5.0.3", "algoliasearch": "^4.22.1", "antd": "^5.16.0", "axios": "^1.6.5", "cheerio": "^1.0.0-rc.12", "concurrently": "^8.2.2", "dayjs": "^1.11.13", "dotenv": "^16.4.5", "firebase": "^10.9.0", "fs-extra": "^11.2.0", "html-react-parser": "^5.2.3", "lodash": "^4.17.21", "mapbox-gl": "^3.4.0", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "next": "14.0.4", "next-nprogress-bar": "^2.3.11", "node-cron": "^3.0.3", "react": "^18", "react-dom": "^18", "react-dropzone": "^14.2.3", "react-infinite-scroll-component": "^6.1.0", "react-use-clipboard": "^1.0.9", "sharp": "^0.33.4", "socket.io-client": "^4.8.1", "swiper": "^9.4.1"}, "devDependencies": {"@svgr/webpack": "^8.1.0", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "postcss": "^8", "tailwindcss": "^3.4.1"}}