/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    // container: {
    //   padding: {
    //     DEFAULT: "2rem",
    //     sm: "1rem",
    //     md: "2rem",
    //     lg: "2.5rem",
    //     xl: "10rem",
    //     "4xl": "12rem",
    //   },
    // },
    extend: {
      // screens: {
      //   425: "424px",
      //   375: "375px",
      //   320: { max: "339px" },
      //   1280: "1280px",
      //   "lg-1": { min: "1025", max: "1279px" },
      //   xs: { max: "400px" },
      //   // => @media (min-width: 340px and max-width: 639px) { ... }
      //   "2xl": { min: "1440px" },
      //   "3xl": { min: "1536px" },
      // },
      maxWidth: {
        exs: "319px",
        xs: "320px",
        425: "420px",
        // lg: "1024px",
        xl: "1280px",
        "2xl": "1536px",
        "3xl": "1792px",
        "4xl": "2048px",
      },
      screens: {
        exs: {max: "319px"},
        xs: {min: "280px", max: " 576px"},
        425: "424px",
        // sm: "640px",
        // md: "768px",
        // lg: "1024px",
        xl: "1280px",
        "2xl": {min: "1440px"},
        "3xl": {min: "1536px"},
        // "2xl": "1536px",
        // "3xl": "1920px",
        // "4xl": "2560px",
        // "5xl": "3840px",
        // "6xl": "4096px",
        "4xl": {min: "1900px"},
      },
      fontSize: {
        "sandee-xs": "0.5rem",
        "sandee-sm": "0.9rem",
        "sandee-10": "0.6rem",
        "sandee-12": "0.75rem",
        "sandee-14": "0.9rem",
        "sandee-18": "1.125rem",
        "sandee-20": "1.25rem",
        "sandee-24": "1.5rem",
        "sandee-32": "2rem",
        "sandee-48": "3rem",
        "sandee-base": "1rem",
        "sandee-xl": "1.25rem",
        "sandee-2xl": "1.563rem",
        "sandee-3xl": "1.953rem",
        "sandee-4xl": "2.441rem",
        "sandee-5xl": "3.052rem",
        "sandee-6xl": "3.8rem",
        "sandee-7xl": "4.8rem",
        "sandee-8xl": "5.5rem",
      },
      borderRadius: {
        sandee: "1.25rem",
      },
      colors: {
        primary: {
          600: "#00aae3",
        },
        " success-green-50": " #ECFDF5",
        "success-green-100": "#D1FAE5",
        "success-green-200": "#A7F3D0",
        "success-green-300": "#6EE7B7",
        "success-green-400": "#34D399",
        "success-green-500": "#10B981",
        "success-green-600": "#059669",
        "success-green-700": "#047857",
        "success-green-800": "#065F46",
        "success-green-900": "#064E3B",
        "error-red-50": "#FEF2F2",
        " error-red-100": "#FEE2E2",
        "error-red-200": " #FECACA",
        "error-red-300": "#FCA5A5",
        "error-red-400": "#F87171",
        "error-red-500": "#EF4444",
        "error-red-600": "#DC2626",
        "error-red-700": "#B91C1C",
        "error-red-800": "#991B1B",
        "error-red-900": "#7F1D1D",
        "sandee-blue": "#00AAE3",
        "sandee-footer": "#00AAE30D",
        "sandee-grey": "#7D7D7D",
        "sandee-orange": "#FF6B00",
        "sandee-cream": "#f7f5f0",
      },
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-conic":
          "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
      },
    },
  },
  plugins: [],
};
